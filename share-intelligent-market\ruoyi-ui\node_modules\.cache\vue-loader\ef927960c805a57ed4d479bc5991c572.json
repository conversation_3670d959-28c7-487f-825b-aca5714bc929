{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\product.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\product.vue", "mtime": 1750151094284}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBzZWFyY2hEYXRhIH0gZnJvbSAiQC9hcGkvZW50ZXJwcmlzZS9hcHBseSI7DQppbXBvcnQgeyBsaXN0RW51bSwgbGlzdENsYXNzaWZ5IH0gZnJvbSAiQC9hcGkvdG9vbC91dGlsIjsNCmltcG9ydCBwcm9kdWN0ZGV0YWlsIGZyb20gIi4vY29tcG9uZW50cy9wcm9kdWN0ZGV0YWlsLnZ1ZSI7DQppbXBvcnQgZVNvcnQgZnJvbSAiLi9jb21wb25lbnRzL2Utc29ydC52dWUiOw0KaW1wb3J0IHsNCiAgICBsaXN0RGF0YSwNCiAgICBvcERhdGEsDQogICAgb3BSZWNvbW1lbmQsDQogICAgcHJvZHVjdFN0b2NrLA0KfSBmcm9tICJAL2FwaS9zdG9yZS9wcm9kdWN0IjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgICBjb21wb25lbnRzOiB7DQogICAgICAgIHByb2R1Y3RkZXRhaWwsDQogICAgICAgIGVTb3J0LA0KICAgIH0sDQogICAgZGF0YSgpIHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHN0b2NrOiAwLCAvL+WVhuWTgeW6k+WtmOmHjw0KICAgICAgICAgICAgZm9ybToge30sDQogICAgICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgICAgICAvLyDkvpvlupTllYbliJfooagNCiAgICAgICAgICAgIGVudGVycHJpc2VPcHRpb25zOiBbXSwNCiAgICAgICAgICAgIC8vIOmAieS4reeahOS+m+W6lOWVhg0KICAgICAgICAgICAgZW50ZXJwcmlzZToge30sDQogICAgICAgICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgICAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAgICAgICB0eXBlT3B0aW9uczogW10sDQogICAgICAgICAgICBzdGF0dXNPcHRpb25zOiBbXSwNCiAgICAgICAgICAgIGNlbnRyYWxPcHRpb25zOiBbXSwNCiAgICAgICAgICAgIGdyb3VwT3B0aW9uczogW10sDQogICAgICAgICAgICByZWNPcHRpb25zOiBbDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICBrZXk6IDAsDQogICAgICAgICAgICAgICAgICAgIHZhbHVlOiAi5pmu6YCaIiwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAga2V5OiAxLA0KICAgICAgICAgICAgICAgICAgICB2YWx1ZTogIuaOqOiNkCIsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIF0sDQogICAgICAgICAgICAvLyDkuqflk4HliIbnsbsNCiAgICAgICAgICAgIGNsYXNzaWZ5T3B0aW9uczogW10sDQogICAgICAgICAgICAvLyDmgLvmnaHmlbANCiAgICAgICAgICAgIHRvdGFsOiAwLA0KICAgICAgICAgICAgLy8g6YCJ5Lit5Lqn5ZOB5YiG57G7DQogICAgICAgICAgICBjbGFzc2lmeTogW10sDQogICAgICAgICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgICAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgICAgICAgICAgZW50ZXJwcmlzZV9pZDogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIGNsYXNzaWZ5X2lkOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgY2xhc3NpZnkyX2lkOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgY2xhc3NpZnkzX2lkOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgdHlwZTogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIG5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICBzeXN0ZW1fbm86IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICByZWNvbW1lbmQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICBjZW50cmFsX3N0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIGdyb3VwX3N0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vIOWIl+ihqOaVsOaNrg0KICAgICAgICAgICAgbGlzdDogW10sDQogICAgICAgICAgICAvLyDlm77niYfpooTop4jlnLDlnYANCiAgICAgICAgICAgIHNyY0xpc3Q6IFtdLA0KICAgICAgICAgICAgLy8g5o+Q56S65by556qXDQogICAgICAgICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgICAgICAgIC8vIOivpuaDheW8ueeqlw0KICAgICAgICAgICAgLy8g5by556qX5YaF5a65DQogICAgICAgICAgICBvcEZvcm06IHsNCiAgICAgICAgICAgICAgICBvcGlkOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2VsZWN0aW9uSWRzOiAnJywNCiAgICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLmdldEVudW1zKCk7DQogICAgICAgIHRoaXMuZ2V0Q2xhc3NpZnkoKTsNCiAgICB9LA0KICAgIG1ldGhvZHM6IHsNCiAgICAgICAgYmF0Y2hPcFN0YXR1cyhzdGF0dXMpew0KICAgICAgICAgICAgbGV0IHRpdGxlID0gc3RhdHVzID09ICJPRkZMSU5FIiA/ICLmibnph4/kuIvmnrYiIDogIuaJuemHj+S4iuaetiI7DQogICAgICAgICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKYiICsgdGl0bGUgKyAi6L+Z5Lqb5ZWG5ZOBPyIsICLmj5DnpLoiLCB7DQogICAgICAgICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB7DQogICAgICAgICAgICAgICAgICAgIG9waWQ6IHRoaXMuc2VsZWN0aW9uSWRzLA0KICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHN0YXR1cywNCiAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgIG9wRGF0YShkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfISIsDQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGlvbklkcyA9ICcnOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7DQogICAgICAgICAgICBsZXQgaWRzID0gdmFsLm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS5pZDsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgdGhpcy5zZWxlY3Rpb25JZHMgPSBpZHMuam9pbignLCcpOw0KICAgICAgICB9LA0KICAgICAgICAvKiDmn6Xor6LkvIHkuJrkv6Hmga8gKi8NCiAgICAgICAgcmVtb3RlRW50ZXJwcmlzZShlKSB7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgICAgc2VhcmNoRGF0YShlKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICB0aGlzLmVudGVycHJpc2VPcHRpb25zID0gcmVzLmRhdGE7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyog5YiH5o2i5LyB5Lia5L+h5oGvICovDQogICAgICAgIGNoYW5nZUVudGVycHJpc2UoZSkgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbnRlcnByaXNlX2lkID0gdGhpcy5lbnRlcnByaXNlLmlkOw0KICAgICAgICB9LA0KICAgICAgICBnZXRFbnVtcygpIHsNCiAgICAgICAgICAgIGxpc3RFbnVtKCkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy50eXBlT3B0aW9ucyA9IHJlcy5kYXRhLnByb2R1Y3RUeXBlOw0KICAgICAgICAgICAgICAgIHRoaXMuc3RhdHVzT3B0aW9ucyA9IHJlcy5kYXRhLnByb2R1Y3RTdGF0dXM7DQogICAgICAgICAgICAgICAgdGhpcy5jZW50cmFsT3B0aW9ucyA9IHJlcy5kYXRhLmNlbnRyYWxTdGF0dXM7DQogICAgICAgICAgICAgICAgdGhpcy5ncm91cE9wdGlvbnMgPSByZXMuZGF0YS5ncm91cFN0YXR1czsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICBnZXRDbGFzc2lmeSgpIHsNCiAgICAgICAgICAgIGxpc3RDbGFzc2lmeSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuY2xhc3NpZnlPcHRpb25zID0gcmVzLmRhdGE7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOafpeivouWIl+ihqCAqLw0KICAgICAgICBnZXRMaXN0KCkgew0KICAgICAgICAgICAgaWYgKHRoaXMuY2xhc3NpZnkpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNsYXNzaWZ5X2lkID0gdGhpcy5jbGFzc2lmeVswXTsNCiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNsYXNzaWZ5Ml9pZCA9DQogICAgICAgICAgICAgICAgICAgIHRoaXMuY2xhc3NpZnkubGVuZ3RoID4gMSA/IHRoaXMuY2xhc3NpZnlbMV0gOiAtMTsNCiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNsYXNzaWZ5M19pZCA9DQogICAgICAgICAgICAgICAgICAgIHRoaXMuY2xhc3NpZnkubGVuZ3RoID4gMiA/IHRoaXMuY2xhc3NpZnlbMl0gOiAtMTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jbGFzc2lmeV9pZCA9IC0xOw0KICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2xhc3NpZnkyX2lkID0gLTE7DQogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jbGFzc2lmeTNfaWQgPSAtMTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGxpc3REYXRhKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMubGlzdCA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXMuY291bnQ7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOihqOWNleaQnOe0oiAqLw0KICAgICAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5p+l6K+iDQogICAgICAgIGhhbmRsZVByZXZpZXcodXJsKSB7DQogICAgICAgICAgICB0aGlzLnNyY0xpc3QgPSBbdXJsXTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOmHjee9riAqLw0KICAgICAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5jbGFzc2lmeSA9IFtdOw0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgICAgICAgICBlbnRlcnByaXNlX2lkOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgY2xhc3NpZnlfaWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICBjbGFzc2lmeTJfaWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICBjbGFzc2lmeTNfaWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICB0eXBlOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgbmFtZTogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIHN0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIHByb2R1Y3Rfbm86IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICByZWNvbW1lbmQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICBjZW50cmFsX3N0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIGdyb3VwX3N0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB9LA0KICAgICAgICAvKiDnvJbovpHmjpLluo8gKi8NCiAgICAgICAgaGFuZGxlU29ydChyb3cpIHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuc29ydC5vcGVuKHJvdy5pZCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOivpuaDhQ0KICAgICAgICBoYW5kbGVEZWF0aWxzKHJvdykgew0KICAgICAgICAgICAgdGhpcy4kcmVmcy5wcm9kdWN0ZGV0YWlsLm9wZW4ocm93LmlkKTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5a6h5qC46YCa6L+HDQogICAgICAgIGhhbmRsZVN0YXR1cyhyb3cpIHsNCiAgICAgICAgICAgIHRoaXMub3BGb3JtLm9waWQgPSByb3cuaWQ7DQogICAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICB9LA0KICAgICAgICBvcFN0YXR1cyhzdGF0dXMpIHsNCiAgICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5vcEZvcm0uc3RhdHVzID0gc3RhdHVzOw0KICAgICAgICAgICAgb3BEYXRhKHRoaXMub3BGb3JtKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvKiDngrnlh7vmjqjojZDmjInpkq4gKi8NCiAgICAgICAgaGFuZGxlUmVjb21tZW5kKHJvdywgcmVjb21tZW5kLCBpbmRleCkgew0KICAgICAgICAgICAgbGV0IHRpdGxlID0gcmVjb21tZW5kID09IDEgPyAi5o6o6I2QIiA6ICLlj5bmtojmjqjojZAiOw0KICAgICAgICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCmIiArIHRpdGxlICsgIuivpeWVhuWTgT8iLCAi5o+Q56S6Iiwgew0KICAgICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gew0KICAgICAgICAgICAgICAgICAgICBvcGlkOiByb3cuaWQsDQogICAgICAgICAgICAgICAgICAgIHJlY29tbWVuZDogcmVjb21tZW5kLA0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgICAgb3BSZWNvbW1lbmQoZGF0YSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5saXN0W2luZGV4XS5yZWNvbW1lbmQgPSByZWNvbW1lbmQ7DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyog54K55Ye75LiK5LiL5p625oyJ6ZKuICovDQogICAgICAgIGhhbmRsZU9ubGluZShyb3csIHN0YXR1cywgaW5kZXgpIHsNCiAgICAgICAgICAgIGxldCB0aXRsZSA9IHN0YXR1cyA9PSAiT0ZGTElORSIgPyAi5LiL5p62IiA6ICLkuIrmnrYiOw0KICAgICAgICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCmIiArIHRpdGxlICsgIuivpeWVhuWTgT8iLCAi5o+Q56S6Iiwgew0KICAgICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gew0KICAgICAgICAgICAgICAgICAgICBvcGlkOiByb3cuaWQsDQogICAgICAgICAgICAgICAgICAgIHN0YXR1czogc3RhdHVzLA0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgICAgb3BEYXRhKGRhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8hIiwNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIHRoaXMubGlzdFtpbmRleF0uc3RhdHVzID0gc3RhdHVzOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLmxpc3RbaW5kZXhdLnN0YXR1c1N0ciA9DQogICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXMgPT0gIk9GRkxJTkUiID8gIuW3suS4i+aetiIgOiAi5bey5LiK5p62IjsNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlvLnnqpflj5bmtogNCiAgICAgICAgZGlhbG9nQ2FuY2VsVmlzaWJsZSgpIHsNCiAgICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlh7rlhaXlupMNCiAgICAgICAgaGFuZE9wZW4ocm93KSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZyhyb3cpOw0KICAgICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICAgIHByb2R1Y3RfbmFtZTogcm93Lm5hbWUsDQogICAgICAgICAgICAgICAgcHJvZHVjdF9pZDogcm93LmlkLA0KICAgICAgICAgICAgICAgIG51bTogMSwNCiAgICAgICAgICAgICAgICB0eXBlOiAxLCAvLzDlhaXlupMgMeWHuuW6kw0KICAgICAgICAgICAgICAgIGRlYml0OiBudWxsLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIHRoaXMuc3RvY2sgPSBOdW1iZXIocm93LnN0b2NrKTsNCiAgICAgICAgICAgIGlmIChyb3cuc3RvY2sgPT0gMCkgew0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS50eXBlID0gTnVtYmVyKDApOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgfSwNCiAgICAgICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgICAgICAgIGlmICghdGhpcy5mb3JtLm51bSB8fCB0aGlzLmZvcm0ubnVtID09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5pWw6YePIiwNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5kZWJpdCB8fCB0aGlzLmZvcm0uZGViaXQgPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmlLbmlrnlkI3np7Av5YWl5pa55ZCN56ewIiwNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICB9DQogICAgICAgICAgICBsZXQgb2JqID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm0pKTsNCiAgICAgICAgICAgIG9iai5udW0gPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybS5udW0udG9GaXhlZCg2KSkpOw0KICAgICAgICAgICAgcHJvZHVjdFN0b2NrKG9iaikudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8iLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5kaWFsb2dDYW5jZWwoKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5ZWG5ZOB5pWw6YeP5pS55Y+YDQogICAgICAgIGNoYW5nZU51bShlKSB7DQogICAgICAgICAgICB2YXIgcmVzdWx0ID0gdGhpcy5udW07DQogICAgICAgICAgICB0aGlzLmZvcm0ubnVtID0gTnVtYmVyKFN0cmluZyhlKS5yZXBsYWNlKC9cLS9nLCAiIikpOw0KICAgICAgICAgICAgaWYgKCF0aGlzLnJ1bGVCdXlWZXJpZnkoKSkgew0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5udW0gPSByZXN1bHQ7DQogICAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIC8v6LSt5Lmw6LW35Lu35qCh6aqMDQogICAgICAgIHJ1bGVCdXlWZXJpZnkoKSB7DQogICAgICAgICAgICAvLyBpZiAodGhpcy5mb3JtLnR5cGUgPT0gMCkgew0KICAgICAgICAgICAgLy8gICAgIHRoaXMuJG1lc3NhZ2UoIui2heWHuuS6p+WTgeW6k+WtmOmHjyIpOw0KICAgICAgICAgICAgLy8gICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgIGlmICh0aGlzLmZvcm0udHlwZSA9PSAxICYmIHRoaXMuZm9ybS5udW0gPiB0aGlzLnN0b2NrKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSgi6LaF5Ye65Lqn5ZOB5bqT5a2Y6YePIik7DQogICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5udW0gPD0gMCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoIuivt+i+k+WFpeaVsOmHj++8gSIpOw0KICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgICB9LA0KICAgICAgICBkaWFsb2dDYW5jZWwoKSB7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuZm9ybSA9IHt9Ow0KICAgICAgICAgICAgdGhpcy5yZXNldFF1ZXJ5KCk7DQogICAgICAgIH0sDQogICAgfSwNCn07DQo="}, {"version": 3, "sources": ["product.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "product.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row>\r\n            <el-col :span=\"24\" :xs=\"24\">\r\n                <el-form\r\n                    :model=\"queryParams\"\r\n                    ref=\"queryForm\"\r\n                    :inline=\"true\"\r\n                    v-show=\"showSearch\"\r\n                    label-width=\"68px\"\r\n                >\r\n                    <el-form-item label=\"\" prop=\"type\">\r\n                        <el-select\r\n                            clearable\r\n                            v-model=\"queryParams.type\"\r\n                            placeholder=\"产品类型\"\r\n                            size=\"small\"\r\n                            style=\"width: 120px\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in typeOptions\"\r\n                                :key=\"item.key\"\r\n                                :label=\"item.value\"\r\n                                :value=\"item.key\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\" prop=\"status\">\r\n                        <el-select\r\n                            clearable\r\n                            v-model=\"queryParams.status\"\r\n                            placeholder=\"审核状态\"\r\n                            size=\"small\"\r\n                            style=\"width: 120px\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in statusOptions\"\r\n                                :key=\"item.key\"\r\n                                :label=\"item.value\"\r\n                                :value=\"item.key\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\" prop=\"group_status\">\r\n                        <el-select\r\n                            clearable\r\n                            v-model=\"queryParams.group_status\"\r\n                            placeholder=\"团购状态\"\r\n                            size=\"small\"\r\n                            style=\"width: 120px\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in groupOptions\"\r\n                                :key=\"item.key\"\r\n                                :label=\"item.value\"\r\n                                :value=\"item.key\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\" prop=\"central_status\">\r\n                        <el-select\r\n                            clearable\r\n                            v-model=\"queryParams.central_status\"\r\n                            placeholder=\"集采状态\"\r\n                            size=\"small\"\r\n                            style=\"width: 120px\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in centralOptions\"\r\n                                :key=\"item.key\"\r\n                                :label=\"item.value\"\r\n                                :value=\"item.key\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\" prop=\"recommend\">\r\n                        <el-select\r\n                            clearable\r\n                            v-model=\"queryParams.recommend\"\r\n                            placeholder=\"推荐状态\"\r\n                            size=\"small\"\r\n                            style=\"width: 120px\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in recOptions\"\r\n                                :key=\"item.key\"\r\n                                :label=\"item.value\"\r\n                                :value=\"item.key\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\" prop=\"enterprise_id\">\r\n                        <el-select\r\n                            clearable\r\n                            style=\"width: 280px\"\r\n                            v-model=\"enterprise\"\r\n                            filterable\r\n                            size=\"small\"\r\n                            remote\r\n                            reserve-keyword\r\n                            placeholder=\"请输入企业名称\"\r\n                            :remote-method=\"remoteEnterprise\"\r\n                            @change=\"changeEnterprise\"\r\n                            value-key=\"id\"\r\n                            :loading=\"loading\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in enterpriseOptions\"\r\n                                :key=\"item.id\"\r\n                                :label=\"item.name\"\r\n                                :value=\"item\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\">\r\n                        <el-cascader\r\n                            filterable\r\n                            clearable\r\n                            placeholder=\"选择产品分类\"\r\n                            v-model=\"classify\"\r\n                            size=\"small\"\r\n                            :options=\"classifyOptions\"\r\n                            :props=\"{\r\n                                label: 'name',\r\n                                value: 'id',\r\n                                checkStrictly: true,\r\n                            }\"\r\n                        ></el-cascader>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\">\r\n                        <el-input\r\n                            clearable\r\n                            v-model=\"queryParams.name\"\r\n                            placeholder=\"输入商品名称\"\r\n                            :maxlength=\"60\"\r\n                            size=\"small\"\r\n                            style=\"width: 280px\"\r\n                        ></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\">\r\n                        <el-input\r\n                            clearable\r\n                            v-model=\"queryParams.system_no\"\r\n                            placeholder=\"输入系统编码\"\r\n                            size=\"small\"\r\n                            style=\"width: 200px\"\r\n                        ></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item>\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            icon=\"el-icon-search\"\r\n                            size=\"mini\"\r\n                            @click=\"handleQuery\"\r\n                            >搜索</el-button\r\n                        >\r\n                        <el-button\r\n                            icon=\"el-icon-refresh\"\r\n                            size=\"mini\"\r\n                            @click=\"resetQuery\"\r\n                            >重置</el-button\r\n                        >\r\n                    </el-form-item>\r\n                </el-form>\r\n\r\n                <el-row :gutter=\"10\" class=\"mb8\">\r\n                    <el-button type=\"primary\" @click=\"batchOpStatus('ONLINE')\">批量上架</el-button>\r\n                    <el-button type=\"primary\" @click=\"batchOpStatus('OFFLINE')\">批量下架</el-button>\r\n                    <right-toolbar\r\n                        :showSearch.sync=\"showSearch\"\r\n                        @queryTable=\"getList\"\r\n                    ></right-toolbar>\r\n\r\n                </el-row>\r\n\r\n                <el-table \r\n                    v-loading=\"loading\" \r\n                    height=\"500\" \r\n                    :data=\"list\" \r\n                    @selection-change=\"handleSelectionChange\">\r\n                    <el-table-column\r\n                        type=\"selection\"\r\n                        width=\"55\">\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                        label=\"创建时间\"\r\n                        align=\"center\"\r\n                        prop=\"create_time\"\r\n                        width=\"160\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"系统编码\"\r\n                        align=\"center\"\r\n                        prop=\"system_no\"\r\n                        width=\"140\"\r\n                    />\r\n                    <!-- <el-table-column label=\"产品编码\" align=\"center\" prop=\"product_no\" width=\"140\" /> -->\r\n                    <el-table-column\r\n                        label=\"企业名称\"\r\n                        align=\"center\"\r\n                        prop=\"enterprise_name\"\r\n                        width=\"240\"\r\n                        :show-overflow-tooltip=\"true\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品分类\"\r\n                        align=\"center\"\r\n                        prop=\"classify_name\"\r\n                        width=\"180\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品名称\"\r\n                        align=\"center\"\r\n                        prop=\"name\"\r\n                        width=\"240\"\r\n                        :show-overflow-tooltip=\"true\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品类型\"\r\n                        align=\"center\"\r\n                        prop=\"typeStr\"\r\n                        width=\"100\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品含税价\"\r\n                        align=\"center\"\r\n                        prop=\"tax_price\"\r\n                        width=\"100\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品税率\"\r\n                        align=\"center\"\r\n                        prop=\"tax_rate\"\r\n                        width=\"100\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品单位\"\r\n                        align=\"center\"\r\n                        prop=\"unit\"\r\n                        width=\"100\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品品牌\"\r\n                        align=\"center\"\r\n                        prop=\"brand\"\r\n                        width=\"120\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品库存\"\r\n                        align=\"center\"\r\n                        prop=\"stock\"\r\n                        width=\"120\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品销量\"\r\n                        align=\"center\"\r\n                        prop=\"sales\"\r\n                        width=\"120\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"产品型号\"\r\n                        align=\"center\"\r\n                        prop=\"model\"\r\n                        width=\"120\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"批次号\"\r\n                        align=\"center\"\r\n                        prop=\"batchno\"\r\n                        width=\"120\"\r\n                    />\r\n                    <el-table-column label=\"排序\" align=\"center\" width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div>\r\n                                <span>{{ scope.row.sorts }}</span>\r\n                                <el-button\r\n                                    type=\"text\"\r\n                                    icon=\"el-icon-edit\"\r\n                                    size=\"mini\"\r\n                                    @click=\"handleSort(scope.row)\"\r\n                                    >编辑</el-button\r\n                                >\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                        label=\"审核状态\"\r\n                        align=\"center\"\r\n                        prop=\"status\"\r\n                        width=\"120px\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"mini\" type=\"warning\">{{\r\n                                scope.row.statusStr\r\n                            }}</el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                        label=\"操作\"\r\n                        align=\"center\"\r\n                        width=\"250\"\r\n                        fixed=\"right\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button\r\n                                v-if=\"scope.row.status == 'WAIT'\"\r\n                                type=\"text\"\r\n                                size=\"mini\"\r\n                                @click=\"handleStatus(scope.row)\"\r\n                                >审核\r\n                            </el-button>\r\n                            <el-button\r\n                                type=\"text\"\r\n                                size=\"mini\"\r\n                                icon=\"el-icon-view\"\r\n                                @click=\"handleDeatils(scope.row)\"\r\n                                >详情</el-button\r\n                            >\r\n                            <el-button\r\n                                v-if=\"scope.row.status == 'ONLINE'\"\r\n                                type=\"text\"\r\n                                size=\"mini\"\r\n                                icon=\"el-icon-upload2\"\r\n                                @click=\"\r\n                                    handleOnline(\r\n                                        scope.row,\r\n                                        'OFFLINE',\r\n                                        scope.$index\r\n                                    )\r\n                                \"\r\n                                >下架\r\n                            </el-button>\r\n                            <el-button\r\n                                v-if=\"scope.row.status == 'OFFLINE'\"\r\n                                type=\"text\"\r\n                                size=\"mini\"\r\n                                icon=\"el-icon-download\"\r\n                                @click=\"\r\n                                    handleOnline(\r\n                                        scope.row,\r\n                                        'ONLINE',\r\n                                        scope.$index\r\n                                    )\r\n                                \"\r\n                                >上架\r\n                            </el-button>\r\n                            <el-button\r\n                                v-if=\"\r\n                                    scope.row.status == 'ONLINE' &&\r\n                                    scope.row.recommend == 0\r\n                                \"\r\n                                type=\"text\"\r\n                                size=\"mini\"\r\n                                icon=\"el-icon-caret-top\"\r\n                                @click=\"\r\n                                    handleRecommend(scope.row, 1, scope.$index)\r\n                                \"\r\n                                >设为推荐\r\n                            </el-button>\r\n                            <el-button\r\n                                v-if=\"\r\n                                    scope.row.status == 'ONLINE' &&\r\n                                    scope.row.recommend == 1\r\n                                \"\r\n                                type=\"text\"\r\n                                size=\"mini\"\r\n                                icon=\"el-icon-caret-bottom\"\r\n                                @click=\"\r\n                                    handleRecommend(scope.row, 0, scope.$index)\r\n                                \"\r\n                                >取消推荐\r\n                            </el-button>\r\n                            <el-button\r\n                                type=\"text\"\r\n                                size=\"mini\"\r\n                                @click=\"handOpen(scope.row)\"\r\n                                >出入库\r\n                            </el-button>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <pagination\r\n                    v-show=\"total > 0\"\r\n                    :total=\"total\"\r\n                    :page.sync=\"queryParams.pageNum\"\r\n                    :limit.sync=\"queryParams.pageSize\"\r\n                    @pagination=\"getList\"\r\n                />\r\n            </el-col>\r\n        </el-row>\r\n        <el-dialog\r\n            title=\"产品审核\"\r\n            :visible.sync=\"dialogVisible\"\r\n            width=\"30%\"\r\n            center\r\n        >\r\n            <el-input\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                placeholder=\"审核备注\"\r\n                v-model=\"opForm.remark\"\r\n            >\r\n            </el-input>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"opStatus('ONLINE')\"\r\n                    >通 过</el-button\r\n                >\r\n                <el-button type=\"danger\" @click=\"opStatus('DENY')\"\r\n                    >驳 回</el-button\r\n                >\r\n                <el-button @click=\"dialogCancelVisible\">取 消</el-button>\r\n            </span>\r\n        </el-dialog>\r\n        <productdetail ref=\"productdetail\" @refresh=\"getList\"></productdetail>\r\n        <!-- 编辑排序-->\r\n        <e-sort ref=\"sort\" @refresh=\"getList\"></e-sort>\r\n        <!-- 出入库数量 -->\r\n        <el-dialog\r\n            title=\"出入库\"\r\n            :visible.sync=\"open\"\r\n            append-to-body\r\n            center\r\n            width=\"40%\"\r\n        >\r\n            <el-form\r\n                :label-position=\"'rigth'\"\r\n                :model=\"form\"\r\n                label-width=\"120px\"\r\n            >\r\n                <el-form-item\r\n                    class=\"is-required\"\r\n                    label=\"商品名称\"\r\n                    prop=\"product_name\"\r\n                >\r\n                    <el-input\r\n                        disabled\r\n                        v-model=\"form.product_name\"\r\n                        type=\"text\"\r\n                        placeholder=\"商品名称\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item class=\"is-required\" label=\"数量\" prop=\"num\">\r\n                    <el-input\r\n                        v-model=\"form.num\"\r\n                        @input=\"changeNum\"\r\n                        type=\"number\"\r\n                        :min=\"1\"\r\n                        placeholder=\"请输入数量\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item class=\"is-required\" label=\"出库/入库\" prop=\"type\">\r\n                    <el-radio-group v-model=\"form.type\">\r\n                        <el-radio\r\n                            v-model=\"form.type\"\r\n                            v-if=\"stock != 0\"\r\n                            :label=\"1\"\r\n                            >出库</el-radio\r\n                        >\r\n                        <el-radio v-model=\"form.type\" :label=\"0\">入库</el-radio>\r\n                    </el-radio-group>\r\n                </el-form-item>\r\n                <el-form-item\r\n                    class=\"is-required\"\r\n                    label=\"收方名称/入方名称\"\r\n                    prop=\"debit\"\r\n                >\r\n                    <el-input\r\n                        v-model=\"form.debit\"\r\n                        type=\"text\"\r\n                        placeholder=\"收方名称/入方名称\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">提 交</el-button>\r\n                <el-button @click=\"dialogCancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <!---->\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { searchData } from \"@/api/enterprise/apply\";\r\nimport { listEnum, listClassify } from \"@/api/tool/util\";\r\nimport productdetail from \"./components/productdetail.vue\";\r\nimport eSort from \"./components/e-sort.vue\";\r\nimport {\r\n    listData,\r\n    opData,\r\n    opRecommend,\r\n    productStock,\r\n} from \"@/api/store/product\";\r\nexport default {\r\n    components: {\r\n        productdetail,\r\n        eSort,\r\n    },\r\n    data() {\r\n        return {\r\n            stock: 0, //商品库存量\r\n            form: {},\r\n            open: false,\r\n            // 遮罩层\r\n            loading: false,\r\n            // 供应商列表\r\n            enterpriseOptions: [],\r\n            // 选中的供应商\r\n            enterprise: {},\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            typeOptions: [],\r\n            statusOptions: [],\r\n            centralOptions: [],\r\n            groupOptions: [],\r\n            recOptions: [\r\n                {\r\n                    key: 0,\r\n                    value: \"普通\",\r\n                },\r\n                {\r\n                    key: 1,\r\n                    value: \"推荐\",\r\n                },\r\n            ],\r\n            // 产品分类\r\n            classifyOptions: [],\r\n            // 总条数\r\n            total: 0,\r\n            // 选中产品分类\r\n            classify: [],\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                enterprise_id: undefined,\r\n                classify_id: undefined,\r\n                classify2_id: undefined,\r\n                classify3_id: undefined,\r\n                type: undefined,\r\n                name: undefined,\r\n                status: undefined,\r\n                system_no: undefined,\r\n                recommend: undefined,\r\n                central_status: undefined,\r\n                group_status: undefined,\r\n            },\r\n            // 列表数据\r\n            list: [],\r\n            // 图片预览地址\r\n            srcList: [],\r\n            // 提示弹窗\r\n            dialogVisible: false,\r\n            // 详情弹窗\r\n            // 弹窗内容\r\n            opForm: {\r\n                opid: undefined,\r\n                status: undefined,\r\n                remark: undefined,\r\n            },\r\n            selectionIds: '',\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n        this.getEnums();\r\n        this.getClassify();\r\n    },\r\n    methods: {\r\n        batchOpStatus(status){\r\n            let title = status == \"OFFLINE\" ? \"批量下架\" : \"批量上架\";\r\n            this.$confirm(\"是否\" + title + \"这些商品?\", \"提示\", {\r\n                type: \"warning\",\r\n            }).then(() => {\r\n                let data = {\r\n                    opid: this.selectionIds,\r\n                    status: status,\r\n                };\r\n                opData(data).then((res) => {\r\n                    this.$message({\r\n                        type: \"success\",\r\n                        message: \"操作成功!\",\r\n                    });\r\n                    this.selectionIds = '';\r\n                    this.getList();\r\n                });\r\n            });\r\n        },\r\n        handleSelectionChange(val) {\r\n            let ids = val.map(item => {\r\n                return item.id;\r\n            });\r\n            this.selectionIds = ids.join(',');\r\n        },\r\n        /* 查询企业信息 */\r\n        remoteEnterprise(e) {\r\n            this.loading = true;\r\n            searchData(e).then((res) => {\r\n                this.loading = false;\r\n                this.enterpriseOptions = res.data;\r\n            });\r\n        },\r\n        /* 切换企业信息 */\r\n        changeEnterprise(e) {\r\n            this.queryParams.enterprise_id = this.enterprise.id;\r\n        },\r\n        getEnums() {\r\n            listEnum().then((res) => {\r\n                this.typeOptions = res.data.productType;\r\n                this.statusOptions = res.data.productStatus;\r\n                this.centralOptions = res.data.centralStatus;\r\n                this.groupOptions = res.data.groupStatus;\r\n            });\r\n        },\r\n        getClassify() {\r\n            listClassify().then((res) => {\r\n                this.classifyOptions = res.data;\r\n            });\r\n        },\r\n        /** 查询列表 */\r\n        getList() {\r\n            if (this.classify) {\r\n                this.queryParams.classify_id = this.classify[0];\r\n                this.queryParams.classify2_id =\r\n                    this.classify.length > 1 ? this.classify[1] : -1;\r\n                this.queryParams.classify3_id =\r\n                    this.classify.length > 2 ? this.classify[2] : -1;\r\n            } else {\r\n                this.queryParams.classify_id = -1;\r\n                this.queryParams.classify2_id = -1;\r\n                this.queryParams.classify3_id = -1;\r\n            }\r\n            listData(this.queryParams).then((res) => {\r\n                this.list = res.data;\r\n                this.total = res.count;\r\n            });\r\n        },\r\n        /** 表单搜索 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        // 查询\r\n        handlePreview(url) {\r\n            this.srcList = [url];\r\n        },\r\n        /** 重置 */\r\n        resetQuery() {\r\n            this.classify = [];\r\n            this.queryParams = {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                enterprise_id: undefined,\r\n                classify_id: undefined,\r\n                classify2_id: undefined,\r\n                classify3_id: undefined,\r\n                type: undefined,\r\n                name: undefined,\r\n                status: undefined,\r\n                product_no: undefined,\r\n                recommend: undefined,\r\n                central_status: undefined,\r\n                group_status: undefined,\r\n            };\r\n            this.resetForm(\"queryForm\");\r\n            this.getList();\r\n        },\r\n        /* 编辑排序 */\r\n        handleSort(row) {\r\n            this.$refs.sort.open(row.id);\r\n        },\r\n        // 详情\r\n        handleDeatils(row) {\r\n            this.$refs.productdetail.open(row.id);\r\n        },\r\n        // 审核通过\r\n        handleStatus(row) {\r\n            this.opForm.opid = row.id;\r\n            this.dialogVisible = true;\r\n        },\r\n        opStatus(status) {\r\n            this.dialogVisible = false;\r\n            this.opForm.status = status;\r\n            opData(this.opForm).then((res) => {\r\n                this.$message({\r\n                    type: \"success\",\r\n                    message: \"操作成功\",\r\n                });\r\n                this.getList();\r\n            });\r\n        },\r\n        /* 点击推荐按钮 */\r\n        handleRecommend(row, recommend, index) {\r\n            let title = recommend == 1 ? \"推荐\" : \"取消推荐\";\r\n            this.$confirm(\"是否\" + title + \"该商品?\", \"提示\", {\r\n                type: \"warning\",\r\n            }).then(() => {\r\n                let data = {\r\n                    opid: row.id,\r\n                    recommend: recommend,\r\n                };\r\n                opRecommend(data).then((res) => {\r\n                    this.$message({\r\n                        type: \"success\",\r\n                        message: \"操作成功!\",\r\n                    });\r\n                    this.list[index].recommend = recommend;\r\n                });\r\n            });\r\n        },\r\n        /* 点击上下架按钮 */\r\n        handleOnline(row, status, index) {\r\n            let title = status == \"OFFLINE\" ? \"下架\" : \"上架\";\r\n            this.$confirm(\"是否\" + title + \"该商品?\", \"提示\", {\r\n                type: \"warning\",\r\n            }).then(() => {\r\n                let data = {\r\n                    opid: row.id,\r\n                    status: status,\r\n                };\r\n                opData(data).then((res) => {\r\n                    this.$message({\r\n                        type: \"success\",\r\n                        message: \"操作成功!\",\r\n                    });\r\n                    this.list[index].status = status;\r\n                    this.list[index].statusStr =\r\n                        status == \"OFFLINE\" ? \"已下架\" : \"已上架\";\r\n                });\r\n            });\r\n        },\r\n        // 弹窗取消\r\n        dialogCancelVisible() {\r\n            this.dialogVisible = false;\r\n        },\r\n        // 出入库\r\n        handOpen(row) {\r\n            console.log(row);\r\n            this.form = {\r\n                product_name: row.name,\r\n                product_id: row.id,\r\n                num: 1,\r\n                type: 1, //0入库 1出库\r\n                debit: null,\r\n            };\r\n            this.stock = Number(row.stock);\r\n            if (row.stock == 0) {\r\n                this.form.type = Number(0);\r\n            }\r\n            this.open = true;\r\n        },\r\n        submitForm() {\r\n            if (!this.form.num || this.form.num == undefined) {\r\n                this.$message({\r\n                    message: \"请输入数量\",\r\n                    type: \"error\",\r\n                });\r\n                return;\r\n            }\r\n            if (!this.form.debit || this.form.debit == undefined) {\r\n                this.$message({\r\n                    message: \"请输入收方名称/入方名称\",\r\n                    type: \"error\",\r\n                });\r\n                return;\r\n            }\r\n            let obj = JSON.parse(JSON.stringify(this.form));\r\n            obj.num = JSON.parse(JSON.stringify(this.form.num.toFixed(6)));\r\n            productStock(obj).then((res) => {\r\n                if (res.code == 200) {\r\n                    this.$message({\r\n                        message: \"操作成功\",\r\n                        type: \"success\",\r\n                    });\r\n                    this.dialogCancel();\r\n                }\r\n            });\r\n        },\r\n        // 商品数量改变\r\n        changeNum(e) {\r\n            var result = this.num;\r\n            this.form.num = Number(String(e).replace(/\\-/g, \"\"));\r\n            if (!this.ruleBuyVerify()) {\r\n                this.form.num = result;\r\n            }\r\n        },\r\n        //购买起价校验\r\n        ruleBuyVerify() {\r\n            // if (this.form.type == 0) {\r\n            //     this.$message(\"超出产品库存量\");\r\n            //     return false;\r\n            // }\r\n            if (this.form.type == 1 && this.form.num > this.stock) {\r\n                this.$message(\"超出产品库存量\");\r\n                return false;\r\n            }\r\n            if (this.form.num <= 0) {\r\n                this.$message(\"请输入数量！\");\r\n                return false;\r\n            }\r\n            return true;\r\n        },\r\n        dialogCancel() {\r\n            this.open = false;\r\n            this.form = {};\r\n            this.resetQuery();\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}