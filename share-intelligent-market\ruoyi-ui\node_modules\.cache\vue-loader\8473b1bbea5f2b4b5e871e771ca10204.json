{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\login.vue", "mtime": 1750151094240}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\r\n      <h3 class=\"title\">产销平台</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaOnOff\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right;\" v-if=\"register\">\r\n          <router-link class=\"link-type\" :to=\"'/register'\">立即注册</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2018-2022 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from \"@/api/login\";\r\nimport Cookies from \"js-cookie\";\r\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\r\n\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      codeUrl: \"\",\r\n      loginForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        rememberMe: false,\r\n        code: \"\",\r\n        uuid: \"\"\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\r\n        ],\r\n        // code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\r\n      },\r\n      loading: false,\r\n      // 验证码开关\r\n      captchaOnOff: true,\r\n      // 注册开关\r\n      register: false,\r\n      redirect: undefined\r\n    };\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        this.redirect = route.query && route.query.redirect;\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode();\r\n    this.getCookie();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        console.log(res)\r\n        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;\r\n        if (this.captchaOnOff) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.loginForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    getCookie() {\r\n      const username = Cookies.get(\"username\");\r\n      const password = Cookies.get(\"password\");\r\n      const rememberMe = Cookies.get('rememberMe')\r\n      this.loginForm = {\r\n        username: username === undefined ? this.loginForm.username : username,\r\n        password: password === undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)\r\n      };\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\r\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 });\r\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });\r\n          } else {\r\n            Cookies.remove(\"username\");\r\n            Cookies.remove(\"password\");\r\n            Cookies.remove('rememberMe');\r\n          }\r\n          this.$store.dispatch(\"Login\", this.loginForm).then(() => {\r\n            this.$router.push({ path: \"/\" }).catch(()=>{});\r\n          }).catch(() => {\r\n            this.loading = false;\r\n            if (this.captchaOnOff) {\r\n              this.getCode();\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"]}]}