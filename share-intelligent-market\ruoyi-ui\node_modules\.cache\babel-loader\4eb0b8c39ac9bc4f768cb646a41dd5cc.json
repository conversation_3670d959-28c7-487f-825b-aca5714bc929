{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\components\\orders.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\components\\orders.vue", "mtime": 1750151094225}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfbGlzdCA9IHJlcXVpcmUoIkAvYXBpL2NlbnRyYWwvbGlzdCIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlvLnnqpcKICAgICAgY29sbGVjdGlvbkRpYWxvZzogZmFsc2UsCiAgICAgIHRpdGxlOiAn6aKE57qm5YiX6KGoJwogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdChpZCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICAoMCwgX2xpc3QubGlzdHJlc291cmNlcykoaWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzLmNvbGxlY3Rpb25EaWFsb2cgPSB0cnVlOwogICAgICAgIF90aGlzLmxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyog54K55Ye756Gu6K6kICovc3VibWl0OiBmdW5jdGlvbiBzdWJtaXQoKSB7CiAgICAgIHRoaXMuY29sbGVjdGlvbkRpYWxvZyA9IGZhbHNlOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_list", "require", "data", "loading", "list", "collectionDialog", "title", "created", "methods", "getList", "id", "_this", "listresources", "then", "res", "submit"], "sources": ["src/views/central/components/orders.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :title=\"title\" :visible.sync=\"collectionDialog\" width=\"90%\" center>\r\n      <el-table :data=\"list\">\r\n        <el-table-column type='index' label=\"序号\" align=\"center\"  width=\"50px\" />\r\n        <el-table-column label=\"公司名称\" align=\"center\" prop=\"demand_name\" />\r\n        <el-table-column label=\"联系人\" align=\"center\" prop=\"demand_proxy\" />\r\n        <el-table-column label=\"联系方式\" align=\"center\" prop=\"demand_phone\" />\r\n        <el-table-column label=\"联系地址\" align=\"center\" prop=\"demand_location\" />\r\n        <el-table-column label=\"集采数量\" align=\"center\" prop=\"total_number\" />\r\n        <el-table-column label=\"订金金额\" align=\"center\" prop=\"total_price\" />\r\n        <el-table-column label=\"订单号\" align=\"center\" prop=\"order_no\" />\r\n      </el-table>\r\n      <el-row>\r\n        <el-col :span=\"24\">\r\n          <div class=\"text-center mt-30\">\r\n            <el-button type=\"primary\" @click=\"submit\">关闭</el-button>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import { listresources } from '@/api/central/list';\r\n  export default {\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: false,\r\n        // 表格数据\r\n        list: [],\r\n        // 弹窗\r\n        collectionDialog: false,\r\n        title: '预约列表'\r\n      };\r\n    },\r\n    created() {\r\n    },\r\n    methods: {\r\n      /** 查询列表 */\r\n      getList(id) {\r\n        listresources(id).then(res => {\r\n          this.collectionDialog = true;\r\n          this.list = res.data;\r\n        })\r\n      },\r\n      /* 点击确认 */\r\n      submit() {\r\n        this.collectionDialog = false;\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style scoped>\r\n</style>\r\n"], "mappings": ";;;;;;AAyBA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,IAAA;MACA;MACAC,gBAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACA,WACAC,OAAA,WAAAA,QAAAC,EAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,mBAAA,EAAAF,EAAA,EAAAG,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAN,gBAAA;QACAM,KAAA,CAAAP,IAAA,GAAAU,GAAA,CAAAZ,IAAA;MACA;IACA;IACA,UACAa,MAAA,WAAAA,OAAA;MACA,KAAAV,gBAAA;IACA;EACA;AACA", "ignoreList": []}]}