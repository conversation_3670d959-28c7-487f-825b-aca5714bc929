11:48:30.801 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:48:32.121 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0
11:48:32.225 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:32.271 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:32.283 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:32.526 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 240 ms to scan 271 urls, producing 0 keys and 0 values 
11:48:32.538 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:32.552 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:32.569 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:32.784 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 212 ms to scan 271 urls, producing 0 keys and 0 values 
11:48:32.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:32.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/788592721
11:48:32.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1107779742
11:48:32.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:32.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:32.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:34.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477714642_127.0.0.1_63784
11:48:34.985 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] Notify connected event to listeners.
11:48:34.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:34.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63b15f3-d7b5-4a9b-9902-fba28625802d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1919497442
11:48:35.131 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:48:41.369 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
11:48:41.370 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:48:41.371 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:48:42.006 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:48:44.199 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
11:48:44.204 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
11:48:44.204 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:48:53.372 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:48:54.130 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5aaf3493-f348-4e6b-b8ee-248fcad0634a
11:48:54.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] RpcClient init label, labels = {module=naming, source=sdk}
11:48:54.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:48:54.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:48:54.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:48:54.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:54.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477734145_127.0.0.1_64249
11:48:54.250 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] Notify connected event to listeners.
11:48:54.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:54.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5aaf3493-f348-4e6b-b8ee-248fcad0634a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1919497442
