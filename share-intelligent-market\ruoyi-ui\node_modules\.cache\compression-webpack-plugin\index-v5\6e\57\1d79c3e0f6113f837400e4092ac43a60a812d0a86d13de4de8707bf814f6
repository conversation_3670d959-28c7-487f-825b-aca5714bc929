
5d642a9d6c736d553b3ce790ade931ff60f53e7f	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fui\\u002Ftinymce-5\\u002Fskin.css\",\"contentHash\":\"3e29b83db7c541f438fb4498c2f45870\"}","integrity":"sha512-l7GlWtaKrx5JmL9aipyc7dTfJe0op3ThQR+JUN9qcXuam3rAQaYTjm0KS6g7Zn8awjKF45AcYkOfEmcREv/tng==","time":1750496064265,"size":45560}