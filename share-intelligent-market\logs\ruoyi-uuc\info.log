11:48:39.321 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:48:40.866 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0
11:48:40.988 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 59 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:41.059 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:41.081 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:41.377 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 292 ms to scan 237 urls, producing 0 keys and 0 values 
11:48:41.399 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:41.419 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:41.436 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:41.742 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 303 ms to scan 237 urls, producing 0 keys and 0 values 
11:48:41.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:41.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1963980755
11:48:41.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1672736386
11:48:41.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:41.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:41.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:44.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477723661_127.0.0.1_64027
11:48:44.016 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] Notify connected event to listeners.
11:48:44.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:44.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbd25e8f-857d-496d-be6a-294ef7b5e82e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/561772958
11:48:44.148 [main] INFO  c.r.u.RuoyiUucApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:48:48.866 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9705"]
11:48:48.868 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:48:48.868 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:48:49.252 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:48:54.112 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:48:54.827 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0781e874-0a1a-45b2-b972-b97cd6a121b4
11:48:54.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] RpcClient init label, labels = {module=naming, source=sdk}
11:48:54.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:48:54.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:48:54.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:48:54.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:54.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477734846_127.0.0.1_64259
11:48:54.965 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Notify connected event to listeners.
11:48:54.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:54.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/561772958
11:48:58.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9705"]
11:48:58.615 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-uuc ************:9705 register finished
11:48:59.081 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Receive server push request, request = NotifySubscriberRequest, requestId = 52
11:48:59.085 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0781e874-0a1a-45b2-b972-b97cd6a121b4] Ack server push request, request = NotifySubscriberRequest, requestId = 52
11:48:59.103 [main] INFO  c.r.u.RuoyiUucApplication - [logStarted,61] - Started RuoyiUucApplication in 20.918 seconds (JVM running for 22.537)
11:48:59.138 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc.yaml, group=DEFAULT_GROUP
11:48:59.139 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc-dev.yaml, group=DEFAULT_GROUP
11:48:59.139 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-uuc, group=DEFAULT_GROUP
11:48:59.557 [RMI TCP Connection(5)-************] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:49:00.007 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
