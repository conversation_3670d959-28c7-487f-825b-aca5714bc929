{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\service\\banner.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\service\\banner.js", "mtime": 1750151093970}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGREYXRhID0gYWRkRGF0YTsKZXhwb3J0cy5kZWxEYXRhID0gZGVsRGF0YTsKZXhwb3J0cy5lZGl0RGF0YSA9IGVkaXREYXRhOwpleHBvcnRzLmdldERhdGEgPSBnZXREYXRhOwpleHBvcnRzLmxpc3REYXRhID0gbGlzdERhdGE7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyBiYW5uZXIKCi8vIOiOt+WPluWIl+ihqOaVsOaNrgpmdW5jdGlvbiBsaXN0RGF0YSgpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogInNob3AvYWRtaW4vYmFubmVyL2xpc3QiLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDojrflj5bor6bmg4XmlbDmja4KZnVuY3Rpb24gZ2V0RGF0YShpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAic2hvcC9hZG1pbi9iYW5uZXIvZGV0YWlsLyIuY29uY2F0KGlkKSwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5pWw5o2uCmZ1bmN0aW9uIGFkZERhdGEocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICdzaG9wL2FkbWluL2Jhbm5lci9hZGQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBwYXJhbXM6IHBhcmFtcwogIH0pOwp9CgovLyDnvJbovpHmlbDmja4KZnVuY3Rpb24gZWRpdERhdGEocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICdzaG9wL2FkbWluL2Jhbm5lci9lZGl0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQoKLy8g5Yig6Zmk5pWw5o2uCmZ1bmN0aW9uIGRlbERhdGEoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJ3Nob3AvYWRtaW4vYmFubmVyL2RlbD9vcGlkPScgKyBpZCwKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listData", "request", "url", "method", "getData", "id", "concat", "addData", "params", "editData", "delData"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/service/banner.js"], "sourcesContent": ["// banner\r\nimport request from '@/utils/request'\r\n\r\n// 获取列表数据\r\nexport function listData() {\r\n  return request({\r\n    url: `shop/admin/banner/list`,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 获取详情数据\r\nexport function getData(id) {\r\n  return request({\r\n    url: `shop/admin/banner/detail/${id}`,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 新增数据\r\nexport function addData(params) {\r\n  return request({\r\n    url: 'shop/admin/banner/add',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n// 编辑数据\r\nexport function editData(params) {\r\n  return request({\r\n    url: 'shop/admin/banner/edit',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n// 删除数据\r\nexport function delData(id) {\r\n  return request({\r\n    url: 'shop/admin/banner/del?opid='+id,\r\n    method: 'post',\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AACA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AADA;;AAGA;AACO,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,0BAA0B;IAC7BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,OAAOA,CAACC,EAAE,EAAE;EAC1B,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,8BAAAI,MAAA,CAA8BD,EAAE,CAAE;IACrCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdK,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,QAAQA,CAACD,MAAM,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdK,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACL,EAAE,EAAE;EAC1B,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B,GAACG,EAAE;IACrCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}