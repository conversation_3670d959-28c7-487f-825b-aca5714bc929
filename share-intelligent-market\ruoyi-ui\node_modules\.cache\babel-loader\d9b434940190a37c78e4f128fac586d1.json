{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\product\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\product\\index.vue", "mtime": 1750151094260}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_product", "require", "_product_follow", "name", "dicts", "data", "checkPhone", "rule", "value", "callback", "reg", "test", "Error", "loading", "ids", "single", "multiple", "showSearch", "total", "productList", "title", "open", "open1", "queryParams", "pageNum", "pageSize", "productName", "collaborative", "contact", "phone", "recommendStatus", "form", "form1", "rules", "productNo", "required", "message", "trigger", "type", "validator", "pictures", "rules1", "productId", "principal", "watch", "handler", "newVal", "oldVal", "_this", "$refs", "validateField", "_ref", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "valid", "w", "_context", "n", "clearValidate", "a", "_x", "apply", "arguments", "deep", "created", "getList", "methods", "handleFollow", "item", "reset", "id", "remark", "_this2", "listProduct", "then", "response", "rows", "cancel", "cancel1", "btypeCode", "btypeName", "shortName", "label", "image", "content", "attaches", "createBy", "createTime", "updateBy", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "length", "handleAdd", "handleUpdate", "row", "_this3", "getProduct", "submitForm", "_this4", "validate", "updateProduct", "$modal", "msgSuccess", "addProduct", "submitForm1", "_this5", "addProduct_follow", "handleDelete", "_this6", "confirm", "delProduct", "catch", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime"], "sources": ["src/views/ningmengdou/product/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"服务名称\" prop=\"productName\">\r\n                <el-input\r\n                    v-model=\"queryParams.productName\"\r\n                    placeholder=\"请输入服务名称\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"合作领域\" prop=\"collaborative\">\r\n                <el-select\r\n                    v-model=\"queryParams.collaborative\"\r\n                    placeholder=\"请选择合作领域\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_collaborative_areas\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"contact\">\r\n                <el-input\r\n                    v-model=\"queryParams.contact\"\r\n                    placeholder=\"请输入联系人\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                <el-input\r\n                    v-model=\"queryParams.phone\"\r\n                    placeholder=\"请输入联系电话\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"recommendStatus\">\r\n                <el-select\r\n                    v-model=\"queryParams.recommendStatus\"\r\n                    placeholder=\"请选择状态\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_online\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:product:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:product:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:product:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['uuc:product:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"productList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n            <el-table-column label=\"产品编号\" align=\"center\" prop=\"productNo\" />\r\n            <el-table-column\r\n                label=\"服务名称\"\r\n                align=\"center\"\r\n                prop=\"productName\"\r\n            />\r\n            <el-table-column\r\n                label=\"供应商编码\"\r\n                align=\"center\"\r\n                prop=\"btypeCode\"\r\n            />\r\n            <el-table-column\r\n                label=\"供应商名称\"\r\n                align=\"center\"\r\n                prop=\"btypeName\"\r\n            />\r\n            <el-table-column\r\n                label=\"供应商简称\"\r\n                align=\"center\"\r\n                prop=\"shortName\"\r\n            />\r\n            <el-table-column\r\n                label=\"合作领域\"\r\n                align=\"center\"\r\n                prop=\"collaborative\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_collaborative_areas\"\r\n                        :value=\"scope.row.collaborative\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"contact\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"phone\" />\r\n            <!-- <el-table-column label=\"介绍\" align=\"center\" prop=\"content\" /> -->\r\n            <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n            <el-table-column label=\"状态\" align=\"center\" prop=\"recommendStatus\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_online\"\r\n                        :value=\"scope.row.recommendStatus\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"图片\"\r\n                align=\"center\"\r\n                prop=\"pictures\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <image-preview\r\n                        :src=\"scope.row.pictures\"\r\n                        :width=\"50\"\r\n                        :height=\"50\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:product:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:product:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleFollow(scope.row)\"\r\n                        v-hasPermi=\"['uuc:product:edit']\"\r\n                        >跟进</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改服务管理对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            v-if=\"open\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n                <el-form-item label=\"产品编号\" prop=\"productNo\">\r\n                    <el-input\r\n                        v-model=\"form.productNo\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入产品编号\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"服务名称\" prop=\"productName\">\r\n                    <el-input\r\n                        v-model=\"form.productName\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入服务名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"供应商编码\" prop=\"btypeCode\">\r\n                    <el-input\r\n                        v-model=\"form.btypeCode\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入供应商编码\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"供应商名称\" prop=\"btypeName\">\r\n                    <el-input\r\n                        v-model=\"form.btypeName\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入供应商名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"供应商简称\" prop=\"shortName\">\r\n                    <el-input\r\n                        v-model=\"form.shortName\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入供应商简称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"合作领域\" prop=\"collaborative\">\r\n                    <el-select\r\n                        v-model=\"form.collaborative\"\r\n                        placeholder=\"请选择合作领域\"\r\n                    >\r\n                        <el-option\r\n                            v-for=\"dict in dict.type.uuc_collaborative_areas\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.label\"\r\n                            :value=\"dict.value\"\r\n                        ></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" prop=\"contact\">\r\n                    <el-input\r\n                        v-model=\"form.contact\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入联系人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                    <el-input\r\n                        v-model=\"form.phone\"\r\n                        maxlength=\"50\"\r\n                        type=\"number\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"介绍\">\r\n                    <el-input\r\n                        v-model=\"form.content\"\r\n                        maxlength=\"500\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                    <!-- <editor v-model=\"form.content\" :min-height=\"192\"/> -->\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        maxlength=\"500\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"标签\" prop=\"label\">\r\n                    <el-input\r\n                        v-model=\"form.label\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入标签\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\">\r\n                    <el-radio-group v-model=\"form.recommendStatus\">\r\n                        <el-radio\r\n                            v-for=\"dict in dict.type.uuc_online\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.value\"\r\n                            >{{ dict.label }}</el-radio\r\n                        >\r\n                    </el-radio-group>\r\n                </el-form-item>\r\n                <el-form-item label=\"图片\" prop=\"pictures\">\r\n                    <image-upload v-model=\"form.pictures\" :limit=\"1\" />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <!-- 跟进 -->\r\n        <el-dialog\r\n            title=\"服务跟进\"\r\n            :visible.sync=\"open1\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form\r\n                ref=\"form1\"\r\n                :model=\"form1\"\r\n                :rules=\"rules1\"\r\n                label-width=\"80px\"\r\n            >\r\n                <el-form-item label=\"服务id\" prop=\"productId\">\r\n                    <el-input\r\n                        disabled\r\n                        v-model=\"form1.productId\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入服务id\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"服务名称\" prop=\"productName\">\r\n                    <el-input\r\n                        disabled\r\n                        v-model=\"form1.productName\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入服务名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"负责人\" prop=\"principal\">\r\n                    <el-input\r\n                        v-model=\"form1.principal\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入负责人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form1.remark\"\r\n                        maxlength=\"500\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm1\">确 定</el-button>\r\n                <el-button @click=\"cancel1\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listProduct,\r\n    getProduct,\r\n    delProduct,\r\n    addProduct,\r\n    updateProduct,\r\n} from \"@/api/uuc/product\";\r\nimport { addProduct_follow } from \"@/api/uuc/product_follow\";\r\n\r\nexport default {\r\n    name: \"Product\",\r\n    dicts: [\"uuc_online\", \"uuc_collaborative_areas\"],\r\n    data() {\r\n        let checkPhone = (rule, value, callback) => {\r\n            let reg = /^1[345789]\\d{9}$/;\r\n            if (!reg.test(value)) {\r\n                callback(new Error(\"请输入11位手机号\"));\r\n            } else {\r\n                callback();\r\n            }\r\n        };\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 服务管理表格数据\r\n            productList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            open1: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                productName: null,\r\n                collaborative: null,\r\n                contact: null,\r\n                phone: null,\r\n                recommendStatus: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            form1: {},\r\n            // 表单校验\r\n            rules: {\r\n                productNo: [\r\n                    {\r\n                        required: true,\r\n                        message: \"产品编号不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                productName: [\r\n                    {\r\n                        required: true,\r\n                        message: \"服务名称不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                // btypeCode:[\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"供应商编码不能为空\",\r\n                //         trigger: \"blur\",\r\n                //     },\r\n                // ],\r\n                collaborative: [\r\n                    {\r\n                        required: true,\r\n                        message: \"合作领域不能为空\",\r\n                        trigger: \"change\",\r\n                    },\r\n                ],\r\n                contact: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                phone: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系电话不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    {\r\n                        type: \"number\",\r\n                        validator: checkPhone,\r\n                        message: \"请输入正确的手机号\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                pictures:[\r\n                    {\r\n                        required: true,\r\n                        message: \"图片不能为空\",\r\n                        trigger: \"change\",\r\n                    },\r\n                ],\r\n            },\r\n            rules1: {\r\n                productId: [\r\n                    {\r\n                        required: true,\r\n                        message: \"服务id不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                productName: [\r\n                    {\r\n                        required: true,\r\n                        message: \"服务名称不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                principal: [\r\n                    {\r\n                        required: true,\r\n                        message: \"负责人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    watch: {\r\n        form: {\r\n            handler(newVal, oldVal) {\r\n                this.$refs[\"form\"].validateField([\"pictures\"],async (valid)=>{\r\n                  if(this.form.pictures){\r\n                      if(valid){\r\n                        this.$refs[\"form\"].clearValidate('pictures'); \r\n                      }\r\n                    }\r\n                })\r\n            },\r\n            deep: true,\r\n        },\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        handleFollow(item) {\r\n            this.reset();\r\n            this.form1 = {\r\n                productId: item.id,\r\n                productName: item.productName,\r\n                principal: null,\r\n                remark: null,\r\n            };\r\n            this.open1 = true;\r\n        },\r\n        /** 查询服务管理列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listProduct(this.queryParams).then((response) => {\r\n                this.productList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        cancel1() {\r\n            this.open1 = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                productNo: null,\r\n                productName: null,\r\n                btypeCode: null,\r\n                btypeName: null,\r\n                shortName: null,\r\n                collaborative: null,\r\n                contact: null,\r\n                phone: null,\r\n                label: null,\r\n                image: null,\r\n                content: null,\r\n                remark: null,\r\n                recommendStatus: \"0\",\r\n                pictures: null,\r\n                attaches: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.form1 = {\r\n                productId: null,\r\n                productName: null,\r\n                principal: null,\r\n                remark: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n            this.resetForm(\"form1\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加服务管理\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getProduct(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改服务管理\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.id != null) {\r\n                        updateProduct(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addProduct(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        submitForm1() {\r\n            this.$refs[\"form1\"].validate((valid) => {\r\n                if (valid) {\r\n                    addProduct_follow(this.form1).then((response) => {\r\n                        this.$modal.msgSuccess(\"跟进成功\");\r\n                        this.open1 = false;\r\n                        this.getList();\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除服务管理编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delProduct(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/product/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `product_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAwZA,IAAAA,QAAA,GAAAC,OAAA;AAOA,IAAAC,eAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,UAAA,YAAAA,WAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,GAAA;MACA,KAAAA,GAAA,CAAAC,IAAA,CAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACA;MACA;MACAI,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACAC,KAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,OAAA;QACAC,KAAA;QACAC,eAAA;MACA;MACA;MACAC,IAAA;MACAC,KAAA;MACA;MACAC,KAAA;QACAC,SAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAX,WAAA,GACA;UACAS,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAV,aAAA,GACA;UACAQ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAT,OAAA,GACA;UACAO,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAR,KAAA,GACA;UACAM,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,GACA;UACAC,IAAA;UACAC,SAAA,EAAAjC,UAAA;UACA8B,OAAA;UACAC,OAAA;QACA,EACA;QACAG,QAAA,GACA;UACAL,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAI,MAAA;QACAC,SAAA,GACA;UACAP,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAX,WAAA,GACA;UACAS,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAM,SAAA,GACA;UACAR,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAO,KAAA;IACAb,IAAA;MACAc,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAAC,KAAA;QACA,KAAAC,KAAA,SAAAC,aAAA;UAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAAC,KAAA;YAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;cAAA,kBAAAA,QAAA,CAAAC,CAAA;gBAAA;kBACA,IAAAZ,KAAA,CAAAjB,IAAA,CAAAS,QAAA;oBACA,IAAAiB,KAAA;sBACAT,KAAA,CAAAC,KAAA,SAAAY,aAAA;oBACA;kBACA;gBAAA;kBAAA,OAAAF,QAAA,CAAAG,CAAA;cAAA;YAAA,GAAAN,OAAA;UAAA,CACA;UAAA,iBAAAO,EAAA;YAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;UAAA;QAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MACA,KAAAC,KAAA;MACA,KAAAxC,KAAA;QACAU,SAAA,EAAA6B,IAAA,CAAAE,EAAA;QACA/C,WAAA,EAAA6C,IAAA,CAAA7C,WAAA;QACAiB,SAAA;QACA+B,MAAA;MACA;MACA,KAAApD,KAAA;IACA;IACA,eACA8C,OAAA,WAAAA,QAAA;MAAA,IAAAO,MAAA;MACA,KAAA9D,OAAA;MACA,IAAA+D,oBAAA,OAAArD,WAAA,EAAAsD,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAAxD,WAAA,GAAA2D,QAAA,CAAAC,IAAA;QACAJ,MAAA,CAAAzD,KAAA,GAAA4D,QAAA,CAAA5D,KAAA;QACAyD,MAAA,CAAA9D,OAAA;MACA;IACA;IACA;IACAmE,MAAA,WAAAA,OAAA;MACA,KAAA3D,IAAA;MACA,KAAAmD,KAAA;IACA;IACAS,OAAA,WAAAA,QAAA;MACA,KAAA3D,KAAA;MACA,KAAAkD,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAzC,IAAA;QACA0C,EAAA;QACAvC,SAAA;QACAR,WAAA;QACAwD,SAAA;QACAC,SAAA;QACAC,SAAA;QACAzD,aAAA;QACAC,OAAA;QACAC,KAAA;QACAwD,KAAA;QACAC,KAAA;QACAC,OAAA;QACAb,MAAA;QACA5C,eAAA;QACAU,QAAA;QACAgD,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACA,KAAA5D,KAAA;QACAU,SAAA;QACAhB,WAAA;QACAiB,SAAA;QACA+B,MAAA;MACA;MACA,KAAAmB,SAAA;MACA,KAAAA,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAvE,WAAA,CAAAC,OAAA;MACA,KAAA4C,OAAA;IACA;IACA,aACA2B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnF,GAAA,GAAAmF,SAAA,CAAAC,GAAA,WAAA3B,IAAA;QAAA,OAAAA,IAAA,CAAAE,EAAA;MAAA;MACA,KAAA1D,MAAA,GAAAkF,SAAA,CAAAE,MAAA;MACA,KAAAnF,QAAA,IAAAiF,SAAA,CAAAE,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAA5B,KAAA;MACA,KAAAnD,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAiF,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA/B,KAAA;MACA,IAAAC,EAAA,GAAA6B,GAAA,CAAA7B,EAAA,SAAA3D,GAAA;MACA,IAAA0F,mBAAA,EAAA/B,EAAA,EAAAI,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAAxE,IAAA,GAAA+C,QAAA,CAAAzE,IAAA;QACAkG,MAAA,CAAAlF,IAAA;QACAkF,MAAA,CAAAnF,KAAA;MACA;IACA;IACA,WACAqF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAzD,KAAA,SAAA0D,QAAA,WAAAlD,KAAA;QACA,IAAAA,KAAA;UACA,IAAAiD,MAAA,CAAA3E,IAAA,CAAA0C,EAAA;YACA,IAAAmC,sBAAA,EAAAF,MAAA,CAAA3E,IAAA,EAAA8C,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAG,MAAA,CAAAC,UAAA;cACAJ,MAAA,CAAArF,IAAA;cACAqF,MAAA,CAAAtC,OAAA;YACA;UACA;YACA,IAAA2C,mBAAA,EAAAL,MAAA,CAAA3E,IAAA,EAAA8C,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAG,MAAA,CAAAC,UAAA;cACAJ,MAAA,CAAArF,IAAA;cACAqF,MAAA,CAAAtC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA4C,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAhE,KAAA,UAAA0D,QAAA,WAAAlD,KAAA;QACA,IAAAA,KAAA;UACA,IAAAyD,iCAAA,EAAAD,MAAA,CAAAjF,KAAA,EAAA6C,IAAA,WAAAC,QAAA;YACAmC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;YACAG,MAAA,CAAA3F,KAAA;YACA2F,MAAA,CAAA7C,OAAA;UACA;QACA;MACA;IACA;IACA,aACA+C,YAAA,WAAAA,aAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,IAAAtG,GAAA,GAAAwF,GAAA,CAAA7B,EAAA,SAAA3D,GAAA;MACA,KAAA+F,MAAA,CACAQ,OAAA,oBAAAvG,GAAA,aACA+D,IAAA;QACA,WAAAyC,mBAAA,EAAAxG,GAAA;MACA,GACA+D,IAAA;QACAuC,MAAA,CAAAhD,OAAA;QACAgD,MAAA,CAAAP,MAAA,CAAAC,UAAA;MACA,GACAS,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,0BAAAC,cAAA,CAAArE,OAAA,MAEA,KAAA9B,WAAA,cAAAoG,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}