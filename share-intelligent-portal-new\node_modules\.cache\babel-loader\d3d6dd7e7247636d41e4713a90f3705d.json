{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\utils\\request.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\utils\\request.js", "mtime": 1750401870524}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_axios", "_interopRequireDefault", "require", "_elementUi", "_store", "_auth", "_errorCode", "_ruoyi", "_cache", "_fileSaver", "downloadLoadingInstance", "is<PERSON><PERSON>gin", "exports", "show", "axios", "defaults", "headers", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "isToken", "isRepeatSubmit", "repeatSubmit", "getToken", "method", "params", "url", "tansParams", "slice", "requestObj", "data", "_typeof2", "default", "JSON", "stringify", "time", "Date", "getTime", "session<PERSON>bj", "cache", "local", "getJSON", "undefined", "setJSON", "s_url", "s_data", "s_time", "interval", "message", "console", "warn", "concat", "Promise", "reject", "Error", "error", "log", "response", "res", "code", "msg", "errorCode", "responseType", "MessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "store", "dispatch", "location", "href", "catch", "Message", "Notification", "title", "includes", "substr", "length", "duration", "download", "filename", "Loading", "text", "spinner", "background", "post", "_objectSpread2", "transformRequest", "_ref", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "is<PERSON>ogin", "blob", "resText", "rspObj", "errMsg", "w", "_context", "n", "blobValidate", "v", "Blob", "saveAs", "parse", "close", "a", "_x", "apply", "arguments", "r", "_default"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/utils/request.js"], "sourcesContent": ["import axios from \"axios\";\r\nimport { Notification, MessageBox, Message, Loading } from \"element-ui\";\r\nimport store from \"@/store\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport errorCode from \"@/utils/errorCode\";\r\nimport { tansParams, blobValidate } from \"@/utils/ruoyi\";\r\nimport cache from \"@/plugins/cache\";\r\nimport { saveAs } from \"file-saver\";\r\n\r\nlet downloadLoadingInstance;\r\n// 是否显示重新登录\r\nexport let isRelogin = { show: false };\r\n\r\naxios.defaults.headers[\"Content-Type\"] = \"application/json;charset=utf-8\";\r\n// 创建axios实例\r\nconst service = axios.create({\r\n  // axios中请求配置有baseURL选项，表示请求URL公共部分\r\n  baseURL: process.env.VUE_APP_BASE_API,\r\n  // baseURL: \"\",\r\n  // 超时 - 增加到30秒以支持复杂操作如密码设置、SSO同步等\r\n  timeout: 30000,\r\n});\r\n\r\n// request拦截器\r\nservice.interceptors.request.use(\r\n  (config) => {\r\n    // 是否需要设置 token\r\n    const isToken = (config.headers || {}).isToken === false;\r\n    // 是否需要防止数据重复提交\r\n    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;\r\n    if (getToken() && !isToken) {\r\n      config.headers[\"Authorization\"] = \"Bearer \" + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改\r\n    }\r\n    // get请求映射params参数\r\n    if (config.method === \"get\" && config.params) {\r\n      let url = config.url + \"?\" + tansParams(config.params);\r\n      url = url.slice(0, -1);\r\n      config.params = {};\r\n      config.url = url;\r\n    }\r\n    if (\r\n      !isRepeatSubmit &&\r\n      (config.method === \"post\" || config.method === \"put\")\r\n    ) {\r\n      const requestObj = {\r\n        url: config.url,\r\n        data:\r\n          typeof config.data === \"object\"\r\n            ? JSON.stringify(config.data)\r\n            : config.data,\r\n        time: new Date().getTime(),\r\n      };\r\n      const sessionObj = cache.local.getJSON(\"sessionObj\");\r\n      if (\r\n        sessionObj === undefined ||\r\n        sessionObj === null ||\r\n        sessionObj === \"\"\r\n      ) {\r\n        cache.local.setJSON(\"sessionObj\", requestObj);\r\n      } else {\r\n        const s_url = sessionObj.url; // 请求地址\r\n        const s_data = sessionObj.data; // 请求数据\r\n        const s_time = sessionObj.time; // 请求时间\r\n        const interval = 1000; // 间隔时间(ms)，小于此时间视为重复提交\r\n        if (\r\n          s_data === requestObj.data &&\r\n          requestObj.time - s_time < interval &&\r\n          s_url === requestObj.url &&\r\n          s_url != \"/system/im/getUserListByIds\"\r\n        ) {\r\n          const message = \"数据正在处理，请勿重复提交\";\r\n          console.warn(`[${s_url}]: ` + message);\r\n          return Promise.reject(new Error(message));\r\n        } else {\r\n          cache.local.setJSON(\"sessionObj\", requestObj);\r\n        }\r\n      }\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.log(error);\r\n    Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 响应拦截器\r\nservice.interceptors.response.use(\r\n  (res) => {\r\n    // 未设置状态码则默认成功状态\r\n    const code = res.data.code || 200;\r\n    // 获取错误信息\r\n    const msg = errorCode[code] || res.data.msg || errorCode[\"default\"];\r\n    // 二进制数据则直接返回\r\n    if (\r\n      res.request.responseType === \"blob\" ||\r\n      res.request.responseType === \"arraybuffer\"\r\n    ) {\r\n      return res.data;\r\n    }\r\n    if (code === 401) {\r\n      if (!isRelogin.show) {\r\n        isRelogin.show = true;\r\n        MessageBox.confirm(\r\n          \"登录状态已过期，您可以继续留在该页面，或者重新登录\",\r\n          \"系统提示\",\r\n          {\r\n            confirmButtonText: \"重新登录\",\r\n            cancelButtonText: \"取消\",\r\n            type: \"warning\",\r\n          }\r\n        )\r\n          .then(() => {\r\n            isRelogin.show = false;\r\n            store.dispatch(\"LogOut\").then(() => {\r\n              location.href = \"/login\";\r\n            });\r\n          })\r\n          .catch(() => {\r\n            isRelogin.show = false;\r\n          });\r\n      }\r\n      return Promise.reject(\"无效的会话，或者会话已过期，请重新登录。\");\r\n    } else if (code === 500) {\r\n      Message({ message: msg, type: \"error\" });\r\n      return Promise.reject(new Error(msg));\r\n    } else if (code === 601) {\r\n      Message({ message: msg, type: \"warning\" });\r\n      return Promise.reject(\"error\");\r\n    } else if (code !== 200) {\r\n      Notification.error({ title: msg });\r\n      return Promise.reject(\"error\");\r\n    } else {\r\n      return res.data;\r\n    }\r\n  },\r\n  (error) => {\r\n    console.log(\"err\" + error);\r\n    let { message } = error;\r\n    if (message == \"Network Error\") {\r\n      message = \"后端接口连接异常\";\r\n    } else if (message.includes(\"timeout\")) {\r\n      message = \"系统接口请求超时\";\r\n    } else if (message.includes(\"Request failed with status code\")) {\r\n      message = \"系统接口\" + message.substr(message.length - 3) + \"异常\";\r\n    }\r\n    Message({ message: message, type: \"error\", duration: 5 * 1000 });\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 通用下载方法\r\nexport function download(url, params, filename, config) {\r\n  downloadLoadingInstance = Loading.service({\r\n    text: \"正在下载数据，请稍候\",\r\n    spinner: \"el-icon-loading\",\r\n    background: \"rgba(0, 0, 0, 0.7)\",\r\n  });\r\n  return service\r\n    .post(url, params, {\r\n      transformRequest: [\r\n        (params) => {\r\n          return tansParams(params);\r\n        },\r\n      ],\r\n      headers: { \"Content-Type\": \"application/x-www-form-urlencoded\" },\r\n      responseType: \"blob\",\r\n      ...config,\r\n    })\r\n    .then(async (data) => {\r\n      const isLogin = await blobValidate(data);\r\n      if (isLogin) {\r\n        const blob = new Blob([data]);\r\n        saveAs(blob, filename);\r\n      } else {\r\n        const resText = await data.text();\r\n        const rspObj = JSON.parse(resText);\r\n        const errMsg =\r\n          errorCode[rspObj.code] || rspObj.msg || errorCode[\"default\"];\r\n        Message.error(errMsg);\r\n      }\r\n      downloadLoadingInstance.close();\r\n    })\r\n    .catch((r) => {\r\n      console.error(r);\r\n      Message.error(\"下载文件出现错误，请联系管理员！\");\r\n      downloadLoadingInstance.close();\r\n    });\r\n}\r\n\r\nexport default service;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AAEA,IAAIQ,uBAAuB;AAC3B;AACO,IAAIC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG;EAAEE,IAAI,EAAE;AAAM,CAAC;AAEtCC,cAAK,CAACC,QAAQ,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;AACzE;AACA,IAAMC,OAAO,GAAGH,cAAK,CAACI,MAAM,CAAC;EAC3B;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrC;EACA;EACAC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAACC,MAAM,EAAK;EACV;EACA,IAAMC,OAAO,GAAG,CAACD,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEY,OAAO,KAAK,KAAK;EACxD;EACA,IAAMC,cAAc,GAAG,CAACF,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEc,YAAY,KAAK,KAAK;EACpE,IAAI,IAAAC,cAAQ,EAAC,CAAC,IAAI,CAACH,OAAO,EAAE;IAC1BD,MAAM,CAACX,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,IAAAe,cAAQ,EAAC,CAAC,CAAC,CAAC;EAC5D;EACA;EACA,IAAIJ,MAAM,CAACK,MAAM,KAAK,KAAK,IAAIL,MAAM,CAACM,MAAM,EAAE;IAC5C,IAAIC,GAAG,GAAGP,MAAM,CAACO,GAAG,GAAG,GAAG,GAAG,IAAAC,iBAAU,EAACR,MAAM,CAACM,MAAM,CAAC;IACtDC,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBT,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC;IAClBN,MAAM,CAACO,GAAG,GAAGA,GAAG;EAClB;EACA,IACE,CAACL,cAAc,KACdF,MAAM,CAACK,MAAM,KAAK,MAAM,IAAIL,MAAM,CAACK,MAAM,KAAK,KAAK,CAAC,EACrD;IACA,IAAMK,UAAU,GAAG;MACjBH,GAAG,EAAEP,MAAM,CAACO,GAAG;MACfI,IAAI,EACF,IAAAC,QAAA,CAAAC,OAAA,EAAOb,MAAM,CAACW,IAAI,MAAK,QAAQ,GAC3BG,IAAI,CAACC,SAAS,CAACf,MAAM,CAACW,IAAI,CAAC,GAC3BX,MAAM,CAACW,IAAI;MACjBK,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;IAC3B,CAAC;IACD,IAAMC,UAAU,GAAGC,cAAK,CAACC,KAAK,CAACC,OAAO,CAAC,YAAY,CAAC;IACpD,IACEH,UAAU,KAAKI,SAAS,IACxBJ,UAAU,KAAK,IAAI,IACnBA,UAAU,KAAK,EAAE,EACjB;MACAC,cAAK,CAACC,KAAK,CAACG,OAAO,CAAC,YAAY,EAAEd,UAAU,CAAC;IAC/C,CAAC,MAAM;MACL,IAAMe,KAAK,GAAGN,UAAU,CAACZ,GAAG,CAAC,CAAC;MAC9B,IAAMmB,MAAM,GAAGP,UAAU,CAACR,IAAI,CAAC,CAAC;MAChC,IAAMgB,MAAM,GAAGR,UAAU,CAACH,IAAI,CAAC,CAAC;MAChC,IAAMY,QAAQ,GAAG,IAAI,CAAC,CAAC;MACvB,IACEF,MAAM,KAAKhB,UAAU,CAACC,IAAI,IAC1BD,UAAU,CAACM,IAAI,GAAGW,MAAM,GAAGC,QAAQ,IACnCH,KAAK,KAAKf,UAAU,CAACH,GAAG,IACxBkB,KAAK,IAAI,6BAA6B,EACtC;QACA,IAAMI,OAAO,GAAG,eAAe;QAC/BC,OAAO,CAACC,IAAI,CAAC,IAAAC,MAAA,CAAIP,KAAK,WAAQI,OAAO,CAAC;QACtC,OAAOI,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACN,OAAO,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLT,cAAK,CAACC,KAAK,CAACG,OAAO,CAAC,YAAY,EAAEd,UAAU,CAAC;MAC/C;IACF;EACF;EACA,OAAOV,MAAM;AACf,CAAC,EACD,UAACoC,KAAK,EAAK;EACTN,OAAO,CAACO,GAAG,CAACD,KAAK,CAAC;EAClBH,OAAO,CAACC,MAAM,CAACE,KAAK,CAAC;AACvB,CACF,CAAC;;AAED;AACA9C,OAAO,CAACO,YAAY,CAACyC,QAAQ,CAACvC,GAAG,CAC/B,UAACwC,GAAG,EAAK;EACP;EACA,IAAMC,IAAI,GAAGD,GAAG,CAAC5B,IAAI,CAAC6B,IAAI,IAAI,GAAG;EACjC;EACA,IAAMC,GAAG,GAAGC,kBAAS,CAACF,IAAI,CAAC,IAAID,GAAG,CAAC5B,IAAI,CAAC8B,GAAG,IAAIC,kBAAS,CAAC,SAAS,CAAC;EACnE;EACA,IACEH,GAAG,CAACzC,OAAO,CAAC6C,YAAY,KAAK,MAAM,IACnCJ,GAAG,CAACzC,OAAO,CAAC6C,YAAY,KAAK,aAAa,EAC1C;IACA,OAAOJ,GAAG,CAAC5B,IAAI;EACjB;EACA,IAAI6B,IAAI,KAAK,GAAG,EAAE;IAChB,IAAI,CAACxD,SAAS,CAACE,IAAI,EAAE;MACnBF,SAAS,CAACE,IAAI,GAAG,IAAI;MACrB0D,qBAAU,CAACC,OAAO,CAChB,2BAA2B,EAC3B,MAAM,EACN;QACEC,iBAAiB,EAAE,MAAM;QACzBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CACF,CAAC,CACEC,IAAI,CAAC,YAAM;QACVjE,SAAS,CAACE,IAAI,GAAG,KAAK;QACtBgE,cAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAACF,IAAI,CAAC,YAAM;UAClCG,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC1B,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAAC,YAAM;QACXtE,SAAS,CAACE,IAAI,GAAG,KAAK;MACxB,CAAC,CAAC;IACN;IACA,OAAO+C,OAAO,CAACC,MAAM,CAAC,sBAAsB,CAAC;EAC/C,CAAC,MAAM,IAAIM,IAAI,KAAK,GAAG,EAAE;IACvB,IAAAe,kBAAO,EAAC;MAAE1B,OAAO,EAAEY,GAAG;MAAEO,IAAI,EAAE;IAAQ,CAAC,CAAC;IACxC,OAAOf,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACM,GAAG,CAAC,CAAC;EACvC,CAAC,MAAM,IAAID,IAAI,KAAK,GAAG,EAAE;IACvB,IAAAe,kBAAO,EAAC;MAAE1B,OAAO,EAAEY,GAAG;MAAEO,IAAI,EAAE;IAAU,CAAC,CAAC;IAC1C,OAAOf,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM,IAAIM,IAAI,KAAK,GAAG,EAAE;IACvBgB,uBAAY,CAACpB,KAAK,CAAC;MAAEqB,KAAK,EAAEhB;IAAI,CAAC,CAAC;IAClC,OAAOR,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM;IACL,OAAOK,GAAG,CAAC5B,IAAI;EACjB;AACF,CAAC,EACD,UAACyB,KAAK,EAAK;EACTN,OAAO,CAACO,GAAG,CAAC,KAAK,GAAGD,KAAK,CAAC;EAC1B,IAAMP,OAAO,GAAKO,KAAK,CAAjBP,OAAO;EACb,IAAIA,OAAO,IAAI,eAAe,EAAE;IAC9BA,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAAC6B,QAAQ,CAAC,SAAS,CAAC,EAAE;IACtC7B,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAAC6B,QAAQ,CAAC,iCAAiC,CAAC,EAAE;IAC9D7B,OAAO,GAAG,MAAM,GAAGA,OAAO,CAAC8B,MAAM,CAAC9B,OAAO,CAAC+B,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC9D;EACA,IAAAL,kBAAO,EAAC;IAAE1B,OAAO,EAAEA,OAAO;IAAEmB,IAAI,EAAE,OAAO;IAAEa,QAAQ,EAAE,CAAC,GAAG;EAAK,CAAC,CAAC;EAChE,OAAO5B,OAAO,CAACC,MAAM,CAACE,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACO,SAAS0B,QAAQA,CAACvD,GAAG,EAAED,MAAM,EAAEyD,QAAQ,EAAE/D,MAAM,EAAE;EACtDjB,uBAAuB,GAAGiF,kBAAO,CAAC1E,OAAO,CAAC;IACxC2E,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,iBAAiB;IAC1BC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,OAAO7E,OAAO,CACX8E,IAAI,CAAC7D,GAAG,EAAED,MAAM,MAAA+D,cAAA,CAAAxD,OAAA;IACfyD,gBAAgB,EAAE,CAChB,UAAChE,MAAM,EAAK;MACV,OAAO,IAAAE,iBAAU,EAACF,MAAM,CAAC;IAC3B,CAAC,CACF;IACDjB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAoC,CAAC;IAChEsD,YAAY,EAAE;EAAM,GACjB3C,MAAM,CACV,CAAC,CACDiD,IAAI;IAAA,IAAAsB,IAAA,OAAAC,kBAAA,CAAA3D,OAAA,mBAAA4D,aAAA,CAAA5D,OAAA,IAAA6D,CAAA,CAAC,SAAAC,QAAOhE,IAAI;MAAA,IAAAiE,OAAA,EAAAC,IAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,MAAA;MAAA,WAAAP,aAAA,CAAA5D,OAAA,IAAAoE,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OACO,IAAAC,mBAAY,EAACzE,IAAI,CAAC;UAAA;YAAlCiE,OAAO,GAAAM,QAAA,CAAAG,CAAA;YAAA,KACTT,OAAO;cAAAM,QAAA,CAAAC,CAAA;cAAA;YAAA;YACHN,IAAI,GAAG,IAAIS,IAAI,CAAC,CAAC3E,IAAI,CAAC,CAAC;YAC7B,IAAA4E,iBAAM,EAACV,IAAI,EAAEd,QAAQ,CAAC;YAACmB,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OAEDxE,IAAI,CAACsD,IAAI,CAAC,CAAC;UAAA;YAA3Ba,OAAO,GAAAI,QAAA,CAAAG,CAAA;YACPN,MAAM,GAAGjE,IAAI,CAAC0E,KAAK,CAACV,OAAO,CAAC;YAC5BE,MAAM,GACVtC,kBAAS,CAACqC,MAAM,CAACvC,IAAI,CAAC,IAAIuC,MAAM,CAACtC,GAAG,IAAIC,kBAAS,CAAC,SAAS,CAAC;YAC9Da,kBAAO,CAACnB,KAAK,CAAC4C,MAAM,CAAC;UAAC;YAExBjG,uBAAuB,CAAC0G,KAAK,CAAC,CAAC;UAAC;YAAA,OAAAP,QAAA,CAAAQ,CAAA;QAAA;MAAA,GAAAf,OAAA;IAAA,CACjC;IAAA,iBAAAgB,EAAA;MAAA,OAAApB,IAAA,CAAAqB,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC,CACDvC,KAAK,CAAC,UAACwC,CAAC,EAAK;IACZhE,OAAO,CAACM,KAAK,CAAC0D,CAAC,CAAC;IAChBvC,kBAAO,CAACnB,KAAK,CAAC,kBAAkB,CAAC;IACjCrD,uBAAuB,CAAC0G,KAAK,CAAC,CAAC;EACjC,CAAC,CAAC;AACN;AAAC,IAAAM,QAAA,GAAA9G,OAAA,CAAA4B,OAAA,GAEcvB,OAAO", "ignoreList": []}]}