{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\offer.vue?vue&type=template&id=c691c16e&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\offer.vue", "mtime": 1750151094273}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}