{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\demand\\index.vue?vue&type=template&id=42a99e5c", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\demand\\index.vue", "mtime": 1750151094254}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}