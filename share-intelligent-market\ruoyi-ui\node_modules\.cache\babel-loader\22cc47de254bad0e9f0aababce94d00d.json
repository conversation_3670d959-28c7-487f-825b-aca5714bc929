{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\library.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\library.vue", "mtime": 1750151094283}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_product", "require", "layout", "data", "open", "total", "form", "queryParams", "page", "limit", "type", "undefined", "product_id", "shopList", "list", "dialogVisible", "title", "loading", "options", "label", "value", "created", "getList", "remoteMethod", "methods", "query", "_this", "listData", "pageNum", "pageSize", "classify2_id", "classify3_id", "then", "res", "console", "log", "map", "item", "push", "id", "name", "_this2", "stockRecord", "count", "reset<PERSON><PERSON>y", "enterprise_id"], "sources": ["src/views/store/library.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row>\r\n            <!--用户数据-->\r\n            <el-col :span=\"24\" :xs=\"24\">\r\n                <el-form\r\n                    :model=\"queryParams\"\r\n                    ref=\"queryForm\"\r\n                    :inline=\"true\"\r\n                    label-width=\"68px\"\r\n                >\r\n                    <el-form-item label=\"\">\r\n                        <el-select\r\n                            v-model=\"queryParams.product_id\"\r\n                            placeholder=\"请选择商品\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in shopList\"\r\n                                :key=\"item.value\"\r\n                                :label=\"item.label\"\r\n                                :value=\"item.value\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\">\r\n                        <el-select\r\n                            v-model=\"queryParams.type\"\r\n                            placeholder=\"请选择出库/入库\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in options\"\r\n                                :key=\"item.value\"\r\n                                :label=\"item.label\"\r\n                                :value=\"item.value\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item>\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            icon=\"el-icon-search\"\r\n                            size=\"mini\"\r\n                            @click=\"getList\"\r\n                            >搜索</el-button\r\n                        >\r\n                        <el-button\r\n                            icon=\"el-icon-refresh\"\r\n                            size=\"mini\"\r\n                            @click=\"resetQuery\"\r\n                            >重置</el-button\r\n                        >\r\n                    </el-form-item>\r\n                </el-form>\r\n\r\n                <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n                    <right-toolbar\r\n                        :showSearch.sync=\"showSearch\"\r\n                        @queryTable=\"getList\"\r\n                    ></right-toolbar>\r\n                </el-row> -->\r\n\r\n                <el-table v-loading=\"loading\" border :data=\"list\">\r\n                    <el-table-column label=\"序号\" align=\"center\" width=\"50px\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.$index + 1 }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                        label=\"商品名称\"\r\n                        align=\"center\"\r\n                        prop=\"product_name\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"数量\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"num\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"操作类型\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"inquiry_no\"\r\n                    >\r\n                    <template slot-scope=\"scope\">\r\n                        <div v-if=\"scope.row.type == 0\">入库</div>\r\n                        <div v-if=\"scope.row.type == 1\">出库</div>\r\n                    </template>\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                        label=\"操作人\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"create_by\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"收方名称/入方名称\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"debit\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"操作时间\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"create_time\"\r\n                    />\r\n                </el-table>\r\n\r\n                <pagination\r\n                    v-show=\"total > 0\"\r\n                    :total=\"total\"\r\n                    :page.sync=\"queryParams.page\"\r\n                    :limit.sync=\"queryParams.limit\"\r\n                    @pagination=\"getList\"\r\n                />\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { listData, stockRecord } from \"@/api/store/product\";\r\nexport default {\r\n    layout: \"supply\",\r\n\r\n    data() {\r\n        return {\r\n            // 详情\r\n            open: false,\r\n            // 总条数\r\n            total: 0,\r\n            // 表单参数\r\n            form: {},\r\n            // 查询参数\r\n            queryParams: {\r\n                page: 1,\r\n                limit: 10,\r\n                type: undefined,\r\n                product_id: undefined,\r\n            },\r\n            // 搜索商品列表\r\n            shopList: [],\r\n            // 表格数据\r\n            list: [],\r\n            // 取消弹窗显示\r\n            dialogVisible: false,\r\n            // 弹窗标题\r\n            title: \"\",\r\n            loading: false,\r\n            //0入库 1出库\r\n            options: [\r\n                {\r\n                    label: '入库',\r\n                    value: 0,\r\n                },\r\n                {\r\n                    label: '出库',\r\n                    value: 1,\r\n                },\r\n            ],\r\n        };\r\n    },\r\n    created() {\r\n        this.getList()\r\n        this.remoteMethod()\r\n    },\r\n    methods: {\r\n        remoteMethod(query) {\r\n            this.loading = true;\r\n            listData({\r\n                pageNum: 1,\r\n                pageSize: 10000,\r\n                classify2_id: -1,\r\n                classify3_id: -1,\r\n            }).then((res) => {\r\n                console.log(res);\r\n                res.data.map(item=>{\r\n                    this.shopList.push({\r\n                        value:item.id,\r\n                        label:item.name\r\n                    })\r\n                })\r\n                this.loading = false;\r\n            });\r\n        },\r\n        getList() {\r\n            stockRecord(this.queryParams).then(res=>{\r\n                console.log(res)\r\n                this.list = res.data\r\n                this.total = res.count\r\n            })\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            (this.queryParams = {\r\n                page: 1,\r\n                limit: 10,\r\n                type: undefined,\r\n                product_id: undefined,\r\n                enterprise_id: null,\r\n            }),\r\n                (this.list = []);\r\n            this.getList();\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.item-form {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n}\r\n.wire {\r\n    background: rgb(219, 219, 219);\r\n    height: 1px;\r\n    margin-bottom: 20px;\r\n}\r\n.form1-box {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n.form1-search {\r\n    padding: 10px 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-evenly;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AA4HA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,MAAA;EAEAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,UAAA,EAAAD;MACA;MACA;MACAE,QAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAD,YAAA,WAAAA,aAAAE,KAAA;MAAA,IAAAC,KAAA;MACA,KAAAT,OAAA;MACA,IAAAU,iBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,YAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAA,GAAA,CAAA9B,IAAA,CAAAiC,GAAA,WAAAC,IAAA;UACAX,KAAA,CAAAb,QAAA,CAAAyB,IAAA;YACAlB,KAAA,EAAAiB,IAAA,CAAAE,EAAA;YACApB,KAAA,EAAAkB,IAAA,CAAAG;UACA;QACA;QACAd,KAAA,CAAAT,OAAA;MACA;IACA;IACAK,OAAA,WAAAA,QAAA;MAAA,IAAAmB,MAAA;MACA,IAAAC,oBAAA,OAAAnC,WAAA,EAAAyB,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAQ,MAAA,CAAA3B,IAAA,GAAAmB,GAAA,CAAA9B,IAAA;QACAsC,MAAA,CAAApC,KAAA,GAAA4B,GAAA,CAAAU,KAAA;MACA;IACA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAArC,WAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,UAAA,EAAAD,SAAA;QACAkC,aAAA;MACA,GACA,KAAA/B,IAAA;MACA,KAAAQ,OAAA;IACA;EACA;AACA", "ignoreList": []}]}