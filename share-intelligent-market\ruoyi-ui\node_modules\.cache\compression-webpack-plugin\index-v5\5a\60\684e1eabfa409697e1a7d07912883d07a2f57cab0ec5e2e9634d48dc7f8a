
9508dec0917b6e48d3a0d57238cfabf0c004b364	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fui\\u002Foxide\\u002Fskin.css\",\"contentHash\":\"f4daac0434b1866e926daceb5c9b5ad9\"}","integrity":"sha512-DTp1aAc<PERSON><PERSON>ky/SttLp6b/vBmUPLqUynReVjODWZittcGUMY29feqG7ByMKvmVPvGpMgwgSa2lP3C8uw3hnrg3Q==","time":1750496064275,"size":43732}