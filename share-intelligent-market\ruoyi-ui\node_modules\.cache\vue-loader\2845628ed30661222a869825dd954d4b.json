{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\creditRating.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\creditRating.vue", "mtime": 1750151094288}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0LGFkZCxlZGl0LGRlbCxvcCB9IGZyb20gIkAvYXBpL3N1cHBseS9jcmVkaXRSYXRpbmciOw0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJJbmZvciIsDQogICAgZGF0YSgpIHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgICAgIHNob3c6IGZhbHNlLA0KICAgICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgICAgZm9ybTogew0KICAgICAgICAgICAgICAgIGlkOicnLA0KICAgICAgICAgICAgICAgIHJhbms6ICIiLA0KICAgICAgICAgICAgICAgIHJlbWFyazogJycsDQogICAgICAgICAgICAgICAgc3RhdHVzOiAxLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHJ1bGVzOiB7DQogICAgICAgICAgICAgICAgcmFuazogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7floavlhpnkv6HnlKjnrYnnuqciLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g5oC75p2h5pWwDQogICAgICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgICAgIC8vIOWFrOWRiuihqOagvOaVsOaNrg0KICAgICAgICAgICAgaW5mb3JMaXN0OiBbXSwNCiAgICAgICAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgICAgICAgICBwYWdlOiAxLA0KICAgICAgICAgICAgICAgIHNpemU6IDEwLA0KICAgICAgICAgICAgICAgIC8vIHJhbms6ICcnLA0KICAgICAgICAgICAgICAgIC8vIHN0YXR1czogJycsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g5Zu+54mH6aKE6KeI5Zyw5Z2ADQogICAgICAgICAgICBzcmNMaXN0OiBbXSwNCiAgICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgdXBsb2FkU3VjY2VzcyhldmVudCl7DQogICAgICAgIHRoaXMuZm9ybS5pY29uID0gZXZlbnQNCiAgICAgIH0sDQogICAgICBoYW5kbGVQcmV2aWV3KHVybCkgew0KICAgICAgICB0aGlzLnNyY0xpc3QgPSBbdXJsXTsNCiAgICAgIH0sDQogICAgICAgIC8qKiDmn6Xor6LlhazlkYrliJfooaggKi8NCiAgICAgICAgZ2V0TGlzdCgpIHsNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgICBsaXN0KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5pbmZvckxpc3QgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLmNvdW50DQogICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZSA9IDE7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgICAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsNCiAgICAgICAgICAgICAgICBwYWdlOiAxLA0KICAgICAgICAgICAgICAgIHNpemU6IDEwLA0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOeKtuaAgeaUueWPmA0KICAgICAgICBjaGFuZ2VPUChyb3cpew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgIGxldCBvYmogPXsNCiAgICAgICAgICAgICAgICBpZDpyb3cuaWQsDQogICAgICAgICAgICAgICAgc3RhdHVzOnJvdy5zdGF0dXMgPT0gMSA/IDAgOiAxDQogICAgICAgICAgICB9DQogICAgICAgICAgICBvcChvYmopLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmk43kvZzmiJDlip8nKTsNCiAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVBZGQoKSB7DQogICAgICAgICAgICB0aGlzLmFkZCgpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCg0KICAgICAgICB9LA0KICAgICAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZURlbGV0ZShyb3csaW5kZXgpIHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgICAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOW6j+WPt+S4uiInICsgaW5kZXggKyAnIueahOaVsOaNrumhue+8nycpDQogICAgICAgICAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGVsKHtvcGlkOnJvdy5pZH0pOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICAgICAgfSwNCiAgICAgICAgcmVzZXQoKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgICAgICAgICAgaWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICByYW5rOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgc3RhdHVzOiAxLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgfSwNCiAgICAgICAgYWRkKCkgew0KICAgICAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqAiOw0KICAgICAgICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsNCiAgICAgICAgfSwNCiAgICAgICAgZWRpdChkYXRhKSB7DQogICAgICAgICAgICB0aGlzLnRpdGxlID0gIue8lui+kSI7DQogICAgICAgICAgICB0aGlzLnNob3cgPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICAgIGlkOiBkYXRhLmlkLA0KICAgICAgICAgICAgICAgIGNyZWRpdF9yYXRpbmdfbmFtZTogZGF0YS5jcmVkaXRfcmF0aW5nX25hbWUsDQogICAgICAgICAgICAgICAgaWNvbjpkYXRhLmljb24sDQogICAgICAgICAgICAgICAgcmVtYXJrOiBkYXRhLnJlbWFyaywNCiAgICAgICAgICAgIH07DQogICAgICAgIH0sDQogICAgICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZSgodmFsaWRhdGUpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodmFsaWRhdGUpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgaWYgKCF0aGlzLmZvcm0uaWQpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGFkZCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgZWRpdCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+WujOWWhOS/oeaBr+WGjeaPkOS6pCEiKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["creditRating.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "creditRating.vue", "sourceRoot": "src/views/supply", "sourcesContent": ["\r\n<template>\r\n    <div class=\"app-container\">\r\n        <!-- <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            @submit.native.prevent\r\n        >\r\n            <el-form-item label=\"信用等级\" prop='rank'>\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.rank\"\r\n                    style=\"width: 200px\"\r\n                    placeholder=\"请输入信用等级\"\r\n                    :maxlength=\"60\"\r\n                     @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form> -->\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"inforList\"\r\n        >\r\n            <el-table-column\r\n                label=\"序号\"\r\n                width=\"55\"\r\n                align=\"center\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"信用等级\"\r\n                align=\"center\"\r\n                prop=\"credit_rating_name\"\r\n                width=\"200\"\r\n            />\r\n            <el-table-column\r\n                label=\"图标\"\r\n                width=\"120\"\r\n                align=\"center\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                  <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.icon\"\r\n                    @click=\"handlePreview(scope.row.icon)\" :preview-src-list=\"srcList\">\r\n                  </el-image>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"备注\"\r\n                align=\"center\"\r\n                prop=\"remark\"\r\n            />\r\n            <el-table-column\r\n                label=\"状态\"\r\n                align=\"center\"\r\n                prop=\"create_by\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <!-- 开启 -->\r\n                 <!-- <el-switch v-model=\"form.delivery\"></el-switch> -->\r\n                    <el-tag\r\n                        type=\"success\"\r\n                        v-if=\"scope.row.status == 1\"\r\n                        >启用</el-tag>\r\n                        <el-tag\r\n                        type=\"danger\"\r\n                        v-else\r\n                        >禁用</el-tag\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                fixed=\"right\"\r\n                width=\"180\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        v-if=\"scope.row.status == 0\"\r\n                        style=\"color:#85ce61\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"changeOP(scope.row)\"\r\n                        >启用</el-button\r\n                    >\r\n                    <el-button\r\n                        v-if=\"scope.row.status == 1\"\r\n                        style=\"color:#ebb563\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"changeOP(scope.row)\"\r\n                        >禁用</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"edit(scope.row)\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        style=\"color:red\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"handleDelete(scope.row, scope.$index+1)\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n        <!-- 添加弹窗 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"show\"\r\n            width=\"30%\"\r\n            :before-close=\"() => (show = false)\"\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" :rules=\"rules\">\r\n                <el-form-item label=\"信用等级\" prop=\"credit_rating_name\">\r\n                    <el-input\r\n                        clearable\r\n                        v-model=\"form.credit_rating_name\"\r\n                        :maxlength=\"60\"\r\n                        placeholder=\"请输入信用等级\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"图标\" prop=\"icon\">\r\n                   <ImageUpload @input=\"uploadSuccess($event)\" sizeTxt='1920X412' style=\"width: 100%\" :value='form.icon'\r\n                     :limit='1'></ImageUpload>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"备注\">\r\n                    <el-input\r\n                        clearable\r\n                        v-model=\"form.remark\"\r\n                        :maxlength=\"60\"\r\n                        placeholder=\"请输入备注\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"show = false\">取 消</el-button>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    :loading=\"loading\"\r\n                    @click=\"handleSubmit\"\r\n                    >确 定</el-button\r\n                >\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { list,add,edit,del,op } from \"@/api/supply/creditRating\";\r\nexport default {\r\n    name: \"Infor\",\r\n    data() {\r\n        return {\r\n            showSearch: true,\r\n            loading: false,\r\n            show: false,\r\n            title: \"\",\r\n            form: {\r\n                id:'',\r\n                rank: \"\",\r\n                remark: '',\r\n                status: 1,\r\n            },\r\n            rules: {\r\n                rank: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请填写信用等级\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n            // 总条数\r\n            total: 0,\r\n            // 公告表格数据\r\n            inforList: [],\r\n            // 查询参数\r\n            queryParams: {\r\n                page: 1,\r\n                size: 10,\r\n                // rank: '',\r\n                // status: '',\r\n            },\r\n            // 图片预览地址\r\n            srcList: [],\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n      uploadSuccess(event){\r\n        this.form.icon = event\r\n      },\r\n      handlePreview(url) {\r\n        this.srcList = [url];\r\n      },\r\n        /** 查询公告列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            list(this.queryParams).then((response) => {\r\n                this.inforList = response.data\r\n                this.total = response.count\r\n                this.loading = false;\r\n            });\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.page = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.queryParams = {\r\n                page: 1,\r\n                size: 10,\r\n            }\r\n            this.getList();\r\n        },\r\n        // 状态改变\r\n        changeOP(row){\r\n            this.loading = true;\r\n            let obj ={\r\n                id:row.id,\r\n                status:row.status == 1 ? 0 : 1\r\n            }\r\n            op(obj).then((response) => {\r\n                this.loading = false;\r\n                this.$message.success('操作成功');\r\n                this.handleQuery();\r\n            });\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.add();\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row,index) {\r\n            this.$modal\r\n                .confirm('是否确认删除序号为\"' + index + '\"的数据项？')\r\n                .then(function () {\r\n                    return del({opid:row.id});\r\n                })\r\n                .then(() => {\r\n                    this.handleQuery();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        reset() {\r\n            this.form = {\r\n                id: undefined,\r\n                rank: undefined,\r\n                remark: undefined,\r\n                status: 1,\r\n            };\r\n        },\r\n        add() {\r\n            this.reset();\r\n            this.title = \"添加\";\r\n            this.show = true;\r\n        },\r\n        edit(data) {\r\n            this.title = \"编辑\";\r\n            this.show = true;\r\n            this.form = {\r\n                id: data.id,\r\n                credit_rating_name: data.credit_rating_name,\r\n                icon:data.icon,\r\n                remark: data.remark,\r\n            };\r\n        },\r\n        handleSubmit() {\r\n            this.$refs.form.validate((validate) => {\r\n                if (validate) {\r\n                    this.loading = true;\r\n                    if (!this.form.id) {\r\n                        add(this.form).then((response) => {\r\n                            this.$message({\r\n                                type: \"success\",\r\n                                message: \"操作成功!\",\r\n                            });\r\n                            this.loading = false;\r\n                            this.show = false;\r\n                            this.handleQuery();\r\n                        });\r\n                    } else {\r\n                        edit(this.form).then((response) => {\r\n                            this.$message({\r\n                                type: \"success\",\r\n                                message: \"操作成功!\",\r\n                            });\r\n                            this.loading = false;\r\n                            this.show = false;\r\n                            this.handleQuery();\r\n                        });\r\n                    }\r\n                } else {\r\n                    this.$modal.msgError(\"请完善信息再提交!\");\r\n                }\r\n            });\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}