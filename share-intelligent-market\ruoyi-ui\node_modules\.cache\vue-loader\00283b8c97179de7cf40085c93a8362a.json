{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\activity\\index.vue?vue&type=template&id=a3b90cd4", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\activity\\index.vue", "mtime": 1750151094250}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDxlbC1mb3JtCiAgICA6bW9kZWw9InF1ZXJ5UGFyYW1zIgogICAgcmVmPSJxdWVyeUZvcm0iCiAgICBzaXplPSJzbWFsbCIKICAgIDppbmxpbmU9InRydWUiCiAgICB2LXNob3c9InNob3dTZWFyY2giCiAgICBsYWJlbC13aWR0aD0iNjhweCIKICA+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmtLvliqjnvJblj7ciIHByb3A9ImFjdGl2aXR5Tm8iPgogICAgICA8ZWwtaW5wdXQKICAgICAgICB2LW1vZGVsPSJxdWVyeVBhcmFtcy5hY3Rpdml0eU5vIgogICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmtLvliqjnvJblj7ciCiAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgQGtleXVwLmVudGVyLm5hdGl2ZT0iaGFuZGxlUXVlcnkiCiAgICAgIC8+CiAgICA8L2VsLWZvcm0taXRlbT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iua0u+WKqOWQjeensCIgcHJvcD0idGl0bGUiPgogICAgICA8ZWwtaW5wdXQKICAgICAgICB2LW1vZGVsPSJxdWVyeVBhcmFtcy50aXRsZSIKICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5rS75Yqo5ZCN56ewIgogICAgICAgIGNsZWFyYWJsZQogICAgICAgIEBrZXl1cC5lbnRlci5uYXRpdmU9ImhhbmRsZVF1ZXJ5IgogICAgICAvPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlvIDlp4vml7bpl7QiIHByb3A9InN0YXJ0RGF0ZSI+CiAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgIGNsZWFyYWJsZQogICAgICAgIHYtbW9kZWw9InF1ZXJ5UGFyYW1zLnN0YXJ0RGF0ZSIKICAgICAgICB0eXBlPSJkYXRlIgogICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCIKICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5byA5aeL5pe26Ze0IgogICAgICA+CiAgICAgIDwvZWwtZGF0ZS1waWNrZXI+CiAgICA8L2VsLWZvcm0taXRlbT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iue7k+adn+aXtumXtCIgcHJvcD0iZW5kRGF0ZSI+CiAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgIGNsZWFyYWJsZQogICAgICAgIHYtbW9kZWw9InF1ZXJ5UGFyYW1zLmVuZERhdGUiCiAgICAgICAgdHlwZT0iZGF0ZSIKICAgICAgICB2YWx1ZS1mb3JtYXQ9Inl5eXktTU0tZGQiCiAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqee7k+adn+aXtumXtCIKICAgICAgPgogICAgICA8L2VsLWRhdGUtcGlja2VyPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnirbmgIEiIHByb3A9InN0YXR1cyI+CiAgICAgIDxlbC1zZWxlY3QKICAgICAgICB2LW1vZGVsPSJxdWVyeVBhcmFtcy5zdGF0dXMiCiAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeeKtuaAgSIKICAgICAgICBjbGVhcmFibGUKICAgICAgPgogICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgIHYtZm9yPSJkaWN0IGluIGRpY3QudHlwZS51dWNfb25saW5lIgogICAgICAgICAgOmtleT0iZGljdC52YWx1ZSIKICAgICAgICAgIDpsYWJlbD0iZGljdC5sYWJlbCIKICAgICAgICAgIDp2YWx1ZT0iZGljdC52YWx1ZSIKICAgICAgICAvPgogICAgICA8L2VsLXNlbGVjdD4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbT4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgaWNvbj0iZWwtaWNvbi1zZWFyY2giCiAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICBAY2xpY2s9ImhhbmRsZVF1ZXJ5IgogICAgICAgID7mkJzntKI8L2VsLWJ1dHRvbgogICAgICA+CiAgICAgIDxlbC1idXR0b24gaWNvbj0iZWwtaWNvbi1yZWZyZXNoIiBzaXplPSJtaW5pIiBAY2xpY2s9InJlc2V0UXVlcnkiCiAgICAgICAgPumHjee9rjwvZWwtYnV0dG9uCiAgICAgID4KICAgIDwvZWwtZm9ybS1pdGVtPgogIDwvZWwtZm9ybT4KCiAgPGVsLXJvdyA6Z3V0dGVyPSIxMCIgY2xhc3M9Im1iOCI+CiAgICA8ZWwtY29sIDpzcGFuPSIxLjUiPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICBwbGFpbgogICAgICAgIGljb249ImVsLWljb24tcGx1cyIKICAgICAgICBzaXplPSJtaW5pIgogICAgICAgIEBjbGljaz0iaGFuZGxlQWRkIgogICAgICAgIHYtaGFzUGVybWk9IlsndXVjOmFjdGl2aXR5OmFkZCddIgogICAgICAgID7mlrDlop48L2VsLWJ1dHRvbgogICAgICA+CiAgICA8L2VsLWNvbD4KICAgIDxlbC1jb2wgOnNwYW49IjEuNSI+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJzdWNjZXNzIgogICAgICAgIHBsYWluCiAgICAgICAgaWNvbj0iZWwtaWNvbi1lZGl0IgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgOmRpc2FibGVkPSJzaW5nbGUiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVVcGRhdGUiCiAgICAgICAgdi1oYXNQZXJtaT0iWyd1dWM6YWN0aXZpdHk6ZWRpdCddIgogICAgICAgID7kv67mlLk8L2VsLWJ1dHRvbgogICAgICA+CiAgICA8L2VsLWNvbD4KICAgIDxlbC1jb2wgOnNwYW49IjEuNSI+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJkYW5nZXIiCiAgICAgICAgcGxhaW4KICAgICAgICBpY29uPSJlbC1pY29uLWRlbGV0ZSIKICAgICAgICBzaXplPSJtaW5pIgogICAgICAgIDpkaXNhYmxlZD0ibXVsdGlwbGUiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVEZWxldGUiCiAgICAgICAgdi1oYXNQZXJtaT0iWyd1dWM6YWN0aXZpdHk6cmVtb3ZlJ10iCiAgICAgICAgPuWIoOmZpDwvZWwtYnV0dG9uCiAgICAgID4KICAgIDwvZWwtY29sPgogICAgPCEtLSA8ZWwtY29sIDpzcGFuPSIxLjUiPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0id2FybmluZyIKICAgICAgICBwbGFpbgogICAgICAgIGljb249ImVsLWljb24tZG93bmxvYWQiCiAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICBAY2xpY2s9ImhhbmRsZUV4cG9ydCIKICAgICAgICB2LWhhc1Blcm1pPSJbJ3V1YzphY3Rpdml0eTpleHBvcnQnXSIKICAgICAgPuWvvOWHujwvZWwtYnV0dG9uPgogICAgPC9lbC1jb2w+IC0tPgogICAgPHJpZ2h0LXRvb2xiYXIKICAgICAgOnNob3dTZWFyY2guc3luYz0ic2hvd1NlYXJjaCIKICAgICAgQHF1ZXJ5VGFibGU9ImdldExpc3QiCiAgICA+PC9yaWdodC10b29sYmFyPgogIDwvZWwtcm93PgoKICA8ZWwtdGFibGUKICAgIHYtbG9hZGluZz0ibG9hZGluZyIKICAgIDpkYXRhPSJhY3Rpdml0eUxpc3QiCiAgICBAc2VsZWN0aW9uLWNoYW5nZT0iaGFuZGxlU2VsZWN0aW9uQ2hhbmdlIgogID4KICAgIDxlbC10YWJsZS1jb2x1bW4gdHlwZT0ic2VsZWN0aW9uIiB3aWR0aD0iNTUiIGFsaWduPSJjZW50ZXIiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSJpZCIgYWxpZ249ImNlbnRlciIgcHJvcD0iaWQiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmtLvliqjnvJblj7ciIGFsaWduPSJjZW50ZXIiIHByb3A9ImFjdGl2aXR5Tm8iIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLkuLvpopjlm77niYciIGFsaWduPSJjZW50ZXIiIHByb3A9ImltYWdlIiB3aWR0aD0iMTAwIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8aW1hZ2UtcHJldmlldyA6c3JjPSJzY29wZS5yb3cuaW1hZ2UiIDp3aWR0aD0iNTAiIDpoZWlnaHQ9IjUwIiAvPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmtLvliqjlkI3np7AiIGFsaWduPSJjZW50ZXIiIHByb3A9InRpdGxlIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5aSH5rOoIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJyZW1hcmsiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uCiAgICAgIGxhYmVsPSLlvIDlp4vml7bpl7QiCiAgICAgIGFsaWduPSJjZW50ZXIiCiAgICAgIHByb3A9InN0YXJ0RGF0ZSIKICAgICAgd2lkdGg9IjE4MCIKICAgID4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8c3Bhbj57eyBwYXJzZVRpbWUoc2NvcGUucm93LnN0YXJ0RGF0ZSwgInt5fS17bX0te2R9IikgfX08L3NwYW4+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgIDxlbC10YWJsZS1jb2x1bW4KICAgICAgbGFiZWw9Iue7k+adn+aXtumXtCIKICAgICAgYWxpZ249ImNlbnRlciIKICAgICAgcHJvcD0iZW5kRGF0ZSIKICAgICAgd2lkdGg9IjE4MCIKICAgID4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8c3Bhbj57eyBwYXJzZVRpbWUoc2NvcGUucm93LmVuZERhdGUsICJ7eX0te219LXtkfSIpIH19PC9zcGFuPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLnirbmgIEiIGFsaWduPSJjZW50ZXIiIHByb3A9InN0YXR1cyI+CiAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgPGRpY3QtdGFnIDpvcHRpb25zPSJkaWN0LnR5cGUudXVjX29ubGluZSIgOnZhbHVlPSJzY29wZS5yb3cuc3RhdHVzIiAvPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uCiAgICAgIGxhYmVsPSLmk43kvZwiCiAgICAgIGFsaWduPSJjZW50ZXIiCiAgICAgIGNsYXNzLW5hbWU9InNtYWxsLXBhZGRpbmcgZml4ZWQtd2lkdGgiCiAgICA+CiAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICBpY29uPSJlbC1pY29uLWVkaXQiCiAgICAgICAgICBAY2xpY2s9ImhhbmRsZVVwZGF0ZShzY29wZS5yb3cpIgogICAgICAgICAgdi1oYXNQZXJtaT0iWyd1dWM6YWN0aXZpdHk6ZWRpdCddIgogICAgICAgICAgPuS/ruaUuTwvZWwtYnV0dG9uCiAgICAgICAgPgogICAgICAgIDxlbC1idXR0b24KICAgICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgaWNvbj0iZWwtaWNvbi1kZWxldGUiCiAgICAgICAgICBAY2xpY2s9ImhhbmRsZURlbGV0ZShzY29wZS5yb3cpIgogICAgICAgICAgdi1oYXNQZXJtaT0iWyd1dWM6YWN0aXZpdHk6cmVtb3ZlJ10iCiAgICAgICAgICA+5Yig6ZmkPC9lbC1idXR0b24KICAgICAgICA+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICA8L2VsLXRhYmxlPgoKICA8cGFnaW5hdGlvbgogICAgdi1zaG93PSJ0b3RhbCA+IDAiCiAgICA6dG90YWw9InRvdGFsIgogICAgOnBhZ2Uuc3luYz0icXVlcnlQYXJhbXMucGFnZU51bSIKICAgIDpsaW1pdC5zeW5jPSJxdWVyeVBhcmFtcy5wYWdlU2l6ZSIKICAgIEBwYWdpbmF0aW9uPSJnZXRMaXN0IgogIC8+CgogIDwhLS0g5re75Yqg5oiW5L+u5pS55rS75Yqo566h55CG5a+56K+d5qGGIC0tPgogIDxlbC1kaWFsb2cKICAgIHYtaWY9Im9wZW4iCiAgICA6dGl0bGU9InRpdGxlIgogICAgOnZpc2libGUuc3luYz0ib3BlbiIKICAgIHdpZHRoPSI1MDBweCIKICAgIGFwcGVuZC10by1ib2R5CiAgPgogICAgPGVsLWZvcm0gcmVmPSJmb3JtIiA6bW9kZWw9ImZvcm0iIDpydWxlcz0icnVsZXMiIGxhYmVsLXdpZHRoPSI4MHB4Ij4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5rS75Yqo57yW5Y+3IiBwcm9wPSJhY3Rpdml0eU5vIj4KICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgIHYtbW9kZWw9ImZvcm0uYWN0aXZpdHlObyIKICAgICAgICAgIG1heGxlbmd0aD0iMjAiCiAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5rS75Yqo57yW5Y+3IgogICAgICAgIC8+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLkuLvpopjlm77niYciIHByb3A9ImltYWdlIj4KICAgICAgICA8aW1hZ2UtdXBsb2FkIHYtbW9kZWw9ImZvcm0uaW1hZ2UiIDpsaW1pdD0iMSIgLz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iua0u+WKqOWQjeensCIgcHJvcD0idGl0bGUiPgogICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgdi1tb2RlbD0iZm9ybS50aXRsZSIKICAgICAgICAgIG1heGxlbmd0aD0iMTAwIgogICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpea0u+WKqOWQjeensCIKICAgICAgICAvPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5aSH5rOoIiBwcm9wPSJyZW1hcmsiPgogICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgdi1tb2RlbD0iZm9ybS5yZW1hcmsiCiAgICAgICAgICBtYXhsZW5ndGg9IjEwMDAiCiAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlhoXlrrkiCiAgICAgICAgLz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iua0u+WKqOaXtumXtCI+CiAgICAgICAgPGVsLWRhdGUtcGlja2VyCiAgICAgICAgICB2LW1vZGVsPSJ0aW1lU2xvdCIKICAgICAgICAgIHR5cGU9ImRhdGVyYW5nZSIKICAgICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCIKICAgICAgICAgIHJhbmdlLXNlcGFyYXRvcj0i6IezIgogICAgICAgICAgc3RhcnQtcGxhY2Vob2xkZXI9IuW8gOWni+aXpeacnyIKICAgICAgICAgIGVuZC1wbGFjZWhvbGRlcj0i57uT5p2f5pel5pyfIgogICAgICAgICAgOnBpY2tlci1vcHRpb25zPSJwaWNrZXJPcHRpb25zIgogICAgICAgID4KICAgICAgICA8L2VsLWRhdGUtcGlja2VyPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPCEtLSA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlvIDlp4vml7bpl7QiIHByb3A9InN0YXJ0RGF0ZSI+CiAgICAgICAgPGVsLWRhdGUtcGlja2VyIGNsZWFyYWJsZQogICAgICAgICAgdi1tb2RlbD0iZm9ybS5zdGFydERhdGUiCiAgICAgICAgICB0eXBlPSJkYXRlIgogICAgICAgICAgdmFsdWUtZm9ybWF0PSJ5eXl5LU1NLWRkIgogICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeW8gOWni+aXtumXtCI+CiAgICAgICAgPC9lbC1kYXRlLXBpY2tlcj4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iue7k+adn+aXtumXtCIgcHJvcD0iZW5kRGF0ZSI+CiAgICAgICAgPGVsLWRhdGUtcGlja2VyIGNsZWFyYWJsZQogICAgICAgICAgdi1tb2RlbD0iZm9ybS5lbmREYXRlIgogICAgICAgICAgdHlwZT0iZGF0ZSIKICAgICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCIKICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nnu5PmnZ/ml7bpl7QiPgogICAgICAgIDwvZWwtZGF0ZS1waWNrZXI+CiAgICAgIDwvZWwtZm9ybS1pdGVtPiAtLT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i54q25oCBIj4KICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0iZm9ybS5zdGF0dXMiPgogICAgICAgICAgPGVsLXJhZGlvCiAgICAgICAgICAgIHYtZm9yPSJkaWN0IGluIGRpY3QudHlwZS51dWNfb25saW5lIgogICAgICAgICAgICA6a2V5PSJkaWN0LnZhbHVlIgogICAgICAgICAgICA6bGFiZWw9ImRpY3QudmFsdWUiCiAgICAgICAgICAgID57eyBkaWN0LmxhYmVsIH19PC9lbC1yYWRpbwogICAgICAgICAgPgogICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLor6bmg4UiPgogICAgICAgIDxlZGl0b3Igdi1tb2RlbD0iZm9ybS5jb250ZW50IiBtYXhsZW5ndGg9IjIyNSIgOm1pbi1oZWlnaHQ9IjE5MiIgLz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8L2VsLWZvcm0+CiAgICA8ZGl2IHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0ic3VibWl0Rm9ybSI+56GuIOWumjwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2FuY2VsIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KPC9kaXY+Cg=="}, null]}