export { default as AddRess } from '../..\\components\\AddRess\\index.vue'
export { default as FileUpload } from '../..\\components\\FileUpload\\index.vue'
export { default as ImageUpload } from '../..\\components\\ImageUpload\\index.vue'
export { default as IssueAccurate } from '../..\\components\\issue\\accurate.vue'
export { default as IssueEnquiryDetails } from '../..\\components\\issue\\enquiryDetails.vue'
export { default as IssueTopspeed } from '../..\\components\\issue\\topspeed.vue'
export { default as LayoutNavbar } from '../..\\components\\layout\\Navbar.vue'
export { default as ShopCart } from '../..\\components\\shop\\cart.vue'
export { default as ShopCompanyProfile } from '../..\\components\\shop\\company-profile.vue'
export { default as ShopCompany } from '../..\\components\\shop\\company.vue'
export { default as ShopEcharts } from '../..\\components\\shop\\echarts.vue'
export { default as ShopEditor } from '../..\\components\\shop\\editor.vue'
export { default as ShopFooter } from '../..\\components\\shop\\footer.vue'
export { default as ShopOrderForm } from '../..\\components\\shop\\order-form.vue'
export { default as ShopProductStatus } from '../..\\components\\shop\\product-status.vue'
export { default as ShopScreen } from '../..\\components\\shop\\screen.vue'
export { default as ShopSearchBar } from '../..\\components\\shop\\search-bar.vue'
export { default as ShopItem } from '../..\\components\\shop\\shop-item.vue'
export { default as ShopTabBar } from '../..\\components\\shop\\tab_bar.vue'
export { default as CommonPagination } from '../..\\components\\common\\Pagination\\index.vue'
export { default as CommonShopNoData } from '../..\\components\\common\\shop\\no-data.vue'
export { default as LayoutSidebar } from '../..\\components\\layout\\Sidebar\\index.vue'
export { default as LayoutSidebarItem } from '../..\\components\\layout\\Sidebar\\item.vue'
export { default as CommonCenterEnterprise } from '../..\\components\\common\\center\\enterprise\\index.vue'
export { default as CommonCenterProfileCompany } from '../..\\components\\common\\center\\profile\\company.vue'
export { default as CommonCenterProfileInfo } from '../..\\components\\common\\center\\profile\\info.vue'
export { default as CommonCenterProfilePhone } from '../..\\components\\common\\center\\profile\\phone.vue'
export { default as CommonCenterProfilePwd } from '../..\\components\\common\\center\\profile\\pwd.vue'
export { default as CommonShopProductDtModal } from '../..\\components\\common\\shop\\product\\dt-modal.vue'
export { default as CommonShopProductProductdetail } from '../..\\components\\common\\shop\\product\\productdetail.vue'
export { default as CommonSystemOrgAddDep } from '../..\\components\\common\\system\\org\\add-dep.vue'
export { default as CommonSystemOrgList } from '../..\\components\\common\\system\\org\\list.vue'
export { default as CommonSystemPermList } from '../..\\components\\common\\system\\perm\\list.vue'
export { default as CommonSystemPermUserModal } from '../..\\components\\common\\system\\perm\\user-modal.vue'
export { default as CommonSystemRoleList } from '../..\\components\\common\\system\\role\\list.vue'
export { default as CommonSystemRoleUpdateRole } from '../..\\components\\common\\system\\role\\update-role.vue'

// nuxt/nuxt.js#8607
function wrapFunctional(options) {
  if (!options || !options.functional) {
    return options
  }

  const propKeys = Array.isArray(options.props) ? options.props : Object.keys(options.props || {})

  return {
    render(h) {
      const attrs = {}
      const props = {}

      for (const key in this.$attrs) {
        if (propKeys.includes(key)) {
          props[key] = this.$attrs[key]
        } else {
          attrs[key] = this.$attrs[key]
        }
      }

      return h(options, {
        on: this.$listeners,
        attrs,
        props,
        scopedSlots: this.$scopedSlots,
      }, this.$slots.default)
    }
  }
}
