{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\Editor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\Editor\\index.vue", "mtime": 1750151094133}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "_tinymce", "_interopRequireDefault", "_tinymceVue", "components", "Editor", "name", "props", "value", "type", "String", "default", "disabled", "Boolean", "fontfamily", "Array", "fontsize", "plugins", "toolbar", "data", "maxSize", "accept", "init", "myValue", "created", "_this$init", "that", "auto_focus", "language_url", "language", "skin_url", "branding", "menubar", "content_css", "height", "fontsize_formats", "font_formats", "toolbar1", "_defineProperty2", "images_upload_handler", "blobInfo", "success", "failure", "blob", "size", "$message", "error", "indexOf", "uploadPic", "mounted", "methods", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "file", "w", "_context", "n", "FormData", "set", "uploadAvatar", "then", "res", "url", "catch", "err", "a", "onClick", "e", "$emit", "<PERSON><PERSON><PERSON>", "clear", "watch", "newValue"], "sources": ["src/components/Editor/index.vue"], "sourcesContent": ["<!-- 旧版本富文本\r\n<template>\r\n  <div>\r\n    <el-upload\r\n      :action=\"uploadUrl\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadError\"\r\n      name=\"file\"\r\n      :show-file-list=\"false\"\r\n      :headers=\"headers\"\r\n      style=\"display: none\"\r\n      ref=\"upload\"\r\n      v-if=\"this.type == 'url'\"\r\n    >\r\n    </el-upload>\r\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Quill from \"quill\";\r\nimport \"quill/dist/quill.core.css\";\r\nimport \"quill/dist/quill.snow.css\";\r\nimport \"quill/dist/quill.bubble.css\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Editor\",\r\n  props: {\r\n    /* 编辑器的内容 */\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    /* 高度 */\r\n    height: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    /* 最小高度 */\r\n    minHeight: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    /* 只读 */\r\n    readOnly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    // 上传文件大小限制(MB)\r\n    fileSize: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    /* 类型（base64格式、url格式） */\r\n    type: {\r\n      type: String,\r\n      default: \"url\",\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/shop/data/upload/image\", // 上传的图片服务器地址\r\n      headers: {\r\n        authorization: getToken(),\r\n      },\r\n      Quill: null,\r\n      currentValue: \"\",\r\n      options: {\r\n        theme: \"snow\",\r\n        bounds: document.body,\r\n        debug: \"warn\",\r\n        modules: {\r\n          // 工具栏配置\r\n          toolbar: [\r\n            [\"bold\", \"italic\", \"underline\", \"strike\"], // 加粗 斜体 下划线 删除线\r\n            [\"blockquote\", \"code-block\"], // 引用  代码块\r\n            [{ list: \"ordered\" }, { list: \"bullet\" }], // 有序、无序列表\r\n            [{ indent: \"-1\" }, { indent: \"+1\" }], // 缩进\r\n            [{ size: [\"small\", false, \"large\", \"huge\"] }], // 字体大小\r\n            [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题\r\n            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色\r\n            [{ align: [] }], // 对齐方式\r\n            [\"clean\"], // 清除文本格式\r\n            [\"link\", \"image\", \"video\"], // 链接、图片、视频\r\n          ],\r\n        },\r\n        placeholder: \"请输入内容\",\r\n        readOnly: this.readOnly,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    styles() {\r\n      let style = {};\r\n      if (this.minHeight) {\r\n        style.minHeight = `${this.minHeight}px`;\r\n      }\r\n      if (this.height) {\r\n        style.height = `${this.height}px`;\r\n      }\r\n      return style;\r\n    },\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val !== this.currentValue) {\r\n          this.currentValue = val === null ? \"\" : val;\r\n          if (this.Quill) {\r\n            this.Quill.pasteHTML(this.currentValue);\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n  beforeDestroy() {\r\n    this.Quill = null;\r\n  },\r\n  methods: {\r\n    init() {\r\n      const editor = this.$refs.editor;\r\n      this.Quill = new Quill(editor, this.options);\r\n      // 如果设置了上传地址则自定义图片上传事件\r\n      if (this.type == \"url\") {\r\n        let toolbar = this.Quill.getModule(\"toolbar\");\r\n        toolbar.addHandler(\"image\", (value) => {\r\n          this.uploadType = \"image\";\r\n          if (value) {\r\n            this.$refs.upload.$children[0].$refs.input.click();\r\n          } else {\r\n            this.quill.format(\"image\", false);\r\n          }\r\n        });\r\n      }\r\n      this.Quill.pasteHTML(this.currentValue);\r\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\r\n        const html = this.$refs.editor.children[0].innerHTML;\r\n        const text = this.Quill.getText();\r\n        const quill = this.Quill;\r\n        this.currentValue = html;\r\n        this.$emit(\"input\", html);\r\n        this.$emit(\"on-change\", { html, text, quill });\r\n      });\r\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\r\n        this.$emit(\"on-text-change\", delta, oldDelta, source);\r\n      });\r\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\r\n        this.$emit(\"on-selection-change\", range, oldRange, source);\r\n      });\r\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\r\n        this.$emit(\"on-editor-change\", eventName, ...args);\r\n      });\r\n    },\r\n    // 上传前校检格式和大小\r\n    handleBeforeUpload(file) {\r\n      // 校检文件大小\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    handleUploadSuccess(res, file) {\r\n      // 获取富文本组件实例\r\n      let quill = this.Quill;\r\n      // 如果上传成功\r\n      if (res.status == 0) {\r\n        // 获取光标所在位置\r\n        let length = quill.getSelection().index;\r\n        // 插入图片  res.url为服务器返回的图片地址\r\n        quill.insertEmbed(length, \"image\", res.url);\r\n        // 调整光标到最后\r\n        quill.setSelection(length + 1);\r\n      } else {\r\n        this.$message.error(\"图片插入失败\");\r\n      }\r\n    },\r\n    handleUploadError() {\r\n      this.$message.error(\"图片插入失败\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.editor,\r\n.ql-toolbar {\r\n  white-space: pre-wrap !important;\r\n  line-height: normal !important;\r\n}\r\n.quill-img {\r\n  display: none;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\r\n  content: \"请输入链接地址:\";\r\n}\r\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\r\n  border-right: 0px;\r\n  content: \"保存\";\r\n  padding-right: 0px;\r\n}\r\n\r\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\r\n  content: \"请输入视频地址:\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\r\n  content: \"14px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\r\n  content: \"10px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\r\n  content: \"18px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\r\n  content: \"32px\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\r\n  content: \"文本\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\r\n  content: \"标题1\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\r\n  content: \"标题2\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\r\n  content: \"标题3\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\r\n  content: \"标题4\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\r\n  content: \"标题5\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\r\n  content: \"标题6\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\r\n  content: \"标准字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\r\n  content: \"衬线字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\r\n  content: \"等宽字体\";\r\n}\r\n</style> -->\r\n<!-- 新富文本框 -->\r\n<template>\r\n  <div class=\"tinymce-box\">\r\n    <Editor\r\n      class=\"editor\"\r\n      v-model=\"myValue\"\r\n      :init=\"init\"\r\n      :disabled=\"disabled\"\r\n      @onClick=\"onClick\"\r\n    />\r\n    <button @click=\"myValue = ''\">清空内容</button> &emsp;\r\n    <button @click=\"disabled = !disabled\">禁用</button>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { uploadAvatar } from \"@/api/system/user\";\r\nimport tinymce from \"tinymce/tinymce\";\r\nimport Editor from \"@tinymce/tinymce-vue\";\r\n\r\nimport \"tinymce/themes/silver\";\r\nimport \"tinymce/icons/default/icons\";\r\n\r\nimport \"tinymce/plugins/image\";\r\nimport \"tinymce/plugins/media\";\r\nimport \"tinymce/plugins/table\";\r\nimport \"tinymce/plugins/lists\";\r\nimport \"tinymce/plugins/paste\";\r\nimport \"tinymce/plugins/preview\";\r\nimport \"tinymce/plugins/fullscreen\";\r\nimport \"tinymce/plugins/wordcount\";\r\nimport \"tinymce/plugins/textcolor\";\r\nimport \"tinymce/plugins/advlist\";\r\nimport \"tinymce/plugins/toc\";\r\nimport \"tinymce/plugins/link\";\r\n\r\nexport default {\r\n  components: {\r\n    Editor,\r\n  },\r\n  name: \"RichText\",\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    fontfamily: {\r\n      type: [String, Array],\r\n      default: `微软雅黑=微软雅黑;\r\n        宋体=宋体;\r\n        黑体=黑体;\r\n        仿宋=仿宋;\r\n        楷体=楷体;\r\n        隶书=隶书;\r\n        幼圆=幼圆;\r\n        sans-serif=sans-serif;\r\n        Andale Mono=andale mono,times;\r\n        Arial=arial, helvetica,\r\n        sans-serif;\r\n        Arial Black=arial black, avant garde;\r\n        Book Antiqua=book antiqua,palatino;\r\n        Comic Sans MS=comic sans ms,sans-serif;\r\n        Courier New=courier new,courier;\r\n        Georgia=georgia,palatino;\r\n        Helvetica=helvetica;\r\n        Impact=impact,chicago;\r\n        Symbol=symbol;\r\n        Tahoma=tahoma,arial,helvetica,sans-serif;\r\n        Terminal=terminal,monaco;\r\n        Times New Roman=times new roman,times;\r\n        Trebuchet MS=trebuchet ms,geneva;\r\n        Verdana=verdana,geneva;\r\n        Webdings=webdings;\r\n        Wingdings=wingdings,zapf dingbats`,\r\n    },\r\n    fontsize: {\r\n      type: [String, Array],\r\n      default: \"10px 11px 12px 14px 16px 18px 20px 24px\",\r\n    },\r\n\r\n    plugins: {\r\n      type: [String, Array],\r\n      default:\r\n        \"advlist lists image table paste preview fullscreen wordcount toc link code\",\r\n    },\r\n    toolbar: {\r\n      type: [String, Array],\r\n      default:\r\n        \"undo redo | fontselect fontsizeselect formatselect | bold italic forecolor backcolor | alignleft aligncenter alignright  alignjustify toc | link quicklink h2 h3 blockquote bullist numlist outdent indent | lists image media table | removeformat | preview code fullscreen wordcount\",\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      maxSize: 10 * 1024 * 1024,\r\n      accept: \"image/jpg,image/jpeg,image/bmp,image/png,image/gif\",\r\n      init: {},\r\n      myValue: this.value,\r\n    };\r\n  },\r\n  created() {\r\n    const that = this;\r\n    this.init = {\r\n      auto_focus: true,\r\n      language_url: \"/tinymce/langs/zh-Hans.js\",\r\n      language: \"zh_CN\",\r\n      // skin_url: \"/tinymce/skins/ui/oxide\",\r\n      skin_url: \"tinymce/skins/ui/oxide-dark\",\r\n      plugins: this.plugins,\r\n      toolbar: this.toolbar,\r\n      branding: false,\r\n      menubar: false,\r\n      content_css: \"tinymce/skins/content/default/content.css\",\r\n      height: \"60vh\",\r\n      fontsize_formats: this.fontsize,\r\n      font_formats: this.fontfamily,\r\n      toolbar1: this.toolbar,\r\n      branding: false,\r\n      menubar: false,\r\n      browser_spellcheck: true,\r\n      elementpath: true,\r\n      statusbar: true,\r\n      paste_retain_style_properties: \"all\",\r\n      paste_word_valid_elements: \"*[*]\",\r\n      paste_data_images: true,\r\n      paste_convert_word_fake_lists: false,\r\n      paste_webkit_styles: \"all\",\r\n      paste_merge_formats: true,\r\n      nonbreaking_force_tab: false,\r\n\r\n      images_upload_handler: function (blobInfo, success, failure) {\r\n        if (blobInfo.blob().size > that.maxSize) {\r\n          return that.$message.error(\"图片上传最多3M\");\r\n        }\r\n        if (that.accept.indexOf(blobInfo.blob().type) >= 0) {\r\n          that.uploadPic(blobInfo, success, failure);\r\n        } else {\r\n          that.$message.error(\"图片格式错误\");\r\n        }\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    // tinymce.init({})//不调用也无妨\r\n  },\r\n  methods: {\r\n    // 调用上传图片\r\n    async uploadPic(blobInfo, success, failure) {\r\n      let file = new FormData();\r\n      file.set(\"file\", blobInfo.blob());\r\n      await uploadAvatar(file)\r\n        .then((res) => {\r\n          success(res.url);\r\n          // console.log(\"uploadPic ~ res:\", res);\r\n        })\r\n        .catch((err) => {\r\n          // console.log(\"上传异常 ~ err:\", err);\r\n          failure(err);\r\n        });\r\n    },\r\n    onClick(e) {\r\n      this.$emit(\"onClick\", e, tinymce);\r\n    },\r\n    clear() {\r\n      this.myValue = \"\";\r\n    },\r\n  },\r\n  watch: {\r\n    value(newValue) {\r\n      if (newValue !== this.myValue) {\r\n        this.myValue = newValue;\r\n      }\r\n    },\r\n    myValue(newValue) {\r\n      if (newValue !== this.value) {\r\n        this.$emit(\"input\", newValue);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.tox-tinymce-aux {\r\n  z-index: 99999 !important;\r\n}\r\n\r\n.tinymce.ui.FloatPanel {\r\n  z-index: 99;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AAkSA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,WAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEAA,OAAA;AACAA,OAAA;AAEAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,UAAA;IACAC,MAAA,EAAAA;EACA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAG,UAAA;MACAL,IAAA,GAAAC,MAAA,EAAAK,KAAA;MACAJ,OAAA;IA0BA;IACAK,QAAA;MACAP,IAAA,GAAAC,MAAA,EAAAK,KAAA;MACAJ,OAAA;IACA;IAEAM,OAAA;MACAR,IAAA,GAAAC,MAAA,EAAAK,KAAA;MACAJ,OAAA,EACA;IACA;IACAO,OAAA;MACAT,IAAA,GAAAC,MAAA,EAAAK,KAAA;MACAJ,OAAA,EACA;IACA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,MAAA;MACAC,IAAA;MACAC,OAAA,OAAAf;IACA;EACA;EACAgB,OAAA,WAAAA,QAAA;IAAA,IAAAC,UAAA;IACA,IAAAC,IAAA;IACA,KAAAJ,IAAA,IAAAG,UAAA;MACAE,UAAA;MACAC,YAAA;MACAC,QAAA;MACA;MACAC,QAAA;MACAb,OAAA,OAAAA,OAAA;MACAC,OAAA,OAAAA,OAAA;MACAa,QAAA;MACAC,OAAA;MACAC,WAAA;MACAC,MAAA;MACAC,gBAAA,OAAAnB,QAAA;MACAoB,YAAA,OAAAtB,UAAA;MACAuB,QAAA,OAAAnB;IAAA,OAAAoB,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,EAAAc,UAAA,cACA,mBACA,8BACA,sBACA,oBACA,wCACA,qCACA,8BACA,wCACA,+BACA,YAAAa,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,MAAA2B,gBAAA,CAAA3B,OAAA,EAAAc,UAAA,yBACA,gCACA,iCAEA,SAAAc,sBAAAC,QAAA,EAAAC,OAAA,EAAAC,OAAA;MACA,IAAAF,QAAA,CAAAG,IAAA,GAAAC,IAAA,GAAAlB,IAAA,CAAAN,OAAA;QACA,OAAAM,IAAA,CAAAmB,QAAA,CAAAC,KAAA;MACA;MACA,IAAApB,IAAA,CAAAL,MAAA,CAAA0B,OAAA,CAAAP,QAAA,CAAAG,IAAA,GAAAlC,IAAA;QACAiB,IAAA,CAAAsB,SAAA,CAAAR,QAAA,EAAAC,OAAA,EAAAC,OAAA;MACA;QACAhB,IAAA,CAAAmB,QAAA,CAAAC,KAAA;MACA;IACA,GACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACA;IACAF,SAAA,WAAAA,UAAAR,QAAA,EAAAC,OAAA,EAAAC,OAAA;MAAA,WAAAS,kBAAA,CAAAxC,OAAA,mBAAAyC,aAAA,CAAAzC,OAAA,IAAA0C,CAAA,UAAAC,QAAA;QAAA,IAAAC,IAAA;QAAA,WAAAH,aAAA,CAAAzC,OAAA,IAAA6C,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAH,IAAA,OAAAI,QAAA;cACAJ,IAAA,CAAAK,GAAA,SAAApB,QAAA,CAAAG,IAAA;cAAAc,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAG,kBAAA,EAAAN,IAAA,EACAO,IAAA,WAAAC,GAAA;gBACAtB,OAAA,CAAAsB,GAAA,CAAAC,GAAA;gBACA;cACA,GACAC,KAAA,WAAAC,GAAA;gBACA;gBACAxB,OAAA,CAAAwB,GAAA;cACA;YAAA;cAAA,OAAAT,QAAA,CAAAU,CAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IACA;IACAc,OAAA,WAAAA,QAAAC,CAAA;MACA,KAAAC,KAAA,YAAAD,CAAA,EAAAE,gBAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAjD,OAAA;IACA;EACA;EACAkD,KAAA;IACAjE,KAAA,WAAAA,MAAAkE,QAAA;MACA,IAAAA,QAAA,UAAAnD,OAAA;QACA,KAAAA,OAAA,GAAAmD,QAAA;MACA;IACA;IACAnD,OAAA,WAAAA,QAAAmD,QAAA;MACA,IAAAA,QAAA,UAAAlE,KAAA;QACA,KAAA8D,KAAA,UAAAI,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}