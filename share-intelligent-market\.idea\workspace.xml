<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ff989628-50ad-4970-9820-d886365176eb" name="Changes" comment="单点登录和短信问题">
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/config/QWTSmsConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/config/SSOClientConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/config/SSOWebConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/controller/SSOClientController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/controller/SmsController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/model/QWTSmsResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/service/SSOClientService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/util/QWTSendUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/resources/static/login.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-ui/src/router/sso-guard.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-ui/src/utils/sso.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shop-ui/pages/sso-callback.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sso-integration/SSOController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sso-integration/SSOService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/controller/TokenController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/java/com/ruoyi/auth/controller/TokenController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-auth/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-auth/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/java/com/ruoyi/biz/shop/app/callback/WxPayInformController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/java/com/ruoyi/biz/shop/app/callback/WxPayInformController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/java/com/ruoyi/biz/shop/app/util/UtilApiController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/java/com/ruoyi/biz/shop/app/util/UtilApiController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/java/com/ruoyi/biz/shop/biz/order/OrderBiz.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/java/com/ruoyi/biz/shop/biz/order/OrderBiz.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/java/com/ruoyi/biz/shop/config/WechatPayJcscConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/java/com/ruoyi/biz/shop/config/WechatPayJcscConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-biz/ruoyi-biz-shop/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-core/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-core/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-gateway/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-gateway/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-file/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-file/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-gen/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-gen/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-job/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-job/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-shop/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-shop/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-uuc/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-uuc/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-visual/ruoyi-monitor/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-visual/ruoyi-monitor/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shop-ui/apis/login.js" beforeDir="false" afterPath="$PROJECT_DIR$/shop-ui/apis/login.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shop-ui/nuxt.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/shop-ui/nuxt.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shop-ui/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/shop-ui/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shop-ui/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/shop-ui/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shop-ui/pages/login/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/shop-ui/pages/login/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shop-ui/utils/config.js" beforeDir="false" afterPath="$PROJECT_DIR$/shop-ui/utils/config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="gzqd" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yd4LsNC5Uw89XPthOLpM2iCLvk" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.RuoYiAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiGatewayApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiShopApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiSystemApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoyiBizShopApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoyiUucApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;gzqd&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;vuejs.nuxt.types-notification-shown&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.RuoYiAuthApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="share-intelligent-market" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="share-intelligent-market" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="RuoYiAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.auth.RuoYiAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-modules-file" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.file.RuoYiFileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.gateway.RuoYiGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiGenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-modules-gen" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.gen.RuoYiGenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiMonitorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-visual-monitor" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.modules.monitor.RuoYiMonitorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiShopApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-modules-shop" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.shop.RuoYiShopApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-modules-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.system.RuoYiSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoyiBizShopApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-biz-shop" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.biz.shop.RuoyiBizShopApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoyiUucApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-uuc" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.uuc.RuoyiUucApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.21565.193" />
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ff989628-50ad-4970-9820-d886365176eb" name="Changes" comment="" />
      <created>1750151063510</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750151063510</updated>
      <workItem from="1750151064605" duration="3459000" />
      <workItem from="1750208427772" duration="596000" />
      <workItem from="1750214905213" duration="13658000" />
      <workItem from="1750295332091" duration="349000" />
      <workItem from="1750295813825" duration="22430000" />
      <workItem from="1750381230275" duration="10155000" />
      <workItem from="1750400627879" duration="3359000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="单点登录和短信问题" />
    <option name="LAST_COMMIT_MESSAGE" value="单点登录和短信问题" />
  </component>
</project>