{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\components\\add.vue?vue&type=style&index=0&id=7b775b9c&scoped=true&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\components\\add.vue", "mtime": 1750151094223}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5iZzZlNmU2ZSB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2U2ZTZlNjsKICBoZWlnaHQ6IDFweDsKfQoKLnRhZy1pbnB1dCB7CiAgd2lkdGg6IDIwMHB4Owp9Cg=="}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAukBA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "add.vue", "sourceRoot": "src/views/central/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :title=\"title\" :visible.sync=\"collectionDialog\" width=\"90%\" center>\r\n      <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"110px\" label-position=\"right\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"产品名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" :maxlength='60' placeholder=\"请输入产品名称\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"产品分类\" prop=\"classify_id\">\r\n              <el-cascader filterable style='width: 100%;' v-model=\"sType\"\r\n                :options=\"types\" @change=\"changeClassify\"\r\n                :props='{label: \"name\", value: \"id\"}'></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <!-- <el-col :span=\"12\">\r\n            <el-form-item label=\"产品编码\" prop=\"product_no\">\r\n              <el-input v-model=\"form.product_no\" :maxlength='30' placeholder=\"请输入产品编码\"></el-input>\r\n            </el-form-item>\r\n          </el-col> -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"产品型号\" prop=\"model\">\r\n              <el-input v-model=\"form.model\" :maxlength='30' placeholder=\"请输入产品型号\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span='12'>\r\n            <el-form-item label='上传规格书' >\r\n              <FileUpload ref='normfile' @inputChange='uploadNorm' :limit='1'\r\n                :value='normsList'></FileUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"含税价格\" prop=\"tax_price\">\r\n              <el-input type=\"number\" min=\"0\" v-model=\"form.tax_price\" placeholder=\"请输入含税价格\">\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"税率\" prop=\"tax_rate\">\r\n              <el-input v-model=\"form.tax_rate\" type=\"number\"  min=\"0\" max='100' placeholder=\"请输入税率\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"非集采价格\" prop=\"sale_price\">\r\n              <el-input type=\"number\" min=\"0\" v-model=\"form.sale_price\" placeholder=\"请输入集采价格\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"单位\" prop=\"unit\">\r\n              <el-input v-model=\"form.unit\" :maxlength='10' placeholder=\"请输入单位(吨/件...)\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"目标数量\" prop=\"central_goal\">\r\n              <el-input v-model=\"form.central_goal\" type=\"number\"  min=\"0\" placeholder=\"请输入目标数量\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"集采进度\" prop=\"central_percent\">\r\n              <el-input v-model=\"form.central_percent\" type=\"number\"  min=\"0\" max='100' placeholder=\"请输入集采进度\">\r\n                  <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <div class=\"w-100-scale bg6e6e6e mb-20\"></div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"集采截止日\" prop=\"deadline\">\r\n              <el-date-picker class='width-full' v-model=\"form.deadline\" value-format='yyyy-MM-dd HH:mm:ss' format=\"yyyy-MM-dd HH:mm\" type=\"datetime\"\r\n                placeholder=\"选择日期\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"预计交货时间\" prop='deliver_start'>\r\n              <el-date-picker class='width-full' v-model=\"rangeDate\" type=\"datetimerange\" range-separator=\"至\"\r\n                start-placeholder=\"开始日期\" value-format='yyyy-MM-dd' format=\"yyyy-MM-dd\" end-placeholder=\"结束日期\" @change='changeDate'>\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"供应商\" prop='enterprise_id'>\r\n              <el-select class='width-full' v-model=\"sEnterprise\" filterable remote reserve-keyword placeholder=\"请输入供应商\"\r\n                :remote-method=\"remoteEp\" @change='changeEp' value-key='id' :loading=\"loading\">\r\n                <el-option v-for=\"item in enterprise\" :key=\"item.id\" :label=\"item.name\" :value=\"item\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"定金比例\" prop=\"payment_rate\">\r\n              <el-input type=\"number\" v-model=\"form.payment_rate\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"运费\" prop='freight'>\r\n              <el-input type=\"number\"  min=\"0\" v-model=\"form.freight\" placeholder=\"请输入运费\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"最小包装量\" prop='start_pack'>\r\n              <el-input type=\"number\"  min=\"0\" v-model=\"form.start_pack\" placeholder=\"请输入最小包装量\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"品牌\" prop=\"brand\">\r\n              <el-input v-model=\"form.brand\" :maxlength='20' placeholder=\"请输入品牌\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"库存\" prop='stock'>\r\n              <el-input type=\"number\"  min=\"0\" v-model=\"form.stock\" placeholder=\"请输入库存\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"最小起订量\" prop='start_order'>\r\n              <el-input type=\"number\"  min=\"0\" v-model=\"form.start_order\" placeholder=\"请输入最小起订量\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"产品描述\" prop='description'>\r\n              <el-input type='textarea' :maxlength=\"200\" :rows=\"4\" v-model=\"form.description\" placeholder=\"请输入产品描述\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"集采规则\">\r\n              <el-input type='textarea' :rows=\"5\" :maxlength=\"300\" v-model=\"form.central_rule\" placeholder=\"请输入集采规则\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"产品主图\" prop='pictures'>\r\n              <ImageUpload ref='image1' @input=\"input\" :value='pictures'></ImageUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"产品详情图\" prop='details'>\r\n              <ImageUpload @input=\"input1\"  ref='image2' :value='details'></ImageUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-row>\r\n        <el-col :span=\"24\">\r\n          <div class=\"text-center\">\r\n            <el-button class=\"mr-50\" @click=\"reset\">重置</el-button>\r\n            <el-button  type=\"primary\" @click=\"handleSubmit\">提交</el-button>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\n  import {\r\n    addData,\r\n    updateData\r\n  } from '@/api/central/list';\r\n  import {\r\n    getData\r\n  } from '@/api/store/product';\r\n  import {\r\n    listData\r\n  } from '@/api/service/classify';\r\n  import {\r\n    searchData\r\n  } from '@/api/enterprise/apply';\r\n  import {\r\n    childrenNull\r\n  } from '@/utils/index';\r\n  import {\r\n    listEnum,listDict\r\n  } from '@/api/tool/util';\r\n  export default {\r\n    data() {\r\n\r\n      return {\r\n\r\n\r\n        loading: false,\r\n        status: [],\r\n        productStatus: [],\r\n        // 分类\r\n        types: [],\r\n        // 供应商列表\r\n        enterprise: [],\r\n        // 规格附件\r\n        normsList: [],\r\n        title: '',\r\n        // 弹窗\r\n        collectionDialog: false,\r\n        // 选中的产品分类\r\n        sType: [],\r\n        // 选中的供应商\r\n        sEnterprise: {},\r\n        // 选中的预计交货时间\r\n        rangeDate: [],\r\n        // 附件\r\n        files: [],\r\n        // 产品\r\n        pictures: [],\r\n        // 详情\r\n        details: [],\r\n        // 表单\r\n        form: {\r\n        },\r\n        // 表单验证\r\n        rules: {\r\n          name: [{\r\n            required: true,\r\n            message: \"产品名称不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          enterprise_id: [{\r\n            required: true,\r\n            message: \"供应商不能为空\",\r\n            trigger: \"change\",\r\n          }],\r\n          classify_id: [{\r\n            required: true,\r\n            message: \"产品分类不能为空\",\r\n            trigger: \"change\",\r\n          }],\r\n          central_status: [{\r\n            required: true,\r\n            message: \"集采状态不能为空\",\r\n            trigger: \"change\",\r\n          }],\r\n          status: [{\r\n            required: true,\r\n            message: \"审核状态不能为空\",\r\n            trigger: \"change\",\r\n          }],\r\n          type: [{\r\n            required: true,\r\n            message: \"产品类型不能为空\",\r\n            trigger: \"change\",\r\n          }],\r\n          start_order: [{\r\n            required: true,\r\n            message: \"最小起订量不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          start_pack: [{\r\n            required: true,\r\n            message: \"最小包装量不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          tax_price: [{\r\n            required: true,\r\n            message: \"产品含税价不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          unit: [{\r\n            required: true,\r\n            message: \"产品单位不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          model: [{\r\n            required: true,\r\n            message: \"产品型号不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          central_goal: [{\r\n            required: true,\r\n            message: \"目标数量不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          deadline: [{\r\n            required: true,\r\n            message: \"截止日期不能为空\",\r\n            trigger: \"change\",\r\n          }],\r\n          tax_rate: [{\r\n            required: true,\r\n            message: \"税率不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          payment_rate: [{\r\n            required: true,\r\n            message: \"定金比例不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          deliver_start: [{\r\n            required: true,\r\n            message: \"预计交货时间不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          freight: [{\r\n            required: true,\r\n            message: \"运费不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n\r\n          central_real: [{\r\n            required: true,\r\n            message: \"达成数量不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          details: [{\r\n            required: true,\r\n            message: \"产品详情不能为空\",\r\n            trigger: \"chagne\",\r\n          }],\r\n          pictures: [{\r\n            required: true,\r\n            message: \"产品图片不能为空\",\r\n            trigger: \"change\",\r\n          }],\r\n          description: [{\r\n            required: true,\r\n            message: \"产品描述不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          stock: [{\r\n            required: true,\r\n            message: \"库存不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          brand: [{\r\n            required: true,\r\n            message: \"品牌不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n          sale_price: [{\r\n            required: true,\r\n            message: \"集采价格不能为空\",\r\n            trigger: \"blur\",\r\n          }],\r\n\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.getTypes()\r\n      this.getEnum()\r\n      this.getDict()\r\n    },\r\n    methods: {\r\n      input(){\r\n         this.$refs['form'].clearValidate(['pictures']);\r\n\r\n      },\r\n      input1(){\r\n         this.$refs['form'].clearValidate(['details']);\r\n\r\n      },\r\n      /* 获取枚举 */\r\n      getEnum() {\r\n        listEnum().then(res => {\r\n          this.status = res.data.centralStatus;\r\n          this.productStatus = res.data.productStatus;\r\n        })\r\n      },\r\n\r\n      reset() {\r\n        this.sType = [];\r\n        this.rangeDate = [];\r\n\r\n        this.files = [];\r\n        this.pictures = [];\r\n        this.details = [];\r\n        this.enterprise = [];\r\n        this.sEnterprise = {};\r\n        this.normsList = [];\r\n        this.form = {\r\n          id: undefined,\r\n          infoId:undefined,\r\n          name: undefined,\r\n          enterprise_id: undefined, //公司id\r\n          enterprise_name: undefined, //公司名称\r\n          cover: undefined, //封面图片\r\n          normfile: undefined,\r\n          normurl: undefined,\r\n          product_no: undefined, //产品编号/集采编号\r\n          classify_id: undefined, //一级分类ID\r\n          classify2_id: undefined, //二级分类ID\r\n          classify3_id: undefined, //三级分类ID\r\n          central_percent: 0,\r\n          type: \"CENTRAL\", //活动类型  NORMAL 普通产品 GROUP 团购 CENTRAL 集采\r\n          tax_price: undefined, //产品含税价\r\n          unit: undefined, //产品单位\r\n          model: undefined, //型号\r\n          central_goal: undefined, //集采目标数量\r\n          central_rule: undefined, //集采规则\r\n          deadline: undefined, //集采截至日期\r\n          tax_rate: undefined, //税率\r\n          payment_rate: 10, //采集定金比例\r\n          deliver_start: undefined, //集采预计交货时间\r\n          deliver_end: undefined, //集采预计交货时间\r\n          freight: undefined, //运费说明\r\n          file_name: undefined, //集采附件名称\r\n          file_path: undefined, //集采附件路径\r\n          central_real: undefined, //集采达成数量\r\n          create_by: undefined, //创建人\r\n          remark: undefined, //审批备注\r\n          details: undefined, //产品详情\r\n          pictures: undefined, //产品图片\r\n          description: undefined,\r\n          stock: undefined,\r\n          brand: undefined,\r\n          sale_price: undefined\r\n        }\r\n        this.resetForm('form')\r\n      },\r\n      /* 新增弹窗 */\r\n      add() {\r\n        this.reset();\r\n        this.form.status = \"ONLINE\"\r\n        this.title = '发起集采';\r\n        this.collectionDialog = true;\r\n      },\r\n      /* 编辑弹窗 */\r\n      edit(id) {\r\n        this.reset();\r\n        this.title = '编辑集采';\r\n        this.collectionDialog = true;\r\n        getData(id).then(res => {\r\n          let { classify_id, classify2_id, classify3_id, deliver_start, deliver_end,\r\n            enterprise_id, enterprise_name, file_name, file_path, cover,\r\n            pictures, details, normfile, normurl } = res.data;\r\n          this.form = res.data;\r\n          let sType = [];\r\n          if(classify3_id && classify3_id != -1) {\r\n            sType = [classify_id, classify2_id, classify3_id];\r\n          } else if(classify2_id && classify2_id != -1) {\r\n            sType = [classify_id, classify2_id];\r\n          } else if(classify_id && classify_id != -1) {\r\n            sType = [classify_id]\r\n          }\r\n          this.sType = [...sType]\r\n          this.rangeDate = [deliver_start, deliver_end];\r\n          this.enterprise = [{id: enterprise_id, name: enterprise_name}];\r\n          this.sEnterprise = {id: enterprise_id, name: enterprise_name};\r\n\r\n          if(normurl) {\r\n            this.normsList = [{name: normfile, url: normurl}];\r\n          }\r\n          if(file_path) {\r\n            this.files = [{name: file_name, url: file_path}]\r\n          }\r\n          if(pictures.length) {\r\n            this.pictures = pictures.map(item => ({name: item, url: item}))\r\n          }\r\n          if(details.length) {\r\n            this.details = details.map(item => ({name: item, url: item}))\r\n          }\r\n          this.resetForm('form')\r\n        })\r\n      },\r\n      /* 获取产品分类 */\r\n      getTypes() {\r\n        listData().then(res => {\r\n          this.types = childrenNull(res.data, 'children');\r\n        })\r\n      },\r\n      /* 切换产品分类 */\r\n      changeClassify(e) {\r\n        this.form.classify_id = e[0];\r\n        this.form.classify2_id = e[1];\r\n        this.form.classify3_id = e[2];\r\n      },\r\n      /* 查询企业信息 */\r\n      remoteEp(e) {\r\n        this.loading = true;\r\n        searchData(e).then(res => {\r\n          this.loading = false;\r\n          this.enterprise = res.data;\r\n        })\r\n      },\r\n      /* 切换企业信息 */\r\n      changeEp(e) {\r\n        this.form.enterprise_id = this.sEnterprise.id;\r\n        this.form.enterprise_name = this.sEnterprise.name;\r\n      },\r\n      /* 添加规格 */\r\n      uploadNorm(fileList) {\r\n        let name = undefined;\r\n        let url = undefined;\r\n        if(fileList.length) {\r\n          name = fileList[0].name;\r\n          url = fileList[0].url;\r\n        }\r\n        this.form.normfile = name;\r\n        this.form.normurl = url;\r\n        console.log(this.form)\r\n      },\r\n      /* 切换预计交货时间 */\r\n      changeDate(e) {\r\n        this.form.deliver_start = e[0];\r\n        this.form.deliver_end = e[1];\r\n      },\r\n      handleSubmit() {\r\n        // 数值校验\r\n        if(this.form.tax_price < 0) return this.$modal.msgError('产品含税价不能小于0！');\r\n        if(this.form.tax_rate < 0) return this.$modal.msgError('产品税率不能小于0！');\r\n        if(this.form.freight < 0) return this.$modal.msgError('运费不能小于0！');\r\n        if(this.form.stock < 0) return this.$modal.msgError('产品库存不能小于0！');\r\n        if(this.form.start_order < 0) return this.$modal.msgError('最小起订量不能小于0！');\r\n        if(this.form.start_pack < 0) return this.$modal.msgError('最小包装量不能小于0！');\r\n        if(this.form.sale_price < 0) return this.$modal.msgError('集采价格不能小于0！');\r\n        if(this.form.central_goal < 0) return this.$modal.msgError('目标数量不能小于0！');\r\n        if(this.form.payment_rate < 0) return this.$modal.msgError('定金比例不能小于0！');\r\n        if(this.form.payment_rate > 100) return this.$modal.msgError('定金比例不能大于100！');\r\n        if(Number(this.form.central_goal) < Number(this.form.start_order)) return this.$modal.msgError('目标数量不能小于最小起订量！');\r\n        // 封面\r\n        if (this.$refs.image1.fileList.length) {\r\n          this.form.cover = this.$refs.image1.fileList[0].url;\r\n        } else {\r\n          this.form.cover = undefined;\r\n        }\r\n\r\n        // 产品图片\r\n        if (this.$refs.image1.fileList.length) {\r\n          this.form.pictures = this.$refs.image1.fileList.map(item => item.url).join('@');\r\n        } else {\r\n          this.form.pictures = undefined;\r\n        }\r\n        // 详情图片\r\n        if (this.$refs.image2.fileList.length) {\r\n          this.form.details = this.$refs.image2.fileList.map(item => item.url).join('@');\r\n        } else {\r\n          this.form.details = undefined;\r\n        }\r\n\r\n        this.form.central_status = \"GOING\"\r\n        this.form.status = \"ONLINE\"\r\n        this.$refs.form.validate(validate => {\r\n          if (validate) {\r\n            this.loading = true\r\n            if (this.form.id) {\r\n              updateData(this.form).then(() => {\r\n                this.loading = false\r\n                this.$modal.msgSuccess('修改成功');\r\n                this.collectionDialog = false;\r\n                this.$parent.getList()\r\n              })\r\n            } else {\r\n              addData(this.form).then(() => {\r\n                this.loading = false\r\n                this.$modal.msgSuccess('发起成功');\r\n                this.collectionDialog = false;\r\n                this.$parent.getList()\r\n              })\r\n            }\r\n          } else {\r\n            this.$modal.msgError('请完善信息再提交!')\r\n          }\r\n        })\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style scoped>\r\n  .bg6e6e6e {\r\n    background-color: #e6e6e6;\r\n    height: 1px;\r\n  }\r\n\r\n  .tag-input {\r\n    width: 200px;\r\n  }\r\n</style>\r\n"]}]}