09:11:00.945 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:11:03.101 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0
09:11:03.333 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 115 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:03.447 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:03.483 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 31 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:04.186 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 696 ms to scan 234 urls, producing 0 keys and 0 values 
09:11:04.201 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:04.221 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:04.240 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:04.452 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 208 ms to scan 234 urls, producing 0 keys and 0 values 
09:11:04.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:04.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1825923873
09:11:04.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/883735648
09:11:04.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:04.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:04.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:07.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381866810_127.0.0.1_49475
09:11:07.302 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] Notify connected event to listeners.
09:11:07.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:07.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66186e99-feff-4e07-9c02-ca7e2e38f23c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/792210014
09:11:07.780 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:11:24.781 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:11:26.748 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 06393116-0874-4865-8470-9790bc21066f
09:11:26.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] RpcClient init label, labels = {module=naming, source=sdk}
09:11:26.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:11:26.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:11:26.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:11:26.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:26.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381886790_127.0.0.1_49808
09:11:26.902 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Notify connected event to listeners.
09:11:26.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:26.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/792210014
09:11:28.395 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:11:31.198 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0
09:11:31.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:31.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1825923873
09:11:31.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/883735648
09:11:31.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:31.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:31.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:31.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381891215_127.0.0.1_49910
09:11:31.340 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] Notify connected event to listeners.
09:11:31.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:31.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14a87f0c-3837-4f55-a2a1-44edfb5cc5d5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/792210014
09:11:33.282 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:11:33.291 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:11:34.674 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
09:11:34.807 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 35.076 seconds (JVM running for 37.247)
09:11:34.824 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:11:34.825 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
09:11:34.826 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
09:11:35.178 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:11:35.180 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:12:04.202 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:12:04.202 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:12:04.203 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:12:04.204 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:12:04.207 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:12:04.208 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:12:04.209 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:12:04.210 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:12:34.228 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:12:34.229 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06393116-0874-4865-8470-9790bc21066f] Ack server push request, request = NotifySubscriberRequest, requestId = 14
12:39:46.253 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:39:46.256 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:39:46.601 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:39:46.602 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@509602f5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:39:46.602 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750381886790_127.0.0.1_49808
12:39:46.621 [nacos-grpc-client-executor-4457] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750381886790_127.0.0.1_49808]Ignore complete event,isRunning:false,isAbandon=false
12:39:46.678 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@351345a6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 4458]
