{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\list.vue", "mtime": 1750151094290}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBlbnRlcnByaXNlRGV0YWlsIGZyb20gJy4vY29tcG9uZW50cy9lbnRlcnByaXNlLWRldGFpbCc7CmltcG9ydCBlbnRlcnByaXNlVXNlciBmcm9tICcuL2NvbXBvbmVudHMvZW50ZXJwcmlzZS11c2VyJzsKaW1wb3J0IHsKICBsaXN0RW51bQp9IGZyb20gJ0AvYXBpL3Rvb2wvdXRpbCc7Ci8vIOaWsOWinuW8ueeqlwppbXBvcnQgewogIGxpc3REYXRhLAogIGdldERhdGEsCiAgb3BEYXRhLAogIHVwRW50ZXJwcmlzZQp9IGZyb20gIkAvYXBpL2VudGVycHJpc2Uvbm9ybWFsIjsKaW1wb3J0IHtsaXN0fSBmcm9tICJAL2FwaS9zdXBwbHkvY3JlZGl0UmF0aW5nIjsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIGVudGVycHJpc2VEZXRhaWwsCiAgICBlbnRlcnByaXNlVXNlcgogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgbmFtZTogdW5kZWZpbmVkLAogICAgICAgIGJ1c2luZXNzX25vOiB1bmRlZmluZWQsCiAgICAgICAgbGlua2VyOiB1bmRlZmluZWQsCiAgICAgICAgbGlua3Bob25lOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsCiAgICAgIH0sCiAgICAgIC8vIOWIl+ihqOaVsOaNrgogICAgICBsaXN0OiBbXSwKICAgICAgLy8g5Zu+54mH6aKE6KeI5Zyw5Z2ACiAgICAgIHNyY0xpc3Q6IFtdLAogICAgICBvcHRpb25zR3JhZGVUeXBlOltdLAogICAgICBzaG93OmZhbHNlLAogICAgICBzaG93MTpmYWxzZQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogICAgdGhpcy5nZXRFbnVtcygpOwogICAgdGhpcy54eUxpc3QoKQogIH0sCgogIG1ldGhvZHM6IHsKICAgIHh5TGlzdCgpIHsKICAgICAgICBsaXN0KHsKICAgICAgICAgICAgcGFnZTogMSwKICAgICAgICAgICAgc2l6ZTogMTAwLAogICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgfSkKICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgdGhpcy5vcHRpb25zR3JhZGVUeXBlID0gcmVzLmRhdGE7CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7fSk7CiAgICB9LAogICAgaGFuZGxlSW52aXRlKCkgewogICAgICBjb25zdCBjbGlwYm9hcmRPYmogPSBuYXZpZ2F0b3IuY2xpcGJvYXJkOwogICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICBtZXNzYWdlOiAn6ZO+5o6l5bey5aSN5Yi277yM5b+r5Y675om+5pyL5Y+L5YiG5Lqr5ZCnficsCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgIH0pCiAgICAgIGNsaXBib2FyZE9iai53cml0ZVRleHQoJ2h0dHBzOi8vc2MuY251ZGouY29tL2xvZ2luJyk7CiAgICB9LAogICAgZ2V0RW51bXMoKSB7CiAgICAgIGxpc3RFbnVtKCkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuc3RhdHVzT3B0aW9ucyA9IHJlcy5kYXRhLmVudGVycHJpc2VOb3JtYWxTdGF0dXM7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOafpeivouWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdERhdGEodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuY291bnQ7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIHNldEdyYWRlKHJvdyl7CiAgICAgIHRoaXMuZm9ybT1yb3cKICAgICAgdGhpcy5zaG93PXRydWUKCiAgICB9LAogICAgaGFuZGxlU3VibWl0KCl7CiAgICAgIHVwRW50ZXJwcmlzZSh7CiAgICAgICAgY3JlZGl0X2lkOnRoaXMuZm9ybS5jcmVkaXRfaWQsCiAgICAgICAgcG9pbnRzOnRoaXMuZm9ybS5wb2ludHMsCiAgICAgICAgaWQ6dGhpcy5mb3JtLmlkCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+u5pS55oiQ5YqfJykKICAgICAgICB0aGlzLnNob3cgPSBmYWxzZTsKICAgICAgICB0aGlzLnNob3cxID0gZmFsc2U7CiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSk7CiAgICB9LAogICAgc2V0SmYocm93KXsKICAgICAgdGhpcy5mb3JtPXJvdwogICAgICB0aGlzLnNob3cxPXRydWUKICAgIH0sCiAgICAvKiog6KGo5Y2V5pCc57SiICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlUHJldmlldyh1cmwpIHsKICAgICAgdGhpcy5zcmNMaXN0ID0gW3VybF07CiAgICB9LAogICAgLyoqIOaWsOWiniAqLwogICAgaGFuZGxlSW52aXRlKCkgewogICAgICBjb25zdCBjbGlwYm9hcmRPYmogPSBuYXZpZ2F0b3IuY2xpcGJvYXJkOwogICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICBtZXNzYWdlOiAn6ZO+5o6l5bey5aSN5Yi277yM5b+r5Y675om+5pyL5Y+L5YiG5Lqr5ZCnficsCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgIH0pCiAgICAgIGNsaXBib2FyZE9iai53cml0ZVRleHQoJ2h0dHBzOi8vc2MuY251ZGouY29tL2xvZ2luJyk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8vIOmHjee9rgogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBuYW1lOiB1bmRlZmluZWQsCiAgICAgICAgYnVzaW5lc3Nfbm86IHVuZGVmaW5lZCwKICAgICAgICBsaW5rZXI6IHVuZGVmaW5lZCwKICAgICAgICBsaW5rcGhvbmU6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZCwKICAgICAgfTsKICAgICAgdGhpcy5yZXNlckZvcm0oJ3F1ZXJ5Rm9ybScpCiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGV0YWlsKHJvdykgewogICAgICBnZXREYXRhKHJvdy5pZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLiRyZWZzLmRldGFpbC5vcGVuKCkKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlT3Aoc3RhdHVzKSB7CiAgICAgIHZhciBpZHMgPSB0aGlzLmlkcwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmk43kvZznvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gb3BEYXRhKHsKICAgICAgICAgICJvcGlkIjogaWRzLmpvaW4oIiwiKSwKICAgICAgICAgIHN0YXR1czogc3RhdHVzCiAgICAgICAgfSk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLy8g6K6+572u566h55CG5ZGYCiAgICBoYW5kbGVBZG1pbihyb3cpIHsKICAgICAgdGhpcy4kcmVmcy51c2VyLm9wZW4ocm93LmlkKQogICAgfSwKICB9LAp9Owo="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/supply", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\" prop='name'>\r\n            <el-input clearable v-model=\"queryParams.name\" :maxlength=\"50\" placeholder=\"输入公司名称\" size='small'\r\n              style=\"width: 300px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='business_no'>\r\n            <el-input clearable v-model=\"queryParams.business_no\" :maxlength=\"18\" placeholder=\"输入营业执照\" size='small'\r\n              style=\"width: 180px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='linker'>\r\n            <el-input clearable v-model=\"queryParams.linker\" :maxlength=\"20\" placeholder=\"输入联系人\" size='small'\r\n              style=\"width: 140px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='linkphone'>\r\n            <el-input clearable v-model=\"queryParams.linkphone\" :maxlength=\"11\" placeholder=\"输入联系电话\" size='small'\r\n              style=\"width: 140px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"状态\" clearable size=\"small\" style=\"width: 120px\">\r\n              <el-option v-for=\"item in statusOptions\" :key=\"item.key\" :label='item.value' :value=\"item.key\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleInvite\">邀请入驻</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button type=\"primary\" :disabled=\"multiple\" plain icon=\"el-icon-upload2\" size=\"mini\"\r\n              @click=\"handleOp('PASS')\">上线</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button type=\"danger\" :disabled=\"multiple\" plain icon=\"el-icon-download\" size=\"mini\"\r\n              @click=\"handleOp('DENY')\">下线</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"50\" />\r\n          <el-table-column label=\"企业名称\" align=\"center\" prop=\"name\" width=\"280\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"积分\" align=\"center\" prop=\"points\" width=\"50\" />\r\n          <el-table-column label=\"营业执照号\" align=\"center\" prop=\"business_no\" width=\"180\" />\r\n\r\n          <el-table-column label=\"公司授权文件\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <a :href=\"scope.row.certification\" target=\"_blank\">查看授权文件</a>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"营业执照\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.business_image\"\r\n                @click=\"handlePreview(scope.row.business_image)\" :preview-src-list=\"srcList\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"信用等级\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning' v-if=\"scope.row.credit_rating_name\">{{scope.row.credit_rating_name}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"品牌\" align=\"center\" prop=\"brand\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"联系人\" align=\"center\" prop=\"linker\" width=\"120\" />\r\n          <el-table-column label=\"联系电话\" align=\"center\" prop=\"linkphone\" width=\"140\" />\r\n          <el-table-column label=\"职务\" align=\"center\" prop=\"post\" width=\"120\" />\r\n          <el-table-column label=\"邮箱\" align=\"center\" prop=\"email\" width=\"120\" />\r\n          <el-table-column label=\"公司地址\" align=\"center\" prop=\"location\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"申请者\" align=\"center\" prop=\"create_by\" width=\"100\" />\r\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n          <el-table-column label=\"操作\" align=\"center\" width=\"200\" fixed='right'>\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"setGrade(scope.row)\"\r\n                  >信用等级</el-button\r\n              >\r\n              <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"setJf(scope.row)\"\r\n                  >积分</el-button\r\n              >\r\n              <el-button type=\"text\" icon=\"el-icon-view\" size=\"mini\" @click=\"handleDetail(scope.row)\">详情\r\n              </el-button>\r\n              <el-button type=\"text\" icon=\"el-icon-user\" size=\"mini\" @click=\"handleAdmin(scope.row)\">设置管理员\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <enterprise-detail ref='detail' :form=\"form\" @refresh='getList'></enterprise-detail>\r\n    <enterprise-user ref='user'></enterprise-user>\r\n    <el-dialog\r\n        title=\"信用等级\"\r\n        :visible.sync=\"show\"\r\n        width=\"30%\"\r\n        :before-close=\"() => (show = false)\"\r\n    >\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n            <el-form-item label=\"信用等级\" prop=\"credit_rating_name\">\r\n               <el-select\r\n                   clearable\r\n                   v-model=\"form.credit_id\"\r\n                   placeholder=\"请选择信用等级\"\r\n               >\r\n                   <el-option\r\n                       v-for=\"item in optionsGradeType\"\r\n                       :key=\"item.id\"\r\n                       :label=\"item.credit_rating_name\"\r\n                       :value=\"item.id\"\r\n                   >\r\n                   </el-option>\r\n               </el-select>\r\n            </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"show = false\">取 消</el-button>\r\n            <el-button\r\n                type=\"primary\"\r\n                :loading=\"loading\"\r\n                @click=\"handleSubmit\"\r\n                >确 定</el-button\r\n            >\r\n        </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n        title=\"修改积分\"\r\n        :visible.sync=\"show1\"\r\n        width=\"30%\"\r\n        :before-close=\"() => (show = false)\"\r\n    >\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" >\r\n            <el-form-item label=\"积分\" prop=\"points\">\r\n               <el-input-number v-model=\"form.points\" placeholder=\"请输入积分\"></el-input-number>\r\n            </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"show1 = false\">取 消</el-button>\r\n            <el-button\r\n                type=\"primary\"\r\n                :loading=\"loading\"\r\n                @click=\"handleSubmit\"\r\n                >确 定</el-button\r\n            >\r\n        </span>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import enterpriseDetail from './components/enterprise-detail';\r\n  import enterpriseUser from './components/enterprise-user';\r\n  import {\r\n    listEnum\r\n  } from '@/api/tool/util';\r\n  // 新增弹窗\r\n  import {\r\n    listData,\r\n    getData,\r\n    opData,\r\n    upEnterprise\r\n  } from \"@/api/enterprise/normal\";\r\n  import {list} from \"@/api/supply/creditRating\";\r\n  export default {\r\n    components: {\r\n      enterpriseDetail,\r\n      enterpriseUser\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: false,\r\n        // 选中数组\r\n        ids: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        statusOptions: [],\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          business_no: undefined,\r\n          linker: undefined,\r\n          linkphone: undefined,\r\n          status: undefined,\r\n        },\r\n        // 列表数据\r\n        list: [],\r\n        // 图片预览地址\r\n        srcList: [],\r\n        optionsGradeType:[],\r\n        show:false,\r\n        show1:false\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n      this.getEnums();\r\n      this.xyList()\r\n    },\r\n\r\n    methods: {\r\n      xyList() {\r\n          list({\r\n              page: 1,\r\n              size: 100,\r\n              status: 1,\r\n          })\r\n              .then((res) => {\r\n                  this.optionsGradeType = res.data;\r\n              })\r\n              .catch((err) => {});\r\n      },\r\n      handleInvite() {\r\n        const clipboardObj = navigator.clipboard;\r\n        this.$message({\r\n          message: '链接已复制，快去找朋友分享吧~',\r\n          type: 'success'\r\n        })\r\n        clipboardObj.writeText('https://sc.cnudj.com/login');\r\n      },\r\n      getEnums() {\r\n        listEnum().then(res => {\r\n          this.statusOptions = res.data.enterpriseNormalStatus;\r\n        })\r\n      },\r\n      /** 查询列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listData(this.queryParams).then(response => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      setGrade(row){\r\n        this.form=row\r\n        this.show=true\r\n\r\n      },\r\n      handleSubmit(){\r\n        upEnterprise({\r\n          credit_id:this.form.credit_id,\r\n          points:this.form.points,\r\n          id:this.form.id\r\n        }).then(response => {\r\n          this.$message.success('修改成功')\r\n          this.show = false;\r\n          this.show1 = false;\r\n          this.getList()\r\n        });\r\n      },\r\n      setJf(row){\r\n        this.form=row\r\n        this.show1=true\r\n      },\r\n      /** 表单搜索 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      handlePreview(url) {\r\n        this.srcList = [url];\r\n      },\r\n      /** 新增 */\r\n      handleInvite() {\r\n        const clipboardObj = navigator.clipboard;\r\n        this.$message({\r\n          message: '链接已复制，快去找朋友分享吧~',\r\n          type: 'success'\r\n        })\r\n        clipboardObj.writeText('https://sc.cnudj.com/login');\r\n      },\r\n      // 多选框选中数据\r\n      handleSelectionChange(selection) {\r\n        this.ids = selection.map(item => item.id)\r\n        this.single = selection.length != 1\r\n        this.multiple = !selection.length\r\n      },\r\n      // 重置\r\n      resetQuery() {\r\n        this.queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          business_no: undefined,\r\n          linker: undefined,\r\n          linkphone: undefined,\r\n          status: undefined,\r\n        };\r\n        this.reserForm('queryForm')\r\n      },\r\n      /** 修改按钮操作 */\r\n      handleDetail(row) {\r\n        getData(row.id).then(response => {\r\n          this.form = response.data;\r\n          this.$refs.detail.open()\r\n        });\r\n      },\r\n      /** 按钮操作 */\r\n      handleOp(status) {\r\n        var ids = this.ids\r\n        this.$modal.confirm('是否确认操作编号为\"' + ids + '\"的数据项？').then(function() {\r\n          return opData({\r\n            \"opid\": ids.join(\",\"),\r\n            status: status\r\n          });\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"操作成功\");\r\n        }).catch(() => {});\r\n      },\r\n      // 设置管理员\r\n      handleAdmin(row) {\r\n        this.$refs.user.open(row.id)\r\n      },\r\n    },\r\n  };\r\n</script>\r\n"]}]}