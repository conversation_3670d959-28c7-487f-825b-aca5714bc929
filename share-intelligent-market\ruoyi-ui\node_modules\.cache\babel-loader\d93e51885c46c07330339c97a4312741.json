{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\notice\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\notice\\index.vue", "mtime": 1750151094297}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_notice", "require", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "noticeList", "title", "open", "queryParams", "pageNum", "pageSize", "noticeTitle", "undefined", "createBy", "status", "form", "rules", "required", "message", "trigger", "noticeType", "created", "getList", "methods", "_this", "listNotice", "then", "response", "rows", "cancel", "reset", "noticeId", "noticeContent", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getNotice", "submitForm", "_this3", "$refs", "validate", "valid", "updateNotice", "$modal", "msgSuccess", "addNotice", "handleDelete", "_this4", "noticeIds", "confirm", "delNotice", "catch"], "sources": ["src/views/system/notice/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\r\n        <el-input\r\n          v-model=\"queryParams.noticeTitle\"\r\n          placeholder=\"请输入公告标题\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作人员\" prop=\"createBy\">\r\n        <el-input\r\n          v-model=\"queryParams.createBy\"\r\n          placeholder=\"请输入操作人员\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"类型\" prop=\"noticeType\">\r\n        <el-select\r\n          v-model=\"queryParams.noticeType\"\r\n          placeholder=\"公告类型\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_notice_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:notice:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['system:notice:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['system:notice:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"noticeList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column\r\n        label=\"序号\"\r\n        align=\"center\"\r\n        prop=\"noticeId\"\r\n        width=\"100\"\r\n      />\r\n      <el-table-column\r\n        label=\"公告标题\"\r\n        align=\"center\"\r\n        prop=\"noticeTitle\"\r\n        :show-overflow-tooltip=\"true\"\r\n      />\r\n      <el-table-column\r\n        label=\"公告类型\"\r\n        align=\"center\"\r\n        prop=\"noticeType\"\r\n        width=\"100\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.sys_notice_type\"\r\n            :value=\"scope.row.noticeType\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.sys_notice_status\"\r\n            :value=\"scope.row.status\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"创建者\"\r\n        align=\"center\"\r\n        prop=\"createBy\"\r\n        width=\"100\"\r\n      />\r\n      <el-table-column\r\n        label=\"创建时间\"\r\n        align=\"center\"\r\n        prop=\"createTime\"\r\n        width=\"100\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:notice:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:notice:remove']\"\r\n            >删除</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改公告对话框 -->\r\n    <el-dialog\r\n      v-if=\"open\"\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"780px\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\r\n              <el-input\r\n                v-model=\"form.noticeTitle\"\r\n                placeholder=\"请输入公告标题\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"公告类型\" prop=\"noticeType\">\r\n              <el-select v-model=\"form.noticeType\" placeholder=\"请选择公告类型\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_notice_type\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_notice_status\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                  >{{ dict.label }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"内容\">\r\n              <editor v-model=\"form.noticeContent\" :min-height=\"192\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listNotice,\r\n  getNotice,\r\n  delNotice,\r\n  addNotice,\r\n  updateNotice,\r\n} from \"@/api/system/notice\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\"sys_notice_status\", \"sys_notice_type\"],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 公告表格数据\r\n      noticeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        noticeTitle: undefined,\r\n        createBy: undefined,\r\n        status: undefined,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        noticeTitle: [\r\n          { required: true, message: \"公告标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        noticeType: [\r\n          { required: true, message: \"公告类型不能为空\", trigger: \"change\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询公告列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listNotice(this.queryParams).then((response) => {\r\n        this.noticeList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        noticeId: undefined,\r\n        noticeTitle: undefined,\r\n        noticeType: undefined,\r\n        noticeContent: undefined,\r\n        status: \"0\",\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.noticeId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加公告\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const noticeId = row.noticeId || this.ids;\r\n      getNotice(noticeId).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改公告\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function () {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.noticeId != undefined) {\r\n            updateNotice(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addNotice(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const noticeIds = row.noticeId || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除公告编号为\"' + noticeIds + '\"的数据项？')\r\n        .then(function () {\r\n          return delNotice(noticeIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;AAmPA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAQA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACA;MACAC,KAAA;QACAL,WAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,UAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAzB,OAAA;MACA,IAAA0B,kBAAA,OAAAjB,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAnB,UAAA,GAAAsB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAApB,KAAA,GAAAuB,QAAA,CAAAvB,KAAA;QACAoB,KAAA,CAAAzB,OAAA;MACA;IACA;IACA;IACA8B,MAAA,WAAAA,OAAA;MACA,KAAAtB,IAAA;MACA,KAAAuB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAf,IAAA;QACAgB,QAAA,EAAAnB,SAAA;QACAD,WAAA,EAAAC,SAAA;QACAQ,UAAA,EAAAR,SAAA;QACAoB,aAAA,EAAApB,SAAA;QACAE,MAAA;MACA;MACA,KAAAmB,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA1B,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAa,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArC,GAAA,GAAAqC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAR,QAAA;MAAA;MACA,KAAA9B,MAAA,GAAAoC,SAAA,CAAAG,MAAA;MACA,KAAAtC,QAAA,IAAAmC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAX,KAAA;MACA,KAAAvB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAoC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAd,KAAA;MACA,IAAAC,QAAA,GAAAY,GAAA,CAAAZ,QAAA,SAAA/B,GAAA;MACA,IAAA6C,iBAAA,EAAAd,QAAA,EAAAL,IAAA,WAAAC,QAAA;QACAiB,MAAA,CAAA7B,IAAA,GAAAY,QAAA,CAAA7B,IAAA;QACA8C,MAAA,CAAArC,IAAA;QACAqC,MAAA,CAAAtC,KAAA;MACA;IACA;IACA;IACAwC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhC,IAAA,CAAAgB,QAAA,IAAAnB,SAAA;YACA,IAAAuC,oBAAA,EAAAJ,MAAA,CAAAhC,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAoB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxC,IAAA;cACAwC,MAAA,CAAAzB,OAAA;YACA;UACA;YACA,IAAAgC,iBAAA,EAAAP,MAAA,CAAAhC,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAoB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxC,IAAA;cACAwC,MAAA,CAAAzB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,SAAA,GAAAd,GAAA,CAAAZ,QAAA,SAAA/B,GAAA;MACA,KAAAoD,MAAA,CACAM,OAAA,kBAAAD,SAAA,aACA/B,IAAA;QACA,WAAAiC,iBAAA,EAAAF,SAAA;MACA,GACA/B,IAAA;QACA8B,MAAA,CAAAlC,OAAA;QACAkC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAO,KAAA;IACA;EACA;AACA", "ignoreList": []}]}