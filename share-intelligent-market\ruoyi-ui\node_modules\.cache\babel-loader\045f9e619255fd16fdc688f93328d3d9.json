{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\components\\enterprise-detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\components\\enterprise-detail.vue", "mtime": 1750151094286}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfaW5kZXggPSByZXF1aXJlKCJAL3V0aWxzL2luZGV4Iik7CnZhciBfYXBwbHkgPSByZXF1aXJlKCJAL2FwaS9lbnRlcnByaXNlL2FwcGx5Iik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBwcm9wczogewogICAgZm9ybTogT2JqZWN0CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNob3c6IGZhbHNlCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgb3BlbjogZnVuY3Rpb24gb3BlbigpIHsKICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsKICAgIH0sCiAgICBoYW5kbGVTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRsZVN1Ym1pdChzdGF0dXMpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgKDAsIF9hcHBseS5vcERhdGEpKHsKICAgICAgICBpZDogdGhpcy5mb3JtLmlkLAogICAgICAgIHJlbWFyazogdGhpcy5mb3JtLnJlbWFyaywKICAgICAgICBzdGF0dXM6IHN0YXR1cwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgIG1lc3NhZ2U6ICfmk43kvZzmiJDlip8hJwogICAgICAgIH0pOwogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICBfdGhpcy5zaG93ID0gZmFsc2U7CiAgICAgICAgX3RoaXMuJGVtaXQoJ3JlZnJlc2gnKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_index", "require", "_apply", "props", "form", "Object", "data", "loading", "show", "methods", "open", "handleSubmit", "status", "_this", "opData", "id", "remark", "then", "response", "$message", "type", "message", "$emit"], "sources": ["src/views/supply/components/enterprise-detail.vue"], "sourcesContent": ["<!-- 添加文章 -->\r\n<template>\r\n  <el-dialog title=\"公司详情\" :visible.sync=\"show\" width=\"80%\" :before-close=\"() => show = false\">\r\n    <el-descriptions class=\"margin-top\" title=\"基本信息\" :column=\"3\" direction=\"horizontal\" border>\r\n      <el-descriptions-item label=\"审核状态\">\r\n        <el-tag type=\"warning\">{{form.statusStr}}</el-tag>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"授权文件\">\r\n        <a :href=\"form.certification\" target=\"_blank\">查看授权文件</a>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"公司名称\">{{form.name}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"营业执照号码\">{{form.business_no}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"公司Logo\">\r\n        <el-image v-if=\"form.logo\" style=\"width: 100px; height: 100px\" :src=\"form.logo\" :preview-src-list=\"[form.logo]\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"营业执照\">\r\n        <el-image v-if=\"form.business_image\" style=\"width: 100px; height: 100px\" :src=\"form.business_image\"\r\n          :preview-src-list=\"[form.business_image]\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"公司简介\">{{form.description}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"主营产品\">{{form.product_main}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"品牌信息\">{{form.brand}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"业务范围\">{{form.business}}</el-descriptions-item>\r\n      <el-descriptions-item v-if=\"form.application\" label=\"应用领域\">\r\n        <span\r\n          v-for=\"item in form.application\">{{item.classify_name}}-{{item.classify2_name}}-{{item.classify3_name}},</span>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"公司地址\">{{form.province}}{{form.city}}{{form.region}}{{form.address}}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"联系人\">{{form.linker}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"联系电话\">{{form.linkphone}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"职务\">{{form.post}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"联系邮箱\">{{form.email}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"申请人\">{{form.create_by}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"申请时间\">{{form.create_time}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"审核备注\">{{form.remark || ''}}</el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions v-if=\"form.product_image\" style=\"margin-top:20px\" title=\"产品图片\" :column=\"1\" direction=\"horizontal\"\r\n      border>\r\n      <el-descriptions-item label=\"图片预览\">\r\n        <el-image style=\"width: 100px; height: 100px\" :src=\"item\" v-for=\"(item,index) in form.product_image\"\r\n          :key=\"index\" :preview-src-list=\"form.product_image\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n    </el-descriptions>\r\n\r\n    <el-descriptions  title=\"积分、信用等级\" :column=\"3\" direction=\"horizontal\" border style=\"margin-top:20px\">\r\n      <el-descriptions-item label=\"积分\">{{form.points}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"信用等级\">{{form.credit_rating_name}}</el-descriptions-item>\r\n      <el-descriptions-item label=\"图标\"><el-image style=\"width: 100px; height: 100px\" :src=\"form.icon\" :preview-src-list=\"[form.icon]\">\r\n        </el-image></el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-row v-if=\"form.type=='apply' && form.status=='NEW'\" style=\"margin-top: 20px;\">\r\n      <el-form size=\"small\" label-width=\"68px\">\r\n        <el-form-item label=\"审核备注\" prop=\"remark\">\r\n          <el-input clearable type='textarea' v-model=\"form.remark\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-row>\r\n    <div slot=\"footer\" class=\"dialog-footer\" style=\"text-align: center;\">\r\n      <el-button @click=\"show = false\">取 消</el-button>\r\n      <el-button v-if=\"form.type=='apply' && form.status=='NEW'\" type=\"primary\" :loading=\"loading\"\r\n        @click=\"handleSubmit('PASS')\">通 过\r\n      </el-button>\r\n      <el-button v-if=\"form.type=='apply' && form.status=='NEW'\" type=\"danger\" :loading=\"loading\"\r\n        @click=\"handleSubmit('DENY')\">驳 回\r\n      </el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\n  import {formatDate} from '@/utils/index'\r\n  import {\r\n    opData\r\n  } from \"@/api/enterprise/apply\";\r\n  export default {\r\n    props: {\r\n      form: Object\r\n    },\r\n    data() {\r\n      return {\r\n        loading: false,\r\n        show: false\r\n      }\r\n    },\r\n    methods: {\r\n      open() {\r\n        this.show = true\r\n      },\r\n      handleSubmit(status) {\r\n        this.loading = true\r\n        opData({\r\n          id: this.form.id,\r\n          remark: this.form.remark,\r\n          status: status\r\n        }).then(response => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '操作成功!'\r\n          });\r\n          this.loading = false\r\n          this.show = false;\r\n          this.$emit('refresh')\r\n        });\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n  .el-descriptions-item__cell {\r\n    max-width: 300px;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;AA0EA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAE,KAAA;IACAC,IAAA,EAAAC;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAF,IAAA;IACA;IACAG,YAAA,WAAAA,aAAAC,MAAA;MAAA,IAAAC,KAAA;MACA,KAAAN,OAAA;MACA,IAAAO,aAAA;QACAC,EAAA,OAAAX,IAAA,CAAAW,EAAA;QACAC,MAAA,OAAAZ,IAAA,CAAAY,MAAA;QACAJ,MAAA,EAAAA;MACA,GAAAK,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAAM,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACAR,KAAA,CAAAN,OAAA;QACAM,KAAA,CAAAL,IAAA;QACAK,KAAA,CAAAS,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}