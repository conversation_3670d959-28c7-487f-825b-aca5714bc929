{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\generator\\html.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\generator\\html.js", "mtime": 1750151094214}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_config", "require", "confGlobal", "someSpanIsNot24", "dialogWrapper", "str", "concat", "vueTemplate", "vueScript", "cssStyle", "cssStr", "buildFormTemplate", "conf", "child", "type", "labelPosition", "disabled", "formRef", "formModel", "formRules", "size", "labelWidth", "buildFromBtns", "gutter", "formBtns", "colWrapper", "element", "span", "layouts", "colFormItem", "required", "trigger", "tag", "tagDom", "tags", "label", "vModel", "rowFormItem", "justify", "align", "children", "map", "el", "layout", "join", "elButton", "_attrBuilder", "attrBuilder", "icon", "buildElButtonChild", "elInput", "_attrBuilder2", "clearable", "placeholder", "width", "maxlength", "showWordLimit", "readonly", "prefixIcon", "suffixIcon", "showPassword", "autosize", "minRows", "maxRows", "buildElInputChild", "elInputNumber", "_attrBuilder3", "controlsPosition", "min", "max", "step", "stepStrictly", "precision", "elSelect", "_attrBuilder4", "filterable", "multiple", "buildElSelectChild", "elRadioGroup", "_attrBuilder5", "buildElRadioGroupChild", "elCheckboxGroup", "_attrBuilder6", "buildElCheckboxGroupChild", "elSwitch", "_attrBuilder7", "activeText", "inactiveText", "activeColor", "inactiveColor", "activeValue", "JSON", "stringify", "inactiveValue", "elCascader", "_attrBuilder8", "options", "props", "showAllLevels", "separator", "<PERSON><PERSON><PERSON><PERSON>", "_attrBuilder9", "range", "showStops", "elTimePicker", "_attrBuilder0", "startPlaceholder", "endPlaceholder", "rangeSeparator", "isRange", "format", "valueFormat", "pickerOptions", "elDatePicker", "_attrBuilder1", "elRate", "_attrBuilder10", "allowHalf", "showText", "showScore", "elColorPicker", "_attrBuilder11", "showAlpha", "colorFormat", "elUpload", "action", "listType", "accept", "name", "autoUpload", "beforeUpload", "fileList", "ref", "buildElUploadChild", "style", "default", "push", "prepend", "append", "length", "optionType", "border", "list", "buttonText", "showTip", "fileSize", "sizeUnit", "makeUpHtml", "htmlList", "fields", "some", "item", "for<PERSON>ach", "htmlStr", "temp"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/utils/generator/html.js"], "sourcesContent": ["/* eslint-disable max-len */\r\nimport { trigger } from './config'\r\n\r\nlet confGlobal\r\nlet someSpanIsNot24\r\n\r\nexport function dialogWrapper(str) {\r\n  return `<el-dialog v-bind=\"$attrs\" v-on=\"$listeners\" @open=\"onOpen\" @close=\"onClose\" title=\"Dialog Title\">\r\n    ${str}\r\n    <div slot=\"footer\">\r\n      <el-button @click=\"close\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\r\n    </div>\r\n  </el-dialog>`\r\n}\r\n\r\nexport function vueTemplate(str) {\r\n  return `<template>\r\n    <div>\r\n      ${str}\r\n    </div>\r\n  </template>`\r\n}\r\n\r\nexport function vueScript(str) {\r\n  return `<script>\r\n    ${str}\r\n  </script>`\r\n}\r\n\r\nexport function cssStyle(cssStr) {\r\n  return `<style>\r\n    ${cssStr}\r\n  </style>`\r\n}\r\n\r\nfunction buildFormTemplate(conf, child, type) {\r\n  let labelPosition = ''\r\n  if (conf.labelPosition !== 'right') {\r\n    labelPosition = `label-position=\"${conf.labelPosition}\"`\r\n  }\r\n  const disabled = conf.disabled ? `:disabled=\"${conf.disabled}\"` : ''\r\n  let str = `<el-form ref=\"${conf.formRef}\" :model=\"${conf.formModel}\" :rules=\"${conf.formRules}\" size=\"${conf.size}\" ${disabled} label-width=\"${conf.labelWidth}px\" ${labelPosition}>\r\n      ${child}\r\n      ${buildFromBtns(conf, type)}\r\n    </el-form>`\r\n  if (someSpanIsNot24) {\r\n    str = `<el-row :gutter=\"${conf.gutter}\">\r\n        ${str}\r\n      </el-row>`\r\n  }\r\n  return str\r\n}\r\n\r\nfunction buildFromBtns(conf, type) {\r\n  let str = ''\r\n  if (conf.formBtns && type === 'file') {\r\n    str = `<el-form-item size=\"large\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>`\r\n    if (someSpanIsNot24) {\r\n      str = `<el-col :span=\"24\">\r\n          ${str}\r\n        </el-col>`\r\n    }\r\n  }\r\n  return str\r\n}\r\n\r\n// span不为24的用el-col包裹\r\nfunction colWrapper(element, str) {\r\n  if (someSpanIsNot24 || element.span !== 24) {\r\n    return `<el-col :span=\"${element.span}\">\r\n      ${str}\r\n    </el-col>`\r\n  }\r\n  return str\r\n}\r\n\r\nconst layouts = {\r\n  colFormItem(element) {\r\n    let labelWidth = ''\r\n    if (element.labelWidth && element.labelWidth !== confGlobal.labelWidth) {\r\n      labelWidth = `label-width=\"${element.labelWidth}px\"`\r\n    }\r\n    const required = !trigger[element.tag] && element.required ? 'required' : ''\r\n    const tagDom = tags[element.tag] ? tags[element.tag](element) : null\r\n    let str = `<el-form-item ${labelWidth} label=\"${element.label}\" prop=\"${element.vModel}\" ${required}>\r\n        ${tagDom}\r\n      </el-form-item>`\r\n    str = colWrapper(element, str)\r\n    return str\r\n  },\r\n  rowFormItem(element) {\r\n    const type = element.type === 'default' ? '' : `type=\"${element.type}\"`\r\n    const justify = element.type === 'default' ? '' : `justify=\"${element.justify}\"`\r\n    const align = element.type === 'default' ? '' : `align=\"${element.align}\"`\r\n    const gutter = element.gutter ? `gutter=\"${element.gutter}\"` : ''\r\n    const children = element.children.map(el => layouts[el.layout](el))\r\n    let str = `<el-row ${type} ${justify} ${align} ${gutter}>\r\n      ${children.join('\\n')}\r\n    </el-row>`\r\n    str = colWrapper(element, str)\r\n    return str\r\n  }\r\n}\r\n\r\nconst tags = {\r\n  'el-button': el => {\r\n    const {\r\n      tag, disabled\r\n    } = attrBuilder(el)\r\n    const type = el.type ? `type=\"${el.type}\"` : ''\r\n    const icon = el.icon ? `icon=\"${el.icon}\"` : ''\r\n    const size = el.size ? `size=\"${el.size}\"` : ''\r\n    let child = buildElButtonChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${type} ${icon} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-input': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const maxlength = el.maxlength ? `:maxlength=\"${el.maxlength}\"` : ''\r\n    const showWordLimit = el['show-word-limit'] ? 'show-word-limit' : ''\r\n    const readonly = el.readonly ? 'readonly' : ''\r\n    const prefixIcon = el['prefix-icon'] ? `prefix-icon='${el['prefix-icon']}'` : ''\r\n    const suffixIcon = el['suffix-icon'] ? `suffix-icon='${el['suffix-icon']}'` : ''\r\n    const showPassword = el['show-password'] ? 'show-password' : ''\r\n    const type = el.type ? `type=\"${el.type}\"` : ''\r\n    const autosize = el.autosize && el.autosize.minRows\r\n      ? `:autosize=\"{minRows: ${el.autosize.minRows}, maxRows: ${el.autosize.maxRows}}\"`\r\n      : ''\r\n    let child = buildElInputChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${type} ${placeholder} ${maxlength} ${showWordLimit} ${readonly} ${disabled} ${clearable} ${prefixIcon} ${suffixIcon} ${showPassword} ${autosize} ${width}>${child}</${el.tag}>`\r\n  },\r\n  'el-input-number': el => {\r\n    const { disabled, vModel, placeholder } = attrBuilder(el)\r\n    const controlsPosition = el['controls-position'] ? `controls-position=${el['controls-position']}` : ''\r\n    const min = el.min ? `:min='${el.min}'` : ''\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const step = el.step ? `:step='${el.step}'` : ''\r\n    const stepStrictly = el['step-strictly'] ? 'step-strictly' : ''\r\n    const precision = el.precision ? `:precision='${el.precision}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${placeholder} ${step} ${stepStrictly} ${precision} ${controlsPosition} ${min} ${max} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-select': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const filterable = el.filterable ? 'filterable' : ''\r\n    const multiple = el.multiple ? 'multiple' : ''\r\n    let child = buildElSelectChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${placeholder} ${disabled} ${multiple} ${filterable} ${clearable} ${width}>${child}</${el.tag}>`\r\n  },\r\n  'el-radio-group': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    let child = buildElRadioGroupChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-checkbox-group': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    const min = el.min ? `:min=\"${el.min}\"` : ''\r\n    const max = el.max ? `:max=\"${el.max}\"` : ''\r\n    let child = buildElCheckboxGroupChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${min} ${max} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-switch': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const activeText = el['active-text'] ? `active-text=\"${el['active-text']}\"` : ''\r\n    const inactiveText = el['inactive-text'] ? `inactive-text=\"${el['inactive-text']}\"` : ''\r\n    const activeColor = el['active-color'] ? `active-color=\"${el['active-color']}\"` : ''\r\n    const inactiveColor = el['inactive-color'] ? `inactive-color=\"${el['inactive-color']}\"` : ''\r\n    const activeValue = el['active-value'] !== true ? `:active-value='${JSON.stringify(el['active-value'])}'` : ''\r\n    const inactiveValue = el['inactive-value'] !== false ? `:inactive-value='${JSON.stringify(el['inactive-value'])}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${activeText} ${inactiveText} ${activeColor} ${inactiveColor} ${activeValue} ${inactiveValue} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-cascader': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const options = el.options ? `:options=\"${el.vModel}Options\"` : ''\r\n    const props = el.props ? `:props=\"${el.vModel}Props\"` : ''\r\n    const showAllLevels = el['show-all-levels'] ? '' : ':show-all-levels=\"false\"'\r\n    const filterable = el.filterable ? 'filterable' : ''\r\n    const separator = el.separator === '/' ? '' : `separator=\"${el.separator}\"`\r\n\r\n    return `<${el.tag} ${vModel} ${options} ${props} ${width} ${showAllLevels} ${placeholder} ${separator} ${filterable} ${clearable} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-slider': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const min = el.min ? `:min='${el.min}'` : ''\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const step = el.step ? `:step='${el.step}'` : ''\r\n    const range = el.range ? 'range' : ''\r\n    const showStops = el['show-stops'] ? `:show-stops=\"${el['show-stops']}\"` : ''\r\n\r\n    return `<${el.tag} ${min} ${max} ${step} ${vModel} ${range} ${showStops} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-time-picker': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const startPlaceholder = el['start-placeholder'] ? `start-placeholder=\"${el['start-placeholder']}\"` : ''\r\n    const endPlaceholder = el['end-placeholder'] ? `end-placeholder=\"${el['end-placeholder']}\"` : ''\r\n    const rangeSeparator = el['range-separator'] ? `range-separator=\"${el['range-separator']}\"` : ''\r\n    const isRange = el['is-range'] ? 'is-range' : ''\r\n    const format = el.format ? `format=\"${el.format}\"` : ''\r\n    const valueFormat = el['value-format'] ? `value-format=\"${el['value-format']}\"` : ''\r\n    const pickerOptions = el['picker-options'] ? `:picker-options='${JSON.stringify(el['picker-options'])}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${isRange} ${format} ${valueFormat} ${pickerOptions} ${width} ${placeholder} ${startPlaceholder} ${endPlaceholder} ${rangeSeparator} ${clearable} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-date-picker': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const startPlaceholder = el['start-placeholder'] ? `start-placeholder=\"${el['start-placeholder']}\"` : ''\r\n    const endPlaceholder = el['end-placeholder'] ? `end-placeholder=\"${el['end-placeholder']}\"` : ''\r\n    const rangeSeparator = el['range-separator'] ? `range-separator=\"${el['range-separator']}\"` : ''\r\n    const format = el.format ? `format=\"${el.format}\"` : ''\r\n    const valueFormat = el['value-format'] ? `value-format=\"${el['value-format']}\"` : ''\r\n    const type = el.type === 'date' ? '' : `type=\"${el.type}\"`\r\n    const readonly = el.readonly ? 'readonly' : ''\r\n\r\n    return `<${el.tag} ${type} ${vModel} ${format} ${valueFormat} ${width} ${placeholder} ${startPlaceholder} ${endPlaceholder} ${rangeSeparator} ${clearable} ${readonly} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-rate': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const allowHalf = el['allow-half'] ? 'allow-half' : ''\r\n    const showText = el['show-text'] ? 'show-text' : ''\r\n    const showScore = el['show-score'] ? 'show-score' : ''\r\n\r\n    return `<${el.tag} ${vModel} ${allowHalf} ${showText} ${showScore} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-color-picker': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    const showAlpha = el['show-alpha'] ? 'show-alpha' : ''\r\n    const colorFormat = el['color-format'] ? `color-format=\"${el['color-format']}\"` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${size} ${showAlpha} ${colorFormat} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-upload': el => {\r\n    const disabled = el.disabled ? ':disabled=\\'true\\'' : ''\r\n    const action = el.action ? `:action=\"${el.vModel}Action\"` : ''\r\n    const multiple = el.multiple ? 'multiple' : ''\r\n    const listType = el['list-type'] !== 'text' ? `list-type=\"${el['list-type']}\"` : ''\r\n    const accept = el.accept ? `accept=\"${el.accept}\"` : ''\r\n    const name = el.name !== 'file' ? `name=\"${el.name}\"` : ''\r\n    const autoUpload = el['auto-upload'] === false ? ':auto-upload=\"false\"' : ''\r\n    const beforeUpload = `:before-upload=\"${el.vModel}BeforeUpload\"`\r\n    const fileList = `:file-list=\"${el.vModel}fileList\"`\r\n    const ref = `ref=\"${el.vModel}\"`\r\n    let child = buildElUploadChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${ref} ${fileList} ${action} ${autoUpload} ${multiple} ${beforeUpload} ${listType} ${accept} ${name} ${disabled}>${child}</${el.tag}>`\r\n  }\r\n}\r\n\r\nfunction attrBuilder(el) {\r\n  return {\r\n    vModel: `v-model=\"${confGlobal.formModel}.${el.vModel}\"`,\r\n    clearable: el.clearable ? 'clearable' : '',\r\n    placeholder: el.placeholder ? `placeholder=\"${el.placeholder}\"` : '',\r\n    width: el.style && el.style.width ? ':style=\"{width: \\'100%\\'}\"' : '',\r\n    disabled: el.disabled ? ':disabled=\\'true\\'' : ''\r\n  }\r\n}\r\n\r\n// el-buttin 子级\r\nfunction buildElButtonChild(conf) {\r\n  const children = []\r\n  if (conf.default) {\r\n    children.push(conf.default)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\n// el-input innerHTML\r\nfunction buildElInputChild(conf) {\r\n  const children = []\r\n  if (conf.prepend) {\r\n    children.push(`<template slot=\"prepend\">${conf.prepend}</template>`)\r\n  }\r\n  if (conf.append) {\r\n    children.push(`<template slot=\"append\">${conf.append}</template>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElSelectChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    children.push(`<el-option v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\"></el-option>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElRadioGroupChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    const tag = conf.optionType === 'button' ? 'el-radio-button' : 'el-radio'\r\n    const border = conf.border ? 'border' : ''\r\n    children.push(`<${tag} v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ${border}>{{item.label}}</${tag}>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElCheckboxGroupChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    const tag = conf.optionType === 'button' ? 'el-checkbox-button' : 'el-checkbox'\r\n    const border = conf.border ? 'border' : ''\r\n    children.push(`<${tag} v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ${border}>{{item.label}}</${tag}>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElUploadChild(conf) {\r\n  const list = []\r\n  if (conf['list-type'] === 'picture-card') list.push('<i class=\"el-icon-plus\"></i>')\r\n  else list.push(`<el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">${conf.buttonText}</el-button>`)\r\n  if (conf.showTip) list.push(`<div slot=\"tip\" class=\"el-upload__tip\">只能上传不超过 ${conf.fileSize}${conf.sizeUnit} 的${conf.accept}文件</div>`)\r\n  return list.join('\\n')\r\n}\r\n\r\nexport function makeUpHtml(conf, type) {\r\n  const htmlList = []\r\n  confGlobal = conf\r\n  someSpanIsNot24 = conf.fields.some(item => item.span !== 24)\r\n  conf.fields.forEach(el => {\r\n    htmlList.push(layouts[el.layout](el))\r\n  })\r\n  const htmlStr = htmlList.join('\\n')\r\n\r\n  let temp = buildFormTemplate(conf, htmlStr, type)\r\n  if (type === 'dialog') {\r\n    temp = dialogWrapper(temp)\r\n  }\r\n  confGlobal = null\r\n  return temp\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AADA;;AAGA,IAAIC,UAAU;AACd,IAAIC,eAAe;AAEZ,SAASC,aAAaA,CAACC,GAAG,EAAE;EACjC,4HAAAC,MAAA,CACID,GAAG;AAMT;AAEO,SAASE,WAAWA,CAACF,GAAG,EAAE;EAC/B,uCAAAC,MAAA,CAEMD,GAAG;AAGX;AAEO,SAASG,SAASA,CAACH,GAAG,EAAE;EAC7B,wBAAAC,MAAA,CACID,GAAG;AAET;AAEO,SAASI,QAAQA,CAACC,MAAM,EAAE;EAC/B,uBAAAJ,MAAA,CACII,MAAM;AAEZ;AAEA,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC5C,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIH,IAAI,CAACG,aAAa,KAAK,OAAO,EAAE;IAClCA,aAAa,uBAAAT,MAAA,CAAsBM,IAAI,CAACG,aAAa,OAAG;EAC1D;EACA,IAAMC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ,kBAAAV,MAAA,CAAiBM,IAAI,CAACI,QAAQ,UAAM,EAAE;EACpE,IAAIX,GAAG,qBAAAC,MAAA,CAAoBM,IAAI,CAACK,OAAO,kBAAAX,MAAA,CAAaM,IAAI,CAACM,SAAS,kBAAAZ,MAAA,CAAaM,IAAI,CAACO,SAAS,gBAAAb,MAAA,CAAWM,IAAI,CAACQ,IAAI,SAAAd,MAAA,CAAKU,QAAQ,qBAAAV,MAAA,CAAiBM,IAAI,CAACS,UAAU,WAAAf,MAAA,CAAOS,aAAa,eAAAT,MAAA,CAC5KO,KAAK,cAAAP,MAAA,CACLgB,aAAa,CAACV,IAAI,EAAEE,IAAI,CAAC,qBAClB;EACb,IAAIX,eAAe,EAAE;IACnBE,GAAG,wBAAAC,MAAA,CAAuBM,IAAI,CAACW,MAAM,mBAAAjB,MAAA,CAC/BD,GAAG,sBACG;EACd;EACA,OAAOA,GAAG;AACZ;AAEA,SAASiB,aAAaA,CAACV,IAAI,EAAEE,IAAI,EAAE;EACjC,IAAIT,GAAG,GAAG,EAAE;EACZ,IAAIO,IAAI,CAACY,QAAQ,IAAIV,IAAI,KAAK,MAAM,EAAE;IACpCT,GAAG,qNAGiB;IACpB,IAAIF,eAAe,EAAE;MACnBE,GAAG,uCAAAC,MAAA,CACGD,GAAG,wBACG;IACd;EACF;EACA,OAAOA,GAAG;AACZ;;AAEA;AACA,SAASoB,UAAUA,CAACC,OAAO,EAAErB,GAAG,EAAE;EAChC,IAAIF,eAAe,IAAIuB,OAAO,CAACC,IAAI,KAAK,EAAE,EAAE;IAC1C,0BAAArB,MAAA,CAAyBoB,OAAO,CAACC,IAAI,iBAAArB,MAAA,CACjCD,GAAG;EAET;EACA,OAAOA,GAAG;AACZ;AAEA,IAAMuB,OAAO,GAAG;EACdC,WAAW,WAAXA,WAAWA,CAACH,OAAO,EAAE;IACnB,IAAIL,UAAU,GAAG,EAAE;IACnB,IAAIK,OAAO,CAACL,UAAU,IAAIK,OAAO,CAACL,UAAU,KAAKnB,UAAU,CAACmB,UAAU,EAAE;MACtEA,UAAU,oBAAAf,MAAA,CAAmBoB,OAAO,CAACL,UAAU,SAAK;IACtD;IACA,IAAMS,QAAQ,GAAG,CAACC,eAAO,CAACL,OAAO,CAACM,GAAG,CAAC,IAAIN,OAAO,CAACI,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC5E,IAAMG,MAAM,GAAGC,IAAI,CAACR,OAAO,CAACM,GAAG,CAAC,GAAGE,IAAI,CAACR,OAAO,CAACM,GAAG,CAAC,CAACN,OAAO,CAAC,GAAG,IAAI;IACpE,IAAIrB,GAAG,oBAAAC,MAAA,CAAoBe,UAAU,eAAAf,MAAA,CAAWoB,OAAO,CAACS,KAAK,gBAAA7B,MAAA,CAAWoB,OAAO,CAACU,MAAM,SAAA9B,MAAA,CAAKwB,QAAQ,iBAAAxB,MAAA,CAC7F2B,MAAM,4BACM;IAClB5B,GAAG,GAAGoB,UAAU,CAACC,OAAO,EAAErB,GAAG,CAAC;IAC9B,OAAOA,GAAG;EACZ,CAAC;EACDgC,WAAW,WAAXA,WAAWA,CAACX,OAAO,EAAE;IACnB,IAAMZ,IAAI,GAAGY,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,aAAAR,MAAA,CAAYoB,OAAO,CAACZ,IAAI,OAAG;IACvE,IAAMwB,OAAO,GAAGZ,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,gBAAAR,MAAA,CAAeoB,OAAO,CAACY,OAAO,OAAG;IAChF,IAAMC,KAAK,GAAGb,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,cAAAR,MAAA,CAAaoB,OAAO,CAACa,KAAK,OAAG;IAC1E,IAAMhB,MAAM,GAAGG,OAAO,CAACH,MAAM,eAAAjB,MAAA,CAAcoB,OAAO,CAACH,MAAM,UAAM,EAAE;IACjE,IAAMiB,QAAQ,GAAGd,OAAO,CAACc,QAAQ,CAACC,GAAG,CAAC,UAAAC,EAAE;MAAA,OAAId,OAAO,CAACc,EAAE,CAACC,MAAM,CAAC,CAACD,EAAE,CAAC;IAAA,EAAC;IACnE,IAAIrC,GAAG,cAAAC,MAAA,CAAcQ,IAAI,OAAAR,MAAA,CAAIgC,OAAO,OAAAhC,MAAA,CAAIiC,KAAK,OAAAjC,MAAA,CAAIiB,MAAM,eAAAjB,MAAA,CACnDkC,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC,oBACb;IACVvC,GAAG,GAAGoB,UAAU,CAACC,OAAO,EAAErB,GAAG,CAAC;IAC9B,OAAOA,GAAG;EACZ;AACF,CAAC;AAED,IAAM6B,IAAI,GAAG;EACX,WAAW,EAAE,SAAbW,QAAWA,CAAEH,EAAE,EAAI;IACjB,IAAAI,YAAA,GAEIC,WAAW,CAACL,EAAE,CAAC;MADjBV,GAAG,GAAAc,YAAA,CAAHd,GAAG;MAAEhB,QAAQ,GAAA8B,YAAA,CAAR9B,QAAQ;IAEf,IAAMF,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,UAAM,EAAE;IAC/C,IAAMkC,IAAI,GAAGN,EAAE,CAACM,IAAI,aAAA1C,MAAA,CAAYoC,EAAE,CAACM,IAAI,UAAM,EAAE;IAC/C,IAAM5B,IAAI,GAAGsB,EAAE,CAACtB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,UAAM,EAAE;IAC/C,IAAIP,KAAK,GAAGoC,kBAAkB,CAACP,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI0C,IAAI,OAAA1C,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC3E,CAAC;EACD,UAAU,EAAE,SAAZkB,OAAUA,CAAER,EAAE,EAAI;IAChB,IAAAS,aAAA,GAEIJ,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAmC,aAAA,CAARnC,QAAQ;MAAEoB,MAAM,GAAAe,aAAA,CAANf,MAAM;MAAEgB,SAAS,GAAAD,aAAA,CAATC,SAAS;MAAEC,WAAW,GAAAF,aAAA,CAAXE,WAAW;MAAEC,KAAK,GAAAH,aAAA,CAALG,KAAK;IAEjD,IAAMC,SAAS,GAAGb,EAAE,CAACa,SAAS,mBAAAjD,MAAA,CAAkBoC,EAAE,CAACa,SAAS,UAAM,EAAE;IACpE,IAAMC,aAAa,GAAGd,EAAE,CAAC,iBAAiB,CAAC,GAAG,iBAAiB,GAAG,EAAE;IACpE,IAAMe,QAAQ,GAAGf,EAAE,CAACe,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAMC,UAAU,GAAGhB,EAAE,CAAC,aAAa,CAAC,mBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,SAAM,EAAE;IAChF,IAAMiB,UAAU,GAAGjB,EAAE,CAAC,aAAa,CAAC,mBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,SAAM,EAAE;IAChF,IAAMkB,YAAY,GAAGlB,EAAE,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,EAAE;IAC/D,IAAM5B,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,UAAM,EAAE;IAC/C,IAAM+C,QAAQ,GAAGnB,EAAE,CAACmB,QAAQ,IAAInB,EAAE,CAACmB,QAAQ,CAACC,OAAO,4BAAAxD,MAAA,CACvBoC,EAAE,CAACmB,QAAQ,CAACC,OAAO,iBAAAxD,MAAA,CAAcoC,EAAE,CAACmB,QAAQ,CAACE,OAAO,WAC5E,EAAE;IACN,IAAIlD,KAAK,GAAGmD,iBAAiB,CAACtB,EAAE,CAAC;IAEjC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIiD,SAAS,OAAAjD,MAAA,CAAIkD,aAAa,OAAAlD,MAAA,CAAImD,QAAQ,OAAAnD,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIoD,UAAU,OAAApD,MAAA,CAAIqD,UAAU,OAAArD,MAAA,CAAIsD,YAAY,OAAAtD,MAAA,CAAIuD,QAAQ,OAAAvD,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC5M,CAAC;EACD,iBAAiB,EAAE,SAAnBiC,aAAiBA,CAAEvB,EAAE,EAAI;IACvB,IAAAwB,aAAA,GAA0CnB,WAAW,CAACL,EAAE,CAAC;MAAjD1B,QAAQ,GAAAkD,aAAA,CAARlD,QAAQ;MAAEoB,MAAM,GAAA8B,aAAA,CAAN9B,MAAM;MAAEiB,WAAW,GAAAa,aAAA,CAAXb,WAAW;IACrC,IAAMc,gBAAgB,GAAGzB,EAAE,CAAC,mBAAmB,CAAC,wBAAApC,MAAA,CAAwBoC,EAAE,CAAC,mBAAmB,CAAC,IAAK,EAAE;IACtG,IAAM0B,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,YAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,SAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMC,IAAI,GAAG5B,EAAE,CAAC4B,IAAI,aAAAhE,MAAA,CAAaoC,EAAE,CAAC4B,IAAI,SAAM,EAAE;IAChD,IAAMC,YAAY,GAAG7B,EAAE,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,EAAE;IAC/D,IAAM8B,SAAS,GAAG9B,EAAE,CAAC8B,SAAS,kBAAAlE,MAAA,CAAkBoC,EAAE,CAAC8B,SAAS,SAAM,EAAE;IAEpE,WAAAlE,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIgE,IAAI,OAAAhE,MAAA,CAAIiE,YAAY,OAAAjE,MAAA,CAAIkE,SAAS,OAAAlE,MAAA,CAAI6D,gBAAgB,OAAA7D,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC3I,CAAC;EACD,WAAW,EAAE,SAAbyC,QAAWA,CAAE/B,EAAE,EAAI;IACjB,IAAAgC,aAAA,GAEI3B,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAA0D,aAAA,CAAR1D,QAAQ;MAAEoB,MAAM,GAAAsC,aAAA,CAANtC,MAAM;MAAEgB,SAAS,GAAAsB,aAAA,CAATtB,SAAS;MAAEC,WAAW,GAAAqB,aAAA,CAAXrB,WAAW;MAAEC,KAAK,GAAAoB,aAAA,CAALpB,KAAK;IAEjD,IAAMqB,UAAU,GAAGjC,EAAE,CAACiC,UAAU,GAAG,YAAY,GAAG,EAAE;IACpD,IAAMC,QAAQ,GAAGlC,EAAE,CAACkC,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAI/D,KAAK,GAAGgE,kBAAkB,CAACnC,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIsE,QAAQ,OAAAtE,MAAA,CAAIqE,UAAU,OAAArE,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC5H,CAAC;EACD,gBAAgB,EAAE,SAAlB8C,YAAgBA,CAAEpC,EAAE,EAAI;IACtB,IAAAqC,aAAA,GAA6BhC,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAA+D,aAAA,CAAR/D,QAAQ;MAAEoB,MAAM,GAAA2C,aAAA,CAAN3C,MAAM;IACxB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAIP,KAAK,GAAGmE,sBAAsB,CAACtC,EAAE,CAAC;IAEtC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACrE,CAAC;EACD,mBAAmB,EAAE,SAArBiD,eAAmBA,CAAEvC,EAAE,EAAI;IACzB,IAAAwC,aAAA,GAA6BnC,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAkE,aAAA,CAARlE,QAAQ;MAAEoB,MAAM,GAAA8C,aAAA,CAAN9C,MAAM;IACxB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAMgD,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,aAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,UAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,aAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,UAAM,EAAE;IAC5C,IAAIxD,KAAK,GAAGsE,yBAAyB,CAACzC,EAAE,CAAC;IAEzC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACnF,CAAC;EACD,WAAW,EAAE,SAAboD,QAAWA,CAAE1C,EAAE,EAAI;IACjB,IAAA2C,aAAA,GAA6BtC,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAqE,aAAA,CAARrE,QAAQ;MAAEoB,MAAM,GAAAiD,aAAA,CAANjD,MAAM;IACxB,IAAMkD,UAAU,GAAG5C,EAAE,CAAC,aAAa,CAAC,oBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,UAAM,EAAE;IAChF,IAAM6C,YAAY,GAAG7C,EAAE,CAAC,eAAe,CAAC,sBAAApC,MAAA,CAAqBoC,EAAE,CAAC,eAAe,CAAC,UAAM,EAAE;IACxF,IAAM8C,WAAW,GAAG9C,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAM+C,aAAa,GAAG/C,EAAE,CAAC,gBAAgB,CAAC,uBAAApC,MAAA,CAAsBoC,EAAE,CAAC,gBAAgB,CAAC,UAAM,EAAE;IAC5F,IAAMgD,WAAW,GAAGhD,EAAE,CAAC,cAAc,CAAC,KAAK,IAAI,qBAAApC,MAAA,CAAqBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,cAAc,CAAC,CAAC,SAAM,EAAE;IAC9G,IAAMmD,aAAa,GAAGnD,EAAE,CAAC,gBAAgB,CAAC,KAAK,KAAK,uBAAApC,MAAA,CAAuBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,gBAAgB,CAAC,CAAC,SAAM,EAAE;IAEvH,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIgF,UAAU,OAAAhF,MAAA,CAAIiF,YAAY,OAAAjF,MAAA,CAAIkF,WAAW,OAAAlF,MAAA,CAAImF,aAAa,OAAAnF,MAAA,CAAIoF,WAAW,OAAApF,MAAA,CAAIuF,aAAa,OAAAvF,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACnJ,CAAC;EACD,aAAa,EAAE,SAAf8D,UAAaA,CAAEpD,EAAE,EAAI;IACnB,IAAAqD,aAAA,GAEIhD,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAA+E,aAAA,CAAR/E,QAAQ;MAAEoB,MAAM,GAAA2D,aAAA,CAAN3D,MAAM;MAAEgB,SAAS,GAAA2C,aAAA,CAAT3C,SAAS;MAAEC,WAAW,GAAA0C,aAAA,CAAX1C,WAAW;MAAEC,KAAK,GAAAyC,aAAA,CAALzC,KAAK;IAEjD,IAAM0C,OAAO,GAAGtD,EAAE,CAACsD,OAAO,iBAAA1F,MAAA,CAAgBoC,EAAE,CAACN,MAAM,iBAAa,EAAE;IAClE,IAAM6D,KAAK,GAAGvD,EAAE,CAACuD,KAAK,eAAA3F,MAAA,CAAcoC,EAAE,CAACN,MAAM,eAAW,EAAE;IAC1D,IAAM8D,aAAa,GAAGxD,EAAE,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,0BAA0B;IAC7E,IAAMiC,UAAU,GAAGjC,EAAE,CAACiC,UAAU,GAAG,YAAY,GAAG,EAAE;IACpD,IAAMwB,SAAS,GAAGzD,EAAE,CAACyD,SAAS,KAAK,GAAG,GAAG,EAAE,kBAAA7F,MAAA,CAAiBoC,EAAE,CAACyD,SAAS,OAAG;IAE3E,WAAA7F,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI0F,OAAO,OAAA1F,MAAA,CAAI2F,KAAK,OAAA3F,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI4F,aAAa,OAAA5F,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAI6F,SAAS,OAAA7F,MAAA,CAAIqE,UAAU,OAAArE,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC1J,CAAC;EACD,WAAW,EAAE,SAAboE,QAAWA,CAAE1D,EAAE,EAAI;IACjB,IAAA2D,aAAA,GAA6BtD,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAqF,aAAA,CAARrF,QAAQ;MAAEoB,MAAM,GAAAiE,aAAA,CAANjE,MAAM;IACxB,IAAMgC,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,YAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,SAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMC,IAAI,GAAG5B,EAAE,CAAC4B,IAAI,aAAAhE,MAAA,CAAaoC,EAAE,CAAC4B,IAAI,SAAM,EAAE;IAChD,IAAMgC,KAAK,GAAG5D,EAAE,CAAC4D,KAAK,GAAG,OAAO,GAAG,EAAE;IACrC,IAAMC,SAAS,GAAG7D,EAAE,CAAC,YAAY,CAAC,oBAAApC,MAAA,CAAmBoC,EAAE,CAAC,YAAY,CAAC,UAAM,EAAE;IAE7E,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIgE,IAAI,OAAAhE,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIgG,KAAK,OAAAhG,MAAA,CAAIiG,SAAS,OAAAjG,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACjG,CAAC;EACD,gBAAgB,EAAE,SAAlBwE,YAAgBA,CAAE9D,EAAE,EAAI;IACtB,IAAA+D,aAAA,GAEI1D,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAyF,aAAA,CAARzF,QAAQ;MAAEoB,MAAM,GAAAqE,aAAA,CAANrE,MAAM;MAAEgB,SAAS,GAAAqD,aAAA,CAATrD,SAAS;MAAEC,WAAW,GAAAoD,aAAA,CAAXpD,WAAW;MAAEC,KAAK,GAAAmD,aAAA,CAALnD,KAAK;IAEjD,IAAMoD,gBAAgB,GAAGhE,EAAE,CAAC,mBAAmB,CAAC,0BAAApC,MAAA,CAAyBoC,EAAE,CAAC,mBAAmB,CAAC,UAAM,EAAE;IACxG,IAAMiE,cAAc,GAAGjE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMkE,cAAc,GAAGlE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMmE,OAAO,GAAGnE,EAAE,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,EAAE;IAChD,IAAMoE,MAAM,GAAGpE,EAAE,CAACoE,MAAM,eAAAxG,MAAA,CAAcoC,EAAE,CAACoE,MAAM,UAAM,EAAE;IACvD,IAAMC,WAAW,GAAGrE,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAMsE,aAAa,GAAGtE,EAAE,CAAC,gBAAgB,CAAC,uBAAApC,MAAA,CAAuBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,gBAAgB,CAAC,CAAC,SAAM,EAAE;IAE7G,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIuG,OAAO,OAAAvG,MAAA,CAAIwG,MAAM,OAAAxG,MAAA,CAAIyG,WAAW,OAAAzG,MAAA,CAAI0G,aAAa,OAAA1G,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIoG,gBAAgB,OAAApG,MAAA,CAAIqG,cAAc,OAAArG,MAAA,CAAIsG,cAAc,OAAAtG,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACvM,CAAC;EACD,gBAAgB,EAAE,SAAlBiF,YAAgBA,CAAEvE,EAAE,EAAI;IACtB,IAAAwE,aAAA,GAEInE,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAkG,aAAA,CAARlG,QAAQ;MAAEoB,MAAM,GAAA8E,aAAA,CAAN9E,MAAM;MAAEgB,SAAS,GAAA8D,aAAA,CAAT9D,SAAS;MAAEC,WAAW,GAAA6D,aAAA,CAAX7D,WAAW;MAAEC,KAAK,GAAA4D,aAAA,CAAL5D,KAAK;IAEjD,IAAMoD,gBAAgB,GAAGhE,EAAE,CAAC,mBAAmB,CAAC,0BAAApC,MAAA,CAAyBoC,EAAE,CAAC,mBAAmB,CAAC,UAAM,EAAE;IACxG,IAAMiE,cAAc,GAAGjE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMkE,cAAc,GAAGlE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMoE,MAAM,GAAGpE,EAAE,CAACoE,MAAM,eAAAxG,MAAA,CAAcoC,EAAE,CAACoE,MAAM,UAAM,EAAE;IACvD,IAAMC,WAAW,GAAGrE,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAM5B,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,KAAK,MAAM,GAAG,EAAE,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,OAAG;IAC1D,IAAM2C,QAAQ,GAAGf,EAAE,CAACe,QAAQ,GAAG,UAAU,GAAG,EAAE;IAE9C,WAAAnD,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIwG,MAAM,OAAAxG,MAAA,CAAIyG,WAAW,OAAAzG,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIoG,gBAAgB,OAAApG,MAAA,CAAIqG,cAAc,OAAArG,MAAA,CAAIsG,cAAc,OAAAtG,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAImD,QAAQ,OAAAnD,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC/L,CAAC;EACD,SAAS,EAAE,SAAXmF,MAASA,CAAEzE,EAAE,EAAI;IACf,IAAA0E,cAAA,GAA6BrE,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAoG,cAAA,CAARpG,QAAQ;MAAEoB,MAAM,GAAAgF,cAAA,CAANhF,MAAM;IACxB,IAAMiC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMgD,SAAS,GAAG3E,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IACtD,IAAM4E,QAAQ,GAAG5E,EAAE,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,EAAE;IACnD,IAAM6E,SAAS,GAAG7E,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IAEtD,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+G,SAAS,OAAA/G,MAAA,CAAIgH,QAAQ,OAAAhH,MAAA,CAAIiH,SAAS,OAAAjH,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC3F,CAAC;EACD,iBAAiB,EAAE,SAAnBwF,aAAiBA,CAAE9E,EAAE,EAAI;IACvB,IAAA+E,cAAA,GAA6B1E,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAyG,cAAA,CAARzG,QAAQ;MAAEoB,MAAM,GAAAqF,cAAA,CAANrF,MAAM;IACxB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAMsG,SAAS,GAAGhF,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IACtD,IAAMiF,WAAW,GAAGjF,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IAEpF,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIoH,SAAS,OAAApH,MAAA,CAAIqH,WAAW,OAAArH,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACzF,CAAC;EACD,WAAW,EAAE,SAAb4F,QAAWA,CAAElF,EAAE,EAAI;IACjB,IAAM1B,QAAQ,GAAG0B,EAAE,CAAC1B,QAAQ,GAAG,oBAAoB,GAAG,EAAE;IACxD,IAAM6G,MAAM,GAAGnF,EAAE,CAACmF,MAAM,gBAAAvH,MAAA,CAAeoC,EAAE,CAACN,MAAM,gBAAY,EAAE;IAC9D,IAAMwC,QAAQ,GAAGlC,EAAE,CAACkC,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAMkD,QAAQ,GAAGpF,EAAE,CAAC,WAAW,CAAC,KAAK,MAAM,kBAAApC,MAAA,CAAiBoC,EAAE,CAAC,WAAW,CAAC,UAAM,EAAE;IACnF,IAAMqF,MAAM,GAAGrF,EAAE,CAACqF,MAAM,eAAAzH,MAAA,CAAcoC,EAAE,CAACqF,MAAM,UAAM,EAAE;IACvD,IAAMC,IAAI,GAAGtF,EAAE,CAACsF,IAAI,KAAK,MAAM,aAAA1H,MAAA,CAAYoC,EAAE,CAACsF,IAAI,UAAM,EAAE;IAC1D,IAAMC,UAAU,GAAGvF,EAAE,CAAC,aAAa,CAAC,KAAK,KAAK,GAAG,sBAAsB,GAAG,EAAE;IAC5E,IAAMwF,YAAY,uBAAA5H,MAAA,CAAsBoC,EAAE,CAACN,MAAM,mBAAe;IAChE,IAAM+F,QAAQ,mBAAA7H,MAAA,CAAkBoC,EAAE,CAACN,MAAM,eAAW;IACpD,IAAMgG,GAAG,YAAA9H,MAAA,CAAWoC,EAAE,CAACN,MAAM,OAAG;IAChC,IAAIvB,KAAK,GAAGwH,kBAAkB,CAAC3F,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8H,GAAG,OAAA9H,MAAA,CAAI6H,QAAQ,OAAA7H,MAAA,CAAIuH,MAAM,OAAAvH,MAAA,CAAI2H,UAAU,OAAA3H,MAAA,CAAIsE,QAAQ,OAAAtE,MAAA,CAAI4H,YAAY,OAAA5H,MAAA,CAAIwH,QAAQ,OAAAxH,MAAA,CAAIyH,MAAM,OAAAzH,MAAA,CAAI0H,IAAI,OAAA1H,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACxJ;AACF,CAAC;AAED,SAASe,WAAWA,CAACL,EAAE,EAAE;EACvB,OAAO;IACLN,MAAM,eAAA9B,MAAA,CAAcJ,UAAU,CAACgB,SAAS,OAAAZ,MAAA,CAAIoC,EAAE,CAACN,MAAM,OAAG;IACxDgB,SAAS,EAAEV,EAAE,CAACU,SAAS,GAAG,WAAW,GAAG,EAAE;IAC1CC,WAAW,EAAEX,EAAE,CAACW,WAAW,oBAAA/C,MAAA,CAAmBoC,EAAE,CAACW,WAAW,UAAM,EAAE;IACpEC,KAAK,EAAEZ,EAAE,CAAC4F,KAAK,IAAI5F,EAAE,CAAC4F,KAAK,CAAChF,KAAK,GAAG,4BAA4B,GAAG,EAAE;IACrEtC,QAAQ,EAAE0B,EAAE,CAAC1B,QAAQ,GAAG,oBAAoB,GAAG;EACjD,CAAC;AACH;;AAEA;AACA,SAASiC,kBAAkBA,CAACrC,IAAI,EAAE;EAChC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAAC2H,OAAO,EAAE;IAChB/F,QAAQ,CAACgG,IAAI,CAAC5H,IAAI,CAAC2H,OAAO,CAAC;EAC7B;EACA,OAAO/F,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;;AAEA;AACA,SAASoB,iBAAiBA,CAACpD,IAAI,EAAE;EAC/B,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAAC6H,OAAO,EAAE;IAChBjG,QAAQ,CAACgG,IAAI,+BAAAlI,MAAA,CAA6BM,IAAI,CAAC6H,OAAO,gBAAa,CAAC;EACtE;EACA,IAAI7H,IAAI,CAAC8H,MAAM,EAAE;IACflG,QAAQ,CAACgG,IAAI,8BAAAlI,MAAA,CAA4BM,IAAI,CAAC8H,MAAM,gBAAa,CAAC;EACpE;EACA,OAAOlG,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASiC,kBAAkBA,CAACjE,IAAI,EAAE;EAChC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvCnG,QAAQ,CAACgG,IAAI,wCAAAlI,MAAA,CAAuCM,IAAI,CAACwB,MAAM,kHAAsG,CAAC;EACxK;EACA,OAAOI,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASoC,sBAAsBA,CAACpE,IAAI,EAAE;EACpC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvC,IAAM3G,GAAG,GAAGpB,IAAI,CAACgI,UAAU,KAAK,QAAQ,GAAG,iBAAiB,GAAG,UAAU;IACzE,IAAMC,MAAM,GAAGjI,IAAI,CAACiI,MAAM,GAAG,QAAQ,GAAG,EAAE;IAC1CrG,QAAQ,CAACgG,IAAI,KAAAlI,MAAA,CAAK0B,GAAG,gCAAA1B,MAAA,CAA4BM,IAAI,CAACwB,MAAM,iFAAA9B,MAAA,CAAuEuI,MAAM,uBAAAvI,MAAA,CAAoB0B,GAAG,MAAG,CAAC;EACtK;EACA,OAAOQ,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASuC,yBAAyBA,CAACvE,IAAI,EAAE;EACvC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvC,IAAM3G,GAAG,GAAGpB,IAAI,CAACgI,UAAU,KAAK,QAAQ,GAAG,oBAAoB,GAAG,aAAa;IAC/E,IAAMC,MAAM,GAAGjI,IAAI,CAACiI,MAAM,GAAG,QAAQ,GAAG,EAAE;IAC1CrG,QAAQ,CAACgG,IAAI,KAAAlI,MAAA,CAAK0B,GAAG,gCAAA1B,MAAA,CAA4BM,IAAI,CAACwB,MAAM,iFAAA9B,MAAA,CAAuEuI,MAAM,uBAAAvI,MAAA,CAAoB0B,GAAG,MAAG,CAAC;EACtK;EACA,OAAOQ,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASyF,kBAAkBA,CAACzH,IAAI,EAAE;EAChC,IAAMkI,IAAI,GAAG,EAAE;EACf,IAAIlI,IAAI,CAAC,WAAW,CAAC,KAAK,cAAc,EAAEkI,IAAI,CAACN,IAAI,CAAC,8BAA8B,CAAC,MAC9EM,IAAI,CAACN,IAAI,uEAAAlI,MAAA,CAAiEM,IAAI,CAACmI,UAAU,iBAAc,CAAC;EAC7G,IAAInI,IAAI,CAACoI,OAAO,EAAEF,IAAI,CAACN,IAAI,0FAAAlI,MAAA,CAAmDM,IAAI,CAACqI,QAAQ,EAAA3I,MAAA,CAAGM,IAAI,CAACsI,QAAQ,aAAA5I,MAAA,CAAKM,IAAI,CAACmH,MAAM,uBAAU,CAAC;EACtI,OAAOe,IAAI,CAAClG,IAAI,CAAC,IAAI,CAAC;AACxB;AAEO,SAASuG,UAAUA,CAACvI,IAAI,EAAEE,IAAI,EAAE;EACrC,IAAMsI,QAAQ,GAAG,EAAE;EACnBlJ,UAAU,GAAGU,IAAI;EACjBT,eAAe,GAAGS,IAAI,CAACyI,MAAM,CAACC,IAAI,CAAC,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAAC5H,IAAI,KAAK,EAAE;EAAA,EAAC;EAC5Df,IAAI,CAACyI,MAAM,CAACG,OAAO,CAAC,UAAA9G,EAAE,EAAI;IACxB0G,QAAQ,CAACZ,IAAI,CAAC5G,OAAO,CAACc,EAAE,CAACC,MAAM,CAAC,CAACD,EAAE,CAAC,CAAC;EACvC,CAAC,CAAC;EACF,IAAM+G,OAAO,GAAGL,QAAQ,CAACxG,IAAI,CAAC,IAAI,CAAC;EAEnC,IAAI8G,IAAI,GAAG/I,iBAAiB,CAACC,IAAI,EAAE6I,OAAO,EAAE3I,IAAI,CAAC;EACjD,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACrB4I,IAAI,GAAGtJ,aAAa,CAACsJ,IAAI,CAAC;EAC5B;EACAxJ,UAAU,GAAG,IAAI;EACjB,OAAOwJ,IAAI;AACb", "ignoreList": []}]}