{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\apply.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\apply.vue", "mtime": 1750151094285}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_enterpriseDetail", "_interopRequireDefault", "require", "_util", "_apply", "_default", "exports", "default", "components", "enterpriseDetail", "data", "loading", "showSearch", "total", "statusOptions", "form", "queryParams", "pageNum", "pageSize", "name", "undefined", "business_no", "linker", "linkphone", "status", "list", "srcList", "created", "getList", "getEnums", "methods", "_this", "listEnum", "then", "res", "enterpriseApplyStatus", "_this2", "listData", "response", "count", "handleQuery", "handlePreview", "url", "handleInvite", "clipboardObj", "navigator", "clipboard", "$message", "message", "type", "writeText", "reset<PERSON><PERSON>y", "reserForm", "handleDetail", "row", "_this3", "getData", "id", "$refs", "detail", "open"], "sources": ["src/views/supply/apply.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\" prop='name'>\r\n            <el-input clearable v-model=\"queryParams.name\" placeholder=\"输入公司名称\" :maxlength=\"50\" size='small'\r\n              style=\"width: 300px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='business_no'>\r\n            <el-input clearable v-model=\"queryParams.business_no\" placeholder=\"输入营业执照\" :maxlength=\"18\" size='small'\r\n              style=\"width: 180px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='linker'>\r\n            <el-input clearable v-model=\"queryParams.linker\" placeholder=\"输入联系人\" :maxlength=\"20\" size='small'\r\n              style=\"width: 140px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='linkphone'>\r\n            <el-input clearable v-model=\"queryParams.linkphone\" placeholder=\"输入联系电话\" :maxlength=\"11\" size='small'\r\n              style=\"width: 140px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"审核状态\" clearable size=\"small\" style=\"width: 120px\">\r\n              <el-option v-for=\"item in statusOptions\" :key=\"item.key\" :label='item.value' :value=\"item.key\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleInvite\">邀请入驻</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"50\" />\r\n          <el-table-column label=\"企业名称\" align=\"center\" prop=\"name\" width=\"280\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"营业执照号\" align=\"center\" prop=\"business_no\" width=\"180\" />\r\n          <el-table-column label=\"公司授权文件\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.certification\"\r\n                @click=\"handlePreview(scope.row.certification)\" :preview-src-list=\"srcList\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"营业执照\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.business_image\"\r\n                @click=\"handlePreview(scope.row.business_image)\" :preview-src-list=\"srcList\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"品牌\" align=\"center\" prop=\"brand\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"联系人\" align=\"center\" prop=\"linker\" width=\"120\" />\r\n          <el-table-column label=\"联系电话\" align=\"center\" prop=\"linkphone\" width=\"140\" />\r\n          <el-table-column label=\"职务\" align=\"center\" prop=\"post\" width=\"120\" />\r\n          <el-table-column label=\"邮箱\" align=\"center\" prop=\"email\" width=\"120\" />\r\n          <el-table-column label=\"公司地址\" align=\"center\" prop=\"location\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"申请者\" align=\"center\" prop=\"create_by\" width=\"100\" />\r\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n          <el-table-column label=\"操作\" align=\"center\" width=\"80\" fixed='right'>\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" v-if=\"scope.row.status=='NEW'\" icon=\"el-icon-user\" size=\"mini\"\r\n                @click=\"handleDetail(scope.row)\">审核\r\n              </el-button>\r\n              <el-button type=\"text\" v-if=\"scope.row.status!='NEW'\" icon=\"el-icon-view\" size=\"mini\"\r\n                @click=\"handleDetail(scope.row)\">详情\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <enterprise-detail ref='detail' :form=\"form\" @refresh='getList'></enterprise-detail>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import enterpriseDetail from './components/enterprise-detail';\r\n  import {\r\n    listEnum\r\n  } from '@/api/tool/util';\r\n  // 新增弹窗\r\n  import {\r\n    listData,\r\n    getData\r\n  } from \"@/api/enterprise/apply\";\r\n  export default {\r\n    components: {\r\n      enterpriseDetail\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        statusOptions: [],\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          business_no: undefined,\r\n          linker: undefined,\r\n          linkphone: undefined,\r\n          status: undefined,\r\n        },\r\n        // 列表数据\r\n        list: [],\r\n        // 图片预览地址\r\n        srcList: [],\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n      this.getEnums();\r\n    },\r\n    methods: {\r\n      getEnums() {\r\n        listEnum().then(res => {\r\n          this.statusOptions = res.data.enterpriseApplyStatus;\r\n        })\r\n      },\r\n      /** 查询列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listData(this.queryParams).then(response => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 表单搜索 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      handlePreview(url) {\r\n        this.srcList = [url];\r\n      },\r\n      handleInvite() {\r\n        const clipboardObj = navigator.clipboard;\r\n        this.$message({\r\n          message: '链接已复制，快去找朋友分享吧~',\r\n          type: 'success'\r\n        })\r\n        clipboardObj.writeText('https://sc.cnudj.com/login');\r\n      },\r\n      // 重置\r\n      resetQuery() {\r\n        this.queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          business_no: undefined,\r\n          linker: undefined,\r\n          linkphone: undefined,\r\n          status: undefined,\r\n        };\r\n        this.reserForm('queryForm')\r\n      },\r\n      /** 修改按钮操作 */\r\n      handleDetail(row) {\r\n        getData(row.id).then(response => {\r\n          this.form = response.data;\r\n          this.$refs.detail.open()\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n"], "mappings": ";;;;;;;AA0FA,IAAAA,iBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAIA,IAAAE,MAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKA;EACAC,UAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACAC,aAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,WAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF,SAAA;QACAG,SAAA,EAAAH,SAAA;QACAI,MAAA,EAAAJ;MACA;MACA;MACAK,IAAA;MACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAD,QAAA,WAAAA,SAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,cAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAjB,aAAA,GAAAoB,GAAA,CAAAxB,IAAA,CAAAyB,qBAAA;MACA;IACA;IACA,WACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,MAAA;MACA,KAAAzB,OAAA;MACA,IAAA0B,eAAA,OAAArB,WAAA,EAAAiB,IAAA,WAAAK,QAAA;QACAF,MAAA,CAAAX,IAAA,GAAAa,QAAA,CAAA5B,IAAA;QACA0B,MAAA,CAAAvB,KAAA,GAAAyB,QAAA,CAAAC,KAAA;QACAH,MAAA,CAAAzB,OAAA;MACA;IACA;IACA,WACA6B,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAW,OAAA;IACA;IACAa,aAAA,WAAAA,cAAAC,GAAA;MACA,KAAAhB,OAAA,IAAAgB,GAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,IAAAC,YAAA,GAAAC,SAAA,CAAAC,SAAA;MACA,KAAAC,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;MACAL,YAAA,CAAAM,SAAA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAnC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,WAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF,SAAA;QACAG,SAAA,EAAAH,SAAA;QACAI,MAAA,EAAAJ;MACA;MACA,KAAAgC,SAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA,EAAAF,GAAA,CAAAG,EAAA,EAAAxB,IAAA,WAAAK,QAAA;QACAiB,MAAA,CAAAxC,IAAA,GAAAuB,QAAA,CAAA5B,IAAA;QACA6C,MAAA,CAAAG,KAAA,CAAAC,MAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}