{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1750151094141}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgcHJvcHM6IHsNCiAgICB2YWx1ZTogW1N0cmluZywgT2JqZWN0LCBBcnJheV0sDQogICAgLy8g5piv5ZCm5pi+56S6d2Vu5paH5Lu25YiX6KGoDQogICAgc2hvd0xpc3Q6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfSwNCiAgICAvLyDlm77niYfmlbDph4/pmZDliLYNCiAgICBsaW1pdDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogNSwNCiAgICB9LA0KICAgIC8vIOWkp+Wwj+mZkOWItihNQikNCiAgICBmaWxlU2l6ZTogew0KICAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDUsDQogICAgfSwNCiAgICAvLyDmlofku7bnsbvlnossIOS+i+WmglsncG5nJywgJ2pwZycsICdqcGVnJ10NCiAgICBmaWxlVHlwZTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbInBuZyIsICJqcGciLCAianBlZyJdLA0KICAgIH0sDQogICAgLy8g5piv5ZCm5pi+56S65o+Q56S6DQogICAgaXNTaG93VGlwOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbnVtYmVyOiAwLA0KICAgICAgdXBsb2FkTGlzdDogW10sDQogICAgICBkaWFsb2dJbWFnZVVybDogIiIsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGhpZGVVcGxvYWQ6IGZhbHNlLA0KICAgICAgdXBsb2FkSW1nVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9zaG9wL2RhdGEvdXBsb2FkL2ltYWdlIiwgLy8g5LiK5Lyg55qE5Zu+54mH5pyN5Yqh5Zmo5Zyw5Z2ADQogICAgICBoZWFkZXJzOiB7DQogICAgICAgIGF1dGhvcml6YXRpb246IGdldFRva2VuKCksDQogICAgICB9LA0KICAgICAgZmlsZUxpc3Q6IFtdDQogICAgfTsNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICB2YWx1ZTogew0KICAgICAgaGFuZGxlcih2YWwpIHsNCiAgICAgICAgaWYgKHZhbCkgew0KICAgICAgICAgIC8vIOmmluWFiOWwhuWAvOi9rOS4uuaVsOe7hA0KICAgICAgICAgIGNvbnN0IGxpc3QgPSBBcnJheS5pc0FycmF5KHZhbCkgPyB2YWwgOiB0aGlzLnZhbHVlLnNwbGl0KCcsJyk7DQogICAgICAgICAgLy8g54S25ZCO5bCG5pWw57uE6L2s5Li65a+56LGh5pWw57uEDQogICAgICAgICAgdGhpcy5maWxlTGlzdCA9IGxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgaWYgKHR5cGVvZiBpdGVtID09PSAic3RyaW5nIikgew0KICAgICAgICAgICAgICBpdGVtID0geyBuYW1lOiBpdGVtLCB1cmw6IGl0ZW0gfTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiBpdGVtOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXTsNCiAgICAgICAgICByZXR1cm4gW107DQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBkZWVwOiB0cnVlLA0KICAgICAgaW1tZWRpYXRlOiB0cnVlDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOaYr+WQpuaYvuekuuaPkOekug0KICAgIHNob3dUaXAoKSB7DQogICAgICByZXR1cm4gdGhpcy5pc1Nob3dUaXAgJiYgKHRoaXMuZmlsZVR5cGUgfHwgdGhpcy5maWxlU2l6ZSk7DQogICAgfSwNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOWIoOmZpOWbvueJhw0KICAgIGhhbmRsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgY29uc3QgZmluZGV4ID0gdGhpcy5maWxlTGlzdC5tYXAoZiA9PiBmLm5hbWUpLmluZGV4T2YoZmlsZS5uYW1lKTsNCiAgICAgIGlmKGZpbmRleCA+IC0xKSB7DQogICAgICAgIHRoaXMuZmlsZUxpc3Quc3BsaWNlKGZpbmRleCwgMSk7DQogICAgICAgIHRoaXMuJGVtaXQoImlucHV0IiwgdGhpcy5saXN0VG9TdHJpbmcodGhpcy5maWxlTGlzdCkpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5LiK5Lyg5oiQ5Yqf5Zue6LCDDQogICAgaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXMpIHsNCiAgICAgIHRoaXMudXBsb2FkTGlzdC5wdXNoKHsgbmFtZTogcmVzLnVybCwgdXJsOiByZXMudXJsIH0pOw0KICAgICAgaWYgKHRoaXMudXBsb2FkTGlzdC5sZW5ndGggPT09IHRoaXMubnVtYmVyKSB7DQogICAgICAgIHRoaXMuZmlsZUxpc3QgPSB0aGlzLmZpbGVMaXN0LmNvbmNhdCh0aGlzLnVwbG9hZExpc3QpOw0KICAgICAgICB0aGlzLnVwbG9hZExpc3QgPSBbXTsNCiAgICAgICAgdGhpcy5udW1iZXIgPSAwOw0KICAgICAgICB0aGlzLiRlbWl0KCJpbnB1dCIsIHRoaXMubGlzdFRvU3RyaW5nKHRoaXMuZmlsZUxpc3QpKTsNCiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDkuIrkvKDliY1sb2FkaW5n5Yqg6L29DQogICAgaGFuZGxlQmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIGxldCBpc0ltZyA9IGZhbHNlOw0KICAgICAgaWYgKHRoaXMuZmlsZVR5cGUubGVuZ3RoKSB7DQogICAgICAgIGxldCBmaWxlRXh0ZW5zaW9uID0gIiI7DQogICAgICAgIGlmIChmaWxlLm5hbWUubGFzdEluZGV4T2YoIi4iKSA+IC0xKSB7DQogICAgICAgICAgZmlsZUV4dGVuc2lvbiA9IGZpbGUubmFtZS5zbGljZShmaWxlLm5hbWUubGFzdEluZGV4T2YoIi4iKSArIDEpOw0KICAgICAgICB9DQogICAgICAgIGlzSW1nID0gdGhpcy5maWxlVHlwZS5zb21lKHR5cGUgPT4gew0KICAgICAgICAgIGlmIChmaWxlLnR5cGUuaW5kZXhPZih0eXBlKSA+IC0xKSByZXR1cm4gdHJ1ZTsNCiAgICAgICAgICBpZiAoZmlsZUV4dGVuc2lvbiAmJiBmaWxlRXh0ZW5zaW9uLmluZGV4T2YodHlwZSkgPiAtMSkgcmV0dXJuIHRydWU7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlzSW1nID0gZmlsZS50eXBlLmluZGV4T2YoImltYWdlIikgPiAtMTsNCiAgICAgIH0NCg0KICAgICAgaWYgKCFpc0ltZykgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcihg5paH5Lu25qC85byP5LiN5q2j56GuLCDor7fkuIrkvKAke3RoaXMuZmlsZVR5cGUuam9pbigiLyIpfeWbvueJh+agvOW8j+aWh+S7tiFgKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuZmlsZVNpemUpIHsNCiAgICAgICAgY29uc3QgaXNMdCA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgdGhpcy5maWxlU2l6ZTsNCiAgICAgICAgaWYgKCFpc0x0KSB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYOS4iuS8oOWktOWDj+WbvueJh+Wkp+Wwj+S4jeiDvei2hei/hyAke3RoaXMuZmlsZVNpemV9IE1CIWApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5LiK5Lyg5Zu+54mH77yM6K+356iN5YCZLi4uIik7DQogICAgICB0aGlzLm51bWJlcisrOw0KICAgIH0sDQogICAgLy8g5paH5Lu25Liq5pWw6LaF5Ye6DQogICAgaGFuZGxlRXhjZWVkKCkgew0KICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYOS4iuS8oOaWh+S7tuaVsOmHj+S4jeiDvei2hei/hyAke3RoaXMubGltaXR9IOS4qiFgKTsNCiAgICB9LA0KICAgIC8vIOS4iuS8oOWksei0pQ0KICAgIGhhbmRsZVVwbG9hZEVycm9yKCkgew0KICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuS4iuS8oOWbvueJh+Wksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgfSwNCiAgICAvLyDpooTop4gNCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgew0KICAgICAgdGhpcy5kaWFsb2dJbWFnZVVybCA9IGZpbGUudXJsOw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOWvueixoei9rOaIkOaMh+WumuWtl+espuS4suWIhumalA0KICAgIGxpc3RUb1N0cmluZyhsaXN0LCBzZXBhcmF0b3IpIHsNCiAgICAgIGxldCBzdHJzID0gIiI7DQogICAgICBzZXBhcmF0b3IgPSBzZXBhcmF0b3IgfHwgIiwiOw0KICAgICAgZm9yIChsZXQgaSBpbiBsaXN0KSB7DQogICAgICAgIHN0cnMgKz0gbGlzdFtpXS51cmwgKyBzZXBhcmF0b3I7DQogICAgICB9DQogICAgICByZXR1cm4gc3RycyAhPSAnJyA/IHN0cnMuc3Vic3RyKDAsIHN0cnMubGVuZ3RoIC0gMSkgOiAnJzsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImageUpload", "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      name=\"file\"\r\n      :on-remove=\"handleRemove\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\" v-if='showList || !fileList.length'></i>\r\n      <!-- <img v-if='!showList && fileList.length' :src='fileList[0].url'></img> -->\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 是否显示wen文件列表\r\n    showList: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5,\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"],\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/shop/data/upload/image\", // 上传的图片服务器地址\r\n      headers: {\r\n        authorization: getToken(),\r\n      },\r\n      fileList: []\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',');\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              item = { name: item, url: item };\r\n            }\r\n            return item;\r\n          });\r\n        } else {\r\n          this.fileList = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n  },\r\n  methods: {\r\n    // 删除图片\r\n    handleRemove(file, fileList) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name);\r\n      if(findex > -1) {\r\n        this.fileList.splice(findex, 1);\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n      }\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res) {\r\n      this.uploadList.push({ name: res.url, url: res.url });\r\n      if (this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n        this.$modal.closeLoading();\r\n      }\r\n    },\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false;\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\";\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true;\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\r\n          return false;\r\n        });\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1;\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join(\"/\")}图片格式文件!`);\r\n        return false;\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候...\");\r\n      this.number++;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        strs += list[i].url + separator;\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n    display: none;\r\n}\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n    transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n    opacity: 0;\r\n    transform: translateY(0);\r\n}\r\n\r\n\r\n</style>\r\n"]}]}