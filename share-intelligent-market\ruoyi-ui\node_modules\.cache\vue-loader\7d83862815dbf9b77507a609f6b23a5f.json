{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\store\\index.vue?vue&type=template&id=f1c6b7e0", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\store\\index.vue", "mtime": 1750151094266}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}