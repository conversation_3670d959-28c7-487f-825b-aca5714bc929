{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\inquiry.vue?vue&type=style&index=0&id=ffa95642&scoped=true&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\inquiry.vue", "mtime": 1750151094270}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouaXRlbS1mb3JtIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwp9Cgoud2lyZSB7CiAgYmFja2dyb3VuZDogcmdiKDIxOSwgMjE5LCAyMTkpOwogIGhlaWdodDogMXB4OwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5mb3JtMS1ib3ggewogIHdpZHRoOiAxMDAlOwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7Cn0KCi5mb3JtMS1zZWFyY2ggewogIHBhZGRpbmc6IDEwcHggMDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1ldmVubHk7Cn0K"}, {"version": 3, "sources": ["inquiry.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiQA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "inquiry.vue", "sourceRoot": "src/views/project/inquiry", "sourcesContent": ["<template>\r\n  <!-- 公开招募寻源 -->\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" size='small' v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\">\r\n            <el-cascader filterable clearable placeholder=\"选择产品分类\" v-model=\"classify\" :options=\"classifyOptions\"\r\n              :props='{label: \"name\", value: \"id\",checkStrictly: true}'></el-cascader>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"type\">\r\n            <el-select clearable v-model=\"queryParams.type\" placeholder=\"询价类型\">\r\n              <el-option v-for=\"item in typeOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select clearable v-model=\"queryParams.status\" placeholder=\"询价状态\">\r\n              <el-option v-for=\"item in statusOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"inquiry_no\">\r\n            <el-input clearable v-model=\"queryParams.inquiry_no\" placeholder=\"输入询价单号\" style=\"width: 200px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"title\">\r\n            <el-input clearable v-model=\"queryParams.title\" placeholder=\"输入询价标题\" :maxlength='100'\r\n              style=\"width: 240px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"enterprise_name\">\r\n            <el-input clearable v-model=\"queryParams.enterprise_name\" placeholder=\"输入询价公司\" :maxlength='50'\r\n              style=\"width: 300px\">\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"50\" />\r\n          <el-table-column label=\"询价日期\" width=\"120\" align=\"center\" prop=\"inquiry_date\" />\r\n          <el-table-column label=\"询价单编号\" width=\"140\" align=\"center\" prop=\"inquiry_no\" />\r\n          <el-table-column label=\"询价类型\" width=\"120\" align=\"center\" prop=\"typeStr\" />\r\n          <el-table-column label=\"询价标题\" width=\"280\" align=\"center\" prop=\"title\" />\r\n          <el-table-column label=\"询价公司\" align=\"center\" width=\"280\" prop=\"enterprise_name\" />\r\n          <el-table-column label=\"截止时间\" width=\"120\" align=\"center\" prop=\"deadline\" />\r\n          <el-table-column label=\"操作员\" width=\"120\" align=\"center\" prop=\"operator\" />\r\n          <el-table-column label=\"询价状态\" width=\"120\" align=\"center\" prop=\"status\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"报价公司数量\" width=\"120\" align=\"center\" prop=\"offers\" />\r\n          <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-view\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n              <el-button v-if=\"scope.row.status == 'GOING'\" icon=\"el-icon-phone-outline\" type=\"text\" size=\"mini\" @click=\"handleDispatch(scope.row)\">匹配供应商</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog title=\"匹配供应商\" :visible.sync=\"dialogVisible\" append-to-body center>\r\n      <el-form ref=\"form1\" :label-position=\"'left'\" :model=\"form\" label-width=\"100px\">\r\n        <el-select clearable style=\"width: 90%;\" v-model=\"enterprise\" filterable remote reserve-keyword placeholder=\"请输入企业名称\"\r\n          :remote-method=\"remoteEnterprise\" @change='changeEnterprise' value-key='id' :loading=\"loading\">\r\n          <el-option v-for=\"item in enterpriseOptions\" :key=\"item.id\" :label=\"item.name\" :value=\"item\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opDispatch\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 急速报价详情 -->\r\n    <lessDetails ref=\"lessDetails\"></lessDetails>\r\n    <!-- 精准报价详情 -->\r\n    <moreDetails ref=\"moreDetails\"></moreDetails>\r\n\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    searchData\r\n  } from '@/api/enterprise/apply';\r\n  import {\r\n    listData,\r\n    dispatchData\r\n  } from '@/api/project/inquiry';\r\n  import {\r\n    listEnum,\r\n    listClassify\r\n  } from '@/api/tool/util';\r\n  import lessDetails from \"./components/lessDetails.vue\";\r\n  import moreDetails from \"./components/moreDetails.vue\";\r\n  export default {\r\n    components: {\r\n      lessDetails,\r\n      moreDetails\r\n    },\r\n    data() {\r\n      return {\r\n        // 供应商列表\r\n        enterpriseOptions: [],\r\n        // 选中的供应商\r\n        enterprise: {},\r\n        classifyOptions: [],\r\n        typeOptions: [],\r\n        statusOptions: [],\r\n        classify: {},\r\n        // 详情\r\n        open: false,\r\n        // 匹配提供商\r\n        providerOpen: false,\r\n        input: \"\",\r\n        // 遮罩层\r\n        loading: false,\r\n        // 选中数组\r\n        ids: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          inquiry_no: undefined,\r\n          title: undefined,\r\n          enterprise_name: undefined,\r\n          status: undefined,\r\n          type: undefined,\r\n        },\r\n        // 表格数据\r\n        list: [],\r\n        // 取消弹窗显示\r\n        dialogVisible: false,\r\n      };\r\n    },\r\n    created() {\r\n      this.getClassify()\r\n      this.getEnums()\r\n      this.getList()\r\n    },\r\n    methods: {\r\n      /* 查询企业信息 */\r\n      remoteEnterprise(e) {\r\n        this.loading = true;\r\n        searchData(e).then(res => {\r\n          this.loading = false;\r\n          this.enterpriseOptions = res.data;\r\n        })\r\n      },\r\n      /* 切换企业信息 */\r\n      changeEnterprise(e) {\r\n        this.form.enterprise_id = this.enterprise.id;\r\n        this.form.enterprise_name = this.enterprise.name;\r\n      },\r\n      getEnums() {\r\n        listEnum().then(res => {\r\n          this.typeOptions = res.data.inquiryType;\r\n          this.statusOptions = res.data.inquiryStatus;\r\n        })\r\n      },\r\n      getClassify() {\r\n        listClassify().then(res => {\r\n          this.classifyOptions = res.data;\r\n        })\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n      /** 查询信息列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        if (this.classify) {\r\n          this.queryParams.classify_id = this.classify[0]\r\n          this.queryParams.classify2_id = this.classify.length > 1 ? this.classify[1] : -1\r\n          this.queryParams.classify3_id = this.classify.length > 2 ? this.classify[2] : -1\r\n        } else {\r\n          this.queryParams.classify_id = -1\r\n          this.queryParams.classify2_id = -1\r\n          this.queryParams.classify3_id = -1\r\n        }\r\n        listData(this.queryParams).then((response) => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      // 查看详情\r\n      handleCheck(row) {\r\n      },\r\n      handleDispatch(row){\r\n        this.enterprise = ''\r\n        this.form = {};\r\n        this.dialogVisible = true\r\n        this.form.inquiry_id = row.id\r\n      },\r\n      handleDetail(row) {\r\n        if(row.type=='NORMAL'){\r\n          this.$refs.lessDetails.open(row.id)\r\n        }\r\n        else{\r\n          this.$refs.moreDetails.open(row.id)\r\n        }\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.resetForm(\"form\");\r\n      },\r\n      opDispatch(status) {\r\n        if(!this.form.enterprise_id){\r\n          this.$message({\r\n            type: 'error',\r\n            message: '请选择供应商'\r\n          })\r\n          return\r\n        }\r\n        dispatchData(this.form).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '操作成功'\r\n          })\r\n          this.dialogVisible = false\r\n        })\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .item-form {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .wire {\r\n    background: rgb(219, 219, 219);\r\n    height: 1px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .form1-box {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .form1-search {\r\n    padding: 10px 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-evenly;\r\n  }\r\n</style>\r\n"]}]}