{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\product\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\product\\index.vue", "mtime": 1750151094260}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3RQcm9kdWN0LA0KICAgIGdldFByb2R1Y3QsDQogICAgZGVsUHJvZHVjdCwNCiAgICBhZGRQcm9kdWN0LA0KICAgIHVwZGF0ZVByb2R1Y3QsDQp9IGZyb20gIkAvYXBpL3V1Yy9wcm9kdWN0IjsNCmltcG9ydCB7IGFkZFByb2R1Y3RfZm9sbG93IH0gZnJvbSAiQC9hcGkvdXVjL3Byb2R1Y3RfZm9sbG93IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJQcm9kdWN0IiwNCiAgICBkaWN0czogWyJ1dWNfb25saW5lIiwgInV1Y19jb2xsYWJvcmF0aXZlX2FyZWFzIl0sDQogICAgZGF0YSgpIHsNCiAgICAgICAgbGV0IGNoZWNrUGhvbmUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICAgICAgICBsZXQgcmVnID0gL14xWzM0NTc4OV1cZHs5fSQvOw0KICAgICAgICAgICAgaWYgKCFyZWcudGVzdCh2YWx1ZSkpIHsNCiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuivt+i+k+WFpTEx5L2N5omL5py65Y+3IikpOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgICAgICAgfQ0KICAgICAgICB9Ow0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgLy8g6YGu572p5bGCDQogICAgICAgICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICAgICAgICBpZHM6IFtdLA0KICAgICAgICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICAgICAgICBzaW5nbGU6IHRydWUsDQogICAgICAgICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgICAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICAgICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgICAgICAgLy8g5oC75p2h5pWwDQogICAgICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgICAgIC8vIOacjeWKoeeuoeeQhuihqOagvOaVsOaNrg0KICAgICAgICAgICAgcHJvZHVjdExpc3Q6IFtdLA0KICAgICAgICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICAgICAgICB0aXRsZTogIiIsDQogICAgICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgICAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgICAgICAgb3BlbjE6IGZhbHNlLA0KICAgICAgICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICAgICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICAgICAgICAgIHByb2R1Y3ROYW1lOiBudWxsLA0KICAgICAgICAgICAgICAgIGNvbGxhYm9yYXRpdmU6IG51bGwsDQogICAgICAgICAgICAgICAgY29udGFjdDogbnVsbCwNCiAgICAgICAgICAgICAgICBwaG9uZTogbnVsbCwNCiAgICAgICAgICAgICAgICByZWNvbW1lbmRTdGF0dXM6IG51bGwsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICAgICAgICBmb3JtOiB7fSwNCiAgICAgICAgICAgIGZvcm0xOiB7fSwNCiAgICAgICAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgICAgICAgcnVsZXM6IHsNCiAgICAgICAgICAgICAgICBwcm9kdWN0Tm86IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Lqn5ZOB57yW5Y+35LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIHByb2R1Y3ROYW1lOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuacjeWKoeWQjeensOS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICAvLyBidHlwZUNvZGU6Ww0KICAgICAgICAgICAgICAgIC8vICAgICB7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIG1lc3NhZ2U6ICLkvpvlupTllYbnvJbnoIHkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgIC8vICAgICB9LA0KICAgICAgICAgICAgICAgIC8vIF0sDQogICAgICAgICAgICAgICAgY29sbGFib3JhdGl2ZTogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLlkIjkvZzpoobln5/kuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICBjb250YWN0OiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuiBlOezu+S6uuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICBwaG9uZTogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLogZTns7vnlLXor53kuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHZhbGlkYXRvcjogY2hlY2tQaG9uZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7ciLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgcGljdHVyZXM6Ww0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLlm77niYfkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBydWxlczE6IHsNCiAgICAgICAgICAgICAgICBwcm9kdWN0SWQ6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5pyN5YqhaWTkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgcHJvZHVjdE5hbWU6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5pyN5Yqh5ZCN56ew5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIHByaW5jaXBhbDogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLotJ/otKPkurrkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICB9LA0KICAgICAgICB9Ow0KICAgIH0sDQogICAgd2F0Y2g6IHsNCiAgICAgICAgZm9ybTogew0KICAgICAgICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZUZpZWxkKFsicGljdHVyZXMiXSxhc3luYyAodmFsaWQpPT57DQogICAgICAgICAgICAgICAgICBpZih0aGlzLmZvcm0ucGljdHVyZXMpew0KICAgICAgICAgICAgICAgICAgICAgIGlmKHZhbGlkKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS5jbGVhclZhbGlkYXRlKCdwaWN0dXJlcycpOyANCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRlZXA6IHRydWUsDQogICAgICAgIH0sDQogICAgfSwNCiAgICBjcmVhdGVkKCkgew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIG1ldGhvZHM6IHsNCiAgICAgICAgaGFuZGxlRm9sbG93KGl0ZW0pIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIHRoaXMuZm9ybTEgPSB7DQogICAgICAgICAgICAgICAgcHJvZHVjdElkOiBpdGVtLmlkLA0KICAgICAgICAgICAgICAgIHByb2R1Y3ROYW1lOiBpdGVtLnByb2R1Y3ROYW1lLA0KICAgICAgICAgICAgICAgIHByaW5jaXBhbDogbnVsbCwNCiAgICAgICAgICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgdGhpcy5vcGVuMSA9IHRydWU7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDmn6Xor6LmnI3liqHnrqHnkIbliJfooaggKi8NCiAgICAgICAgZ2V0TGlzdCgpIHsNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgICBsaXN0UHJvZHVjdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMucHJvZHVjdExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlj5bmtojmjInpkq4NCiAgICAgICAgY2FuY2VsKCkgew0KICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgIH0sDQogICAgICAgIGNhbmNlbDEoKSB7DQogICAgICAgICAgICB0aGlzLm9wZW4xID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOihqOWNlemHjee9rg0KICAgICAgICByZXNldCgpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgICAgICAgICBwcm9kdWN0Tm86IG51bGwsDQogICAgICAgICAgICAgICAgcHJvZHVjdE5hbWU6IG51bGwsDQogICAgICAgICAgICAgICAgYnR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgICAgICAgIGJ0eXBlTmFtZTogbnVsbCwNCiAgICAgICAgICAgICAgICBzaG9ydE5hbWU6IG51bGwsDQogICAgICAgICAgICAgICAgY29sbGFib3JhdGl2ZTogbnVsbCwNCiAgICAgICAgICAgICAgICBjb250YWN0OiBudWxsLA0KICAgICAgICAgICAgICAgIHBob25lOiBudWxsLA0KICAgICAgICAgICAgICAgIGxhYmVsOiBudWxsLA0KICAgICAgICAgICAgICAgIGltYWdlOiBudWxsLA0KICAgICAgICAgICAgICAgIGNvbnRlbnQ6IG51bGwsDQogICAgICAgICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgICAgICAgIHJlY29tbWVuZFN0YXR1czogIjAiLA0KICAgICAgICAgICAgICAgIHBpY3R1cmVzOiBudWxsLA0KICAgICAgICAgICAgICAgIGF0dGFjaGVzOiBudWxsLA0KICAgICAgICAgICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICAgICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgICAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICB0aGlzLmZvcm0xID0gew0KICAgICAgICAgICAgICAgIHByb2R1Y3RJZDogbnVsbCwNCiAgICAgICAgICAgICAgICBwcm9kdWN0TmFtZTogbnVsbCwNCiAgICAgICAgICAgICAgICBwcmluY2lwYWw6IG51bGwsDQogICAgICAgICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgICAgICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybTEiKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgICAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICAgICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsNCiAgICAgICAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgICAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVBZGQoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDmnI3liqHnrqHnkIYiOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgICAgICAgZ2V0UHJvZHVjdChpZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnmnI3liqHnrqHnkIYiOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICAgICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVQcm9kdWN0KHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgYWRkUHJvZHVjdCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICBzdWJtaXRGb3JtMSgpIHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0xIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgICAgICAgICAgIGFkZFByb2R1Y3RfZm9sbG93KHRoaXMuZm9ybTEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLot5/ov5vmiJDlip8iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMub3BlbjEgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICAgICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICAgICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAgICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmnI3liqHnrqHnkIbnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykNCiAgICAgICAgICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkZWxQcm9kdWN0KGlkcyk7DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgICAgICAgdGhpcy5kb3dubG9hZCgNCiAgICAgICAgICAgICAgICAidXVjL3Byb2R1Y3QvZXhwb3J0IiwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBgcHJvZHVjdF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgICAgICAgKTsNCiAgICAgICAgfSwNCiAgICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/ningmengdou/product", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"服务名称\" prop=\"productName\">\r\n                <el-input\r\n                    v-model=\"queryParams.productName\"\r\n                    placeholder=\"请输入服务名称\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"合作领域\" prop=\"collaborative\">\r\n                <el-select\r\n                    v-model=\"queryParams.collaborative\"\r\n                    placeholder=\"请选择合作领域\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_collaborative_areas\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"contact\">\r\n                <el-input\r\n                    v-model=\"queryParams.contact\"\r\n                    placeholder=\"请输入联系人\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                <el-input\r\n                    v-model=\"queryParams.phone\"\r\n                    placeholder=\"请输入联系电话\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"recommendStatus\">\r\n                <el-select\r\n                    v-model=\"queryParams.recommendStatus\"\r\n                    placeholder=\"请选择状态\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_online\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:product:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:product:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:product:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['uuc:product:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"productList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n            <el-table-column label=\"产品编号\" align=\"center\" prop=\"productNo\" />\r\n            <el-table-column\r\n                label=\"服务名称\"\r\n                align=\"center\"\r\n                prop=\"productName\"\r\n            />\r\n            <el-table-column\r\n                label=\"供应商编码\"\r\n                align=\"center\"\r\n                prop=\"btypeCode\"\r\n            />\r\n            <el-table-column\r\n                label=\"供应商名称\"\r\n                align=\"center\"\r\n                prop=\"btypeName\"\r\n            />\r\n            <el-table-column\r\n                label=\"供应商简称\"\r\n                align=\"center\"\r\n                prop=\"shortName\"\r\n            />\r\n            <el-table-column\r\n                label=\"合作领域\"\r\n                align=\"center\"\r\n                prop=\"collaborative\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_collaborative_areas\"\r\n                        :value=\"scope.row.collaborative\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"contact\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"phone\" />\r\n            <!-- <el-table-column label=\"介绍\" align=\"center\" prop=\"content\" /> -->\r\n            <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n            <el-table-column label=\"状态\" align=\"center\" prop=\"recommendStatus\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_online\"\r\n                        :value=\"scope.row.recommendStatus\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"图片\"\r\n                align=\"center\"\r\n                prop=\"pictures\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <image-preview\r\n                        :src=\"scope.row.pictures\"\r\n                        :width=\"50\"\r\n                        :height=\"50\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:product:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:product:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleFollow(scope.row)\"\r\n                        v-hasPermi=\"['uuc:product:edit']\"\r\n                        >跟进</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改服务管理对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            v-if=\"open\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n                <el-form-item label=\"产品编号\" prop=\"productNo\">\r\n                    <el-input\r\n                        v-model=\"form.productNo\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入产品编号\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"服务名称\" prop=\"productName\">\r\n                    <el-input\r\n                        v-model=\"form.productName\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入服务名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"供应商编码\" prop=\"btypeCode\">\r\n                    <el-input\r\n                        v-model=\"form.btypeCode\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入供应商编码\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"供应商名称\" prop=\"btypeName\">\r\n                    <el-input\r\n                        v-model=\"form.btypeName\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入供应商名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"供应商简称\" prop=\"shortName\">\r\n                    <el-input\r\n                        v-model=\"form.shortName\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入供应商简称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"合作领域\" prop=\"collaborative\">\r\n                    <el-select\r\n                        v-model=\"form.collaborative\"\r\n                        placeholder=\"请选择合作领域\"\r\n                    >\r\n                        <el-option\r\n                            v-for=\"dict in dict.type.uuc_collaborative_areas\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.label\"\r\n                            :value=\"dict.value\"\r\n                        ></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" prop=\"contact\">\r\n                    <el-input\r\n                        v-model=\"form.contact\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入联系人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                    <el-input\r\n                        v-model=\"form.phone\"\r\n                        maxlength=\"50\"\r\n                        type=\"number\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"介绍\">\r\n                    <el-input\r\n                        v-model=\"form.content\"\r\n                        maxlength=\"500\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                    <!-- <editor v-model=\"form.content\" :min-height=\"192\"/> -->\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        maxlength=\"500\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"标签\" prop=\"label\">\r\n                    <el-input\r\n                        v-model=\"form.label\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入标签\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\">\r\n                    <el-radio-group v-model=\"form.recommendStatus\">\r\n                        <el-radio\r\n                            v-for=\"dict in dict.type.uuc_online\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.value\"\r\n                            >{{ dict.label }}</el-radio\r\n                        >\r\n                    </el-radio-group>\r\n                </el-form-item>\r\n                <el-form-item label=\"图片\" prop=\"pictures\">\r\n                    <image-upload v-model=\"form.pictures\" :limit=\"1\" />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <!-- 跟进 -->\r\n        <el-dialog\r\n            title=\"服务跟进\"\r\n            :visible.sync=\"open1\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form\r\n                ref=\"form1\"\r\n                :model=\"form1\"\r\n                :rules=\"rules1\"\r\n                label-width=\"80px\"\r\n            >\r\n                <el-form-item label=\"服务id\" prop=\"productId\">\r\n                    <el-input\r\n                        disabled\r\n                        v-model=\"form1.productId\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入服务id\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"服务名称\" prop=\"productName\">\r\n                    <el-input\r\n                        disabled\r\n                        v-model=\"form1.productName\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入服务名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"负责人\" prop=\"principal\">\r\n                    <el-input\r\n                        v-model=\"form1.principal\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入负责人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form1.remark\"\r\n                        maxlength=\"500\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm1\">确 定</el-button>\r\n                <el-button @click=\"cancel1\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listProduct,\r\n    getProduct,\r\n    delProduct,\r\n    addProduct,\r\n    updateProduct,\r\n} from \"@/api/uuc/product\";\r\nimport { addProduct_follow } from \"@/api/uuc/product_follow\";\r\n\r\nexport default {\r\n    name: \"Product\",\r\n    dicts: [\"uuc_online\", \"uuc_collaborative_areas\"],\r\n    data() {\r\n        let checkPhone = (rule, value, callback) => {\r\n            let reg = /^1[345789]\\d{9}$/;\r\n            if (!reg.test(value)) {\r\n                callback(new Error(\"请输入11位手机号\"));\r\n            } else {\r\n                callback();\r\n            }\r\n        };\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 服务管理表格数据\r\n            productList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            open1: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                productName: null,\r\n                collaborative: null,\r\n                contact: null,\r\n                phone: null,\r\n                recommendStatus: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            form1: {},\r\n            // 表单校验\r\n            rules: {\r\n                productNo: [\r\n                    {\r\n                        required: true,\r\n                        message: \"产品编号不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                productName: [\r\n                    {\r\n                        required: true,\r\n                        message: \"服务名称不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                // btypeCode:[\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"供应商编码不能为空\",\r\n                //         trigger: \"blur\",\r\n                //     },\r\n                // ],\r\n                collaborative: [\r\n                    {\r\n                        required: true,\r\n                        message: \"合作领域不能为空\",\r\n                        trigger: \"change\",\r\n                    },\r\n                ],\r\n                contact: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                phone: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系电话不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    {\r\n                        type: \"number\",\r\n                        validator: checkPhone,\r\n                        message: \"请输入正确的手机号\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                pictures:[\r\n                    {\r\n                        required: true,\r\n                        message: \"图片不能为空\",\r\n                        trigger: \"change\",\r\n                    },\r\n                ],\r\n            },\r\n            rules1: {\r\n                productId: [\r\n                    {\r\n                        required: true,\r\n                        message: \"服务id不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                productName: [\r\n                    {\r\n                        required: true,\r\n                        message: \"服务名称不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                principal: [\r\n                    {\r\n                        required: true,\r\n                        message: \"负责人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    watch: {\r\n        form: {\r\n            handler(newVal, oldVal) {\r\n                this.$refs[\"form\"].validateField([\"pictures\"],async (valid)=>{\r\n                  if(this.form.pictures){\r\n                      if(valid){\r\n                        this.$refs[\"form\"].clearValidate('pictures'); \r\n                      }\r\n                    }\r\n                })\r\n            },\r\n            deep: true,\r\n        },\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        handleFollow(item) {\r\n            this.reset();\r\n            this.form1 = {\r\n                productId: item.id,\r\n                productName: item.productName,\r\n                principal: null,\r\n                remark: null,\r\n            };\r\n            this.open1 = true;\r\n        },\r\n        /** 查询服务管理列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listProduct(this.queryParams).then((response) => {\r\n                this.productList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        cancel1() {\r\n            this.open1 = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                productNo: null,\r\n                productName: null,\r\n                btypeCode: null,\r\n                btypeName: null,\r\n                shortName: null,\r\n                collaborative: null,\r\n                contact: null,\r\n                phone: null,\r\n                label: null,\r\n                image: null,\r\n                content: null,\r\n                remark: null,\r\n                recommendStatus: \"0\",\r\n                pictures: null,\r\n                attaches: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.form1 = {\r\n                productId: null,\r\n                productName: null,\r\n                principal: null,\r\n                remark: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n            this.resetForm(\"form1\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加服务管理\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getProduct(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改服务管理\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.id != null) {\r\n                        updateProduct(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addProduct(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        submitForm1() {\r\n            this.$refs[\"form1\"].validate((valid) => {\r\n                if (valid) {\r\n                    addProduct_follow(this.form1).then((response) => {\r\n                        this.$modal.msgSuccess(\"跟进成功\");\r\n                        this.open1 = false;\r\n                        this.getList();\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除服务管理编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delProduct(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/product/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `product_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}