{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\components\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\components\\list.vue", "mtime": 1750151094270}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZm9ybTogewogICAgICAgIHBob25lOiAiIiwKICAgICAgICBuYW1lOiAiIgogICAgICB9LAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgbGlzdDogW3sKICAgICAgICB0YWIxOiAiREQyMDIyMDUyNy0wMDA1IiwKICAgICAgICB0YWIyOiAi6Z2S5bKb5YWs5Y+4IiwKICAgICAgICB0YWIzOiAi546L5LyfIiwKICAgICAgICB0YWI0OiAiMTkxNzg2MjM2NTQiLAogICAgICAgIHRhYjU6ICIxMjAiLAogICAgICAgIHRhYjY6ICIzNTAwIiwKICAgICAgICB0YWI3OiAiMTExIgogICAgICB9XQogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIG9wZW46IGZ1bmN0aW9uIG9wZW4oKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["data", "form", "phone", "name", "dialogVisible", "list", "tab1", "tab2", "tab3", "tab4", "tab5", "tab6", "tab7", "methods", "open", "submitForm"], "sources": ["src/views/project/inquiry/components/list.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog title=\"查看所有报价\" :visible.sync=\"dialogVisible\" append-to-body center>\r\n      <el-form ref=\"form1\" :label-position=\"'left'\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-table v-loading=\"loading\" border :data=\"list\">\r\n          <el-table-column label=\"序号\" align=\"center\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.$index + 1 }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"单号\" align=\"center\" prop=\"tab1\" />\r\n          <el-table-column label=\"报价公司\" align=\"center\" prop=\"tab2\" />\r\n          <el-table-column label=\"联系人\" align=\"center\" prop=\"tab3\" />\r\n          <el-table-column label=\"联系方式\" align=\"center\" prop=\"tab4\" />\r\n          <el-table-column label=\"含税单价\" align=\"center\" prop=\"tab5\" />\r\n          <el-table-column label=\"含税总价\" align=\"center\" prop=\"tab6\" />\r\n          <el-table-column label=\"运费\" align=\"center\" prop=\"tab7\" />\r\n        </el-table>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">确 定</el-button>\r\n        <!-- <el-button @click=\"cancel('open')\">取 消</el-button> -->\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        form: {\r\n          phone: \"\",\r\n          name: \"\",\r\n        },\r\n        dialogVisible: false,\r\n        list: [{\r\n          tab1: \"DD20220527-0005\",\r\n          tab2: \"青岛公司\",\r\n          tab3: \"王伟\",\r\n          tab4: \"19178623654\",\r\n          tab5: \"120\",\r\n          tab6: \"3500\",\r\n          tab7: \"111\",\r\n        }],\r\n      }\r\n    },\r\n    methods: {\r\n      open() {\r\n        this.dialogVisible = true;\r\n      },\r\n      submitForm() {\r\n        this.dialogVisible = false;\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA2BA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACAC,aAAA;MACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAV,aAAA;IACA;IACAW,UAAA,WAAAA,WAAA;MACA,KAAAX,aAAA;IACA;EACA;AACA", "ignoreList": []}]}