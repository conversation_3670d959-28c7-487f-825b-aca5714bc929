{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\dict\\DictOptions.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\dict\\DictOptions.js", "mtime": 1750151094206}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ruoyi", "require", "_DictConverter", "_interopRequireDefault", "options", "exports", "metas", "request", "dictMeta", "console", "log", "concat", "type", "Promise", "resolve", "responseConverter", "labelField", "valueField", "DEFAULT_LABEL_FIELDS", "DEFAULT_VALUE_FIELDS", "response", "dicts", "content", "Array", "undefined", "warn", "map", "d", "dictConverter", "mergeOptions", "src", "mergeRecursive", "_default", "default"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/utils/dict/DictOptions.js"], "sourcesContent": ["import { mergeRecursive } from \"@/utils/ruoyi\";\r\nimport dictConverter from './DictConverter'\r\n\r\nexport const options = {\r\n  metas: {\r\n    '*': {\r\n      /**\r\n       * 字典请求，方法签名为function(dictMeta: DictMeta): Promise\r\n       */\r\n      request: (dictMeta) => {\r\n        console.log(`load dict ${dictMeta.type}`)\r\n        return Promise.resolve([])\r\n      },\r\n      /**\r\n       * 字典响应数据转换器，方法签名为function(response: Object, dictMeta: DictMeta): DictData\r\n       */\r\n      responseConverter,\r\n      labelField: 'label',\r\n      valueField: 'value',\r\n    },\r\n  },\r\n  /**\r\n   * 默认标签字段\r\n   */\r\n  DEFAULT_LABEL_FIELDS: ['label', 'name', 'title'],\r\n  /**\r\n   * 默认值字段\r\n   */\r\n  DEFAULT_VALUE_FIELDS: ['value', 'id', 'uid', 'key'],\r\n}\r\n\r\n/**\r\n * 映射字典\r\n * @param {Object} response 字典数据\r\n * @param {DictMeta} dictMeta 字典元数据\r\n * @returns {DictData}\r\n */\r\nfunction responseConverter(response, dictMeta) {\r\n  const dicts = response.content instanceof Array ? response.content : response\r\n  if (dicts === undefined) {\r\n    console.warn(`no dict data of \"${dictMeta.type}\" found in the response`)\r\n    return []\r\n  }\r\n  return dicts.map(d => dictConverter(d, dictMeta))\r\n}\r\n\r\nexport function mergeOptions(src) {\r\n  mergeRecursive(options, src)\r\n}\r\n\r\nexport default options\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEO,IAAMG,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAG;EACrBE,KAAK,EAAE;IACL,GAAG,EAAE;MACH;AACN;AACA;MACMC,OAAO,EAAE,SAATA,OAAOA,CAAGC,QAAQ,EAAK;QACrBC,OAAO,CAACC,GAAG,cAAAC,MAAA,CAAcH,QAAQ,CAACI,IAAI,CAAE,CAAC;QACzC,OAAOC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC;MAC5B,CAAC;MACD;AACN;AACA;MACMC,iBAAiB,EAAjBA,iBAAiB;MACjBC,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE;IACd;EACF,CAAC;EACD;AACF;AACA;EACEC,oBAAoB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EAChD;AACF;AACA;EACEC,oBAAoB,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,iBAAiBA,CAACK,QAAQ,EAAEZ,QAAQ,EAAE;EAC7C,IAAMa,KAAK,GAAGD,QAAQ,CAACE,OAAO,YAAYC,KAAK,GAAGH,QAAQ,CAACE,OAAO,GAAGF,QAAQ;EAC7E,IAAIC,KAAK,KAAKG,SAAS,EAAE;IACvBf,OAAO,CAACgB,IAAI,sBAAAd,MAAA,CAAqBH,QAAQ,CAACI,IAAI,6BAAyB,CAAC;IACxE,OAAO,EAAE;EACX;EACA,OAAOS,KAAK,CAACK,GAAG,CAAC,UAAAC,CAAC;IAAA,OAAI,IAAAC,sBAAa,EAACD,CAAC,EAAEnB,QAAQ,CAAC;EAAA,EAAC;AACnD;AAEO,SAASqB,YAAYA,CAACC,GAAG,EAAE;EAChC,IAAAC,qBAAc,EAAC3B,OAAO,EAAE0B,GAAG,CAAC;AAC9B;AAAC,IAAAE,QAAA,GAAA3B,OAAA,CAAA4B,OAAA,GAEc7B,OAAO", "ignoreList": []}]}