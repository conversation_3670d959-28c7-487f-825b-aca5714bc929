
576f1f941ec87b8ad61e612881691e8e283a462e	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fui\\u002Ftinymce-5-dark\\u002Fskin.shadowdom.min.css\",\"contentHash\":\"babdff13b82052119ff06b18ebd7101a\"}","integrity":"sha512-Sjb2YANROLKNRGUNGDgSRg9B1fy+ZJmzr2o1Slu5gtQsoK7gzgA3NB9kso5OOuIMz7HBghPHcWgm6gCsh2Yn5A==","time":1750496064279,"size":973}