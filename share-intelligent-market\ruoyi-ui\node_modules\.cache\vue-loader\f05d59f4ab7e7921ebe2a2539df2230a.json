{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\menu\\index.vue?vue&type=template&id=0304e458", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\menu\\index.vue", "mtime": 1750151094295}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}