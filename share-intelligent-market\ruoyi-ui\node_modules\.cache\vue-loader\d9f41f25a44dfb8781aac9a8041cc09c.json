{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\Breadcrumb\\index.vue", "mtime": 1750151094122}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxldmVsTGlzdDogbnVsbA0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAkcm91dGUocm91dGUpIHsNCiAgICAgIC8vIGlmIHlvdSBnbyB0byB0aGUgcmVkaXJlY3QgcGFnZSwgZG8gbm90IHVwZGF0ZSB0aGUgYnJlYWRjcnVtYnMNCiAgICAgIGlmIChyb3V0ZS5wYXRoLnN0YXJ0c1dpdGgoJy9yZWRpcmVjdC8nKSkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRCcmVhZGNydW1iKCkgew0KICAgICAgLy8gb25seSBzaG93IHJvdXRlcyB3aXRoIG1ldGEudGl0bGUNCiAgICAgIGxldCBtYXRjaGVkID0gdGhpcy4kcm91dGUubWF0Y2hlZC5maWx0ZXIoaXRlbSA9PiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlKQ0KICAgICAgY29uc3QgZmlyc3QgPSBtYXRjaGVkWzBdDQoNCiAgICAgIGlmICghdGhpcy5pc0Rhc2hib2FyZChmaXJzdCkpIHsNCiAgICAgICAgbWF0Y2hlZCA9IFt7IHBhdGg6ICcvaW5kZXgnLCBtZXRhOiB7IHRpdGxlOiAn6aaW6aG1JyB9fV0uY29uY2F0KG1hdGNoZWQpDQogICAgICB9DQoNCiAgICAgIHRoaXMubGV2ZWxMaXN0ID0gbWF0Y2hlZC5maWx0ZXIoaXRlbSA9PiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlICYmIGl0ZW0ubWV0YS5icmVhZGNydW1iICE9PSBmYWxzZSkNCiAgICB9LA0KICAgIGlzRGFzaGJvYXJkKHJvdXRlKSB7DQogICAgICBjb25zdCBuYW1lID0gcm91dGUgJiYgcm91dGUubmFtZQ0KICAgICAgaWYgKCFuYW1lKSB7DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIG5hbWUudHJpbSgpID09PSAnSW5kZXgnDQogICAgfSwNCiAgICBoYW5kbGVMaW5rKGl0ZW0pIHsNCiAgICAgIGNvbnN0IHsgcmVkaXJlY3QsIHBhdGggfSA9IGl0ZW0NCiAgICAgIGlmIChyZWRpcmVjdCkgew0KICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaChyZWRpcmVjdCkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaChwYXRoKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect === 'noRedirect' || index == levelList.length - 1\" class=\"no-redirect\">{{ item.meta.title }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      // if you go to the redirect page, do not update the breadcrumbs\r\n      if (route.path.startsWith('/redirect/')) {\r\n        return\r\n      }\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\r\n      const first = matched[0]\r\n\r\n      if (!this.isDashboard(first)) {\r\n        matched = [{ path: '/index', meta: { title: '首页' }}].concat(matched)\r\n      }\r\n\r\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim() === 'Index'\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"]}]}