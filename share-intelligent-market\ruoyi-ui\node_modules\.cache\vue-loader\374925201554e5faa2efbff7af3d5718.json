{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\service\\scene.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\service\\scene.vue", "mtime": 1750151094280}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3REYXRhLA0KICAgIGRlbERhdGEsDQogICAgYWRkRGF0YSwNCiAgICBlZGl0RGF0YSwNCn0gZnJvbSAiQC9hcGkvc2VydmljZS9zY2VuZS5qcyI7DQpleHBvcnQgZGVmYXVsdCB7DQogICAgbmFtZTogIkluZm9yIiwNCiAgICBkYXRhKCkgew0KICAgICAgICByZXR1cm4gew0KDQogICAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgICAgIHNob3c6IGZhbHNlLA0KICAgICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgICAgZm9ybToge30sDQogICAgICAgICAgICBydWxlczogew0KICAgICAgICAgICAgICAgIHNjZW5lX25hbWU6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5Zy65pmv5ZCN56ewIiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIHNjZW5lX3NvcnQ6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5Zy65pmv5o6S5bqPIiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIHNjZW5lX3BpYzogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fmt7vliqDlnLrmma/lsZXnpLrlm74iLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICB9LA0KDQogICAgICAgICAgICAvLyDpga7nvanlsYINCiAgICAgICAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAgICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgICAgICAgIGlkczogW10sDQogICAgICAgICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgICAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAgICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgICAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAgICAgICAvLyDmgLvmnaHmlbANCiAgICAgICAgICAgIHRvdGFsOiAwLA0KICAgICAgICAgICAgLy8g6KGo5qC85pWw5o2uDQogICAgICAgICAgICBsaXN0OiBbXSwNCiAgICAgICAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgICAgICAgICByZWNvbW1lbmQ6IiIsDQogICAgICAgICAgICAgICAgc2NlbmVfbmFtZTonJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGZvcm06IHsNCg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHJlY09wdGlvbnM6IFt7DQogICAgICAgICAgICAgICAga2V5OiAyLA0KICAgICAgICAgICAgICAgIHZhbHVlOiAn5o6o6I2QJw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAga2V5OiAxLA0KICAgICAgICAgICAgICAgIHZhbHVlOiAn5LiN5o6o6I2QJw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgXSwNCiAgICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgY2hhbmdlUmVjb21tZW5kKHJlYywgcm93KSB7DQogICAgICAgIGVkaXREYXRhKHsNCiAgICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICAgIHJlY29tbWVuZDogcmVjDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnycsDQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICB9KTsNCiAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIH0pDQogICAgICB9LA0KICAgICAgdXBsb2FkU3VjY2VzcyhldmVudCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5zY2VuZV9waWMgPSBldmVudDsNCiAgICAgIH0sDQoNCiAgICAgICAgZ2V0TGlzdCgpIHsNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgICBsaXN0RGF0YSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLmNvdW50Ow0KICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICAgICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVBZGQoKSB7DQogICAgICAgICAgICB0aGlzLmFkZCgpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgICAgICAgIGNvbnN0IGluZm9ySWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQoNCiAgICAgICAgICAgIHRoaXMuZm9ybSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93KSk7DQogICAgICAgICAgICB0aGlzLnRpdGxlID0gIue8lui+kSI7DQogICAgICAgICAgICB0aGlzLnNob3cgPSB0cnVlOw0KICAgICAgICAgICAgLy8gZ2V0RGF0YShpbmZvcklkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgLy8gICAgIHRoaXMuZWRpdChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgICAgIC8vIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgICAgICAgIGNvbnN0IGluZm9ySWRzID0gcm93LmlkIHx8IHRoaXMuaWRzLmpvaW4oIiwiKTsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgICAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOe8luWPt+S4uiInICsgaW5mb3JJZHMgKyAnIueahOaVsOaNrumhue+8nycpDQogICAgICAgICAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGVsRGF0YSh7b3BpZDppbmZvcklkc30pOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgICAgICB9LA0KICAgICAgICBoYW5kbGVDb3B5KHJvdykgew0KICAgICAgICAgICAgY29uc3QgY2xpcGJvYXJkT2JqID0gbmF2aWdhdG9yLmNsaXBib2FyZDsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLpk77mjqXlt7LlpI3liLYiLA0KICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgY2xpcGJvYXJkT2JqLndyaXRlVGV4dCgNCiAgICAgICAgICAgICAgICAiaHR0cHM6Ly9zYy5jbnVkai5jb20vaW5mb3I/aWQ9IiArIHJvdy5pZA0KICAgICAgICAgICAgKTsNCiAgICAgICAgfSwNCiAgICAgICAgcmVzZXQoKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgICAgICAgICAgaWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICB0aXRsZTogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIGNvbnRlbnQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgICAgICB9LA0KICAgICAgICBhZGQoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgICAgICB0aGlzLnRpdGxlID0gIua3u+WKoCI7DQogICAgICAgICAgICB0aGlzLnNob3cgPSB0cnVlOw0KICAgICAgICB9LA0KICAgICAgICBlZGl0KGRhdGEpIHsNCiAgICAgICAgICAgIHRoaXMudGl0bGUgPSAi57yW6L6RIjsNCiAgICAgICAgICAgIHRoaXMuc2hvdyA9IHRydWU7DQogICAgICAgICAgICB0aGlzLmZvcm0gPSBkYXRhOw0KICAgICAgICB9LA0KICAgICAgICBoYW5kbGVTdWJtaXQoKSB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGlkYXRlKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRlKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5mb3JtLmlkKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgYWRkRGF0YSh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgicmVmcmVzaCIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICBlZGl0RGF0YSh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCJyZWZyZXNoIik7DQogICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7flrozlloTkv6Hmga/lho3mj5DkuqQhIik7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgfSwNCn07DQo="}, {"version": 3, "sources": ["scene.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "scene.vue", "sourceRoot": "src/views/service", "sourcesContent": [" <!-- 场景分类 -->\r\n<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n        >\r\n            <el-form-item label=\"场景名称\" prop=\"scene_name\">\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.scene_name\"\r\n                    style=\"width: 300px\"\r\n                    placeholder=\"请输入场景名称\"\r\n                    :maxlength=\"60\"\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"\" prop=\"recommend\">\r\n              <el-select clearable v-model=\"queryParams.recommend\" placeholder=\"首页推荐\" style=\"width: 120px;\">\r\n                <el-option v-for=\"item in recOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    >添加场景</el-button\r\n                >\r\n            </el-col>\r\n\r\n\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"list\"\r\n        >\r\n\r\n              <el-table-column\r\n                label=\"序号\"\r\n                align=\"center\"\r\n                prop=\"id\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"场景名称\"\r\n                align=\"center\"\r\n                prop=\"scene_name\"\r\n                :show-overflow-tooltip=\"true\"\r\n            />\r\n            <el-table-column\r\n                label=\"场景排序\"\r\n                align=\"center\"\r\n                prop=\"scene_sort\"\r\n            />\r\n            <el-table-column prop=\"scene_pic\" label=\"场景展示图\" align=\"center\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.scene_pic\"\r\n                  :preview-src-list=\"[scope.row.scene_pic]\">\r\n                </el-image>\r\n              </template>\r\n            </el-table-column>\r\n           <el-table-column label=\"首页推荐\" align=\"center\" prop=\"recommend\">\r\n             <template slot-scope=\"scope\">\r\n               <el-switch @change=\"changeRecommend($event, scope.row)\" v-model=\"scope.row.recommend\" :active-value=\"'2'\"\r\n                 :inactive-value=\"'1'\"></el-switch>\r\n             </template>\r\n           </el-table-column>\r\n           <el-table-column label=\"添加时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                fixed=\"right\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n        <!-- 添加弹窗 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"show\"\r\n            width=\"70%\"\r\n            :before-close=\"() => (show = false)\"\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" :rules=\"rules\">\r\n                <el-form-item label=\"场景名称\" prop=\"scene_name\">\r\n                    <el-input\r\n                        clearable\r\n                        v-model=\"form.scene_name\"\r\n                        placeholder=\"请输入场景名称\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"场景排序\" prop=\"scene_sort\">\r\n                    <el-input\r\n                        clearable\r\n                        type=\"number\"\r\n                        v-model=\"form.scene_sort\"\r\n                        placeholder=\"请输入场景排序\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"场景展示图\" prop=\"scene_pic\">\r\n                    <ImageUpload\r\n                        @input=\"uploadSuccess($event)\"\r\n                        sizeTxt=\"1920X412\"\r\n                        style=\"width: 100%\"\r\n                        :value=\"form.scene_pic\"\r\n                        :limit=\"1\"\r\n                    ></ImageUpload>\r\n                </el-form-item>\r\n\r\n            </el-form>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"show = false\">取 消</el-button>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    :loading=\"loading\"\r\n                    @click=\"handleSubmit\"\r\n                    >确 定</el-button\r\n                >\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listData,\r\n    delData,\r\n    addData,\r\n    editData,\r\n} from \"@/api/service/scene.js\";\r\nexport default {\r\n    name: \"Infor\",\r\n    data() {\r\n        return {\r\n\r\n            loading: false,\r\n            show: false,\r\n            title: \"\",\r\n            form: {},\r\n            rules: {\r\n                scene_name: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请填写场景名称\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                scene_sort: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请输入场景排序\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                scene_pic: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请添加场景展示图\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 表格数据\r\n            list: [],\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                recommend:\"\",\r\n                scene_name:''\r\n            },\r\n            form: {\r\n\r\n            },\r\n            recOptions: [{\r\n                key: 2,\r\n                value: '推荐'\r\n              },\r\n              {\r\n                key: 1,\r\n                value: '不推荐'\r\n              },\r\n            ],\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n      changeRecommend(rec, row) {\r\n        editData({\r\n          id: row.id,\r\n          recommend: rec\r\n        }).then(() => {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          });\r\n         this.getList()\r\n        })\r\n      },\r\n      uploadSuccess(event) {\r\n          this.form.scene_pic = event;\r\n      },\r\n\r\n        getList() {\r\n            this.loading = true;\r\n            listData(this.queryParams).then((response) => {\r\n                this.list = response.data;\r\n                this.total = response.count;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.add();\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            const inforId = row.id || this.ids;\r\n\r\n            this.form = JSON.parse(JSON.stringify(row));\r\n            this.title = \"编辑\";\r\n            this.show = true;\r\n            // getData(inforId).then((response) => {\r\n            //     this.edit(response.data);\r\n            // });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const inforIds = row.id || this.ids.join(\",\");\r\n            this.$modal\r\n                .confirm('是否确认删除编号为\"' + inforIds + '\"的数据项？')\r\n                .then(function () {\r\n                    return delData({opid:inforIds});\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        handleCopy(row) {\r\n            const clipboardObj = navigator.clipboard;\r\n            this.$message({\r\n                message: \"链接已复制\",\r\n                type: \"success\",\r\n            });\r\n            clipboardObj.writeText(\r\n                \"https://sc.cnudj.com/infor?id=\" + row.id\r\n            );\r\n        },\r\n        reset() {\r\n            this.form = {\r\n                id: undefined,\r\n                title: undefined,\r\n                content: undefined,\r\n            };\r\n            this.resetForm(\"form\");\r\n        },\r\n        add() {\r\n            this.reset();\r\n            this.title = \"添加\";\r\n            this.show = true;\r\n        },\r\n        edit(data) {\r\n            this.title = \"编辑\";\r\n            this.show = true;\r\n            this.form = data;\r\n        },\r\n        handleSubmit() {\r\n            this.$refs.form.validate((validate) => {\r\n                if (validate) {\r\n                    this.loading = true;\r\n                    if (!this.form.id) {\r\n                        console.log(this.form);\r\n                        addData(this.form).then((response) => {\r\n                            this.$message({\r\n                                type: \"success\",\r\n                                message: \"操作成功!\",\r\n                            });\r\n                            this.loading = false;\r\n                            this.show = false;\r\n                            this.getList()\r\n                            this.$emit(\"refresh\");\r\n                        });\r\n                    } else {\r\n                        editData(this.form).then((response) => {\r\n                            this.$message({\r\n                                type: \"success\",\r\n                                message: \"操作成功!\",\r\n                            });\r\n                            this.loading = false;\r\n                            this.show = false;\r\n                            this.getList()\r\n\r\n                            this.$emit(\"refresh\");\r\n                        });\r\n                    }\r\n                } else {\r\n                    this.$modal.msgError(\"请完善信息再提交!\");\r\n                }\r\n            });\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}