{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\monitor\\job.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\monitor\\job.js", "mtime": 1750151093960}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listJob", "query", "request", "url", "method", "params", "get<PERSON>ob", "jobId", "addJob", "data", "updateJob", "<PERSON><PERSON><PERSON>", "changeJobStatus", "status", "runJob", "jobGroup"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/monitor/job.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询定时任务调度列表\r\nexport function listJob(query) {\r\n  return request({\r\n    url: '/schedule/job/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询定时任务调度详细\r\nexport function getJob(jobId) {\r\n  return request({\r\n    url: '/schedule/job/' + jobId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增定时任务调度\r\nexport function addJob(data) {\r\n  return request({\r\n    url: '/schedule/job',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改定时任务调度\r\nexport function updateJob(data) {\r\n  return request({\r\n    url: '/schedule/job',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除定时任务调度\r\nexport function delJob(jobId) {\r\n  return request({\r\n    url: '/schedule/job/' + jobId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 任务状态修改\r\nexport function changeJobStatus(jobId, status) {\r\n  const data = {\r\n    jobId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/schedule/job/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n\r\n// 定时任务立即执行一次\r\nexport function runJob(jobId, jobGroup) {\r\n  const data = {\r\n    jobId,\r\n    jobGroup\r\n  }\r\n  return request({\r\n    url: '/schedule/job/run',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,KAAK;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,MAAMA,CAACC,IAAI,EAAE;EAC3B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,SAASA,CAACD,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,MAAMA,CAACJ,KAAK,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,KAAK;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,eAAeA,CAACL,KAAK,EAAEM,MAAM,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXF,KAAK,EAALA,KAAK;IACLM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAGA;AACO,SAASK,MAAMA,CAACP,KAAK,EAAEQ,QAAQ,EAAE;EACtC,IAAMN,IAAI,GAAG;IACXF,KAAK,EAALA,KAAK;IACLQ,QAAQ,EAARA;EACF,CAAC;EACD,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}