{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\service\\classify.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\service\\classify.js", "mtime": 1750151093971}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listData", "params", "request", "url", "method", "treeData", "addData", "editData", "recData", "getData", "id", "concat", "delData", "maprecData"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/service/classify.js"], "sourcesContent": ["// 产品分类配置\r\nimport request from '@/utils/request'\r\n\r\n// 获取列表数据\r\nexport function listData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/classify/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取列表数据\r\nexport function treeData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/classify/tree',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 新增数据\r\nexport function addData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/classify/add',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n// 修改数据\r\nexport function editData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/classify/edit',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n// 推荐数据\r\nexport function recData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/classify/rec',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n// 获取详情数据\r\nexport function getData(id) {\r\n  return request({\r\n    url: `shop/admin/product/classify/detail/${id}`,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 删除数据\r\nexport function delData(id) {\r\n  return request({\r\n    url: 'shop/admin/product/classify/del?id='+id,\r\n    method: 'post',\r\n  })\r\n}\r\n// 场景推荐\r\nexport function maprecData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/classify/mapRec',\r\n    method: 'post',\r\n    params\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AADA;;AAGA;AACO,SAASC,QAAQA,CAACC,MAAM,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,QAAQA,CAACJ,MAAM,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,QAAQA,CAACN,MAAM,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASO,OAAOA,CAACP,MAAM,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASQ,OAAOA,CAACC,EAAE,EAAE;EAC1B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,wCAAAQ,MAAA,CAAwCD,EAAE,CAAE;IAC/CN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,OAAOA,CAACF,EAAE,EAAE;EAC1B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC,GAACO,EAAE;IAC7CN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASS,UAAUA,CAACZ,MAAM,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,MAAM;IACdH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}