{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\AppMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\AppMain.vue", "mtime": 1750151094167}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdBcHBNYWluJywNCiAgY29tcHV0ZWQ6IHsNCiAgICBjYWNoZWRWaWV3cygpIHsNCiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS50YWdzVmlldy5jYWNoZWRWaWV3cw0KICAgIH0sDQogICAga2V5KCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHJvdXRlLnBhdGgNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <section class=\"app-main\">\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'AppMain',\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 50px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.fixed-header+.app-main {\r\n  padding-top: 50px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 84px);\r\n  }\r\n\r\n  .fixed-header+.app-main {\r\n    padding-top: 84px;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 17px;\r\n  }\r\n}\r\n</style>\r\n"]}]}