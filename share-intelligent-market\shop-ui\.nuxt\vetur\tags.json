{"AddRess": {"description": "Auto imported from components/AddRess/index.vue"}, "FileUpload": {"description": "Auto imported from components/FileUpload/index.vue"}, "ImageUpload": {"description": "Auto imported from components/ImageUpload/index.vue"}, "IssueAccurate": {"description": "Auto imported from components/issue/accurate.vue"}, "IssueEnquiryDetails": {"description": "Auto imported from components/issue/enquiryDetails.vue"}, "IssueTopspeed": {"description": "Auto imported from components/issue/topspeed.vue"}, "ShopCart": {"description": "Auto imported from components/shop/cart.vue"}, "ShopCompanyProfile": {"description": "Auto imported from components/shop/company-profile.vue"}, "ShopCompany": {"description": "Auto imported from components/shop/company.vue"}, "ShopEcharts": {"description": "Auto imported from components/shop/echarts.vue"}, "ShopEditor": {"description": "Auto imported from components/shop/editor.vue"}, "ShopFooter": {"description": "Auto imported from components/shop/footer.vue"}, "ShopOrderForm": {"description": "Auto imported from components/shop/order-form.vue"}, "ShopProductStatus": {"description": "Auto imported from components/shop/product-status.vue"}, "ShopScreen": {"description": "Auto imported from components/shop/screen.vue"}, "ShopSearchBar": {"description": "Auto imported from components/shop/search-bar.vue"}, "ShopItem": {"description": "Auto imported from components/shop/shop-item.vue"}, "ShopTabBar": {"description": "Auto imported from components/shop/tab_bar.vue"}, "LayoutNavbar": {"description": "Auto imported from components/layout/Navbar.vue"}, "CommonPagination": {"description": "Auto imported from components/common/Pagination/index.vue"}, "CommonShopNoData": {"description": "Auto imported from components/common/shop/no-data.vue"}, "LayoutSidebar": {"description": "Auto imported from components/layout/Sidebar/index.vue"}, "LayoutSidebarItem": {"description": "Auto imported from components/layout/Sidebar/item.vue"}, "CommonCenterEnterprise": {"description": "Auto imported from components/common/center/enterprise/index.vue"}, "CommonCenterProfileCompany": {"description": "Auto imported from components/common/center/profile/company.vue"}, "CommonCenterProfileInfo": {"description": "Auto imported from components/common/center/profile/info.vue"}, "CommonCenterProfilePhone": {"description": "Auto imported from components/common/center/profile/phone.vue"}, "CommonCenterProfilePwd": {"description": "Auto imported from components/common/center/profile/pwd.vue"}, "CommonShopProductDtModal": {"description": "Auto imported from components/common/shop/product/dt-modal.vue"}, "CommonShopProductProductdetail": {"description": "Auto imported from components/common/shop/product/productdetail.vue"}, "CommonSystemOrgAddDep": {"description": "Auto imported from components/common/system/org/add-dep.vue"}, "CommonSystemOrgList": {"description": "Auto imported from components/common/system/org/list.vue"}, "CommonSystemPermList": {"description": "Auto imported from components/common/system/perm/list.vue"}, "CommonSystemPermUserModal": {"description": "Auto imported from components/common/system/perm/user-modal.vue"}, "CommonSystemRoleList": {"description": "Auto imported from components/common/system/role/list.vue"}, "CommonSystemRoleUpdateRole": {"description": "Auto imported from components/common/system/role/update-role.vue"}}