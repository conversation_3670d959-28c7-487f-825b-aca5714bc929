{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\plugins\\tab.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\plugins\\tab.js", "mtime": 1750151094192}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_router", "_default", "exports", "default", "refreshPage", "obj", "_router$currentRoute", "router", "currentRoute", "path", "query", "matched", "undefined", "for<PERSON>ach", "m", "components", "name", "includes", "store", "dispatch", "then", "_obj", "replace", "closeOpenPage", "push", "closePage", "_ref", "last<PERSON><PERSON>", "closeAllPage", "closeLeftPage", "closeRightPage", "closeOtherPage", "openPage", "title", "url", "params", "meta", "updatePage"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/plugins/tab.js"], "sourcesContent": ["import store from '@/store'\r\nimport router from '@/router';\r\n\r\nexport default {\r\n  // 刷新当前tab页签\r\n  refreshPage(obj) {\r\n    const { path, query, matched } = router.currentRoute;\r\n    if (obj === undefined) {\r\n      matched.forEach((m) => {\r\n        if (m.components && m.components.default && m.components.default.name) {\r\n          if (!['Layout', 'ParentView'].includes(m.components.default.name)) {\r\n            obj = { name: m.components.default.name, path: path, query: query };\r\n          }\r\n        }\r\n      });\r\n    }\r\n    return store.dispatch('tagsView/delCachedView', obj).then(() => {\r\n      const { path, query } = obj\r\n      router.replace({\r\n        path: '/redirect' + path,\r\n        query: query\r\n      })\r\n    })\r\n  },\r\n  // 关闭当前tab页签，打开新页签\r\n  closeOpenPage(obj) {\r\n    store.dispatch(\"tagsView/delView\", router.currentRoute);\r\n    if (obj !== undefined) {\r\n      return router.push(obj);\r\n    }\r\n  },\r\n  // 关闭指定tab页签\r\n  closePage(obj) {\r\n    if (obj === undefined) {\r\n      return store.dispatch('tagsView/delView', router.currentRoute).then(({ lastPath }) => {\r\n        return router.push(lastPath || '/');\r\n      });\r\n    }\r\n    return store.dispatch('tagsView/delView', obj);\r\n  },\r\n  // 关闭所有tab页签\r\n  closeAllPage() {\r\n    return store.dispatch('tagsView/delAllViews');\r\n  },\r\n  // 关闭左侧tab页签\r\n  closeLeftPage(obj) {\r\n    return store.dispatch('tagsView/delLeftTags', obj || router.currentRoute);\r\n  },\r\n  // 关闭右侧tab页签\r\n  closeRightPage(obj) {\r\n    return store.dispatch('tagsView/delRightTags', obj || router.currentRoute);\r\n  },\r\n  // 关闭其他tab页签\r\n  closeOtherPage(obj) {\r\n    return store.dispatch('tagsView/delOthersViews', obj || router.currentRoute);\r\n  },\r\n  // 添加tab页签\r\n  openPage(title, url, params) {\r\n    var obj = { path: url, meta: { title: title } }\r\n    store.dispatch('tagsView/addView', obj);\r\n    return router.push({ path: url, query: params });\r\n  },\r\n  // 修改tab页签\r\n  updatePage(obj) {\r\n    return store.dispatch('tagsView/updateVisitedView', obj);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA8B,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEf;EACb;EACAC,WAAW,WAAXA,WAAWA,CAACC,GAAG,EAAE;IACf,IAAAC,oBAAA,GAAiCC,eAAM,CAACC,YAAY;MAA5CC,IAAI,GAAAH,oBAAA,CAAJG,IAAI;MAAEC,KAAK,GAAAJ,oBAAA,CAALI,KAAK;MAAEC,OAAO,GAAAL,oBAAA,CAAPK,OAAO;IAC5B,IAAIN,GAAG,KAAKO,SAAS,EAAE;MACrBD,OAAO,CAACE,OAAO,CAAC,UAACC,CAAC,EAAK;QACrB,IAAIA,CAAC,CAACC,UAAU,IAAID,CAAC,CAACC,UAAU,CAACZ,OAAO,IAAIW,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI,EAAE;UACrE,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,CAACC,QAAQ,CAACH,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI,CAAC,EAAE;YACjEX,GAAG,GAAG;cAAEW,IAAI,EAAEF,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI;cAAEP,IAAI,EAAEA,IAAI;cAAEC,KAAK,EAAEA;YAAM,CAAC;UACrE;QACF;MACF,CAAC,CAAC;IACJ;IACA,OAAOQ,cAAK,CAACC,QAAQ,CAAC,wBAAwB,EAAEd,GAAG,CAAC,CAACe,IAAI,CAAC,YAAM;MAC9D,IAAAC,IAAA,GAAwBhB,GAAG;QAAnBI,IAAI,GAAAY,IAAA,CAAJZ,IAAI;QAAEC,KAAK,GAAAW,IAAA,CAALX,KAAK;MACnBH,eAAM,CAACe,OAAO,CAAC;QACbb,IAAI,EAAE,WAAW,GAAGA,IAAI;QACxBC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAa,aAAa,WAAbA,aAAaA,CAAClB,GAAG,EAAE;IACjBa,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEZ,eAAM,CAACC,YAAY,CAAC;IACvD,IAAIH,GAAG,KAAKO,SAAS,EAAE;MACrB,OAAOL,eAAM,CAACiB,IAAI,CAACnB,GAAG,CAAC;IACzB;EACF,CAAC;EACD;EACAoB,SAAS,WAATA,SAASA,CAACpB,GAAG,EAAE;IACb,IAAIA,GAAG,KAAKO,SAAS,EAAE;MACrB,OAAOM,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEZ,eAAM,CAACC,YAAY,CAAC,CAACY,IAAI,CAAC,UAAAM,IAAA,EAAkB;QAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;QAC7E,OAAOpB,eAAM,CAACiB,IAAI,CAACG,QAAQ,IAAI,GAAG,CAAC;MACrC,CAAC,CAAC;IACJ;IACA,OAAOT,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEd,GAAG,CAAC;EAChD,CAAC;EACD;EACAuB,YAAY,WAAZA,YAAYA,CAAA,EAAG;IACb,OAAOV,cAAK,CAACC,QAAQ,CAAC,sBAAsB,CAAC;EAC/C,CAAC;EACD;EACAU,aAAa,WAAbA,aAAaA,CAACxB,GAAG,EAAE;IACjB,OAAOa,cAAK,CAACC,QAAQ,CAAC,sBAAsB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC3E,CAAC;EACD;EACAsB,cAAc,WAAdA,cAAcA,CAACzB,GAAG,EAAE;IAClB,OAAOa,cAAK,CAACC,QAAQ,CAAC,uBAAuB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC5E,CAAC;EACD;EACAuB,cAAc,WAAdA,cAAcA,CAAC1B,GAAG,EAAE;IAClB,OAAOa,cAAK,CAACC,QAAQ,CAAC,yBAAyB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC9E,CAAC;EACD;EACAwB,QAAQ,WAARA,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAE;IAC3B,IAAI9B,GAAG,GAAG;MAAEI,IAAI,EAAEyB,GAAG;MAAEE,IAAI,EAAE;QAAEH,KAAK,EAAEA;MAAM;IAAE,CAAC;IAC/Cf,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEd,GAAG,CAAC;IACvC,OAAOE,eAAM,CAACiB,IAAI,CAAC;MAAEf,IAAI,EAAEyB,GAAG;MAAExB,KAAK,EAAEyB;IAAO,CAAC,CAAC;EAClD,CAAC;EACD;EACAE,UAAU,WAAVA,UAAUA,CAAChC,GAAG,EAAE;IACd,OAAOa,cAAK,CAACC,QAAQ,CAAC,4BAA4B,EAAEd,GAAG,CAAC;EAC1D;AACF,CAAC", "ignoreList": []}]}