{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\TopNav\\index.vue", "mtime": 1750151094156}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item :style=\"{'--theme': theme}\" :index=\"item.path\" :key=\"index\" v-if=\"index < visibleNumber\"\r\n        ><svg-icon :icon-class=\"item.meta.icon\" />\r\n        {{ item.meta.title }}</el-menu-item\r\n      >\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu :style=\"{'--theme': theme}\" index=\"more\" v-if=\"topMenus.length > visibleNumber\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          :index=\"item.path\"\r\n          :key=\"index\"\r\n          v-if=\"index >= visibleNumber\"\r\n          ><svg-icon :icon-class=\"item.meta.icon\" />\r\n          {{ item.meta.title }}</el-menu-item\r\n        >\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from \"@/router\";\r\n\r\n// 隐藏侧边栏路由\r\nconst hideList = ['/index', '/user/profile'];\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined\r\n    };\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      let topMenus = [];\r\n      this.routers.map((menu) => {\r\n        if (menu.hidden !== true) {\r\n          // 兼容顶部栏一级菜单内部跳转\r\n          if (menu.path === \"/\") {\r\n              topMenus.push(menu.children[0]);\r\n          } else {\r\n              topMenus.push(menu);\r\n          }\r\n        }\r\n      });\r\n      return topMenus;\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters;\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = [];\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            if(router.path === \"/\") {\r\n              router.children[item].path = \"/\" + router.children[item].path;\r\n            } else {\r\n              if(!this.ishttp(router.children[item].path)) {\r\n                router.children[item].path = router.path + \"/\" + router.children[item].path;\r\n              }\r\n            }\r\n            router.children[item].parentPath = router.path;\r\n          }\r\n          childrenMenus.push(router.children[item]);\r\n        }\r\n      });\r\n      return constantRoutes.concat(childrenMenus);\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path;\r\n      let activePath = path;\r\n      if (path !== undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) === -1) {\r\n        const tmpPath = path.substring(1, path.length);\r\n        activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"));\r\n        this.$store.dispatch('app/toggleSideBarHide', false);\r\n      } else if(!this.$route.children) {\r\n        activePath = path;\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n      this.activeRoutes(activePath);\r\n      return activePath;\r\n    },\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber();\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width / 3;\r\n      this.visibleNumber = parseInt(width / 85);\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      this.currentIndex = key;\r\n      const route = this.routers.find(item => item.path === key);\r\n      if (this.ishttp(key)) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, \"_blank\");\r\n      } else if (!route || !route.children) {\r\n        // 没有子路由路径内部打开\r\n        this.$router.push({ path: key });\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      } else {\r\n        // 显示左侧联动菜单\r\n        this.activeRoutes(key);\r\n        this.$store.dispatch('app/toggleSideBarHide', false);\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = [];\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\r\n            routes.push(item);\r\n          }\r\n        });\r\n      }\r\n      if(routes.length > 0) {\r\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes);\r\n      }\r\n    },\r\n    ishttp(url) {\r\n      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.topmenu-container.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\r\n  border-bottom: 2px solid #{'var(--theme)'} !important;\r\n  color: #303133;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n</style>\r\n"]}]}