# Tomcat
server:
  port: 9700

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-auth
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ca6f4dcd-0826-4ad7-9eff-9180f9a832e1
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ca6f4dcd-0826-4ad7-9eff-9180f9a832e1
        file-extension: yaml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true

# SSO客户端配置
sso:
  client:
    # SSO服务器地址
    server-url: http://localhost:9100
    # 客户端ID
    client-id: market
    # 客户端密钥
    client-secret: market_2024#RuoYi@Share$Key!9999
    # 回调地址（后端回调地址）
    callback-url: http://localhost:9700/sso/callback
    # SSO服务接口路径
    login-url: /sso/login
    token-url: /sso/token
    validate-url: /sso/validate
    user-info-url: /sso/userinfo
    logout-url: /sso/logout

# 全网智能通讯平台配置
qwt:
  login-name: LHBA2010217
  password: LHBA2010217a!
  sign-name: 【易复材】
  api-url: https://wosdk.028lk.com:7072/Api
  fee-type: 2