{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\demand_joint\\index.vue?vue&type=template&id=91037546", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\demand_joint\\index.vue", "mtime": 1750151094256}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}