{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\list.vue", "mtime": 1750151094225}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_productdetail", "_interopRequireDefault", "require", "_add", "_orders", "_eProgress", "_eSort", "_list", "_util", "components", "collectionAdd", "productdetail", "collectionList", "eProgress", "eSort", "data", "loading", "showSearch", "total", "status", "productStatus", "form", "queryParams", "pageNum", "pageSize", "name", "undefined", "product_no", "type", "central_status", "list", "dialogVisible", "collectionDialogTitle", "title", "opForm", "opid", "note", "created", "getList", "getEnum", "methods", "handleDeatils", "row", "$refs", "open", "id", "_this", "listEnum", "then", "res", "centralStatus", "_this2", "listData", "count", "handleQuery", "page", "reset<PERSON><PERSON>y", "resetForm", "handleProgress", "progress", "handleSort", "sort", "handleAdd", "add", "edit", "handleDelete", "val", "central_note", "handleOp", "_this3", "opCentral", "$message", "message"], "sources": ["src/views/central/list.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\" prop=\"central_status\">\r\n            <el-select clearable v-model=\"queryParams.central_status\" placeholder=\"集采状态\" size='small'>\r\n              <el-option v-for=\"item in status\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select clearable v-model=\"queryParams.status\" placeholder=\"审核状态\" size='small'>\r\n              <el-option v-for=\"item in productStatus\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='product_no'>\r\n            <el-input clearable v-model=\"queryParams.product_no\" placeholder=\"集采编号\"  size='small'\r\n              style=\"width: 200px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='name'>\r\n            <el-input clearable v-model=\"queryParams.name\" placeholder=\"产品名称\" :maxlength='50' size='small'\r\n              style=\"width: 300px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd(1)\">发起集采</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"发起时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n          <el-table-column label=\"集采编号\" align=\"center\" prop=\"system_no\" width=\"140\" />\r\n          <el-table-column label=\"产品编码\" align=\"center\" prop=\"product_no\" width=\"140\" />\r\n          <el-table-column label=\"产品名称\" align=\"center\" prop=\"name\" width=\"180\"  :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"集采价格\" align=\"center\" prop=\"tax_price\" width=\"100\" />\r\n          <el-table-column label=\"单位\" align=\"center\" prop=\"unit\" width=\"100\" />\r\n          <el-table-column label=\"产品型号\" align=\"center\" prop=\"model\" width=\"100\" />\r\n          <el-table-column label=\"供应商\" align=\"center\" prop=\"enterprise_name\" width=\"240\"  :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"目标数量\" align=\"center\" prop=\"central_goal\" width=\"100\" />\r\n          <el-table-column label=\"预约数量\" align=\"center\" prop=\"central_real\" width=\"100\" />\r\n          <el-table-column label=\"预约进度\" align=\"center\" width=\"120\">\r\n            <template slot-scope='scope'>\r\n              <div>\r\n                <span>{{scope.row.central_percent}}</span>\r\n                <el-button type=\"text\" icon=\"el-icon-edit\" size=\"mini\" @click=\"handleProgress(scope.row)\">编辑</el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"预约列表\" align=\"center\" width=\"120px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\" size=\"mini\" @click=\"handleAdd(3,scope.row)\">查看\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"截止时间\" align=\"center\" prop=\"deadline\" width=\"160\" />\r\n          <el-table-column label=\"排序\" align=\"center\" width=\"120\">\r\n            <template slot-scope='scope'>\r\n              <div>\r\n                <span>{{scope.row.sorts}}</span>\r\n                <el-button type=\"text\" icon=\"el-icon-edit\" size=\"mini\" @click=\"handleSort(scope.row)\">编辑</el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"central_status\" width=\"120px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"scope.row.central_status != 'CANCEL'\">\r\n                {{scope.row.central_statusStr}}\r\n              </div>\r\n              <div v-if=\"scope.row.central_status == 'CANCEL'\">\r\n                已取消\r\n                <el-button type=\"text\" @click=\"handleDelete(scope.row,1)\">原因</el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" width=\"180\" fixed='right'>\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\" size=\"mini\" @click=\"handleDeatils(scope.row)\">详情\r\n              </el-button>\r\n              <el-button type=\"text\" icon=\"el-icon-edit\" size=\"mini\" @click=\"handleAdd(2,scope.row)\">编辑\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.central_status == 'GOING'\" type=\"text\" icon=\"el-icon-warning-outline\" size=\"mini\"\r\n                @click=\"handleDelete(scope.row,2)\">取消</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog :title=\"title==1?'集采停止原因':'确定停止集采？'\" :visible.sync=\"dialogVisible\" width=\"30%\" center>\r\n      <el-input type=\"textarea\" :rows=\"5\" placeholder=\"请输入停止原因\" :disabled=\"title==1?true:false\"\r\n        v-model=\"opForm.note\">\r\n      </el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"title == 2\" type=\"primary\" @click=\"handleOp\">确 定</el-button>\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 集采详情 -->\r\n    <collectionAdd ref=\"collectionAdd\"></collectionAdd>\r\n    <!-- 采集列表 -->\r\n    <collectionList ref=\"collectionList\" :title=\"collectionDialogTitle\"></collectionList>\r\n    <!-- 修改进度弹窗 -->\r\n    <e-progress ref='progress'></e-progress>\r\n    <!-- 修改排序弹窗 -->\r\n    <e-sort ref='sort'></e-sort>\r\n    <productdetail ref='productdetail' @refresh=\"getList\"></productdetail>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import productdetail from \"../store/components/productdetail.vue\";\r\n  import collectionAdd from \"./components/add.vue\";\r\n  import collectionList from \"./components/orders.vue\";\r\n  import eProgress from \"./components/e-progress.vue\";\r\n  import eSort from \"./components/e-sort.vue\";\r\n  import { listData, opCentral } from '@/api/central/list';\r\n  import { listEnum } from '@/api/tool/util';\r\n  export default {\r\n    components: {\r\n      collectionAdd,\r\n      productdetail,\r\n      collectionList,\r\n      eProgress,\r\n      eSort\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 状态\r\n        status: [],\r\n        // 商品状态\r\n        productStatus: [],\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          product_no:undefined,\r\n          type: 'CENTRAL',\r\n          central_status: undefined,\r\n          status: undefined\r\n        },\r\n        // 表格数据\r\n        list: [],\r\n        // 取消弹窗显示\r\n        dialogVisible: false,\r\n        // 发起采集弹窗\r\n        collectionDialogTitle: '',\r\n        // 本页面弹窗标题\r\n        title: '',\r\n        opForm: {\r\n          opid: undefined,\r\n          status: undefined,\r\n          note: undefined\r\n        }\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n      this.getEnum()\r\n    },\r\n    methods: {\r\n      // 详情\r\n      handleDeatils(row) {\r\n        this.$refs.productdetail.open(row.id)\r\n      },\r\n      /* 获取枚举 */\r\n      getEnum() {\r\n        listEnum().then(res => {\r\n          this.status = res.data.centralStatus;\r\n          this.productStatus = res.data.productStatus;\r\n        })\r\n      },\r\n      /** 查询用户列表 */\r\n      getList() {\r\n        listData(this.queryParams).then(res => {\r\n          this.list = res.data;\r\n          this.total = res.count;\r\n        })\r\n      },\r\n      /** 表单搜索 */\r\n      handleQuery() {\r\n        this.form.page = 1;\r\n        this.getList();\r\n      },\r\n      // 重置\r\n      resetQuery() {\r\n        this.resetForm('queryForm')\r\n        this.getList();\r\n      },\r\n\r\n      /* 编辑进度 */\r\n      handleProgress(row) {\r\n        this.$refs.progress.open(row);\r\n      },\r\n      /* 编辑排序 */\r\n      handleSort(row) {\r\n        this.$refs.sort.open(row);\r\n      },\r\n      /** 发起采集 */\r\n      handleAdd(type, row) {\r\n        if (type == 1) {\r\n          this.$refs.collectionAdd.add();\r\n        } else if (type == 2) {\r\n          this.$refs.collectionAdd.edit(row.id);\r\n        } else if (type == 3) {\r\n          this.collectionDialogTitle = '预约集采列表';\r\n          this.$refs.collectionList.getList(row.id)\r\n        }\r\n      },\r\n      /** 取消集采 */\r\n      handleDelete(row, val) {\r\n        // val 1是取消原因 2是取消采集\r\n        this.dialogVisible = true;\r\n        this.title = val;\r\n        this.opForm = {\r\n          opid: row.id,\r\n          status: 'CANCEL',\r\n          note: undefined\r\n        }\r\n        if(val == 1) {\r\n          this.opForm.note = row.central_note;\r\n        }\r\n      },\r\n      /* 修改状态 */\r\n      handleOp() {\r\n        opCentral(this.opForm).then(res => {\r\n          this.dialogVisible = false;\r\n          this.$message({type: 'success', message: '操作成功!'});\r\n          this.getList()\r\n        })\r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;AAyHA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,IAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAO,UAAA;IACAC,aAAA,EAAAA,YAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,cAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,KAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,MAAA;MACA;MACAC,aAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,UAAA,EAAAD,SAAA;QACAE,IAAA;QACAC,cAAA,EAAAH,SAAA;QACAP,MAAA,EAAAO;MACA;MACA;MACAI,IAAA;MACA;MACAC,aAAA;MACA;MACAC,qBAAA;MACA;MACAC,KAAA;MACAC,MAAA;QACAC,IAAA,EAAAT,SAAA;QACAP,MAAA,EAAAO,SAAA;QACAU,IAAA,EAAAV;MACA;IACA;EACA;EACAW,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,GAAA;MACA,KAAAC,KAAA,CAAAhC,aAAA,CAAAiC,IAAA,CAAAF,GAAA,CAAAG,EAAA;IACA;IACA,UACAN,OAAA,WAAAA,QAAA;MAAA,IAAAO,KAAA;MACA,IAAAC,cAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAA3B,MAAA,GAAA8B,GAAA,CAAAlC,IAAA,CAAAmC,aAAA;QACAJ,KAAA,CAAA1B,aAAA,GAAA6B,GAAA,CAAAlC,IAAA,CAAAK,aAAA;MACA;IACA;IACA,aACAkB,OAAA,WAAAA,QAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,cAAA,OAAA9B,WAAA,EAAA0B,IAAA,WAAAC,GAAA;QACAE,MAAA,CAAArB,IAAA,GAAAmB,GAAA,CAAAlC,IAAA;QACAoC,MAAA,CAAAjC,KAAA,GAAA+B,GAAA,CAAAI,KAAA;MACA;IACA;IACA,WACAC,WAAA,WAAAA,YAAA;MACA,KAAAjC,IAAA,CAAAkC,IAAA;MACA,KAAAjB,OAAA;IACA;IACA;IACAkB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAnB,OAAA;IACA;IAEA,UACAoB,cAAA,WAAAA,eAAAhB,GAAA;MACA,KAAAC,KAAA,CAAAgB,QAAA,CAAAf,IAAA,CAAAF,GAAA;IACA;IACA,UACAkB,UAAA,WAAAA,WAAAlB,GAAA;MACA,KAAAC,KAAA,CAAAkB,IAAA,CAAAjB,IAAA,CAAAF,GAAA;IACA;IACA,WACAoB,SAAA,WAAAA,UAAAlC,IAAA,EAAAc,GAAA;MACA,IAAAd,IAAA;QACA,KAAAe,KAAA,CAAAjC,aAAA,CAAAqD,GAAA;MACA,WAAAnC,IAAA;QACA,KAAAe,KAAA,CAAAjC,aAAA,CAAAsD,IAAA,CAAAtB,GAAA,CAAAG,EAAA;MACA,WAAAjB,IAAA;QACA,KAAAI,qBAAA;QACA,KAAAW,KAAA,CAAA/B,cAAA,CAAA0B,OAAA,CAAAI,GAAA,CAAAG,EAAA;MACA;IACA;IACA,WACAoB,YAAA,WAAAA,aAAAvB,GAAA,EAAAwB,GAAA;MACA;MACA,KAAAnC,aAAA;MACA,KAAAE,KAAA,GAAAiC,GAAA;MACA,KAAAhC,MAAA;QACAC,IAAA,EAAAO,GAAA,CAAAG,EAAA;QACA1B,MAAA;QACAiB,IAAA,EAAAV;MACA;MACA,IAAAwC,GAAA;QACA,KAAAhC,MAAA,CAAAE,IAAA,GAAAM,GAAA,CAAAyB,YAAA;MACA;IACA;IACA,UACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,eAAA,OAAApC,MAAA,EAAAc,IAAA,WAAAC,GAAA;QACAoB,MAAA,CAAAtC,aAAA;QACAsC,MAAA,CAAAE,QAAA;UAAA3C,IAAA;UAAA4C,OAAA;QAAA;QACAH,MAAA,CAAA/B,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}