{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\index.vue?vue&type=style&index=0&id=39cfdb14&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\index.vue", "mtime": 1750151094310}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750495809569}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpib2R5LCBodG1sew0KICBtYXJnaW46IDA7DQogIHBhZGRpbmc6IDA7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIC1tb3otb3N4LWZvbnQtc21vb3RoaW5nOiBncmF5c2NhbGU7DQogIC13ZWJraXQtZm9udC1zbW9vdGhpbmc6IGFudGlhbGlhc2VkOw0KICB0ZXh0LXJlbmRlcmluZzogb3B0aW1pemVMZWdpYmlsaXR5Ow0KICBmb250LWZhbWlseTogLWFwcGxlLXN5c3RlbSxCbGlua01hY1N5c3RlbUZvbnQsU2Vnb2UgVUksSGVsdmV0aWNhLEFyaWFsLHNhbnMtc2VyaWYsQXBwbGUgQ29sb3IgRW1vamksU2Vnb2UgVUkgRW1vamk7DQp9DQoNCmlucHV0LCB0ZXh0YXJlYXsNCiAgZm9udC1mYW1pbHk6IC1hcHBsZS1zeXN0ZW0sQmxpbmtNYWNTeXN0ZW1Gb250LFNlZ29lIFVJLEhlbHZldGljYSxBcmlhbCxzYW5zLXNlcmlmLEFwcGxlIENvbG9yIEVtb2ppLFNlZ29lIFVJIEVtb2ppOw0KfQ0KDQouZWRpdG9yLXRhYnN7DQogIGJhY2tncm91bmQ6ICMxMjEzMTU7DQogIC5lbC10YWJzX19oZWFkZXJ7DQogICAgbWFyZ2luOiAwOw0KICAgIGJvcmRlci1ib3R0b20tY29sb3I6ICMxMjEzMTU7DQogICAgLmVsLXRhYnNfX25hdnsNCiAgICAgIGJvcmRlci1jb2xvcjogIzEyMTMxNTsNCiAgICB9DQogIH0NCiAgLmVsLXRhYnNfX2l0ZW17DQogICAgaGVpZ2h0OiAzMnB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAzMnB4Ow0KICAgIGNvbG9yOiAjODg4YThlOw0KICAgIGJvcmRlci1sZWZ0OiAxcHggc29saWQgIzEyMTMxNSAhaW1wb3J0YW50Ow0KICAgIGJhY2tncm91bmQ6ICMzNjM2MzY7DQogICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgdXNlci1zZWxlY3Q6IG5vbmU7DQogIH0NCiAgLmVsLXRhYnNfX2l0ZW0uaXMtYWN0aXZlew0KICAgIGJhY2tncm91bmQ6ICMxZTFlMWU7DQogICAgYm9yZGVyLWJvdHRvbS1jb2xvcjogIzFlMWUxZSFpbXBvcnRhbnQ7DQogICAgY29sb3I6ICNmZmY7DQogIH0NCiAgLmVsLWljb24tZWRpdHsNCiAgICBjb2xvcjogI2YxZmE4YzsNCiAgfQ0KICAuZWwtaWNvbi1kb2N1bWVudHsNCiAgICBjb2xvcjogI2E5NTgxMjsNCiAgfQ0KfQ0KDQovLyBob21lDQoucmlnaHQtc2Nyb2xsYmFyIHsNCiAgLmVsLXNjcm9sbGJhcl9fdmlldyB7DQogICAgcGFkZGluZzogMTJweCAxOHB4IDE1cHggMTVweDsNCiAgfQ0KfQ0KLmxlZnQtc2Nyb2xsYmFyIC5lbC1zY3JvbGxiYXJfX3dyYXAgew0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICBvdmVyZmxvdy14OiBoaWRkZW4gIWltcG9ydGFudDsNCiAgbWFyZ2luLWJvdHRvbTogMCAhaW1wb3J0YW50Ow0KfQ0KLmNlbnRlci10YWJzew0KICAuZWwtdGFic19faGVhZGVyew0KICAgIG1hcmdpbi1ib3R0b206IDAhaW1wb3J0YW50Ow0KICB9DQogIC5lbC10YWJzX19pdGVtew0KICAgIHdpZHRoOiA1MCU7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB9DQogIC5lbC10YWJzX19uYXZ7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCn0NCi5yZWctaXRlbXsNCiAgcGFkZGluZzogMTJweCA2cHg7DQogIGJhY2tncm91bmQ6ICNmOGY4Zjg7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAuY2xvc2UtYnRuew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICByaWdodDogLTZweDsNCiAgICB0b3A6IC02cHg7DQogICAgZGlzcGxheTogYmxvY2s7DQogICAgd2lkdGg6IDE2cHg7DQogICAgaGVpZ2h0OiAxNnB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAxNnB4Ow0KICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4yKTsNCiAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgY29sb3I6ICNmZmY7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIHotaW5kZXg6IDE7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAmOmhvdmVyew0KICAgICAgYmFja2dyb3VuZDogcmdiYSgyMTAsIDIzLCAyMywgMC41KQ0KICAgIH0NCiAgfQ0KICAmICsgLnJlZy1pdGVtew0KICAgIG1hcmdpbi10b3A6IDE4cHg7DQogIH0NCn0NCi5hY3Rpb24tYmFyew0KICAmIC5lbC1idXR0b24rLmVsLWJ1dHRvbiB7DQogICAgbWFyZ2luLWxlZnQ6IDE1cHg7DQogIH0NCiAgJiBpIHsNCiAgICBmb250LXNpemU6IDIwcHg7DQogICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgdG9wOiAtMXB4Ow0KICB9DQp9DQoNCi5jdXN0b20tdHJlZS1ub2Rlew0KICB3aWR0aDogMTAwJTsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICAubm9kZS1vcGVyYXRpb257DQogICAgZmxvYXQ6IHJpZ2h0Ow0KICB9DQogIGlbY2xhc3MqPSJlbC1pY29uIl0gKyBpW2NsYXNzKj0iZWwtaWNvbiJdew0KICAgIG1hcmdpbi1sZWZ0OiA2cHg7DQogIH0NCiAgLmVsLWljb24tcGx1c3sNCiAgICBjb2xvcjogIzQwOUVGRjsNCiAgfQ0KICAuZWwtaWNvbi1kZWxldGV7DQogICAgY29sb3I6ICMxNTdhMGM7DQogIH0NCn0NCg0KLmxlZnQtc2Nyb2xsYmFyIC5lbC1zY3JvbGxiYXJfX3ZpZXd7DQogIG92ZXJmbG93LXg6IGhpZGRlbjsNCn0NCg0KLmVsLXJhdGV7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgdmVydGljYWwtYWxpZ246IHRleHQtdG9wOw0KfQ0KLmVsLXVwbG9hZF9fdGlwew0KICBsaW5lLWhlaWdodDogMS4yOw0KfQ0KDQokc2VsZWN0ZWRDb2xvcjogI2Y2ZjdmZjsNCiRsaWdodGVyQmx1ZTogIzQwOUVGRjsNCg0KLmNvbnRhaW5lciB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCn0NCg0KLmNvbXBvbmVudHMtbGlzdCB7DQogIHBhZGRpbmc6IDhweDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgaGVpZ2h0OiAxMDAlOw0KICAuY29tcG9uZW50cy1pdGVtIHsNCiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgd2lkdGg6IDQ4JTsNCiAgICBtYXJnaW46IDElOw0KICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwbXMgIWltcG9ydGFudDsNCiAgfQ0KfQ0KLmNvbXBvbmVudHMtZHJhZ2dhYmxlew0KICBwYWRkaW5nLWJvdHRvbTogMjBweDsNCn0NCi5jb21wb25lbnRzLXRpdGxlew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMjIyOw0KICBtYXJnaW46IDZweCAycHg7DQogIC5zdmctaWNvbnsNCiAgICBjb2xvcjogIzY2NjsNCiAgICBmb250LXNpemU6IDE4cHg7DQogIH0NCn0NCg0KLmNvbXBvbmVudHMtYm9keSB7DQogIHBhZGRpbmc6IDhweCAxMHB4Ow0KICBiYWNrZ3JvdW5kOiAkc2VsZWN0ZWRDb2xvcjsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjdXJzb3I6IG1vdmU7DQogIGJvcmRlcjogMXB4IGRhc2hlZCAkc2VsZWN0ZWRDb2xvcjsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICAuc3ZnLWljb257DQogICAgY29sb3I6ICM3Nzc7DQogICAgZm9udC1zaXplOiAxNXB4Ow0KICB9DQogICY6aG92ZXIgew0KICAgIGJvcmRlcjogMXB4IGRhc2hlZCAjNzg3YmU4Ow0KICAgIGNvbG9yOiAjNzg3YmU4Ow0KICAgIC5zdmctaWNvbiB7DQogICAgICBjb2xvcjogIzc4N2JlODsNCiAgICB9DQogIH0NCn0NCg0KLmxlZnQtYm9hcmQgew0KICB3aWR0aDogMjYwcHg7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgbGVmdDogMDsNCiAgdG9wOiAwOw0KICBoZWlnaHQ6IDEwMHZoOw0KfQ0KLmxlZnQtc2Nyb2xsYmFyew0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA0MnB4KTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCi5jZW50ZXItc2Nyb2xsYmFyIHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDJweCk7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJvcmRlci1sZWZ0OiAxcHggc29saWQgI2YxZThlODsNCiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2YxZThlODsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCi5jZW50ZXItYm9hcmQgew0KICBoZWlnaHQ6IDEwMHZoOw0KICB3aWR0aDogYXV0bzsNCiAgbWFyZ2luOiAwIDM1MHB4IDAgMjYwcHg7DQogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQp9DQouZW1wdHktaW5mb3sNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDQ2JTsNCiAgbGVmdDogMDsNCiAgcmlnaHQ6IDA7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBjb2xvcjogI2NjYjFlYTsNCiAgbGV0dGVyLXNwYWNpbmc6IDRweDsNCn0NCi5hY3Rpb24tYmFyew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGhlaWdodDogNDJweDsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQogIHBhZGRpbmc6IDAgMTVweDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDs7DQogIGJvcmRlcjogMXB4IHNvbGlkICNmMWU4ZTg7DQogIGJvcmRlci10b3A6IG5vbmU7DQogIGJvcmRlci1sZWZ0OiBub25lOw0KICAuZGVsZXRlLWJ0bnsNCiAgICBjb2xvcjogI0Y1NkM2QzsNCiAgfQ0KfQ0KLmxvZ28td3JhcHBlcnsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBoZWlnaHQ6IDQycHg7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjFlOGU4Ow0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KfQ0KLmxvZ297DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgbGVmdDogMTJweDsNCiAgdG9wOiA2cHg7DQogIGxpbmUtaGVpZ2h0OiAzMHB4Ow0KICBjb2xvcjogIzAwYWZmZjsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgZm9udC1zaXplOiAxN3B4Ow0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICA+IGltZ3sNCiAgICB3aWR0aDogMzBweDsNCiAgICBoZWlnaHQ6IDMwcHg7DQogICAgdmVydGljYWwtYWxpZ246IHRvcDsNCiAgfQ0KICAuZ2l0aHView0KICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICB2ZXJ0aWNhbC1hbGlnbjogc3ViOw0KICAgIG1hcmdpbi1sZWZ0OiAxNXB4Ow0KICAgID4gaW1new0KICAgICAgaGVpZ2h0OiAyMnB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQouY2VudGVyLWJvYXJkLXJvdyB7DQogIHBhZGRpbmc6IDEycHggMTJweCAxNXB4IDEycHg7DQogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogICYgPiAuZWwtZm9ybSB7DQogICAgLy8gNjkgPSAxMisxNSs0Mg0KICAgIGhlaWdodDogY2FsYygxMDB2aCAtIDY5cHgpOw0KICB9DQp9DQouZHJhd2luZy1ib2FyZCB7DQogIGhlaWdodDogMTAwJTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAuY29tcG9uZW50cy1ib2R5IHsNCiAgICBwYWRkaW5nOiAwOw0KICAgIG1hcmdpbjogMDsNCiAgICBmb250LXNpemU6IDA7DQogIH0NCiAgLnNvcnRhYmxlLWdob3N0IHsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgZGlzcGxheTogYmxvY2s7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAmOjpiZWZvcmUgew0KICAgICAgY29udGVudDogIiAiOw0KICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgbGVmdDogMDsNCiAgICAgIHJpZ2h0OiAwOw0KICAgICAgdG9wOiAwOw0KICAgICAgaGVpZ2h0OiAzcHg7DQogICAgICBiYWNrZ3JvdW5kOiByZ2IoODksIDg5LCAyMjMpOw0KICAgICAgei1pbmRleDogMjsNCiAgICB9DQogIH0NCiAgLmNvbXBvbmVudHMtaXRlbS5zb3J0YWJsZS1naG9zdCB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiA2MHB4Ow0KICAgIGJhY2tncm91bmQtY29sb3I6ICRzZWxlY3RlZENvbG9yOw0KICB9DQogIC5hY3RpdmUtZnJvbS1pdGVtIHsNCiAgICAmID4gLmVsLWZvcm0taXRlbXsNCiAgICAgIGJhY2tncm91bmQ6ICRzZWxlY3RlZENvbG9yOw0KICAgICAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICAgIH0NCiAgICAmID4gLmRyYXdpbmctaXRlbS1jb3B5LCAmID4gLmRyYXdpbmctaXRlbS1kZWxldGV7DQogICAgICBkaXNwbGF5OiBpbml0aWFsOw0KICAgIH0NCiAgICAmID4gLmNvbXBvbmVudC1uYW1lew0KICAgICAgY29sb3I6ICRsaWdodGVyQmx1ZTsNCiAgICB9DQogIH0NCiAgLmVsLWZvcm0taXRlbXsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICB9DQp9DQouZHJhd2luZy1pdGVtew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGN1cnNvcjogbW92ZTsNCiAgJi51bmZvY3VzLWJvcmRlcmVkOm5vdCguYWN0aXZlRnJvbUl0ZW0pID4gZGl2OmZpcnN0LWNoaWxkICB7DQogICAgYm9yZGVyOiAxcHggZGFzaGVkICNjY2M7DQogIH0NCiAgLmVsLWZvcm0taXRlbXsNCiAgICBwYWRkaW5nOiAxMnB4IDEwcHg7DQogIH0NCn0NCi5kcmF3aW5nLXJvdy1pdGVtew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGN1cnNvcjogbW92ZTsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgYm9yZGVyOiAxcHggZGFzaGVkICNjY2M7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgcGFkZGluZzogMCAycHg7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIC5kcmF3aW5nLXJvdy1pdGVtIHsNCiAgICBtYXJnaW4tYm90dG9tOiAycHg7DQogIH0NCiAgLmVsLWNvbHsNCiAgICBtYXJnaW4tdG9wOiAyMnB4Ow0KICB9DQogIC5lbC1mb3JtLWl0ZW17DQogICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgfQ0KICAuZHJhZy13cmFwcGVyew0KICAgIG1pbi1oZWlnaHQ6IDgwcHg7DQogIH0NCiAgJi5hY3RpdmUtZnJvbS1pdGVtew0KICAgIGJvcmRlcjogMXB4IGRhc2hlZCAkbGlnaHRlckJsdWU7DQogIH0NCiAgLmNvbXBvbmVudC1uYW1lew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB0b3A6IDA7DQogICAgbGVmdDogMDsNCiAgICBmb250LXNpemU6IDEycHg7DQogICAgY29sb3I6ICNiYmI7DQogICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgIHBhZGRpbmc6IDAgNnB4Ow0KICB9DQp9DQouZHJhd2luZy1pdGVtLCAuZHJhd2luZy1yb3ctaXRlbXsNCiAgJjpob3ZlciB7DQogICAgJiA+IC5lbC1mb3JtLWl0ZW17DQogICAgICBiYWNrZ3JvdW5kOiAkc2VsZWN0ZWRDb2xvcjsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgICB9DQogICAgJiA+IC5kcmF3aW5nLWl0ZW0tY29weSwgJiA+IC5kcmF3aW5nLWl0ZW0tZGVsZXRlew0KICAgICAgZGlzcGxheTogaW5pdGlhbDsNCiAgICB9DQogIH0NCiAgJiA+IC5kcmF3aW5nLWl0ZW0tY29weSwgJiA+IC5kcmF3aW5nLWl0ZW0tZGVsZXRlew0KICAgIGRpc3BsYXk6IG5vbmU7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIHRvcDogLTEwcHg7DQogICAgd2lkdGg6IDIycHg7DQogICAgaGVpZ2h0OiAyMnB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAyMnB4Ow0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkOw0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB6LWluZGV4OiAxOw0KICB9DQogICYgPiAuZHJhd2luZy1pdGVtLWNvcHl7DQogICAgcmlnaHQ6IDU2cHg7DQogICAgYm9yZGVyLWNvbG9yOiAkbGlnaHRlckJsdWU7DQogICAgY29sb3I6ICRsaWdodGVyQmx1ZTsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICY6aG92ZXJ7DQogICAgICBiYWNrZ3JvdW5kOiAkbGlnaHRlckJsdWU7DQogICAgICBjb2xvcjogI2ZmZjsNCiAgICB9DQogIH0NCiAgJiA+IC5kcmF3aW5nLWl0ZW0tZGVsZXRlew0KICAgIHJpZ2h0OiAyNHB4Ow0KICAgIGJvcmRlci1jb2xvcjogI0Y1NkM2QzsNCiAgICBjb2xvcjogI0Y1NkM2QzsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICY6aG92ZXJ7DQogICAgICBiYWNrZ3JvdW5kOiAjRjU2QzZDOw0KICAgICAgY29sb3I6ICNmZmY7DQogICAgfQ0KICB9DQp9DQoNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <div class=\"left-board\">\r\n      <div class=\"logo-wrapper\">\r\n        <div class=\"logo\">\r\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\r\n        </div>\r\n      </div>\r\n      <el-scrollbar class=\"left-scrollbar\">\r\n        <div class=\"components-list\">\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" />输入型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\"\r\n            :list=\"inputComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :clone=\"cloneComponent\"\r\n            draggable=\".components-item\"\r\n            :sort=\"false\"\r\n            @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in inputComponents\" :key=\"index\" class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" />选择型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\"\r\n            :list=\"selectComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :clone=\"cloneComponent\"\r\n            draggable=\".components-item\"\r\n            :sort=\"false\"\r\n            @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in selectComponents\"\r\n              :key=\"index\"\r\n              class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" /> 布局型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\" :list=\"layoutComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\" :clone=\"cloneComponent\"\r\n            draggable=\".components-item\" :sort=\"false\" @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in layoutComponents\" :key=\"index\" class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <div class=\"center-board\">\r\n      <div class=\"action-bar\">\r\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\r\n          导出vue文件\r\n        </el-button>\r\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\r\n          复制代码\r\n        </el-button>\r\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\r\n          清空\r\n        </el-button>\r\n      </div>\r\n      <el-scrollbar class=\"center-scrollbar\">\r\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\r\n          <el-form\r\n            :size=\"formConf.size\"\r\n            :label-position=\"formConf.labelPosition\"\r\n            :disabled=\"formConf.disabled\"\r\n            :label-width=\"formConf.labelWidth + 'px'\"\r\n          >\r\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\r\n              <draggable-item\r\n                v-for=\"(element, index) in drawingList\"\r\n                :key=\"element.renderKey\"\r\n                :drawing-list=\"drawingList\"\r\n                :element=\"element\"\r\n                :index=\"index\"\r\n                :active-id=\"activeId\"\r\n                :form-conf=\"formConf\"\r\n                @activeItem=\"activeFormItem\"\r\n                @copyItem=\"drawingItemCopy\"\r\n                @deleteItem=\"drawingItemDelete\"\r\n              />\r\n            </draggable>\r\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\r\n              从左侧拖入或点选组件进行表单设计\r\n            </div>\r\n          </el-form>\r\n        </el-row>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <right-panel\r\n      :active-data=\"activeData\"\r\n      :form-conf=\"formConf\"\r\n      :show-field=\"!!drawingList.length\"\r\n      @tag-change=\"tagChange\"\r\n    />\r\n\r\n    <code-type-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"选择生成类型\"\r\n      :show-file-name=\"showFileName\"\r\n      @confirm=\"generate\"\r\n    />\r\n    <input id=\"copyNode\" type=\"hidden\">\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport draggable from 'vuedraggable'\r\nimport beautifier from 'js-beautify'\r\nimport ClipboardJS from 'clipboard'\r\nimport render from '@/utils/generator/render'\r\nimport RightPanel from './RightPanel'\r\nimport { inputComponents, selectComponents, layoutComponents, formConf } from '@/utils/generator/config'\r\nimport { beautifierConf, titleCase } from '@/utils/index'\r\nimport { makeUpHtml, vueTemplate, vueScript, cssStyle } from '@/utils/generator/html'\r\nimport { makeUpJs } from '@/utils/generator/js'\r\nimport { makeUpCss } from '@/utils/generator/css'\r\nimport drawingDefault from '@/utils/generator/drawingDefault'\r\nimport logo from '@/assets/logo/logo.png'\r\nimport CodeTypeDialog from './CodeTypeDialog'\r\nimport DraggableItem from './DraggableItem'\r\n\r\nlet oldActiveId\r\nlet tempActiveData\r\n\r\nexport default {\r\n  components: {\r\n    draggable,\r\n    render,\r\n    RightPanel,\r\n    CodeTypeDialog,\r\n    DraggableItem\r\n  },\r\n  data() {\r\n    return {\r\n      logo,\r\n      idGlobal: 100,\r\n      formConf,\r\n      inputComponents,\r\n      selectComponents,\r\n      layoutComponents,\r\n      labelWidth: 100,\r\n      drawingList: drawingDefault,\r\n      drawingData: {},\r\n      activeId: drawingDefault[0].formId,\r\n      drawerVisible: false,\r\n      formData: {},\r\n      dialogVisible: false,\r\n      generateConf: null,\r\n      showFileName: false,\r\n      activeData: drawingDefault[0]\r\n    }\r\n  },\r\n  created() {\r\n    // 防止 firefox 下 拖拽 会新打卡一个选项卡\r\n    document.body.ondrop = event => {\r\n      event.preventDefault()\r\n      event.stopPropagation()\r\n    }\r\n  },\r\n  watch: {\r\n    // eslint-disable-next-line func-names\r\n    'activeData.label': function (val, oldVal) {\r\n      if (\r\n        this.activeData.placeholder === undefined\r\n        || !this.activeData.tag\r\n        || oldActiveId !== this.activeId\r\n      ) {\r\n        return\r\n      }\r\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\r\n    },\r\n    activeId: {\r\n      handler(val) {\r\n        oldActiveId = val\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    const clipboard = new ClipboardJS('#copyNode', {\r\n      text: trigger => {\r\n        const codeStr = this.generateCode()\r\n        this.$notify({\r\n          title: '成功',\r\n          message: '代码已复制到剪切板，可粘贴。',\r\n          type: 'success'\r\n        })\r\n        return codeStr\r\n      }\r\n    })\r\n    clipboard.on('error', e => {\r\n      this.$message.error('代码复制失败')\r\n    })\r\n  },\r\n  methods: {\r\n    activeFormItem(element) {\r\n      this.activeData = element\r\n      this.activeId = element.formId\r\n    },\r\n    onEnd(obj, a) {\r\n      if (obj.from !== obj.to) {\r\n        this.activeData = tempActiveData\r\n        this.activeId = this.idGlobal\r\n      }\r\n    },\r\n    addComponent(item) {\r\n      const clone = this.cloneComponent(item)\r\n      this.drawingList.push(clone)\r\n      this.activeFormItem(clone)\r\n    },\r\n    cloneComponent(origin) {\r\n      const clone = JSON.parse(JSON.stringify(origin))\r\n      clone.formId = ++this.idGlobal\r\n      clone.span = formConf.span\r\n      clone.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件\r\n      if (!clone.layout) clone.layout = 'colFormItem'\r\n      if (clone.layout === 'colFormItem') {\r\n        clone.vModel = `field${this.idGlobal}`\r\n        clone.placeholder !== undefined && (clone.placeholder += clone.label)\r\n        tempActiveData = clone\r\n      } else if (clone.layout === 'rowFormItem') {\r\n        delete clone.label\r\n        clone.componentName = `row${this.idGlobal}`\r\n        clone.gutter = this.formConf.gutter\r\n        tempActiveData = clone\r\n      }\r\n      return tempActiveData\r\n    },\r\n    AssembleFormData() {\r\n      this.formData = {\r\n        fields: JSON.parse(JSON.stringify(this.drawingList)),\r\n        ...this.formConf\r\n      }\r\n    },\r\n    generate(data) {\r\n      const func = this[`exec${titleCase(this.operationType)}`]\r\n      this.generateConf = data\r\n      func && func(data)\r\n    },\r\n    execRun(data) {\r\n      this.AssembleFormData()\r\n      this.drawerVisible = true\r\n    },\r\n    execDownload(data) {\r\n      const codeStr = this.generateCode()\r\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\r\n      this.$download.saveAs(blob, data.fileName)\r\n    },\r\n    execCopy(data) {\r\n      document.getElementById('copyNode').click()\r\n    },\r\n    empty() {\r\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\r\n        () => {\r\n          this.drawingList = []\r\n        }\r\n      )\r\n    },\r\n    drawingItemCopy(item, parent) {\r\n      let clone = JSON.parse(JSON.stringify(item))\r\n      clone = this.createIdAndKey(clone)\r\n      parent.push(clone)\r\n      this.activeFormItem(clone)\r\n    },\r\n    createIdAndKey(item) {\r\n      item.formId = ++this.idGlobal\r\n      item.renderKey = +new Date()\r\n      if (item.layout === 'colFormItem') {\r\n        item.vModel = `field${this.idGlobal}`\r\n      } else if (item.layout === 'rowFormItem') {\r\n        item.componentName = `row${this.idGlobal}`\r\n      }\r\n      if (Array.isArray(item.children)) {\r\n        item.children = item.children.map(childItem => this.createIdAndKey(childItem))\r\n      }\r\n      return item\r\n    },\r\n    drawingItemDelete(index, parent) {\r\n      parent.splice(index, 1)\r\n      this.$nextTick(() => {\r\n        const len = this.drawingList.length\r\n        if (len) {\r\n          this.activeFormItem(this.drawingList[len - 1])\r\n        }\r\n      })\r\n    },\r\n    generateCode() {\r\n      const { type } = this.generateConf\r\n      this.AssembleFormData()\r\n      const script = vueScript(makeUpJs(this.formData, type))\r\n      const html = vueTemplate(makeUpHtml(this.formData, type))\r\n      const css = cssStyle(makeUpCss(this.formData))\r\n      return beautifier.html(html + script + css, beautifierConf.html)\r\n    },\r\n    download() {\r\n      this.dialogVisible = true\r\n      this.showFileName = true\r\n      this.operationType = 'download'\r\n    },\r\n    run() {\r\n      this.dialogVisible = true\r\n      this.showFileName = false\r\n      this.operationType = 'run'\r\n    },\r\n    copy() {\r\n      this.dialogVisible = true\r\n      this.showFileName = false\r\n      this.operationType = 'copy'\r\n    },\r\n    tagChange(newTag) {\r\n      newTag = this.cloneComponent(newTag)\r\n      newTag.vModel = this.activeData.vModel\r\n      newTag.formId = this.activeId\r\n      newTag.span = this.activeData.span\r\n      delete this.activeData.tag\r\n      delete this.activeData.tagIcon\r\n      delete this.activeData.document\r\n      Object.keys(newTag).forEach(key => {\r\n        if (this.activeData[key] !== undefined\r\n          && typeof this.activeData[key] === typeof newTag[key]) {\r\n          newTag[key] = this.activeData[key]\r\n        }\r\n      })\r\n      this.activeData = newTag\r\n      this.updateDrawingList(newTag, this.drawingList)\r\n    },\r\n    updateDrawingList(newTag, list) {\r\n      const index = list.findIndex(item => item.formId === this.activeId)\r\n      if (index > -1) {\r\n        list.splice(index, 1, newTag)\r\n      } else {\r\n        list.forEach(item => {\r\n          if (Array.isArray(item.children)) this.updateDrawingList(newTag, item.children)\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang='scss'>\r\nbody, html{\r\n  margin: 0;\r\n  padding: 0;\r\n  background: #fff;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  -webkit-font-smoothing: antialiased;\r\n  text-rendering: optimizeLegibility;\r\n  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;\r\n}\r\n\r\ninput, textarea{\r\n  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;\r\n}\r\n\r\n.editor-tabs{\r\n  background: #121315;\r\n  .el-tabs__header{\r\n    margin: 0;\r\n    border-bottom-color: #121315;\r\n    .el-tabs__nav{\r\n      border-color: #121315;\r\n    }\r\n  }\r\n  .el-tabs__item{\r\n    height: 32px;\r\n    line-height: 32px;\r\n    color: #888a8e;\r\n    border-left: 1px solid #121315 !important;\r\n    background: #363636;\r\n    margin-right: 5px;\r\n    user-select: none;\r\n  }\r\n  .el-tabs__item.is-active{\r\n    background: #1e1e1e;\r\n    border-bottom-color: #1e1e1e!important;\r\n    color: #fff;\r\n  }\r\n  .el-icon-edit{\r\n    color: #f1fa8c;\r\n  }\r\n  .el-icon-document{\r\n    color: #a95812;\r\n  }\r\n}\r\n\r\n// home\r\n.right-scrollbar {\r\n  .el-scrollbar__view {\r\n    padding: 12px 18px 15px 15px;\r\n  }\r\n}\r\n.left-scrollbar .el-scrollbar__wrap {\r\n  box-sizing: border-box;\r\n  overflow-x: hidden !important;\r\n  margin-bottom: 0 !important;\r\n}\r\n.center-tabs{\r\n  .el-tabs__header{\r\n    margin-bottom: 0!important;\r\n  }\r\n  .el-tabs__item{\r\n    width: 50%;\r\n    text-align: center;\r\n  }\r\n  .el-tabs__nav{\r\n    width: 100%;\r\n  }\r\n}\r\n.reg-item{\r\n  padding: 12px 6px;\r\n  background: #f8f8f8;\r\n  position: relative;\r\n  border-radius: 4px;\r\n  .close-btn{\r\n    position: absolute;\r\n    right: -6px;\r\n    top: -6px;\r\n    display: block;\r\n    width: 16px;\r\n    height: 16px;\r\n    line-height: 16px;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    border-radius: 50%;\r\n    color: #fff;\r\n    text-align: center;\r\n    z-index: 1;\r\n    cursor: pointer;\r\n    font-size: 12px;\r\n    &:hover{\r\n      background: rgba(210, 23, 23, 0.5)\r\n    }\r\n  }\r\n  & + .reg-item{\r\n    margin-top: 18px;\r\n  }\r\n}\r\n.action-bar{\r\n  & .el-button+.el-button {\r\n    margin-left: 15px;\r\n  }\r\n  & i {\r\n    font-size: 20px;\r\n    vertical-align: middle;\r\n    position: relative;\r\n    top: -1px;\r\n  }\r\n}\r\n\r\n.custom-tree-node{\r\n  width: 100%;\r\n  font-size: 14px;\r\n  .node-operation{\r\n    float: right;\r\n  }\r\n  i[class*=\"el-icon\"] + i[class*=\"el-icon\"]{\r\n    margin-left: 6px;\r\n  }\r\n  .el-icon-plus{\r\n    color: #409EFF;\r\n  }\r\n  .el-icon-delete{\r\n    color: #157a0c;\r\n  }\r\n}\r\n\r\n.left-scrollbar .el-scrollbar__view{\r\n  overflow-x: hidden;\r\n}\r\n\r\n.el-rate{\r\n  display: inline-block;\r\n  vertical-align: text-top;\r\n}\r\n.el-upload__tip{\r\n  line-height: 1.2;\r\n}\r\n\r\n$selectedColor: #f6f7ff;\r\n$lighterBlue: #409EFF;\r\n\r\n.container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.components-list {\r\n  padding: 8px;\r\n  box-sizing: border-box;\r\n  height: 100%;\r\n  .components-item {\r\n    display: inline-block;\r\n    width: 48%;\r\n    margin: 1%;\r\n    transition: transform 0ms !important;\r\n  }\r\n}\r\n.components-draggable{\r\n  padding-bottom: 20px;\r\n}\r\n.components-title{\r\n  font-size: 14px;\r\n  color: #222;\r\n  margin: 6px 2px;\r\n  .svg-icon{\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n.components-body {\r\n  padding: 8px 10px;\r\n  background: $selectedColor;\r\n  font-size: 12px;\r\n  cursor: move;\r\n  border: 1px dashed $selectedColor;\r\n  border-radius: 3px;\r\n  .svg-icon{\r\n    color: #777;\r\n    font-size: 15px;\r\n  }\r\n  &:hover {\r\n    border: 1px dashed #787be8;\r\n    color: #787be8;\r\n    .svg-icon {\r\n      color: #787be8;\r\n    }\r\n  }\r\n}\r\n\r\n.left-board {\r\n  width: 260px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  height: 100vh;\r\n}\r\n.left-scrollbar{\r\n  height: calc(100vh - 42px);\r\n  overflow: hidden;\r\n}\r\n.center-scrollbar {\r\n  height: calc(100vh - 42px);\r\n  overflow: hidden;\r\n  border-left: 1px solid #f1e8e8;\r\n  border-right: 1px solid #f1e8e8;\r\n  box-sizing: border-box;\r\n}\r\n.center-board {\r\n  height: 100vh;\r\n  width: auto;\r\n  margin: 0 350px 0 260px;\r\n  box-sizing: border-box;\r\n}\r\n.empty-info{\r\n  position: absolute;\r\n  top: 46%;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  color: #ccb1ea;\r\n  letter-spacing: 4px;\r\n}\r\n.action-bar{\r\n  position: relative;\r\n  height: 42px;\r\n  text-align: right;\r\n  padding: 0 15px;\r\n  box-sizing: border-box;;\r\n  border: 1px solid #f1e8e8;\r\n  border-top: none;\r\n  border-left: none;\r\n  .delete-btn{\r\n    color: #F56C6C;\r\n  }\r\n}\r\n.logo-wrapper{\r\n  position: relative;\r\n  height: 42px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #f1e8e8;\r\n  box-sizing: border-box;\r\n}\r\n.logo{\r\n  position: absolute;\r\n  left: 12px;\r\n  top: 6px;\r\n  line-height: 30px;\r\n  color: #00afff;\r\n  font-weight: 600;\r\n  font-size: 17px;\r\n  white-space: nowrap;\r\n  > img{\r\n    width: 30px;\r\n    height: 30px;\r\n    vertical-align: top;\r\n  }\r\n  .github{\r\n    display: inline-block;\r\n    vertical-align: sub;\r\n    margin-left: 15px;\r\n    > img{\r\n      height: 22px;\r\n    }\r\n  }\r\n}\r\n\r\n.center-board-row {\r\n  padding: 12px 12px 15px 12px;\r\n  box-sizing: border-box;\r\n  & > .el-form {\r\n    // 69 = 12+15+42\r\n    height: calc(100vh - 69px);\r\n  }\r\n}\r\n.drawing-board {\r\n  height: 100%;\r\n  position: relative;\r\n  .components-body {\r\n    padding: 0;\r\n    margin: 0;\r\n    font-size: 0;\r\n  }\r\n  .sortable-ghost {\r\n    position: relative;\r\n    display: block;\r\n    overflow: hidden;\r\n    &::before {\r\n      content: \" \";\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      height: 3px;\r\n      background: rgb(89, 89, 223);\r\n      z-index: 2;\r\n    }\r\n  }\r\n  .components-item.sortable-ghost {\r\n    width: 100%;\r\n    height: 60px;\r\n    background-color: $selectedColor;\r\n  }\r\n  .active-from-item {\r\n    & > .el-form-item{\r\n      background: $selectedColor;\r\n      border-radius: 6px;\r\n    }\r\n    & > .drawing-item-copy, & > .drawing-item-delete{\r\n      display: initial;\r\n    }\r\n    & > .component-name{\r\n      color: $lighterBlue;\r\n    }\r\n  }\r\n  .el-form-item{\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n.drawing-item{\r\n  position: relative;\r\n  cursor: move;\r\n  &.unfocus-bordered:not(.activeFromItem) > div:first-child  {\r\n    border: 1px dashed #ccc;\r\n  }\r\n  .el-form-item{\r\n    padding: 12px 10px;\r\n  }\r\n}\r\n.drawing-row-item{\r\n  position: relative;\r\n  cursor: move;\r\n  box-sizing: border-box;\r\n  border: 1px dashed #ccc;\r\n  border-radius: 3px;\r\n  padding: 0 2px;\r\n  margin-bottom: 15px;\r\n  .drawing-row-item {\r\n    margin-bottom: 2px;\r\n  }\r\n  .el-col{\r\n    margin-top: 22px;\r\n  }\r\n  .el-form-item{\r\n    margin-bottom: 0;\r\n  }\r\n  .drag-wrapper{\r\n    min-height: 80px;\r\n  }\r\n  &.active-from-item{\r\n    border: 1px dashed $lighterBlue;\r\n  }\r\n  .component-name{\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    font-size: 12px;\r\n    color: #bbb;\r\n    display: inline-block;\r\n    padding: 0 6px;\r\n  }\r\n}\r\n.drawing-item, .drawing-row-item{\r\n  &:hover {\r\n    & > .el-form-item{\r\n      background: $selectedColor;\r\n      border-radius: 6px;\r\n    }\r\n    & > .drawing-item-copy, & > .drawing-item-delete{\r\n      display: initial;\r\n    }\r\n  }\r\n  & > .drawing-item-copy, & > .drawing-item-delete{\r\n    display: none;\r\n    position: absolute;\r\n    top: -10px;\r\n    width: 22px;\r\n    height: 22px;\r\n    line-height: 22px;\r\n    text-align: center;\r\n    border-radius: 50%;\r\n    font-size: 12px;\r\n    border: 1px solid;\r\n    cursor: pointer;\r\n    z-index: 1;\r\n  }\r\n  & > .drawing-item-copy{\r\n    right: 56px;\r\n    border-color: $lighterBlue;\r\n    color: $lighterBlue;\r\n    background: #fff;\r\n    &:hover{\r\n      background: $lighterBlue;\r\n      color: #fff;\r\n    }\r\n  }\r\n  & > .drawing-item-delete{\r\n    right: 24px;\r\n    border-color: #F56C6C;\r\n    color: #F56C6C;\r\n    background: #fff;\r\n    &:hover{\r\n      background: #F56C6C;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}