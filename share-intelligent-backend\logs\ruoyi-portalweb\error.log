10:30:17.677 [main] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.initCertificates(CertManagerSingleton.java:169)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.init(CertManagerSingleton.java:88)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.initCertManager(ScheduledUpdateCertificatesVerifier.java:32)
	at com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier.<init>(ScheduledUpdateCertificatesVerifier.java:24)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3.getVerifier(WxPayConfigV3.java:79)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$a9f8b7ec.CGLIB$getVerifier$7(<generated>)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$a9f8b7ec$$FastClassBySpringCGLIB$$986aad26.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.ruoyi.portalweb.scm.config.WxPayConfigV3$$EnhancerBySpringCGLIB$$a9f8b7ec.getVerifier(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:641)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:477)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:548)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:524)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:677)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.portalweb.RuoYiPortalwebApplication.main(RuoYiPortalwebApplication.java:24)
10:30:17.916 [scheduled_update_cert_thread] ERROR c.w.p.c.a.h.c.CertManagerSingleton - [downloadAndUpdateCert,164] - Download Certificate failed
javax.crypto.AEADBadTagException: Tag mismatch!
	at com.sun.crypto.provider.GaloisCounterMode.decryptFinal(GaloisCounterMode.java:571)
	at com.sun.crypto.provider.CipherCore.finalNoPadding(CipherCore.java:1048)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:985)
	at com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:847)
	at com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	at javax.crypto.Cipher.doFinal(Cipher.java:2165)
	at com.wechat.pay.contrib.apache.httpclient.util.AesUtil.decryptToString(AesUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.util.CertSerializeUtil.deserializeToCerts(CertSerializeUtil.java:42)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.downloadAndUpdateCert(CertManagerSingleton.java:152)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.updateCertificates(CertManagerSingleton.java:177)
	at com.wechat.pay.contrib.apache.httpclient.cert.CertManagerSingleton.lambda$init$0(CertManagerSingleton.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
