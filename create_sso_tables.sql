-- =============================================
-- SSO相关表创建脚本
-- 数据库：industry
-- 说明：请在industry数据库中执行此脚本
-- =============================================

USE industry;

-- 1. 创建SSO统一用户表
DROP TABLE IF EXISTS sso_users;
CREATE TABLE sso_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名（通常是手机号）',
    password VARCHAR(255) NOT NULL COMMENT '密码（BCrypt加密）',
    nickname VARCHAR(100) COMMENT '昵称',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(500) COMMENT '头像地址',
    
    -- 状态和权限
    status TINYINT DEFAULT 1 COMMENT '状态（0禁用 1正常）',
    user_type VARCHAR(20) DEFAULT 'NORMAL' COMMENT '用户类型（NORMAL普通用户 ADMIN管理员）',
    
    -- 系统权限标识
    backend_enabled CHAR(1) DEFAULT '1' COMMENT '主系统权限（0禁用 1启用）',
    market_enabled CHAR(1) DEFAULT '1' COMMENT '市场系统权限（0禁用 1启用）',
    
    -- 登录信息
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    
    -- 审计字段
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    
    -- 索引
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_phone (phone),
    INDEX idx_status (status),
    INDEX idx_user_type (user_type),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SSO统一用户表';

-- 2. 创建SSO用户映射表（可选，用于复杂的用户关联场景）
DROP TABLE IF EXISTS sso_user_mapping;
CREATE TABLE sso_user_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    sso_username VARCHAR(100) NOT NULL COMMENT 'SSO统一用户名（通常是手机号）',
    sso_password VARCHAR(255) NOT NULL COMMENT 'SSO统一密码（BCrypt加密）',
    sso_real_name VARCHAR(100) COMMENT 'SSO用户真实姓名',
    sso_phone VARCHAR(20) COMMENT 'SSO用户手机号',
    sso_email VARCHAR(100) COMMENT 'SSO用户邮箱',
    
    -- 主系统关联信息
    backend_member_id BIGINT COMMENT '主系统member_id',
    backend_member_phone VARCHAR(20) COMMENT '主系统用户手机号',
    
    -- 市场系统关联信息  
    market_member_id BIGINT COMMENT '市场系统member_id',
    market_member_phone VARCHAR(20) COMMENT '市场系统用户手机号',
    
    -- 状态和时间
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    
    -- 索引
    UNIQUE KEY uk_sso_username (sso_username),
    UNIQUE KEY uk_sso_phone (sso_phone),
    INDEX idx_backend_member (backend_member_id),
    INDEX idx_market_member (market_member_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SSO用户映射表';

-- 3. 插入测试数据
INSERT INTO sso_users (
    username, password, nickname, phone, email,
    backend_enabled, market_enabled, status, user_type, remark
) VALUES 
(
    '13800138000',
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '张三',
    '13800138000',
    '<EMAIL>',
    '1', '1', 1, 'NORMAL',
    'SSO测试用户 - 可访问主系统和市场系统'
),
(
    '13900139000',
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '李四',
    '13900139000',
    '<EMAIL>',
    '1', '0', 1, 'NORMAL',
    'SSO测试用户 - 只能访问主系统'
),
(
    'admin',
    '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKlDL4pTdGta9VoEwvzb2.Zb1Ztq', -- 密码: admin123
    '系统管理员',
    '13888888888',
    '<EMAIL>',
    '1', '1', 1, 'ADMIN',
    'SSO系统管理员'
);

-- 4. 验证表创建
SELECT 'sso_users表创建完成' AS message;
SELECT COUNT(*) AS sso_users_count FROM sso_users;

SELECT 'sso_user_mapping表创建完成' AS message;
SELECT COUNT(*) AS sso_user_mapping_count FROM sso_user_mapping;

-- 5. 显示表结构
SHOW CREATE TABLE sso_users;
SHOW CREATE TABLE sso_user_mapping;
