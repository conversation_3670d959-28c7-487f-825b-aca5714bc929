10:30:02.585 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:30:02.674 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:30:03.492 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:30:03.493 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:30:06.766 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:30:13.488 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:30:14.713 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:30:15.570 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:30:15.570 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:30:16.776 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:30:16.777 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:30:17.012 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
10:30:17.348 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
10:30:17.390 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 15.653 seconds (JVM running for 17.439)
10:30:17.396 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
10:30:17.397 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
10:30:17.398 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
10:32:03.275 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
10:32:03.275 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
10:32:03.275 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
10:32:03.284 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
10:32:03.284 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
10:32:03.284 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
10:32:05.527 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
10:32:05.527 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
10:32:23.097 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/sso/token
10:32:23.098 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword]
10:43:31.575 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
10:43:31.577 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
10:43:37.055 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:43:37.111 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:43:37.504 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:43:37.504 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:43:39.702 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:43:44.610 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:43:45.480 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:43:46.134 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:43:46.135 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:43:47.241 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:43:47.241 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:43:47.455 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
10:43:47.740 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
10:43:47.774 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 11.31 seconds (JVM running for 12.747)
10:43:47.780 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
10:43:47.780 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
10:43:47.781 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
10:49:12.892 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/portallogout
10:49:12.900 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:49:13.028 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
10:49:13.029 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:49:16.701 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
10:49:16.702 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:49:37.102 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/sso/token
10:49:37.102 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:49:51.258 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/sso/token
10:49:51.258 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:50:21.074 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/sso/token
10:50:21.074 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:50:23.551 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
10:50:23.551 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
10:50:23.551 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:50:23.551 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:50:23.853 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
10:50:23.854 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:50:43.277 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
10:50:43.277 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
10:50:43.277 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
10:50:43.277 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:50:43.277 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:50:43.277 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:52:51.776 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
10:52:51.779 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
10:52:57.344 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:52:57.403 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:52:57.787 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:52:57.788 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:52:59.974 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:53:05.184 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:53:06.334 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:53:07.079 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:53:07.079 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:53:08.258 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:53:08.259 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:53:08.477 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
10:53:08.761 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
10:53:08.798 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 12.097 seconds (JVM running for 13.598)
10:53:08.804 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
10:53:08.805 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
10:53:08.806 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
10:53:12.692 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
10:53:12.703 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:53:24.275 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/sso/token
10:53:24.275 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
10:59:02.201 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
10:59:02.203 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
10:59:07.871 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:59:07.932 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:59:08.378 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:59:08.379 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:59:10.623 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:59:16.837 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:59:18.538 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:59:19.939 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:59:19.939 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:59:21.271 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:59:21.272 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:59:21.500 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
10:59:21.796 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
10:59:21.845 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 14.595 seconds (JVM running for 16.255)
10:59:21.853 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
10:59:21.855 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
10:59:21.857 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
11:00:14.578 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
11:00:14.585 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
11:00:16.873 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /sso/loginUrl
11:00:16.873 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
11:00:38.701 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/sso/token
11:00:38.701 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
11:00:40.132 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
11:00:40.132 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
11:00:40.445 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
11:00:40.445 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
11:00:40.446 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
11:00:40.446 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
11:00:45.163 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
11:00:45.163 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
11:00:45.163 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
11:00:45.164 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
11:00:45.164 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
11:00:45.164 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list, /sso/**, /portalweb/Member/getMemberInfo, /system/testingItem/**, /auth/portalloginByPassword, /auth/sso/token]
