{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\banner\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\banner\\index.vue", "mtime": 1750151094252}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3RCYW5uZXIsDQogICAgZ2V0QmFubmVyLA0KICAgIGRlbEJhbm5lciwNCiAgICBhZGRCYW5uZXIsDQogICAgdXBkYXRlQmFubmVyLA0KfSBmcm9tICJAL2FwaS91dWMvYmFubmVyIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJCYW5uZXIiLA0KICAgIGRpY3RzOiBbInV1Y19iYW5uZXJfdHlwZSJdLA0KICAgIGRhdGEoKSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAvLyDpga7nvanlsYINCiAgICAgICAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAgICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgICAgICAgIGlkczogW10sDQogICAgICAgICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgICAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAgICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgICAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAgICAgICAvLyDmgLvmnaHmlbANCiAgICAgICAgICAgIHRvdGFsOiAwLA0KICAgICAgICAgICAgLy8g6L2u5pKt5Zu+6KGo5qC85pWw5o2uDQogICAgICAgICAgICBiYW5uZXJMaXN0OiBbXSwNCiAgICAgICAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgICAgICAgICBuYW1lOiBudWxsLA0KICAgICAgICAgICAgICAgIGNhdGVnb3J5OiBudWxsLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgICAgICAgZm9ybToge30sDQogICAgICAgICAgICAvLyDooajljZXmoKHpqowNCiAgICAgICAgICAgIHJ1bGVzOiB7DQogICAgICAgICAgICAgICAgY2F0ZWdvcnk6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi57G75Yir5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIGltYWdlOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIui1hOa6kOS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIG5hbWU6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5ZCN56ew5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIHJlbWFyazogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLlpIfms6jkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICB9LA0KICAgICAgICB9Ow0KICAgIH0sDQogICAgd2F0Y2g6IHsNCiAgICAgICAgZm9ybTogew0KICAgICAgICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZUZpZWxkKFsiaW1hZ2UiXSxhc3luYyAodmFsaWQpPT57DQogICAgICAgICAgICAgICAgICBpZih0aGlzLmZvcm0uaW1hZ2Upew0KICAgICAgICAgICAgICAgICAgICAgIGlmKHZhbGlkKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS5jbGVhclZhbGlkYXRlKCdpbWFnZScpOyANCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRlZXA6IHRydWUsDQogICAgICAgIH0sDQogICAgfSwNCiAgICBjcmVhdGVkKCkgew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIG1ldGhvZHM6IHsNCiAgICAgICAgY2hhbmdlSW1nKGUpIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5p+l6K+i6L2u5pKt5Zu+5YiX6KGoICovDQogICAgICAgIGdldExpc3QoKSB7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgICAgbGlzdEJhbm5lcih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuYmFubmVyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOWPlua2iOaMiemSrg0KICAgICAgICBjYW5jZWwoKSB7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g6KGo5Y2V6YeN572uDQogICAgICAgIHJlc2V0KCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICAgIGlkOiBudWxsLA0KICAgICAgICAgICAgICAgIG5hbWU6IG51bGwsDQogICAgICAgICAgICAgICAgY2F0ZWdvcnk6IG51bGwsDQogICAgICAgICAgICAgICAgaW1hZ2U6IG51bGwsDQogICAgICAgICAgICAgICAgdXJsOiBudWxsLA0KICAgICAgICAgICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgICAgICAgICBzb3J0czogbnVsbCwNCiAgICAgICAgICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICAgICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICAgICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgICAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICAgICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsNCiAgICAgICAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgICAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVBZGQoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDova7mkq3lm74iOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgICAgICAgZ2V0QmFubmVyKGlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuei9ruaSreWbviI7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgICAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtKTsNCiAgICAgICAgICAgIC8vIGlmICghdGhpcy5mb3JtLmNhdGVnb3J5IHx8ICF0aGlzLmZvcm0uaW1hZ2UpIHsNCiAgICAgICAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIC8vICAgICAgICAgbWVzc2FnZTogIuivt+Whq+WGmeW/heWhq+mhuSIsDQogICAgICAgICAgICAvLyAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgICAgIC8vICAgICB9KTsNCiAgICAgICAgICAgIC8vICAgICByZXR1cm47DQogICAgICAgICAgICAvLyB9DQogICAgICAgICAgICAvLyBpZiAodGhpcy5mb3JtLnNvcnRzICYmIHRoaXMuZm9ybS5zb3J0cyA8IDApIHsNCiAgICAgICAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIC8vICAgICAgICAgbWVzc2FnZTogIuivt+Whq+WGmeato+ehrueahOaOkuW6jyIsDQogICAgICAgICAgICAvLyAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgICAgIC8vICAgICB9KTsNCiAgICAgICAgICAgIC8vICAgICByZXR1cm47DQogICAgICAgICAgICAvLyB9DQogICAgICAgICAgICAvLyBsZXQgbnVtID0gTWF0aC5mbG9vcih0aGlzLmZvcm0uc29ydHMpID09IHRoaXMuZm9ybS5zb3J0czsNCiAgICAgICAgICAgIC8vIGlmICh0aGlzLmZvcm0uc29ydHMgJiYgIW51bSkgew0KICAgICAgICAgICAgLy8gICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgLy8gICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5q2j56Gu55qE5o6S5bqPIiwNCiAgICAgICAgICAgIC8vICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICAgICAgLy8gICAgIH0pOw0KICAgICAgICAgICAgLy8gICAgIHJldHVybjsNCiAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLnNvcnRzICYmIHRoaXMuZm9ybS5zb3J0cyA8IDApIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5q2j56Gu55qE5o6S5bqPIiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgbGV0IG51bSA9IE1hdGguZmxvb3IodGhpcy5mb3JtLnNvcnRzKSA9PSB0aGlzLmZvcm0uc29ydHM7DQogICAgICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5zb3J0cyAmJiAhbnVtKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuivt+Whq+WGmeato+ehrueahOaOkuW6jyIsDQogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUJhbm5lcih0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGFkZEJhbm5lcih0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgICAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgICAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOi9ruaSreWbvue8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAgICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRlbEJhbm5lcihpZHMpOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgICAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICAgICAgICAgInV1Yy9iYW5uZXIvZXhwb3J0IiwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBgYmFubmVyXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgDQogICAgICAgICAgICApOw0KICAgICAgICB9LA0KICAgIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8NA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/ningmengdou/banner", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"名称\" prop=\"name\">\r\n                <el-input\r\n                    v-model=\"queryParams.name\"\r\n                    placeholder=\"请输入名称\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"类别\" prop=\"category\">\r\n                <el-select\r\n                    v-model=\"queryParams.category\"\r\n                    placeholder=\"请选择类别\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_banner_type\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:banner:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:banner:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:banner:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"warning\"\r\n                    plain\r\n                    icon=\"el-icon-download\"\r\n                    size=\"mini\"\r\n                    @click=\"handleExport\"\r\n                    v-hasPermi=\"['uuc:banner:export']\"\r\n                    >导出</el-button\r\n                >\r\n            </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"bannerList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n            <el-table-column label=\"名称\" align=\"center\" prop=\"name\" />\r\n            <el-table-column label=\"类别\" align=\"center\" prop=\"category\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_banner_type\"\r\n                        :value=\"scope.row.category\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"资源\"\r\n                align=\"center\"\r\n                prop=\"image\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <image-preview\r\n                        :src=\"scope.row.image\"\r\n                        :width=\"50\"\r\n                        :height=\"50\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n            <el-table-column label=\"排序\" align=\"center\" prop=\"sorts\" />\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:banner:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:banner:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改轮播图对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n                <el-form-item label=\"名称\" prop=\"name\">\r\n                    <el-input v-model=\"form.name\" maxlength=\"20\" placeholder=\"请输入名称\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"类别\" class=\"is-required\" prop=\"category\">\r\n                    <el-select v-model=\"form.category\" placeholder=\"请选择类别\">\r\n                        <el-option\r\n                            v-for=\"dict in dict.type.uuc_banner_type\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.label\"\r\n                            :value=\"dict.value\"\r\n                        ></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"资源\" prop=\"image\" class=\"is-required\">\r\n                    <image-upload\r\n                        :limit=\"1\"\r\n                        @input=\"changeImg\"\r\n                        v-model=\"form.image\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                        maxlength=\"255\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"排序\" prop=\"sorts\">\r\n                    <el-input\r\n                        type=\"number\"\r\n                        min=\"0\"\r\n                        v-model=\"form.sorts\"\r\n                        placeholder=\"请输入排序\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listBanner,\r\n    getBanner,\r\n    delBanner,\r\n    addBanner,\r\n    updateBanner,\r\n} from \"@/api/uuc/banner\";\r\n\r\nexport default {\r\n    name: \"Banner\",\r\n    dicts: [\"uuc_banner_type\"],\r\n    data() {\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 轮播图表格数据\r\n            bannerList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                name: null,\r\n                category: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            // 表单校验\r\n            rules: {\r\n                category: [\r\n                    {\r\n                        required: true,\r\n                        message: \"类别不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                image: [\r\n                    {\r\n                        required: true,\r\n                        message: \"资源不能为空\",\r\n                        trigger: \"change\",\r\n                    },\r\n                ],\r\n                name: [\r\n                    {\r\n                        required: true,\r\n                        message: \"名称不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                remark: [\r\n                    {\r\n                        required: true,\r\n                        message: \"备注不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    watch: {\r\n        form: {\r\n            handler(newVal, oldVal) {\r\n                this.$refs[\"form\"].validateField([\"image\"],async (valid)=>{\r\n                  if(this.form.image){\r\n                      if(valid){\r\n                        this.$refs[\"form\"].clearValidate('image'); \r\n                      }\r\n                    }\r\n                })\r\n            },\r\n            deep: true,\r\n        },\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        changeImg(e) {\r\n            console.log(e);\r\n        },\r\n        /** 查询轮播图列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listBanner(this.queryParams).then((response) => {\r\n                this.bannerList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                name: null,\r\n                category: null,\r\n                image: null,\r\n                url: null,\r\n                remark: null,\r\n                sorts: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加轮播图\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getBanner(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改轮播图\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            console.log(this.form);\r\n            // if (!this.form.category || !this.form.image) {\r\n            //     this.$message({\r\n            //         message: \"请填写必填项\",\r\n            //         type: \"warning\",\r\n            //     });\r\n            //     return;\r\n            // }\r\n            // if (this.form.sorts && this.form.sorts < 0) {\r\n            //     this.$message({\r\n            //         message: \"请填写正确的排序\",\r\n            //         type: \"warning\",\r\n            //     });\r\n            //     return;\r\n            // }\r\n            // let num = Math.floor(this.form.sorts) == this.form.sorts;\r\n            // if (this.form.sorts && !num) {\r\n            //     this.$message({\r\n            //         message: \"请填写正确的排序\",\r\n            //         type: \"warning\",\r\n            //     });\r\n            //     return;\r\n            // }\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (this.form.sorts && this.form.sorts < 0) {\r\n                    this.$message({\r\n                        message: \"请填写正确的排序\",\r\n                        type: \"warning\",\r\n                    });\r\n                    return;\r\n                }\r\n                let num = Math.floor(this.form.sorts) == this.form.sorts;\r\n                if (this.form.sorts && !num) {\r\n                    this.$message({\r\n                        message: \"请填写正确的排序\",\r\n                        type: \"warning\",\r\n                    });\r\n                    return;\r\n                }\r\n                if (valid) {\r\n                    if (this.form.id != null) {\r\n                        updateBanner(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addBanner(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除轮播图编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delBanner(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/banner/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `banner_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}