{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\RightPanel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\RightPanel\\index.vue", "mtime": 1750151094146}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightPanel", "sourcesContent": ["<template>\r\n  <div ref=\"rightPanel\" :class=\"{show:show}\" class=\"rightPanel-container\">\r\n    <div class=\"rightPanel-background\" />\r\n    <div class=\"rightPanel\">\r\n      <div class=\"rightPanel-items\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { addClass, removeClass } from '@/utils'\r\n\r\nexport default {\r\n  name: 'RightPanel',\r\n  props: {\r\n    clickNotClose: {\r\n      default: false,\r\n      type: Boolean\r\n    },\r\n    buttonTop: {\r\n      default: 250,\r\n      type: Number\r\n    }\r\n  },\r\n  computed: {\r\n    show: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    },\r\n  },\r\n  watch: {\r\n    show(value) {\r\n      if (value && !this.clickNotClose) {\r\n        this.addEventClick()\r\n      }\r\n      if (value) {\r\n        addClass(document.body, 'showRightPanel')\r\n      } else {\r\n        removeClass(document.body, 'showRightPanel')\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.insertToBody()\r\n    this.addEventClick()\r\n  },\r\n  beforeDestroy() {\r\n    const elx = this.$refs.rightPanel\r\n    elx.remove()\r\n  },\r\n  methods: {\r\n    addEventClick() {\r\n      window.addEventListener('click', this.closeSidebar)\r\n    },\r\n    closeSidebar(evt) {\r\n      const parent = evt.target.closest('.rightPanel')\r\n      if (!parent) {\r\n        this.show = false\r\n        window.removeEventListener('click', this.closeSidebar)\r\n      }\r\n    },\r\n    insertToBody() {\r\n      const elx = this.$refs.rightPanel\r\n      const body = document.querySelector('body')\r\n      body.insertBefore(elx, body.firstChild)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.showRightPanel {\r\n  overflow: hidden;\r\n  position: relative;\r\n  width: calc(100% - 15px);\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\" scoped>\r\n.rightPanel-background {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  opacity: 0;\r\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\r\n  background: rgba(0, 0, 0, .2);\r\n  z-index: -1;\r\n}\r\n\r\n.rightPanel {\r\n  width: 100%;\r\n  max-width: 260px;\r\n  height: 100vh;\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\r\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n  transform: translate(100%);\r\n  background: #fff;\r\n  z-index: 40000;\r\n}\r\n\r\n.show {\r\n  transition: all .3s cubic-bezier(.7, .3, .1, 1);\r\n\r\n  .rightPanel-background {\r\n    z-index: 20000;\r\n    opacity: 1;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .rightPanel {\r\n    transform: translate(0);\r\n  }\r\n}\r\n\r\n.handle-button {\r\n  width: 48px;\r\n  height: 48px;\r\n  position: absolute;\r\n  left: -48px;\r\n  text-align: center;\r\n  font-size: 24px;\r\n  border-radius: 6px 0 0 6px !important;\r\n  z-index: 0;\r\n  pointer-events: auto;\r\n  cursor: pointer;\r\n  color: #fff;\r\n  line-height: 48px;\r\n  i {\r\n    font-size: 24px;\r\n    line-height: 48px;\r\n  }\r\n}\r\n</style>\r\n"]}]}