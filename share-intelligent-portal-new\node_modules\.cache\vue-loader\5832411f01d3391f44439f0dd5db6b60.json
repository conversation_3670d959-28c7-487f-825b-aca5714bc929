{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue", "mtime": 1750476192303}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sso-callback.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sso-callback.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"sso-callback-container\">\n    <div class=\"loading-content\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">{{ loadingText }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { handleSSOCallback, getSSOTokenByKey } from \"@/api/login\";\nimport { setToken, setExpiresIn } from \"@/utils/auth\";\n\nexport default {\n  name: \"SSOCallback\",\n  data() {\n    return {\n      loadingText: \"正在处理SSO登录...\"\n    };\n  },\n  mounted() {\n    this.handleCallback();\n  },\n  methods: {\n    handleCallback() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const token = urlParams.get('token');\n      const key = urlParams.get('key');\n      const redirect = urlParams.get('redirect');\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n\n      // 如果有key参数，说明是从后端SSO回调重定向过来的，需要用key获取token\n      if (key) {\n        this.loadingText = \"正在获取登录凭证...\";\n\n        // 调用后端API用key换取token\n        getSSOTokenByKey(key)\n          .then(response => {\n            if (response.code === 200 && response.data) {\n              const tokenData = response.data;\n\n              this.loadingText = \"正在设置登录状态...\";\n\n              try {\n                // 设置token到store和cookies\n                const token = tokenData.access_token || tokenData.token;\n                this.$store.commit(\"SET_TOKEN\", token);\n                setToken(token);\n\n                // 设置过期时间\n                if (tokenData.expires_in) {\n                  this.$store.commit(\"SET_EXPIRES_IN\", tokenData.expires_in);\n                  setExpiresIn(tokenData.expires_in);\n                }\n\n                this.loadingText = \"登录成功，正在跳转...\";\n                this.$message.success(\"SSO登录成功\");\n\n                // 跳转到目标页面或首页\n                let redirectUrl = redirect || '/index';\n                // 如果redirect是完整URL，提取路径部分\n                if (redirectUrl.startsWith('http')) {\n                  try {\n                    const url = new URL(redirectUrl);\n                    redirectUrl = url.pathname + url.search + url.hash;\n                    // 如果提取的路径是根路径，跳转到首页\n                    if (redirectUrl === '/' || redirectUrl === '/login') {\n                      redirectUrl = '/index';\n                    }\n                  } catch (e) {\n                    console.warn('解析redirect URL失败，使用默认路径:', e);\n                    redirectUrl = '/index';\n                  }\n                }\n                setTimeout(() => {\n                  this.$router.push(redirectUrl);\n                }, 1000);\n\n              } catch (error) {\n                console.error(\"设置登录状态失败:\", error);\n                this.loadingText = \"登录状态设置失败\";\n                this.$message.error(\"登录状态设置失败\");\n                setTimeout(() => {\n                  this.$router.push('/login');\n                }, 2000);\n              }\n            } else {\n              this.loadingText = \"获取登录凭证失败\";\n              this.$message.error(response.msg || \"获取登录凭证失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"获取登录凭证异常:\", error);\n            this.loadingText = \"获取登录凭证异常\";\n            this.$message.error(\"获取登录凭证服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 如果有token参数，说明是直接传递token的方式\n      if (token) {\n        this.loadingText = \"正在设置登录状态...\";\n\n        try {\n          // 设置token到store和cookies\n          this.$store.commit(\"SET_TOKEN\", token);\n          setToken(token);\n\n          this.loadingText = \"登录成功，正在跳转...\";\n          this.$message.success(\"SSO登录成功\");\n\n          // 跳转到目标页面或首页\n          let redirectUrl = redirect || '/';\n          // 如果redirect是完整URL，提取路径部分\n          if (redirectUrl.startsWith('http')) {\n            try {\n              const url = new URL(redirectUrl);\n              redirectUrl = url.pathname + url.search + url.hash;\n            } catch (e) {\n              console.warn('解析redirect URL失败，使用默认路径:', e);\n              redirectUrl = '/';\n            }\n          }\n          setTimeout(() => {\n            this.$router.push(redirectUrl);\n          }, 1000);\n\n        } catch (error) {\n          console.error(\"设置登录状态失败:\", error);\n          this.loadingText = \"登录状态设置失败\";\n          this.$message.error(\"登录状态设置失败\");\n          setTimeout(() => {\n            this.$router.push('/login');\n          }, 2000);\n        }\n        return;\n      }\n\n      // 如果没有token但有code，说明是旧的API回调方式\n      if (code) {\n        this.loadingText = \"正在验证授权码...\";\n\n        handleSSOCallback(code, state)\n          .then(response => {\n            if (response.code === 200) {\n              this.loadingText = \"登录成功，正在跳转...\";\n              this.$message.success(\"SSO登录成功\");\n\n              // 设置token到store和cookies\n              if (response.data && response.data.access_token) {\n                this.$store.commit(\"SET_TOKEN\", response.data.access_token);\n                setToken(response.data.access_token);\n\n                // 设置过期时间\n                if (response.data.expires_in) {\n                  this.$store.commit(\"SET_EXPIRES_IN\", response.data.expires_in);\n                  setExpiresIn(response.data.expires_in);\n                }\n              }\n\n              // 跳转到目标页面或首页\n              let redirectUrl = state || '/';\n              // 如果redirect是完整URL，提取路径部分\n              if (redirectUrl.startsWith('http')) {\n                try {\n                  const url = new URL(redirectUrl);\n                  redirectUrl = url.pathname + url.search + url.hash;\n                } catch (e) {\n                  console.warn('解析redirect URL失败，使用默认路径:', e);\n                  redirectUrl = '/';\n                }\n              }\n              setTimeout(() => {\n                this.$router.push(redirectUrl);\n              }, 1000);\n            } else {\n              this.loadingText = \"SSO登录失败\";\n              this.$message.error(response.msg || \"SSO登录失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"SSO回调处理失败:\", error);\n            this.loadingText = \"SSO登录异常\";\n            this.$message.error(\"SSO登录服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 既没有token也没有code\n      this.loadingText = \"SSO登录失败：缺少必要参数\";\n      this.$message.error(\"SSO登录失败：缺少必要参数\");\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.sso-callback-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 18px;\n  font-weight: 500;\n}\n</style>\n"]}]}