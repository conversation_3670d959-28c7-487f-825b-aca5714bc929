{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue", "mtime": 1750474296593}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sso-callback.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sso-callback.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"sso-callback-container\">\n    <div class=\"loading-content\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">{{ loadingText }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { handleSSOCallback, getSSOTokenByKey } from \"@/api/login\";\n\nexport default {\n  name: \"SSOCallback\",\n  data() {\n    return {\n      loadingText: \"正在处理SSO登录...\"\n    };\n  },\n  mounted() {\n    this.handleCallback();\n  },\n  methods: {\n    handleCallback() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const token = urlParams.get('token');\n      const key = urlParams.get('key');\n      const redirect = urlParams.get('redirect');\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n\n      // 如果有key参数，说明是从后端SSO回调重定向过来的，需要用key获取token\n      if (key) {\n        this.loadingText = \"正在获取登录凭证...\";\n\n        // 调用后端API用key换取token\n        getSSOTokenByKey(key)\n          .then(response => {\n            if (response.code === 200 && response.data) {\n              const tokenData = response.data;\n\n              this.loadingText = \"正在设置登录状态...\";\n\n              try {\n                // 设置token到store\n                this.$store.commit(\"SET_TOKEN\", tokenData.access_token || tokenData.token);\n\n                this.loadingText = \"登录成功，正在跳转...\";\n                this.$message.success(\"SSO登录成功\");\n\n                // 跳转到目标页面或首页\n                let redirectUrl = redirect || '/';\n                // 如果redirect是完整URL，提取路径部分\n                if (redirectUrl.startsWith('http')) {\n                  try {\n                    const url = new URL(redirectUrl);\n                    redirectUrl = url.pathname + url.search + url.hash;\n                  } catch (e) {\n                    console.warn('解析redirect URL失败，使用默认路径:', e);\n                    redirectUrl = '/';\n                  }\n                }\n                setTimeout(() => {\n                  this.$router.push(redirectUrl);\n                }, 1000);\n\n              } catch (error) {\n                console.error(\"设置登录状态失败:\", error);\n                this.loadingText = \"登录状态设置失败\";\n                this.$message.error(\"登录状态设置失败\");\n                setTimeout(() => {\n                  this.$router.push('/login');\n                }, 2000);\n              }\n            } else {\n              this.loadingText = \"获取登录凭证失败\";\n              this.$message.error(response.msg || \"获取登录凭证失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"获取登录凭证异常:\", error);\n            this.loadingText = \"获取登录凭证异常\";\n            this.$message.error(\"获取登录凭证服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 如果有token参数，说明是直接传递token的方式\n      if (token) {\n        this.loadingText = \"正在设置登录状态...\";\n\n        try {\n          // 设置token到store\n          this.$store.commit(\"SET_TOKEN\", token);\n\n          this.loadingText = \"登录成功，正在跳转...\";\n          this.$message.success(\"SSO登录成功\");\n\n          // 跳转到目标页面或首页\n          let redirectUrl = redirect || '/';\n          // 如果redirect是完整URL，提取路径部分\n          if (redirectUrl.startsWith('http')) {\n            try {\n              const url = new URL(redirectUrl);\n              redirectUrl = url.pathname + url.search + url.hash;\n            } catch (e) {\n              console.warn('解析redirect URL失败，使用默认路径:', e);\n              redirectUrl = '/';\n            }\n          }\n          setTimeout(() => {\n            this.$router.push(redirectUrl);\n          }, 1000);\n\n        } catch (error) {\n          console.error(\"设置登录状态失败:\", error);\n          this.loadingText = \"登录状态设置失败\";\n          this.$message.error(\"登录状态设置失败\");\n          setTimeout(() => {\n            this.$router.push('/login');\n          }, 2000);\n        }\n        return;\n      }\n\n      // 如果没有token但有code，说明是旧的API回调方式\n      if (code) {\n        this.loadingText = \"正在验证授权码...\";\n\n        handleSSOCallback(code, state)\n          .then(response => {\n            if (response.code === 200) {\n              this.loadingText = \"登录成功，正在跳转...\";\n              this.$message.success(\"SSO登录成功\");\n\n              // 设置token\n              if (response.data && response.data.access_token) {\n                this.$store.commit(\"SET_TOKEN\", response.data.access_token);\n              }\n\n              // 跳转到目标页面或首页\n              let redirectUrl = state || '/';\n              // 如果redirect是完整URL，提取路径部分\n              if (redirectUrl.startsWith('http')) {\n                try {\n                  const url = new URL(redirectUrl);\n                  redirectUrl = url.pathname + url.search + url.hash;\n                } catch (e) {\n                  console.warn('解析redirect URL失败，使用默认路径:', e);\n                  redirectUrl = '/';\n                }\n              }\n              setTimeout(() => {\n                this.$router.push(redirectUrl);\n              }, 1000);\n            } else {\n              this.loadingText = \"SSO登录失败\";\n              this.$message.error(response.msg || \"SSO登录失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"SSO回调处理失败:\", error);\n            this.loadingText = \"SSO登录异常\";\n            this.$message.error(\"SSO登录服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 既没有token也没有code\n      this.loadingText = \"SSO登录失败：缺少必要参数\";\n      this.$message.error(\"SSO登录失败：缺少必要参数\");\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.sso-callback-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 18px;\n  font-weight: 500;\n}\n</style>\n"]}]}