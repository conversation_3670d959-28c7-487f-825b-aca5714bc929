{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue", "mtime": 1750413626109}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sso-callback.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sso-callback.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"sso-callback-container\">\n    <div class=\"loading-content\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">{{ loadingText }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { handleSSOCallback } from \"@/api/login\";\n\nexport default {\n  name: \"SSOCallback\",\n  data() {\n    return {\n      loadingText: \"正在处理SSO登录...\"\n    };\n  },\n  mounted() {\n    this.handleCallback();\n  },\n  methods: {\n    handleCallback() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const token = urlParams.get('token');\n      const redirect = urlParams.get('redirect');\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n\n      // 如果有token参数，说明是从后端SSO回调重定向过来的\n      if (token) {\n        this.loadingText = \"正在设置登录状态...\";\n\n        try {\n          // 设置token到store\n          this.$store.commit(\"SET_TOKEN\", token);\n\n          this.loadingText = \"登录成功，正在跳转...\";\n          this.$message.success(\"SSO登录成功\");\n\n          // 跳转到目标页面或首页\n          const redirectUrl = redirect || '/';\n          setTimeout(() => {\n            this.$router.push(redirectUrl);\n          }, 1000);\n\n        } catch (error) {\n          console.error(\"设置登录状态失败:\", error);\n          this.loadingText = \"登录状态设置失败\";\n          this.$message.error(\"登录状态设置失败\");\n          setTimeout(() => {\n            this.$router.push('/login');\n          }, 2000);\n        }\n        return;\n      }\n\n      // 如果没有token但有code，说明是旧的API回调方式\n      if (code) {\n        this.loadingText = \"正在验证授权码...\";\n\n        handleSSOCallback(code, state)\n          .then(response => {\n            if (response.code === 200) {\n              this.loadingText = \"登录成功，正在跳转...\";\n              this.$message.success(\"SSO登录成功\");\n\n              // 设置token\n              if (response.data && response.data.access_token) {\n                this.$store.commit(\"SET_TOKEN\", response.data.access_token);\n              }\n\n              // 跳转到目标页面或首页\n              const redirectUrl = state || '/';\n              setTimeout(() => {\n                this.$router.push(redirectUrl);\n              }, 1000);\n            } else {\n              this.loadingText = \"SSO登录失败\";\n              this.$message.error(response.msg || \"SSO登录失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"SSO回调处理失败:\", error);\n            this.loadingText = \"SSO登录异常\";\n            this.$message.error(\"SSO登录服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 既没有token也没有code\n      this.loadingText = \"SSO登录失败：缺少必要参数\";\n      this.$message.error(\"SSO登录失败：缺少必要参数\");\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.sso-callback-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 18px;\n  font-weight: 500;\n}\n</style>\n"]}]}