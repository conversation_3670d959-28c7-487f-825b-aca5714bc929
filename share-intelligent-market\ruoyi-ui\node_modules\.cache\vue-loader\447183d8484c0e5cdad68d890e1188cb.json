{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\scientific_joint\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\scientific_joint\\index.vue", "mtime": 1750151094264}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3RTY2llbnRpZmljX2pvaW50LA0KICAgIGdldFNjaWVudGlmaWNfam9pbnQsDQogICAgZGVsU2NpZW50aWZpY19qb2ludCwNCiAgICBhZGRTY2llbnRpZmljX2pvaW50LA0KICAgIHVwZGF0ZVNjaWVudGlmaWNfam9pbnQsDQp9IGZyb20gIkAvYXBpL3V1Yy9zY2llbnRpZmljX2pvaW50IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJTY2llbnRpZmljX2pvaW50IiwNCiAgICBkYXRhKCkgew0KICAgICAgICBsZXQgY2hlY2tQaG9uZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgICAgICAgIGxldCByZWcgPSAvXjFbMzQ1Nzg5XVxkezl9JC87DQogICAgICAgICAgICBpZiAoIXJlZy50ZXN0KHZhbHVlKSkgew0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6K+36L6T5YWlMTHkvY3miYvmnLrlj7ciKSk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgICAgICB9DQogICAgICAgIH07DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAvLyDpga7nvanlsYINCiAgICAgICAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAgICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgICAgICAgIGlkczogW10sDQogICAgICAgICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgICAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAgICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgICAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAgICAgICAvLyDmgLvmnaHmlbANCiAgICAgICAgICAgIHRvdGFsOiAwLA0KICAgICAgICAgICAgLy8g5LiT5a625ZCI5L2c6KGo5qC85pWw5o2uDQogICAgICAgICAgICBzY2llbnRpZmljX2pvaW50TGlzdDogW10sDQogICAgICAgICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgICAgICAgIHRpdGxlOiAiIiwNCiAgICAgICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgICAgICAgb3BlbjogZmFsc2UsDQogICAgICAgICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgICAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgICAgICAgICAgc2NpZW50aWZpSWQ6IG51bGwsDQogICAgICAgICAgICAgICAgc2NpZW50aWZpTmFtZTogbnVsbCwNCiAgICAgICAgICAgICAgICBsaW5rTWFuOiBudWxsLA0KICAgICAgICAgICAgICAgIGxpbmtUZWw6IG51bGwsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICAgICAgICBmb3JtOiB7fSwNCiAgICAgICAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgICAgICAgcnVsZXM6IHsNCiAgICAgICAgICAgICAgICBzY2llbnRpZmlJZDogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLkuJPlrrZJROS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICBzY2llbnRpZmlOYW1lOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuS4k+WutuWQjeensOS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICBsaW5rTWFuOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuiBlOezu+S6uuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICBsaW5rVGVsOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuiBlOezu+eUteivneS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdmFsaWRhdG9yOiBjaGVja1Bob25lLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOaJi+acuuWPtyIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIH0sDQogICAgICAgIH07DQogICAgfSwNCiAgICBjcmVhdGVkKCkgew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIG1ldGhvZHM6IHsNCiAgICAgICAgLyoqIOafpeivouS4k+WutuWQiOS9nOWIl+ihqCAqLw0KICAgICAgICBnZXRMaXN0KCkgew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgIGxpc3RTY2llbnRpZmljX2pvaW50KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5zY2llbnRpZmljX2pvaW50TGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOWPlua2iOaMiemSrg0KICAgICAgICBjYW5jZWwoKSB7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g6KGo5Y2V6YeN572uDQogICAgICAgIHJlc2V0KCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICAgIGlkOiBudWxsLA0KICAgICAgICAgICAgICAgIHNjaWVudGlmaUlkOiBudWxsLA0KICAgICAgICAgICAgICAgIHNjaWVudGlmaU5hbWU6IG51bGwsDQogICAgICAgICAgICAgICAgbGlua01hbjogbnVsbCwNCiAgICAgICAgICAgICAgICBsaW5rVGVsOiBudWxsLA0KICAgICAgICAgICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICAgICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICAgICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgICAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICAgICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsNCiAgICAgICAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgICAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVBZGQoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDkuJPlrrblkIjkvZwiOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgICAgICAgZ2V0U2NpZW50aWZpY19qb2ludChpZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnkuJPlrrblkIjkvZwiOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICAgICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVTY2llbnRpZmljX2pvaW50KHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgYWRkU2NpZW50aWZpY19qb2ludCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgICAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgICAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOS4k+WutuWQiOS9nOe8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAgICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRlbFNjaWVudGlmaWNfam9pbnQoaWRzKTsNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICAgICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAgICAgICAgICJ1dWMvc2NpZW50aWZpY19qb2ludC9leHBvcnQiLA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIGBzY2llbnRpZmljX2pvaW50XyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgDQogICAgICAgICAgICApOw0KICAgICAgICB9LA0KICAgIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuNA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/ningmengdou/scientific_joint", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"专家ID\" prop=\"scientifiId\">\r\n                <el-input\r\n                    v-model=\"queryParams.scientifiId\"\r\n                    placeholder=\"请输入专家ID\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"专家名称\" prop=\"scientifiName\">\r\n                <el-input\r\n                    v-model=\"queryParams.scientifiName\"\r\n                    placeholder=\"请输入专家名称\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"linkMan\">\r\n                <el-input\r\n                    v-model=\"queryParams.linkMan\"\r\n                    placeholder=\"请输入联系人\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\" prop=\"linkTel\">\r\n                <el-input\r\n                    v-model=\"queryParams.linkTel\"\r\n                    placeholder=\"请输入联系电话\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:scientific_joint:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:scientific_joint:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:scientific_joint:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['uuc:scientific_joint:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"scientific_jointList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n            <el-table-column label=\"专家ID\" align=\"center\" prop=\"scientifiId\" />\r\n            <el-table-column\r\n                label=\"专家名称\"\r\n                align=\"center\"\r\n                prop=\"scientifiName\"\r\n            />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"linkMan\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"linkTel\" />\r\n            <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:scientific_joint:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:scientific_joint:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改专家合作对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n                <el-form-item label=\"专家ID\" prop=\"scientifiId\">\r\n                    <el-input\r\n                        v-model=\"form.scientifiId\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入专家ID\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"专家名称\" prop=\"scientifiName\">\r\n                    <el-input\r\n                        v-model=\"form.scientifiName\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入专家名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" prop=\"linkMan\">\r\n                    <el-input\r\n                        v-model=\"form.linkMan\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入联系人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系电话\" prop=\"linkTel\">\r\n                    <el-input\r\n                        v-model=\"form.linkTel\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        maxlength=\"500\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listScientific_joint,\r\n    getScientific_joint,\r\n    delScientific_joint,\r\n    addScientific_joint,\r\n    updateScientific_joint,\r\n} from \"@/api/uuc/scientific_joint\";\r\n\r\nexport default {\r\n    name: \"Scientific_joint\",\r\n    data() {\r\n        let checkPhone = (rule, value, callback) => {\r\n            let reg = /^1[345789]\\d{9}$/;\r\n            if (!reg.test(value)) {\r\n                callback(new Error(\"请输入11位手机号\"));\r\n            } else {\r\n                callback();\r\n            }\r\n        };\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 专家合作表格数据\r\n            scientific_jointList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                scientifiId: null,\r\n                scientifiName: null,\r\n                linkMan: null,\r\n                linkTel: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            // 表单校验\r\n            rules: {\r\n                scientifiId: [\r\n                    {\r\n                        required: true,\r\n                        message: \"专家ID不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                scientifiName: [\r\n                    {\r\n                        required: true,\r\n                        message: \"专家名称不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                linkMan: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                linkTel: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系电话不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    {\r\n                        type: \"number\",\r\n                        validator: checkPhone,\r\n                        message: \"请输入正确的手机号\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        /** 查询专家合作列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listScientific_joint(this.queryParams).then((response) => {\r\n                this.scientific_jointList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                scientifiId: null,\r\n                scientifiName: null,\r\n                linkMan: null,\r\n                linkTel: null,\r\n                remark: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加专家合作\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getScientific_joint(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改专家合作\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.id != null) {\r\n                        updateScientific_joint(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addScientific_joint(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除专家合作编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delScientific_joint(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/scientific_joint/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `scientific_joint_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}