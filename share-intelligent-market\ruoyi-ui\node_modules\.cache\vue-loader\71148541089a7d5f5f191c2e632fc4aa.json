{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\list.vue?vue&type=template&id=6911e694", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\list.vue", "mtime": 1750151094290}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}