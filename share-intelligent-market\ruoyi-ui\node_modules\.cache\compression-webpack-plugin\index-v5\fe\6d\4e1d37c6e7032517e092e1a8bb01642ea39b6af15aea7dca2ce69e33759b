
847bf30d8e178fd4ebedf5027c991457e3518f34	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fui\\u002Ftinymce-5\\u002Fskin.shadowdom.css\",\"contentHash\":\"5fe8b8fe42fb864a4a13a34654599273\"}","integrity":"sha512-G+k2YXyxH7CCaCzU5+0MCS3N+Q1vTxxhDEfFEq4L6kzedF1A6ZmGRCjEUuCxEVGtLYIDCOclkl9+188XkuSC5A==","time":1750496064280,"size":1045}