09:11:22.150 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:11:22.306 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:11:22.994 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:11:22.994 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:11:28.257 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:26:15.627 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:26:15.685 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:26:16.026 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:26:16.026 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:26:18.243 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:28:35.520 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:28:35.587 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:28:35.957 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:28:35.958 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:28:38.158 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:28:40.639 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:28:40.644 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:28:40.644 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
09:28:40.979 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:28:43.314 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:28:45.144 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:28:45.204 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:28:45.204 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:28:45.414 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
09:28:45.643 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 10.767 seconds (JVM running for 12.144)
09:28:45.695 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
09:28:45.695 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
09:28:45.696 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
09:28:45.914 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:57:10.915 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
11:57:10.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
11:57:16.833 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:57:16.930 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:57:17.464 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:57:17.466 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:57:21.624 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:57:25.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
11:57:25.508 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:57:25.509 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
11:57:25.838 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:57:28.869 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:57:31.388 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
11:57:31.462 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:57:31.462 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:57:31.683 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
11:57:31.971 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 16.014 seconds (JVM running for 17.676)
11:57:32.040 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
11:57:32.040 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
11:57:32.041 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
11:57:32.231 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:38:13.328 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1940513
13:38:13.328 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：592801,如非本人操作，请忽略本短信！
13:42:00.875 [http-nio-9200-exec-5] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1940925
13:42:00.876 [http-nio-9200-exec-5] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：998192,如非本人操作，请忽略本短信！
13:43:14.285 [http-nio-9200-exec-10] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1941066
13:43:14.286 [http-nio-9200-exec-10] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：148963,如非本人操作，请忽略本短信！
13:55:33.643 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1942393
13:55:33.643 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：177984,如非本人操作，请忽略本短信！
14:26:03.606 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:26:03.723 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:26:04.263 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:04.264 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:26:08.471 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:26:08.637 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:26:08.752 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:26:09.614 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:09.615 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:26:14.284 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:26:14.289 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:26:14.290 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
14:26:14.494 [main] INFO  c.r.c.a.RuoYiCasAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
14:26:14.744 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:26:18.846 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:26:18.852 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:26:18.852 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
14:26:19.323 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:26:19.542 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:26:22.653 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:26:22.664 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:26:22.718 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
14:26:22.718 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:26:22.727 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
14:26:22.728 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
14:26:25.457 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:26:25.546 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:25.547 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:26:25.781 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-cas-auth ************:9300 register finished
14:26:26.107 [main] INFO  c.r.c.a.RuoYiCasAuthApplication - [logStarted,61] - Started RuoYiCasAuthApplication in 18.496 seconds (JVM running for 20.423)
14:26:26.167 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-cas-auth-dev.yml, group=DEFAULT_GROUP
14:26:26.167 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-cas-auth.yml, group=DEFAULT_GROUP
14:26:26.168 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-cas-auth, group=DEFAULT_GROUP
14:26:27.299 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:26:46.776 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:26:46.837 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:26:47.226 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:26:47.226 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:26:49.644 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:26:51.951 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:26:51.955 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:26:51.955 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
14:26:52.229 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:26:55.003 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:26:57.210 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:26:57.260 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
14:26:57.261 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:26:57.269 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
14:26:57.271 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
14:27:14.073 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:27:14.194 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:14.756 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:27:14.757 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:27:26.412 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:27:29.299 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:27:29.303 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:27:29.303 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
14:27:29.618 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:27:32.671 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:27:35.530 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:27:35.613 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:27:35.613 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:27:44.636 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
14:27:44.637 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:27:44.644 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
14:27:44.649 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
14:28:20.738 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:28:20.807 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:28:21.262 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:28:21.262 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:28:24.101 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:28:27.034 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:28:27.037 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:28:27.037 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
14:28:27.350 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:28:30.598 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:28:32.896 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:28:33.033 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:28:33.033 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:28:33.269 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
14:28:33.546 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.549 seconds (JVM running for 15.308)
14:28:33.594 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
14:28:33.595 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
14:28:33.595 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
14:28:33.941 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:35:30.308 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1948621
14:35:30.309 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：788340,如非本人操作，请忽略本短信！
14:36:47.080 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
14:36:47.082 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
14:36:50.448 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:36:50.526 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:36:50.886 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:36:50.886 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:36:53.263 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:36:55.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:36:55.555 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:36:55.556 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
14:36:55.819 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:36:58.154 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:37:00.275 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:37:00.341 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:37:00.341 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:37:00.566 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
14:37:00.809 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 10.97 seconds (JVM running for 12.361)
14:37:00.855 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
14:37:00.856 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
14:37:00.856 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
14:37:01.239 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:37:56.397 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1949079
14:37:56.397 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：747739,如非本人操作，请忽略本短信！
14:39:07.403 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1949328
14:39:07.404 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：447581,如非本人操作，请忽略本短信！
14:41:27.764 [http-nio-9200-exec-7] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1949759
14:41:27.764 [http-nio-9200-exec-7] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：785803,如非本人操作，请忽略本短信！
14:42:44.528 [http-nio-9200-exec-10] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1950030
14:42:44.528 [http-nio-9200-exec-10] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：623043,如非本人操作，请忽略本短信！
14:51:07.456 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1951720
14:51:07.457 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：976190,如非本人操作，请忽略本短信！
14:52:58.326 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
14:52:58.331 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
14:53:02.486 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:53:02.554 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:53:03.012 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:53:03.012 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:53:05.667 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
14:53:08.431 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:53:08.433 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:53:08.435 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
14:53:08.700 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:53:11.550 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:53:13.825 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:53:13.911 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:53:13.911 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:53:14.135 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
14:53:14.437 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 12.705 seconds (JVM running for 14.409)
14:53:14.486 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
14:53:14.488 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
14:53:14.488 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
14:53:14.815 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:53:39.085 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1952434
14:53:39.085 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：432097,如非本人操作，请忽略本短信！
14:54:40.540 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1952711
14:54:40.540 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：303179,如非本人操作，请忽略本短信！
14:57:36.055 [http-nio-9200-exec-8] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1953285
14:57:36.056 [http-nio-9200-exec-8] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：095407,如非本人操作，请忽略本短信！
14:58:22.966 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
14:58:22.968 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
14:58:37.571 [http-nio-9200-exec-9] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1953471
14:58:37.572 [http-nio-9200-exec-9] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：719940,如非本人操作，请忽略本短信！
14:59:24.274 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1953645
14:59:24.274 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：681070,如非本人操作，请忽略本短信！
15:00:31.843 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1953900
15:00:31.844 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：177675,如非本人操作，请忽略本短信！
15:02:09.388 [http-nio-9200-exec-5] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1954283
15:02:09.389 [http-nio-9200-exec-5] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：410542,如非本人操作，请忽略本短信！
15:02:26.796 [http-nio-9200-exec-9] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1954364
15:02:26.796 [http-nio-9200-exec-9] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：388801,如非本人操作，请忽略本短信！
15:03:02.669 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1954513
15:03:02.670 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：492407,如非本人操作，请忽略本短信！
15:05:07.577 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1955230
15:05:07.577 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：889669,如非本人操作，请忽略本短信！
15:05:57.524 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1955961
15:05:57.526 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：683508,如非本人操作，请忽略本短信！
15:06:58.049 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1956163
15:06:58.049 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：748732,如非本人操作，请忽略本短信！
15:17:38.022 [http-nio-9200-exec-10] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1958554
15:17:38.022 [http-nio-9200-exec-10] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：680781,如非本人操作，请忽略本短信！
15:18:17.935 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1958709
15:18:17.935 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 19323037514, content: 您的验证码为：953820,如非本人操作，请忽略本短信！
15:30:03.303 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1961192
15:30:03.303 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：179500,如非本人操作，请忽略本短信！
15:35:00.065 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
15:35:00.067 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
15:35:05.342 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:35:05.412 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:35:05.817 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:35:05.818 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:35:08.222 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:35:10.745 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:35:10.749 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:35:10.749 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
15:35:11.026 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:35:13.542 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:35:15.821 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:35:15.923 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:35:15.923 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:35:16.154 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
15:35:16.475 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 11.755 seconds (JVM running for 13.029)
15:35:16.526 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
15:35:16.526 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
15:35:16.526 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
15:35:17.211 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:35:37.577 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1962232
15:35:37.578 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：759110,如非本人操作，请忽略本短信！
15:38:32.647 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1962726
15:38:32.647 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：028726,如非本人操作，请忽略本短信！
15:43:29.708 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
15:43:29.710 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
15:43:37.664 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:43:37.779 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:43:38.312 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:43:38.312 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:43:41.055 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:43:43.761 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:43:43.769 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:43:43.769 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
15:43:44.070 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:43:47.316 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
15:45:24.213 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:45:24.274 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:45:24.667 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:45:24.667 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:45:27.421 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:45:30.081 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:45:30.086 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:45:30.086 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
15:45:30.434 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:45:34.449 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:45:37.121 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:45:37.220 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:45:37.221 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:45:37.451 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
15:45:37.751 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 14.236 seconds (JVM running for 15.709)
15:45:37.816 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
15:45:37.817 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
15:45:37.819 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
15:45:38.517 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:45:50.802 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1963902
15:45:50.802 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：103831,如非本人操作，请忽略本短信！
15:46:52.191 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1964034
15:46:52.193 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：826566,如非本人操作，请忽略本短信！
15:47:53.140 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1964253
15:47:53.140 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：453144,如非本人操作，请忽略本短信！
15:51:57.093 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
15:51:57.097 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
15:52:07.222 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:52:07.307 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:52:07.764 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:52:07.764 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:52:10.659 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:52:13.532 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:52:13.537 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:52:13.537 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
15:52:13.880 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:52:18.059 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:52:20.656 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:52:20.739 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:52:20.739 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:52:21.000 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
15:52:21.256 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 14.924 seconds (JVM running for 16.805)
15:52:21.321 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
15:52:21.321 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
15:52:21.321 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
15:52:21.587 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:55:00.174 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
15:55:00.177 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
15:55:38.450 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:55:38.518 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:55:38.880 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:55:38.880 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:55:41.348 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:55:43.999 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:55:44.003 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:55:44.003 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
15:55:44.267 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:55:48.269 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:55:50.719 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:55:50.791 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:55:50.792 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:55:51.017 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
15:55:51.253 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.432 seconds (JVM running for 14.826)
15:55:51.269 [main] INFO  c.r.a.c.FeignWarmupConfig - [run,28] - 开始预热Feign客户端...
15:55:51.305 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
15:55:51.306 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
15:55:51.306 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
15:55:52.133 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:55:56.278 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
15:55:57.528 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
15:55:58.543 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
15:55:59.547 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [lambda$run$0,42] - Feign客户端预热完成
15:56:15.723 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1965892
15:56:15.723 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：962909,如非本人操作，请忽略本短信！
15:59:08.465 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1966422
15:59:08.465 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：850635,如非本人操作，请忽略本短信！
15:59:56.403 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1966624
15:59:56.403 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 19323037514, content: 您的验证码为：500361,如非本人操作，请忽略本短信！
16:02:26.024 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
16:02:26.028 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
16:02:31.204 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:02:31.263 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:02:31.636 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:02:31.636 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:02:34.037 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:02:36.308 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:02:36.313 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:02:36.313 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:02:36.561 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:02:39.902 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:02:41.935 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:02:42.003 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:02:42.004 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:02:42.226 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:02:42.465 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 11.899 seconds (JVM running for 13.316)
16:02:42.479 [main] INFO  c.r.a.c.FeignWarmupConfig - [run,28] - 开始预热Feign客户端...
16:02:42.510 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:02:42.511 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:02:42.512 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:02:43.164 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:02:47.487 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:02:47.605 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1967168
16:02:47.607 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 19323037514, content: 您的验证码为：983041,如非本人操作，请忽略本短信！
16:02:49.110 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,65] - SSO服务预热成功
16:02:50.117 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:04:04.560 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,65] - SSO服务预热成功
16:04:05.564 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:04:13.651 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1967507
16:04:13.652 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：511250,如非本人操作，请忽略本短信！
16:05:15.966 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,65] - SSO服务预热成功
16:05:16.977 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [lambda$run$0,42] - Feign客户端预热完成
16:06:08.902 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1967857
16:06:08.902 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：884436,如非本人操作，请忽略本短信！
16:06:27.340 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
16:06:27.342 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
16:06:30.510 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:06:30.573 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:06:30.960 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:06:30.960 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:06:33.892 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:06:36.385 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:06:36.388 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:06:36.389 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:06:36.721 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:06:40.166 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:06:42.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:06:42.420 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:06:42.420 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:06:42.665 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:06:42.934 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.031 seconds (JVM running for 14.361)
16:06:42.950 [main] INFO  c.r.a.c.FeignWarmupConfig - [run,28] - 开始预热Feign客户端...
16:06:42.994 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:06:42.995 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:06:42.996 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:06:43.027 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:06:47.959 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:06:57.857 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,65] - SSO服务预热成功
16:06:58.859 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:07:17.190 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1968020
16:07:17.190 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：459708,如非本人操作，请忽略本短信！
16:08:01.055 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,65] - SSO服务预热成功
16:08:06.135 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:09:12.710 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:09:12.781 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:09:13.185 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:09:13.186 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:09:15.684 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:09:18.190 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:09:18.194 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:09:18.194 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:09:18.486 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:09:22.225 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:09:24.856 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:09:24.938 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:09:24.939 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:09:25.181 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:09:25.439 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.365 seconds (JVM running for 14.902)
16:09:25.454 [main] INFO  c.r.a.c.FeignWarmupConfig - [run,28] - 开始预热Feign客户端...
16:09:25.486 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:09:25.486 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:09:25.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:09:25.989 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:09:30.467 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:09:31.665 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,65] - SSO服务预热成功
16:09:32.669 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:09:34.295 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1968447
16:09:34.295 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：111827,如非本人操作，请忽略本短信！
16:10:32.684 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,65] - SSO服务预热成功
16:10:33.695 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,59] - 预热SSO服务...
16:11:33.702 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [warmupSSOService,65] - SSO服务预热成功
16:11:34.702 [feign-warmup] INFO  c.r.a.c.FeignWarmupConfig - [lambda$run$0,42] - Feign客户端预热完成
16:14:19.639 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:14:19.706 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:14:20.131 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:14:20.131 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:14:22.671 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:14:25.489 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:14:25.492 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:14:25.492 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:14:25.782 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:14:27.572 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
16:14:28.612 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:14:28.637 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:14:28.673 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
16:14:29.208 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:14:31.541 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:14:31.612 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:14:31.612 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:14:31.829 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:14:32.084 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.116 seconds (JVM running for 14.715)
16:14:32.130 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:14:32.130 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:14:32.132 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:14:32.717 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:14:41.198 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1969262
16:14:41.199 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：376479,如非本人操作，请忽略本短信！
16:14:48.995 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/376479, 开始时间: 1750407288995
16:14:50.424 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750407290424
16:15:01.939 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750407301939
16:15:02.589 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750407302589
16:15:03.676 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750407303676
16:15:05.701 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750407305701
16:15:05.714 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /sso/user/create?memberPhone=18454831889&memberRealName=18454831889&password=%242a%2410%24a0LxWAff53JRI4RUF82TJuBNP98Piqygn5GkNDN3fSYu2KCYV/g5K, 开始时间: 1750407305714
16:16:13.469 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:16:13.534 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:16:13.942 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:16:13.942 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:16:16.544 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:16:19.389 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:16:19.393 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:16:19.393 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:16:19.693 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:16:21.687 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
16:16:22.807 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:16:22.841 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:16:22.884 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
16:16:23.466 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:16:25.604 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:16:25.686 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:16:25.687 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:16:25.905 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:16:26.178 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.376 seconds (JVM running for 14.866)
16:16:26.229 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:16:26.229 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:16:26.230 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:16:26.734 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:16:30.167 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1969576
16:16:30.168 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：403373,如非本人操作，请忽略本短信！
16:16:40.607 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/403373, 开始时间: 1750407400607
16:16:42.241 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750407402241
16:16:46.095 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750407406095
16:16:46.702 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750407406702
16:17:01.359 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /sso/user/create?memberPhone=18454831889&memberRealName=18454831889&password=%242a%2410%24a0LxWAff53JRI4RUF82TJuBNP98Piqygn5GkNDN3fSYu2KCYV/g5K, 开始时间: 1750407421359
16:18:41.556 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST http://ruoyi-sso/sso/user/create?memberPhone=18454831889&memberRealName=18454831889&password=%242a%2410%24a0LxWAff53JRI4RUF82TJuBNP98Piqygn5GkNDN3fSYu2KCYV/g5K, 开始时间: 1750407521554
16:20:21.795 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST http://ruoyi-sso/sso/user/create?memberPhone=18454831889&memberRealName=18454831889&password=%242a%2410%24a0LxWAff53JRI4RUF82TJuBNP98Piqygn5GkNDN3fSYu2KCYV/g5K, 开始时间: 1750407621795
16:22:05.400 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750407725400
16:24:58.218 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
16:24:58.221 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
16:25:08.999 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:25:09.071 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:25:09.482 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:25:09.482 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:25:12.014 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:25:14.487 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:25:14.491 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:25:14.491 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:25:14.780 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:25:16.818 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
16:25:18.048 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:25:18.081 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:25:18.120 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
16:25:18.782 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:25:21.478 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:25:21.554 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:25:21.554 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:25:21.795 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:25:22.059 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.722 seconds (JVM running for 15.274)
16:25:22.110 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:25:22.111 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:25:22.111 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:25:22.727 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:25:49.004 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1970985
16:25:49.004 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：597718,如非本人操作，请忽略本短信！
16:25:57.541 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/597718, 开始时间: 1750407957541
16:25:58.031 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750407958031
16:25:58.116 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750407958116
16:25:58.151 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750407958151
16:26:00.125 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /sso/user/create?memberPhone=18454831889&memberRealName=18454831889&password=%242a%2410%24a0LxWAff53JRI4RUF82TJuBNP98Piqygn5GkNDN3fSYu2KCYV/g5K, 开始时间: 1750407960125
16:26:00.264 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750407960264
16:26:31.211 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750407991211
16:26:44.275 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/info/18454831889, 开始时间: 1750408004275
16:26:44.364 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750408004364
16:27:35.282 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1971272
16:27:35.285 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 19323037514, content: 您的验证码为：414212,如非本人操作，请忽略本短信！
16:27:44.523 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/19323037514/414212, 开始时间: 1750408064523
16:27:44.531 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/19323037514, 开始时间: 1750408064531
16:27:44.540 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750408064540
16:27:44.557 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/19323037514, 开始时间: 1750408064557
16:27:44.662 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /sso/user/create?memberPhone=19323037514&memberRealName=19323037514&password=%242a%2410%245y8DFLriuyTFMg1lA7RtbO0mYdh8bBIa110HHxd9Yl5nmdOsBJY2S, 开始时间: 1750408064662
16:27:44.675 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750408064675
16:27:48.785 [http-nio-9200-exec-8] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750408068785
16:28:00.464 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/info/19323037514, 开始时间: 1750408080464
16:28:00.471 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750408080471
16:34:04.208 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
16:34:04.213 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
16:34:09.284 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:34:09.365 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:34:09.892 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:34:09.893 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:34:12.574 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:34:15.150 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:34:15.155 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:34:15.156 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:34:15.420 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:34:17.317 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
16:34:18.455 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:34:18.484 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:34:18.521 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
16:34:19.071 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:34:21.397 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:34:21.465 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:34:21.466 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:34:21.702 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:34:21.952 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.406 seconds (JVM running for 14.668)
16:34:22.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:34:22.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:34:22.007 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:34:22.435 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:35:51.196 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1972670
16:35:51.196 [http-nio-9200-exec-2] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：917446,如非本人操作，请忽略本短信！
16:36:02.620 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/917446, 开始时间: 1750408562620
16:36:03.031 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750408563031
16:36:05.305 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750408565305
16:36:05.339 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750408565339
16:36:05.372 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750408565372
16:39:16.370 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750408756370
16:39:25.423 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1973249
16:39:25.428 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 19323037514, content: 您的验证码为：560606,如非本人操作，请忽略本短信！
16:39:36.633 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/19323037514/560606, 开始时间: 1750408776633
16:39:37.821 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/19323037514, 开始时间: 1750408777821
16:41:17.998 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET http://ruoyi-portalweb/Member/smsCodeInfo/19323037514, 开始时间: 1750408877997
16:44:18.886 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
16:44:18.888 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
16:44:24.109 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:44:24.176 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:44:24.574 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:44:24.574 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:44:27.102 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:44:29.717 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:44:29.721 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:44:29.721 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:44:30.037 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:44:31.917 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
16:44:33.001 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:44:33.026 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:44:33.062 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
16:44:33.637 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:44:35.952 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:44:36.036 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:44:36.037 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:44:36.261 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:44:36.552 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.124 seconds (JVM running for 14.599)
16:44:36.612 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:44:36.624 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:44:36.624 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:44:36.732 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:44:40.749 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1974112
16:44:40.751 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 19323037514, content: 您的验证码为：620742,如非本人操作，请忽略本短信！
16:44:46.431 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/19323037514/620742, 开始时间: 1750409086430
16:44:46.857 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/19323037514, 开始时间: 1750409086857
16:44:46.930 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409086930
16:44:46.957 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/19323037514, 开始时间: 1750409086957
16:44:48.789 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409088789
16:44:55.407 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409095407
16:45:34.032 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1974216
16:45:34.033 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：626764,如非本人操作，请忽略本短信！
16:45:53.335 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/626764, 开始时间: 1750409153335
16:45:53.345 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750409153345
16:46:41.091 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409201091
16:46:41.103 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750409201103
16:46:41.116 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409201116
16:46:55.190 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1974412
16:46:55.192 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：806367,如非本人操作，请忽略本短信！
16:47:08.181 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/806367, 开始时间: 1750409228181
16:47:08.206 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750409228206
16:48:07.344 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409287344
16:48:07.354 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750409287354
16:48:07.362 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409287362
16:48:29.638 [http-nio-9200-exec-8] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1974667
16:48:29.638 [http-nio-9200-exec-8] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：650557,如非本人操作，请忽略本短信！
16:48:34.768 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/650557, 开始时间: 1750409314768
16:48:34.784 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750409314784
16:48:55.534 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
16:48:55.597 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:48:55.982 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:48:55.982 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:48:58.540 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:49:01.043 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:49:01.048 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:49:01.048 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
16:49:01.326 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:49:03.145 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
16:49:04.198 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:49:04.230 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
16:49:04.278 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
16:49:04.835 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:49:07.083 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:49:07.157 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
16:49:07.157 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
16:49:07.378 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
16:49:07.638 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 12.77 seconds (JVM running for 14.336)
16:49:07.686 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
16:49:07.687 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
16:49:07.688 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:49:07.966 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:49:18.450 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1974782
16:49:18.451 [http-nio-9200-exec-1] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：204565,如非本人操作，请忽略本短信！
16:49:24.332 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/204565, 开始时间: 1750409364332
16:50:19.104 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1974980
16:50:19.104 [http-nio-9200-exec-3] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：392584,如非本人操作，请忽略本短信！
16:50:27.773 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/392584, 开始时间: 1750409427773
16:50:29.414 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750409429414
16:50:29.905 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409429905
16:50:29.939 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750409429939
16:50:32.018 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409432018
16:51:25.794 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409485794
16:51:33.328 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1975253
16:51:33.328 [http-nio-9200-exec-6] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：167721,如非本人操作，请忽略本短信！
16:51:41.203 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/167721, 开始时间: 1750409501203
16:51:41.830 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750409501830
16:51:55.462 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409515462
16:51:55.474 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750409515474
16:51:55.486 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409515486
16:52:01.354 [http-nio-9200-exec-8] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409521354
16:52:15.133 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/info/18454831889, 开始时间: 1750409535133
16:52:15.239 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409535239
16:52:26.687 [http-nio-9200-exec-10] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409546687
16:54:26.888 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,66] - 全网智能通讯平台短信发送响应: OK|1975663
16:54:26.888 [http-nio-9200-exec-4] INFO  c.r.a.u.QWTSendUtils - [sendQWTSms,77] - 短信发送成功！telephone: 18454831889, content: 您的验证码为：576519,如非本人操作，请忽略本短信！
16:54:35.945 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /Member/checkUserSmsCode/18454831889/576519, 开始时间: 1750409675945
16:54:35.956 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750409675956
16:54:36.268 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409676268
16:54:36.277 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /sso/user/exists/18454831889, 开始时间: 1750409676277
16:54:36.285 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409676285
16:54:53.783 [http-nio-9200-exec-6] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409693783
16:55:02.924 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/info/18454831889, 开始时间: 1750409702924
16:55:03.006 [http-nio-9200-exec-7] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409703006
16:55:06.009 [http-nio-9200-exec-8] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750409706009
16:55:21.308 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: 31461b38f84a4e29b93b0e640fab53fc, 状态: http://localhost:81/login
17:00:12.920 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
17:00:12.922 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
17:00:18.719 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
17:00:18.781 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:00:19.192 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:00:19.192 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:00:21.605 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:00:24.270 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
17:00:24.274 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:00:24.274 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
17:00:24.561 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:00:26.330 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
17:00:27.392 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:00:27.425 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:00:27.464 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
17:00:27.992 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:00:30.565 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
17:00:30.653 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:00:30.653 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:00:30.873 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
17:00:31.143 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.107 seconds (JVM running for 14.564)
17:00:31.197 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
17:00:31.198 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
17:00:31.199 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
17:00:31.354 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:01:39.211 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: 01597f10cea441feb74b8e1fb3e830b4, 状态: http://localhost:81/login
17:03:45.095 [http-nio-9200-exec-6] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: 74aba07b01274ae6b12f670ce2f78dc3, 状态: http://localhost:81/login
17:05:53.737 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: 2ab613a7298d44fd86982f3ad009120a, 状态: http://localhost:81/login
17:06:50.908 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: b8163a602e594f21849b71c02434c224, 状态: http://localhost:81/login
17:11:13.739 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
17:11:13.742 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
17:11:18.915 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
17:11:18.978 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:11:19.350 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:11:19.350 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:11:21.806 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:11:24.211 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
17:11:24.215 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:11:24.215 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
17:11:24.493 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:11:26.330 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
17:11:27.414 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:11:27.440 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:11:27.482 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
17:11:28.087 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:11:30.426 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
17:11:30.501 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:11:30.501 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:11:30.725 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
17:11:30.994 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 12.763 seconds (JVM running for 14.205)
17:11:31.056 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
17:11:31.056 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
17:11:31.057 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
17:11:31.550 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:11:58.019 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: 432bdf645e894d53a85f6a832bd6d430, 状态: http://localhost:81/login
17:12:13.483 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [exchangeToken,83] - 授权码换取令牌成功
17:12:13.521 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750410733521
17:12:14.121 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,260] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
17:12:14.122 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,204] - 创建临时用户信息: 18454831889
17:12:14.122 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,210] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
17:15:46.285 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
17:15:46.361 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:15:46.756 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:15:46.757 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:15:57.147 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:16:00.101 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
17:16:00.104 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:16:00.105 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
17:16:00.411 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:16:02.366 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
17:16:03.606 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:16:03.640 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:16:03.697 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
17:16:04.332 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:16:07.268 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
17:16:07.344 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:16:07.344 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:16:16.377 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
17:16:16.378 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:16:16.385 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
17:16:16.389 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
17:18:49.243 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
17:18:49.344 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:18:49.839 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:18:49.840 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:18:54.055 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:18:58.101 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
17:18:58.109 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:18:58.109 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
17:18:58.467 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:19:01.030 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
17:19:02.362 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:19:02.401 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:19:02.460 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
17:19:03.167 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:19:06.525 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
17:19:06.655 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:19:06.655 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:19:06.896 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
17:19:07.226 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 18.857 seconds (JVM running for 20.382)
17:19:07.309 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
17:19:07.310 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
17:19:07.311 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
17:19:08.036 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:51:48.156 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
17:51:48.156 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
17:51:54.115 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
17:51:54.180 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:51:54.624 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:51:54.624 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:51:57.196 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:51:59.841 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
17:51:59.845 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:51:59.845 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
17:52:00.146 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:52:01.973 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
17:52:02.976 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:52:03.001 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
17:52:03.048 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
17:52:03.614 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:52:05.963 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
17:52:06.084 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:52:06.084 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:52:06.325 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
17:52:06.575 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.169 seconds (JVM running for 14.693)
17:52:06.622 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
17:52:06.623 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
17:52:06.623 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
17:52:07.096 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:52:25.594 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: 7e8182b8b01e4e2da6f3d39ce5f846c0, 状态: http://localhost:81/login
17:55:39.183 [http-nio-9200-exec-5] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: fda14ddbfd054a9cb67ba3d68d3fd708, 状态: http://localhost:81/login
17:55:42.604 [http-nio-9200-exec-5] INFO  c.r.a.s.SSOClientService - [exchangeToken,83] - 授权码换取令牌成功
17:55:48.497 [http-nio-9200-exec-5] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750413348497
17:55:49.044 [http-nio-9200-exec-5] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,260] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
17:55:49.044 [http-nio-9200-exec-5] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,204] - 创建临时用户信息: 18454831889
17:55:49.044 [http-nio-9200-exec-5] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,210] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
17:55:49.543 [http-nio-9200-exec-5] INFO  c.r.a.s.SSOClientService - [setLoginStatus,292] - 设置用户 18454831889 登录状态成功
17:55:49.543 [http-nio-9200-exec-5] INFO  c.r.a.s.SSOClientService - [createLocalSession,126] - 创建本地会话成功，用户: 18454831889
17:55:56.404 [http-nio-9200-exec-5] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,74] - SSO登录成功，跳转到: http://localhost:81/login
17:56:36.108 [http-nio-9200-exec-8] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: c0a6eec467124e78a2eb3cba632c7fa0, 状态: http://localhost:81/login
17:56:36.129 [http-nio-9200-exec-8] INFO  c.r.a.s.SSOClientService - [exchangeToken,83] - 授权码换取令牌成功
17:56:36.135 [http-nio-9200-exec-8] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750413396135
17:56:36.141 [http-nio-9200-exec-8] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,260] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
17:56:36.142 [http-nio-9200-exec-8] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,204] - 创建临时用户信息: 18454831889
17:56:36.142 [http-nio-9200-exec-8] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,210] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
17:56:36.147 [http-nio-9200-exec-8] INFO  c.r.a.s.SSOClientService - [setLoginStatus,292] - 设置用户 18454831889 登录状态成功
17:56:36.148 [http-nio-9200-exec-8] INFO  c.r.a.s.SSOClientService - [createLocalSession,126] - 创建本地会话成功，用户: 18454831889
17:56:36.148 [http-nio-9200-exec-8] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,74] - SSO登录成功，跳转到: http://localhost:81/login
18:02:11.923 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:02:11.923 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:02:20.411 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:02:20.477 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:02:21.053 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:02:21.053 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:02:23.952 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:02:27.163 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
18:02:27.166 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:02:27.166 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
18:02:27.512 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:02:29.644 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
18:02:30.829 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:02:30.854 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:02:30.906 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
18:02:31.556 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:02:34.265 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
18:02:34.363 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:02:34.363 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:02:34.605 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
18:02:34.917 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 15.259 seconds (JVM running for 17.083)
18:02:34.984 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
18:02:34.986 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
18:02:34.986 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
18:02:35.334 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:07:16.466 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,59] - SSO登录回调，授权码: 4a4a4733d4e143efbc8f9a2c15c4d2e6, 状态: http://localhost:81/login
18:07:16.579 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [exchangeToken,87] - 授权码换取令牌成功
18:07:16.656 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750414036656
18:07:17.250 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,280] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
18:07:17.250 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,224] - 创建临时用户信息: 18454831889
18:07:17.251 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,230] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
18:07:18.243 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,143] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
18:07:18.243 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,78] - SSO登录成功，跳转到前端回调页面: /sso/callback?token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjo1NSwibG9naW5fdHlwZSI6ImxvZ2luX3VzZXIiLCJ1c2VyX2tleSI6ImRiYTkyOGNkLWE1MzItNDVkZC1iNzY2LTU0YmIwNWIwN2E1OSIsInVzZXJuYW1lIjoiMTg0NTQ4MzE4ODkifQ.PUlIJwfC_ySb4KJjL8iL--YQIFiE7ybuITQnMVfbVA_fWR4bPxxpxNgvC7uT80bxX1tsBFg3G9bkvHm1jBJyfA&redirect=http://localhost:81/login
18:11:05.251 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:11:05.251 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:14:06.095 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:14:06.213 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:14:06.937 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:14:06.937 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:18:52.298 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:18:52.367 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:18:52.939 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:18:52.940 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:18:55.175 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 2 profiles are active: "dev", "prod"
18:18:57.364 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
18:18:57.364 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:18:57.364 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
18:18:57.497 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:18:59.494 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:19:01.288 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
18:19:01.317 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:19:01.318 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:19:01.519 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
18:19:01.784 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:19:01.792 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:19:02.274 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
18:19:02.274 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
18:19:02.281 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
18:19:02.283 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
18:19:45.400 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:19:45.475 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:19:45.922 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:19:45.923 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:19:48.578 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:19:51.584 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
18:19:51.587 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:19:51.587 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
18:19:51.853 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:19:53.884 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
18:19:54.991 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:19:55.022 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:19:55.063 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
18:19:55.671 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:19:58.136 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
18:19:58.253 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:19:58.253 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:19:58.479 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
18:19:58.767 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 14.107 seconds (JVM running for 15.682)
18:19:58.824 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
18:19:58.825 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
18:19:58.826 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
18:19:59.166 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:21:02.398 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: d23746e551974ccfaa3ac01e29944637, 状态: http://localhost:81/login
18:21:02.495 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
18:21:02.539 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750414862538
18:21:03.128 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
18:21:03.128 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
18:21:03.129 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
18:21:04.113 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
18:21:04.116 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: b78a06ed0bcb49e28979a770dce92163
18:21:04.116 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,83] - SSO登录成功，跳转到前端回调页面: /sso/callback?key=b78a06ed0bcb49e28979a770dce92163&redirect=http://localhost:81/login
18:23:43.043 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:23:43.046 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:23:48.665 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:23:48.773 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:23:49.325 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:23:49.325 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:23:52.047 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:23:54.901 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
18:23:54.905 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:23:54.906 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
18:23:55.189 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:23:56.990 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
18:23:58.169 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:23:58.202 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:23:58.250 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
18:23:58.893 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:24:01.539 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
18:24:01.631 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:24:01.631 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:24:01.856 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
18:24:02.205 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 14.457 seconds (JVM running for 15.988)
18:24:02.257 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
18:24:02.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
18:24:02.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
18:24:02.724 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:24:19.744 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: acce1ecf58cc4333b6a6f5f2461cb8ea, 状态: http://localhost:81/login
18:24:19.899 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
18:24:19.940 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750415059940
18:24:20.504 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
18:24:20.504 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
18:24:20.504 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
18:24:21.429 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
18:24:21.431 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: 2f079dbc9e2a487fa417959275800717
18:24:21.431 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:80/sso/callback?key=2f079dbc9e2a487fa417959275800717&redirect=http://localhost:81/login
18:24:48.958 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:24:48.961 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:24:55.866 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:24:55.938 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:24:56.332 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:24:56.334 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:24:58.945 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:25:01.479 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
18:25:01.483 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:25:01.483 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
18:25:01.783 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:25:03.739 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
18:25:04.807 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:25:04.830 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:25:04.871 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
18:25:05.475 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:25:07.638 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
18:25:07.706 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:25:07.706 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:25:07.928 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
18:25:08.185 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 12.951 seconds (JVM running for 14.411)
18:25:08.231 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
18:25:08.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
18:25:08.233 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
18:25:08.664 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:25:24.586 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 06b7c0ec001d4e56b43a65edee01215e, 状态: http://localhost:81/login
18:25:24.739 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
18:25:24.776 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750415124776
18:25:25.279 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
18:25:25.281 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
18:25:25.281 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
18:25:26.163 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
18:25:26.166 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: ab77fd9bd36d4c7299c0b7465bbcafef
18:25:26.166 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=ab77fd9bd36d4c7299c0b7465bbcafef&redirect=http://localhost:81/login
18:25:40.196 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 881693dd56574350a2a97b204bb729e2, 状态: http://localhost:81/login
18:25:40.207 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
18:25:40.213 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750415140213
18:25:40.227 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
18:25:40.228 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
18:25:40.228 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
18:25:40.269 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
18:25:40.270 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: 45abffcf504d4a2dac92072af94bff3a
18:25:40.270 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=45abffcf504d4a2dac92072af94bff3a&redirect=http://localhost:81/login
18:29:03.903 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
18:29:03.907 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
18:29:07.605 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
18:29:07.696 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:29:08.148 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:29:08.148 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:29:11.254 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:29:14.696 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
18:29:14.700 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:29:14.702 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
18:29:15.051 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:29:17.812 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
18:29:19.098 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:29:19.132 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
18:29:19.185 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
18:29:19.855 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:29:22.807 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
18:29:22.911 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
18:29:22.911 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
18:29:23.170 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
18:29:23.501 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 16.66 seconds (JVM running for 18.192)
18:29:23.572 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
18:29:23.573 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
18:29:23.574 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
18:29:23.958 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:37:43.538 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: c8f06108c87a467191c6e6ab6bcdd776, 状态: http://localhost:81/login
18:37:43.638 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
18:37:43.676 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750415863676
18:37:44.212 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
18:37:44.213 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
18:37:44.213 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
18:37:45.042 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
18:37:45.043 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: b4329ff1944f413392ce65cc720f9561
18:37:45.043 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=b4329ff1944f413392ce65cc720f9561&redirect=http://localhost:81/login
18:38:28.068 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: f26e2e9815f447daa1868c60f5c6ccdf, 状态: http://localhost:81/login
18:38:28.077 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
18:38:28.084 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750415908084
18:38:28.093 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
18:38:28.093 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
18:38:28.093 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
18:38:28.099 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
18:38:28.100 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: 03c00fe946854e9ea9995ea8d377dd3f
18:38:28.100 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=03c00fe946854e9ea9995ea8d377dd3f&redirect=http://localhost:81/login
18:42:23.286 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: dbd0fc3bca6f4194abf066690344deef, 状态: http://localhost:81/login
18:42:23.295 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
18:42:23.338 [http-nio-9200-exec-10] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750416143338
18:42:23.352 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
18:42:23.353 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
18:42:23.353 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
18:42:23.359 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
18:42:23.362 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: b299747bdb2e4f07b95991b090231d78
18:42:23.362 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=b299747bdb2e4f07b95991b090231d78&redirect=http://localhost:81/login
