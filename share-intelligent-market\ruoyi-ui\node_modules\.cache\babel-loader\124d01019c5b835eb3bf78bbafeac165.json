{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\directive\\permission\\hasPermi.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\directive\\permission\\hasPermi.js", "mtime": 1750151094163}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3Iuc29tZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnZhciBfc3RvcmUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvc3RvcmUiKSk7Ci8qKg0KKiB2LWhhc1Blcm1pIOaTjeS9nOadg+mZkOWkhOeQhg0KKiBDb3B5cmlnaHQgKGMpIDIwMTkgcnVveWkNCiovCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBpbnNlcnRlZDogZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlKSB7CiAgICB2YXIgdmFsdWUgPSBiaW5kaW5nLnZhbHVlOwogICAgdmFyIGFsbF9wZXJtaXNzaW9uID0gIio6KjoqIjsKICAgIHZhciBwZXJtaXNzaW9ucyA9IF9zdG9yZS5kZWZhdWx0LmdldHRlcnMgJiYgX3N0b3JlLmRlZmF1bHQuZ2V0dGVycy5wZXJtaXNzaW9uczsKICAgIGlmICh2YWx1ZSAmJiB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5ICYmIHZhbHVlLmxlbmd0aCA+IDApIHsKICAgICAgdmFyIHBlcm1pc3Npb25GbGFnID0gdmFsdWU7CiAgICAgIHZhciBoYXNQZXJtaXNzaW9ucyA9IHBlcm1pc3Npb25zLnNvbWUoZnVuY3Rpb24gKHBlcm1pc3Npb24pIHsKICAgICAgICByZXR1cm4gYWxsX3Blcm1pc3Npb24gPT09IHBlcm1pc3Npb24gfHwgcGVybWlzc2lvbkZsYWcuaW5jbHVkZXMocGVybWlzc2lvbik7CiAgICAgIH0pOwogICAgICBpZiAoIWhhc1Blcm1pc3Npb25zKSB7CiAgICAgICAgZWwucGFyZW50Tm9kZSAmJiBlbC5wYXJlbnROb2RlLnJlbW92ZUNoaWxkKGVsKTsKICAgICAgfQogICAgfSBlbHNlIHsKICAgICAgdGhyb3cgbmV3IEVycm9yKCJcdThCRjdcdThCQkVcdTdGNkVcdTY0Q0RcdTRGNUNcdTY3NDNcdTk2NTBcdTY4MDdcdTdCN0VcdTUwM0MiKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_default", "exports", "default", "inserted", "el", "binding", "vnode", "value", "all_permission", "permissions", "store", "getters", "Array", "length", "permissionFlag", "hasPermissions", "some", "permission", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/directive/permission/hasPermi.js"], "sourcesContent": [" /**\r\n * v-hasPermi 操作权限处理\r\n * Copyright (c) 2019 ruoyi\r\n */\r\n \r\nimport store from '@/store'\r\n\r\nexport default {\r\n  inserted(el, binding, vnode) {\r\n    const { value } = binding\r\n    const all_permission = \"*:*:*\";\r\n    const permissions = store.getters && store.getters.permissions\r\n\r\n    if (value && value instanceof Array && value.length > 0) {\r\n      const permissionFlag = value\r\n\r\n      const hasPermissions = permissions.some(permission => {\r\n        return all_permission === permission || permissionFlag.includes(permission)\r\n      })\r\n\r\n      if (!hasPermissions) {\r\n        el.parentNode && el.parentNode.removeChild(el)\r\n      }\r\n    } else {\r\n      throw new Error(`请设置操作权限标签值`)\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAKA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AALC;AACD;AACA;AACA;AAHC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOc;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,cAAc,GAAG,OAAO;IAC9B,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;IAE9D,IAAIF,KAAK,IAAIA,KAAK,YAAYK,KAAK,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,cAAc,GAAGP,KAAK;MAE5B,IAAMQ,cAAc,GAAGN,WAAW,CAACO,IAAI,CAAC,UAAAC,UAAU,EAAI;QACpD,OAAOT,cAAc,KAAKS,UAAU,IAAIH,cAAc,CAACI,QAAQ,CAACD,UAAU,CAAC;MAC7E,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,EAAE;QACnBX,EAAE,CAACe,UAAU,IAAIf,EAAE,CAACe,UAAU,CAACC,WAAW,CAAChB,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIiB,KAAK,+DAAa,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}]}