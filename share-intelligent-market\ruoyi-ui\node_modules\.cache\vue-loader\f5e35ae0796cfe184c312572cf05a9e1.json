{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\components\\e-progress.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\components\\e-progress.vue", "mtime": 1750151094223}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IHByb2dyZXNzRGF0YSB9IGZyb20gJ0AvYXBpL3N0b3JlL3Byb2R1Y3QnOwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRpdGxlOiAn57yW6L6R6L+b5bqmJywKICAgICAgc2hvdzogZmFsc2UsCiAgICAgIGZvcm06IHt9LAogICAgICBydWxlczogewogICAgICAgIHByb2dyZXNzOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl6L+b5bqmJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIG9waWQ6IHVuZGVmaW5lZCwKICAgICAgICBwcm9ncmVzczogdW5kZWZpbmVkCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCdmb3JtJyk7CiAgICB9LAogICAgb3Blbihyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmZvcm0ub3BpZCA9IHJvdy5pZDsKICAgICAgdGhpcy5mb3JtLnByb2dyZXNzID0gcm93LmNlbnRyYWxfcGVyY2VudDsKICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsKICAgIH0sCiAgICBoYW5kbGVTdWJtaXQoKSB7CiAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZSh2YWxpZGF0ZSA9PiB7CiAgICAgICAgaWYodmFsaWRhdGUpIHsKICAgICAgICAgIHByb2dyZXNzRGF0YSh0aGlzLmZvcm0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICB0aGlzLnNob3cgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7bWVzc2FnZTogJ+aTjeS9nOaIkOWKnycsIHR5cGU6ICdzdWNjZXNzJ30pCiAgICAgICAgICAgIHRoaXMuJHBhcmVudC5nZXRMaXN0KCk7CiAgICAgICAgICB9KQoKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+WujOWWhOS/oeaBr+WGjeaPkOS6pCEnKQogICAgICAgIH0KICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["e-progress.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "e-progress.vue", "sourceRoot": "src/views/central/components", "sourcesContent": ["<!-- 编辑进度弹窗 -->\r\n<template>\r\n  <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"500px\" center>\r\n    <el-form ref='form' :model='form' label-width='80px' :rules='rules'>\r\n      <el-form-item label='进度' prop='progress'>\r\n        <el-input type=\"number\" min='0' max='100' v-model='form.progress' placeholder='请输入进度'></el-input>\r\n      </el-form-item>\r\n    </el-form>\r\n    <span slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"show = false\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">确 定</el-button>\r\n    </span>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\n  import { progressData } from '@/api/store/product';\r\n  export default {\r\n    data() {\r\n      return {\r\n        title: '编辑进度',\r\n        show: false,\r\n        form: {},\r\n        rules: {\r\n          progress: [{\r\n            required: true,\r\n            message: '请输入进度',\r\n            trigger: 'blur'\r\n          }],\r\n        }\r\n      }\r\n    },\r\n    methods: {\r\n      reset() {\r\n        this.form = {\r\n          opid: undefined,\r\n          progress: undefined\r\n        };\r\n        this.resetForm('form');\r\n      },\r\n      open(row) {\r\n        this.reset();\r\n        this.form.opid = row.id;\r\n        this.form.progress = row.central_percent;\r\n        this.show = true;\r\n      },\r\n      handleSubmit() {\r\n        this.$refs.form.validate(validate => {\r\n          if(validate) {\r\n            progressData(this.form).then(() => {\r\n              this.show = false;\r\n              this.$message({message: '操作成功', type: 'success'})\r\n              this.$parent.getList();\r\n            })\r\n\r\n          } else {\r\n            this.$modal.msgError('请完善信息再提交!')\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n</style>\r\n"]}]}