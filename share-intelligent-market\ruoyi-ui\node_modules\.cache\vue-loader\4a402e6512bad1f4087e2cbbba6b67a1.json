{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\service\\classify.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\service\\classify.vue", "mtime": 1750151094277}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgdHJlZURhdGEsCiAgYWRkRGF0YSwKICBnZXREYXRhLAogIGVkaXREYXRhLAogIG1hcHJlY0RhdGEsCiAgcmVjRGF0YSwKICBkZWxEYXRhCn0gZnJvbSAnQC9hcGkvc2VydmljZS9jbGFzc2lmeSc7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgdmFyIGltZ0NoYW5nZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgIGNvbnNvbGUubG9nKDY2NjY2NjY2NjYpCiAgICAgICAgcmV0dXJuIGNhbGxiYWNrKG5ldyBFcnJvcignbG9nb+S4jeiDveS4uuepuicpKTsKICAgICAgfWVsc2V7CiAgICAgICAgY29uc29sZS5sb2coMTIzNTQ2NTQ2KQogICAgICAgIGNhbGxiYWNrKCkKICAgICAgfQogICAgfTsKICAgIHJldHVybiB7CiAgICAgIGJ0bmxvYWQ6IGZhbHNlLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICByZWNPcHRpb25zOlt7CiAgICAgICAgICBrZXk6IDEsCiAgICAgICAgICB2YWx1ZTogJ+aOqOiNkCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGtleTogMCwKICAgICAgICAgIHZhbHVlOiAn5pmu6YCaJwogICAgICAgIH0sCiAgICAgIF0sCiAgICAgIHN0YXR1czogW3sKICAgICAgICAgIGlkOiAxLAogICAgICAgICAgbmFtZTogJ+S4iue6vycKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiAwLAogICAgICAgICAgbmFtZTogJ+S4i+e6vycKICAgICAgICB9LAogICAgICBdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBuYW1lOiB1bmRlZmluZWQsCiAgICAgICAgcmVjb21tZW5kOnVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDliJfooajmlbDmja4KICAgICAgbGlzdDogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgcnVsZXM6IHsKICAgICAgICBuYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn5YiG57G75ZCN56ew5LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIHNvcnRzOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn5o6S5bqP5LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIHN0YXR1czogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+eKtuaAgeS4jeiDveS4uuepuicsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dLAogICAgICAgIGxvZ286IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIHZhbGlkYXRvcjogaW1nQ2hhbmdlLAogICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZScKICAgICAgICB9XSwKICAgICAgfSwKICAgICAgc2hvd0ltZzpmYWxzZSwKICAgICAgLy8g5by556qX5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCh7CiAgICAgICJwaWQiOiAwCiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGNoYW5nZVNlbmNlKHJlYywgcm93KSB7CiAgICAgIG1hcHJlY0RhdGEoewogICAgICAgIGlkOiByb3cuaWQsCiAgICAgICAgbWFwX3JlYzogcmVjCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnycsCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICB9KTsKICAgICAgICB0aGlzLmdldExpc3QoewogICAgICAgICAgInBpZCI6IDAKICAgICAgICB9KTsKICAgICAgfSkKICAgIH0sCiAgICBnZXRMb2dvKGUpewogICAgICB0aGlzLmZvcm0ubG9nbyA9IGUKICAgICAgaWYodGhpcy5mb3JtLmxvZ28pewogICAgICAgIHRoaXMuJHJlZnMuZm9ybS5jbGVhclZhbGlkYXRlKCdsb2dvJykKICAgICAgfQogICAgfSwKICAgIC8v5Zy65pmv5Zu+CiAgICBnZXRQaWMoZSkgewogICAgICB0aGlzLmZvcm0uc2NlbmVfcGljID0gZQogICAgICAvLyBpZiAodGhpcy5mb3JtLnNjZW5lX3BpYykgewogICAgICAvLyAgIHRoaXMuJHJlZnMuZm9ybS5jbGVhclZhbGlkYXRlKCdzY2VuZV9waWMnKQogICAgICAvLyB9CiAgICB9LAogICAgLyog6YeN572u6KGo5Y2VICovCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHBpZDogdW5kZWZpbmVkLAogICAgICAgIG5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBzb3J0czogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLAogICAgICAgIGNoYXJnZXI6IHVuZGVmaW5lZCwKICAgICAgICBtYW5hZ2VyOiB1bmRlZmluZWQsCiAgICAgICAgZGlyZWN0b3I6IHVuZGVmaW5lZCwKICAgICAgICBsZWFkZXI6IHVuZGVmaW5lZCwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZCwKICAgICAgICBsb2dvOnVuZGVmaW5lZCwKICAgICAgfQogICAgICB0aGlzLnJlc2V0Rm9ybSgnZm9ybScpCiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLnJlY29tbWVuZCE9MSAmJiAhdGhpcy5xdWVyeVBhcmFtcy5uYW1lKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fovpPlhaXliIbnsbvlkI3np7AnKTsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QodGhpcy5xdWVyeVBhcmFtcyk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmdldExpc3QoewogICAgICAgICJwaWQiOiAwCiAgICAgIH0pOwogICAgfSwKICAgIGNoYW5nZVJlY29tbWVuZChyZWMsIHJvdykgewogICAgICByZWNEYXRhKHsKICAgICAgICBpZDogcm93LmlkLAogICAgICAgIHJlY29tbWVuZDogcmVjID8gMSA6IDAKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5oiQ5YqfJywKICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgIH0pOwogICAgICAgIHJvdy5yZWNvbW1lbmQgPSByZWMgPyAxIDogMAogICAgICB9KQogICAgfSwKICAgIGdldExpc3QocGFyYW1zKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgdHJlZURhdGEocGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5saXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICB9LAogICAgbG9hZCh0cmVlLCB0cmVlTm9kZSwgcmVzb2x2ZSkgewogICAgICBpZiAoIXRyZWUubGV2ZWwpIHsKICAgICAgICB0cmVlLmxldmVsID0gMTsKICAgICAgfQogICAgICB0cmVlRGF0YSh7CiAgICAgICAgInBpZCI6IHRyZWUuaWQKICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuZGF0YSAmJiByZXMuZGF0YS5sZW5ndGgpIHsKICAgICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIGl0ZW0ubGV2ZWwgPSB0cmVlLmxldmVsICsgMTsKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIHJlc29sdmUocmVzLmRhdGEpCiAgICAgIH0pCiAgICB9LAogICAgLyoqIOaWsOWinuS4gOe6pyAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMudGl0bGUgPSAi5aKe5Yqg5YiG57G7IjsKICAgICAgdGhpcy5zaG93SW1nID0gdHJ1ZQogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5paw5aKe5LiA57qnICovCiAgICBoYW5kbGVBZGRTdWIocm93KSB7CiAgICAgIHRoaXMuc2hvd0ltZyA9IGZhbHNlCiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5mb3JtLnBpZCA9IHJvdy5pZDsKICAgICAgdGhpcy5mb3JtLmxldmVsID0gcm93LmxldmVsICsgMTsKICAgICAgdGhpcy50aXRsZSA9ICLlop7liqDliIbnsbsiOwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIGlmKHJvdy5waWQgPT0gMCl7CiAgICAgICAgdGhpcy5zaG93SW1nID0gdHJ1ZQogICAgICB9CiAgICAgIGdldERhdGEocm93LmlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMuZm9ybS5sZXZlbCA9IHJvdy5sZXZlbDsKICAgICAgICB0aGlzLnRpdGxlID0gIue8lui+keWIhuexuyI7CiAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy5mb3JtLmxldmVsID0gcm93LmxldmVsOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTnvJblj7fkuLoiJyArIHJvdy5pZCArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsRGF0YShyb3cuaWQpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLnJlZnJlc2hSb3cocm93LnBpZCkKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qIOeCueWHu+aPkOS6pCAqLwogICAgaGFuZGxlU3VibWl0KCkgewogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUodmFsaWRhdGUgPT4gewogICAgICAgIGlmICh2YWxpZGF0ZSkgewogICAgICAgICAgdGhpcy5idG5sb2FkID0gdHJ1ZQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCkgewogICAgICAgICAgICBlZGl0RGF0YSh0aGlzLmZvcm0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnycsCiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLmJ0bmxvYWQgPSBmYWxzZQogICAgICAgICAgICAgIHRoaXMucmVmcmVzaFJvdyh0aGlzLmZvcm0ucGlkKQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCh7CiAgICAgICAgICAgICAgICAicGlkIjogMAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgfSkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZERhdGEodGhpcy5mb3JtKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfmk43kvZzmiJDlip8nLAogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy5yZWZyZXNoUm93KHRoaXMuZm9ybS5waWQpCiAgICAgICAgICAgICAgdGhpcy5idG5sb2FkID0gZmFsc2UKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoewogICAgICAgICAgICAgICAgInBpZCI6IDAKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7flrozlloTkv6Hmga/lho3mj5DkuqQhJykKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgcmVmcmVzaFJvdyhpZCkgewogICAgICB0cmVlRGF0YSh7CiAgICAgICAgcGlkOiBpZAogICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5kYXRhLmxlbmd0aCkgewogICAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgaXRlbS5sZXZlbCA9IHRoaXMuZm9ybS5sZXZlbDsKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIHRoaXMuJHNldCh0aGlzLiRyZWZzLnRhYmxlLnN0b3JlLnN0YXRlcy5sYXp5VHJlZU5vZGVNYXAsIGlkLCByZXMuZGF0YSkKICAgICAgfSkKCiAgICB9CiAgfSwKfTsK"}, {"version": 3, "sources": ["classify.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "classify.vue", "sourceRoot": "src/views/service", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"名称\" prop=\"name\">\r\n        <el-input clearable v-model=\"queryParams.name\" style=\"width: 200px;\" placeholder=\"请输入名称\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"recommend\">\r\n        <el-select clearable v-model=\"queryParams.recommend\" placeholder=\"首页推荐\" style=\"width: 120px;\">\r\n          <el-option v-for=\"item in recOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">添加一级分类</el-button>\r\n          </el-col>\r\n        </el-row>\r\n        <el-table ref='table' v-loading=\"loading\" :data=\"list\" lazy :load=\"load\" row-key=\"id\"\r\n          :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\r\n          <el-table-column prop=\"name\" label=\"品类名称\" width=\"200\"></el-table-column>\r\n          <el-table-column prop=\"charger\" label=\"品类负责人\" width=\"150\"></el-table-column>\r\n          <el-table-column prop=\"manager\" label=\"采购经理\" width=\"150\"></el-table-column>\r\n          <el-table-column prop=\"director\" label=\"采购总监\" width=\"150\"></el-table-column>\r\n          <el-table-column prop=\"leader\" label=\"分管领导\" width=\"150\"></el-table-column>\r\n          <el-table-column label=\"场景推荐\" align=\"center\" prop=\"map_rec\">\r\n            <template slot-scope=\"scope\" >\r\n              <el-switch @change=\"changeSence($event, scope.row)\" v-model=\"scope.row.map_rec\" :active-value=\"2\"\r\n                :inactive-value=\"1\" v-if='scope.row.pid == 0'></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"首页推荐\" align=\"center\" prop=\"recommend\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch @change=\"changeRecommend($event, scope.row)\" v-model=\"scope.row.recommend\" :active-value=\"1\"\r\n                :inactive-value=\"0\"></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='danger' v-if='scope.row.status == 0'>下线</el-tag>\r\n              <el-tag size=\"mini\" type='success' v-else>上线</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注\" align=\"center\" width=\"200\" prop=\"remark\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if='scope.row.level != 3' type=\"text\" size=\"mini\" @click=\"handleAddSub(scope.row)\">添加子分类\r\n              </el-button>\r\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                v-if=\"!scope.row.hasChildren\"\r\n                @click=\"handleDelete(scope.row)\">删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog :title=\"title\" :visible.sync=\"dialogFormVisible\" center width=\"50%\">\r\n      <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"110px\" label-position=\"left\">\r\n        <el-row v-if=\"showImg\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"分类图标\" prop=\"logo\">\r\n              <ImageUpload v-model=\"form.logo\" @input=\"getLogo\" :limit=\"1\" :isShowTip=\"false\"></ImageUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"场景图\" prop=\"scene_pic\">\r\n              <ImageUpload v-model=\"form.scene_pic\" @input=\"getPic\" :limit=\"1\" :isShowTip=\"false\"></ImageUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"分类名称\" prop=\"name\">\r\n              <el-input clearable v-model=\"form.name\" :maxlength='20' placeholder=\"请输入分类名称\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-select class='width-full' v-model='form.status'>\r\n                <el-option v-for='item in status' :key='item.id' :value='item.id' :label='item.name'></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"排序\" prop=\"sorts\">\r\n              <el-input type='number' v-model=\"form.sorts\" placeholder=\"请输入排序\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"品类负责人\" prop=\"charger\">\r\n              <el-input clearable v-model=\"form.charger\" :maxlength='20' placeholder=\"请输入品类负责人\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"采购经理\" prop=\"manager\">\r\n              <el-input clearable v-model=\"form.manager\" placeholder=\"请输入采购经理\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"采购总监\" prop=\"director\">\r\n              <el-input clearable v-model=\"form.director\" :maxlength='20' placeholder=\"请输入采购总监\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"品类分管领导\" prop=\"leader\">\r\n              <el-input clearable v-model=\"form.leader\" :maxlength='20' placeholder=\"请输入品类分管领导\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input clearable type='textarea' v-model=\"form.remark\" :maxlength='150' placeholder=\"请输入备注\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"btnload\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    treeData,\r\n    addData,\r\n    getData,\r\n    editData,\r\n    maprecData,\r\n    recData,\r\n    delData\r\n  } from '@/api/service/classify';\r\n  export default {\r\n    data() {\r\n      var imgChange = (rule, value, callback) => {\r\n        if (!value) {\r\n          console.log(6666666666)\r\n          return callback(new Error('logo不能为空'));\r\n        }else{\r\n          console.log(123546546)\r\n          callback()\r\n        }\r\n      };\r\n      return {\r\n        btnload: false,\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        recOptions:[{\r\n            key: 1,\r\n            value: '推荐'\r\n          },\r\n          {\r\n            key: 0,\r\n            value: '普通'\r\n          },\r\n        ],\r\n        status: [{\r\n            id: 1,\r\n            name: '上线'\r\n          },\r\n          {\r\n            id: 0,\r\n            name: '下线'\r\n          },\r\n        ],\r\n        // 查询参数\r\n        queryParams: {\r\n          name: undefined,\r\n          recommend:undefined\r\n        },\r\n        // 列表数据\r\n        list: [],\r\n        // 表单参数\r\n        form: {},\r\n        rules: {\r\n          name: [{\r\n            required: true,\r\n            message: '分类名称不能为空',\r\n            trigger: 'blur'\r\n          }],\r\n          sorts: [{\r\n            required: true,\r\n            message: '排序不能为空',\r\n            trigger: 'blur'\r\n          }],\r\n          status: [{\r\n            required: true,\r\n            message: '状态不能为空',\r\n            trigger: 'change'\r\n          }],\r\n          logo: [{\r\n            required: true,\r\n            validator: imgChange,\r\n            trigger: 'change'\r\n          }],\r\n        },\r\n        showImg:false,\r\n        // 弹窗标题\r\n        title: \"\",\r\n        dialogFormVisible: false\r\n      };\r\n    },\r\n    created() {\r\n      this.getList({\r\n        \"pid\": 0\r\n      });\r\n    },\r\n    methods: {\r\n      changeSence(rec, row) {\r\n        maprecData({\r\n          id: row.id,\r\n          map_rec: rec\r\n        }).then(() => {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          });\r\n          this.getList({\r\n            \"pid\": 0\r\n          });\r\n        })\r\n      },\r\n      getLogo(e){\r\n        this.form.logo = e\r\n        if(this.form.logo){\r\n          this.$refs.form.clearValidate('logo')\r\n        }\r\n      },\r\n      //场景图\r\n      getPic(e) {\r\n        this.form.scene_pic = e\r\n        // if (this.form.scene_pic) {\r\n        //   this.$refs.form.clearValidate('scene_pic')\r\n        // }\r\n      },\r\n      /* 重置表单 */\r\n      reset() {\r\n        this.form = {\r\n          pid: undefined,\r\n          name: undefined,\r\n          sorts: undefined,\r\n          status: undefined,\r\n          charger: undefined,\r\n          manager: undefined,\r\n          director: undefined,\r\n          leader: undefined,\r\n          remark: undefined,\r\n          logo:undefined,\r\n        }\r\n        this.resetForm('form')\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        if (this.queryParams.recommend!=1 && !this.queryParams.name) {\r\n          return this.$modal.msgError('请输入分类名称');\r\n        }\r\n        this.getList(this.queryParams);\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.getList({\r\n          \"pid\": 0\r\n        });\r\n      },\r\n      changeRecommend(rec, row) {\r\n        recData({\r\n          id: row.id,\r\n          recommend: rec ? 1 : 0\r\n        }).then(() => {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          });\r\n          row.recommend = rec ? 1 : 0\r\n        })\r\n      },\r\n      getList(params) {\r\n        this.loading = true\r\n        treeData(params).then(res => {\r\n          this.list = res.data;\r\n        })\r\n        this.loading = false\r\n      },\r\n      load(tree, treeNode, resolve) {\r\n        if (!tree.level) {\r\n          tree.level = 1;\r\n        }\r\n        treeData({\r\n          \"pid\": tree.id\r\n        }).then(res => {\r\n          if (res.data && res.data.length) {\r\n            res.data.forEach(item => {\r\n              item.level = tree.level + 1;\r\n            })\r\n          }\r\n          resolve(res.data)\r\n        })\r\n      },\r\n      /** 新增一级 */\r\n      handleAdd() {\r\n        this.reset();\r\n        this.title = \"增加分类\";\r\n        this.showImg = true\r\n        this.dialogFormVisible = true;\r\n      },\r\n      /** 新增一级 */\r\n      handleAddSub(row) {\r\n        this.showImg = false\r\n        this.reset();\r\n        this.form.pid = row.id;\r\n        this.form.level = row.level + 1;\r\n        this.title = \"增加分类\";\r\n        this.dialogFormVisible = true;\r\n      },\r\n      /** 修改按钮操作 */\r\n      handleUpdate(row) {\r\n        if(row.pid == 0){\r\n          this.showImg = true\r\n        }\r\n        getData(row.id).then(response => {\r\n          this.form = response.data;\r\n          this.form.level = row.level;\r\n          this.title = \"编辑分类\";\r\n          this.dialogFormVisible = true;\r\n        });\r\n      },\r\n      /** 删除按钮操作 */\r\n      handleDelete(row) {\r\n        this.form.level = row.level;\r\n        this.$modal.confirm('是否确认删除编号为\"' + row.id + '\"的数据项？').then(function() {\r\n          return delData(row.id);\r\n        }).then(() => {\r\n          this.refreshRow(row.pid)\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n      },\r\n      /* 点击提交 */\r\n      handleSubmit() {\r\n        this.$refs.form.validate(validate => {\r\n          if (validate) {\r\n            this.btnload = true\r\n            if (this.form.id) {\r\n              editData(this.form).then(() => {\r\n                this.$message({\r\n                  message: '操作成功',\r\n                  type: 'success'\r\n                });\r\n                this.btnload = false\r\n                this.refreshRow(this.form.pid)\r\n                this.getList({\r\n                  \"pid\": 0\r\n                });\r\n                this.dialogFormVisible = false;\r\n              })\r\n            } else {\r\n              addData(this.form).then(() => {\r\n                this.$message({\r\n                  message: '操作成功',\r\n                  type: 'success'\r\n                });\r\n                this.refreshRow(this.form.pid)\r\n                this.btnload = false\r\n                this.getList({\r\n                  \"pid\": 0\r\n                });\r\n                this.dialogFormVisible = false;\r\n              })\r\n            }\r\n            this.dialogFormVisible = false;\r\n          } else {\r\n            this.$modal.msgError('请完善信息再提交!')\r\n          }\r\n        })\r\n      },\r\n      refreshRow(id) {\r\n        treeData({\r\n          pid: id\r\n        }).then(res => {\r\n          if (res.data.length) {\r\n            res.data.forEach(item => {\r\n              item.level = this.form.level;\r\n            })\r\n          }\r\n          this.$set(this.$refs.table.store.states.lazyTreeNodeMap, id, res.data)\r\n        })\r\n\r\n      }\r\n    },\r\n  };\r\n</script>\r\n"]}]}