{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\dict\\DictConverter.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\dict\\DictConverter.js", "mtime": 1750151094203}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_DictOptions", "_interopRequireDefault", "require", "_DictData", "_default", "dict", "dictMeta", "label", "determineDictField", "apply", "labelField", "concat", "_toConsumableArray2", "default", "DictOptions", "DEFAULT_LABEL_FIELDS", "value", "valueField", "DEFAULT_VALUE_FIELDS", "DictData", "_len", "arguments", "length", "fields", "Array", "_key", "find", "f", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/utils/dict/DictConverter.js"], "sourcesContent": ["import DictOptions from './DictOptions'\r\nimport DictData from './DictData'\r\n\r\nexport default function(dict, dictMeta) {\r\n  const label = determineDictField(dict, dictMeta.labelField, ...DictOptions.DEFAULT_LABEL_FIELDS)\r\n  const value = determineDictField(dict, dictMeta.valueField, ...DictOptions.DEFAULT_VALUE_FIELDS)\r\n  return new DictData(dict[label], dict[value], dict)\r\n}\r\n\r\n/**\r\n * 确定字典字段\r\n * @param {DictData} dict\r\n * @param  {...String} fields\r\n */\r\nfunction determineDictField(dict, ...fields) {\r\n  return fields.find(f => Object.prototype.hasOwnProperty.call(dict, f))\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEe,SAAAE,SAASC,IAAI,EAAEC,QAAQ,EAAE;EACtC,IAAMC,KAAK,GAAGC,kBAAkB,CAAAC,KAAA,UAACJ,IAAI,EAAEC,QAAQ,CAACI,UAAU,EAAAC,MAAA,KAAAC,mBAAA,CAAAC,OAAA,EAAKC,oBAAW,CAACC,oBAAoB,GAAC;EAChG,IAAMC,KAAK,GAAGR,kBAAkB,CAAAC,KAAA,UAACJ,IAAI,EAAEC,QAAQ,CAACW,UAAU,EAAAN,MAAA,KAAAC,mBAAA,CAAAC,OAAA,EAAKC,oBAAW,CAACI,oBAAoB,GAAC;EAChG,OAAO,IAAIC,iBAAQ,CAACd,IAAI,CAACE,KAAK,CAAC,EAAEF,IAAI,CAACW,KAAK,CAAC,EAAEX,IAAI,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAACH,IAAI,EAAa;EAAA,SAAAe,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAARC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EACzC,OAAOF,MAAM,CAACG,IAAI,CAAC,UAAAC,CAAC;IAAA,OAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC1B,IAAI,EAAEsB,CAAC,CAAC;EAAA,EAAC;AACxE", "ignoreList": []}]}