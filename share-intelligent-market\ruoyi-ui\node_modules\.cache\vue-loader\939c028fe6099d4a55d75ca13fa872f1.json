{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\components\\moreDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\components\\moreDetails.vue", "mtime": 1750151094270}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0RGF0YQp9IGZyb20gJ0AvYXBpL3Byb2plY3QvaW5xdWlyeSc7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGZvcm06IHsKICAgICAgICAiaW5xdWlyeSI6IHt9LAogICAgICAgICJvZmZlcnMiOiBbXQogICAgICB9CiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgb3BlbihpbnF1aXJ5SWQpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgZ2V0RGF0YShpbnF1aXJ5SWQpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXMuZGF0YTsKICAgICAgfSkKICAgIH0KICB9LAp9Owo="}, {"version": 3, "sources": ["moreDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "moreDetails.vue", "sourceRoot": "src/views/project/inquiry/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog title=\"精准询价详情\" :visible.sync=\"dialogVisible\" width=\"80%\" center>\r\n      <el-descriptions class=\"margin-top\" title=\"询价信息\" :column=\"3\" direction=\"horizontal\" border>\r\n        <el-descriptions-item label=\"询价公司\">{{form.inquiry.enterprise_name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价单号\">{{form.inquiry.inquiry_no}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价日期\">{{form.inquiry.inquiry_date}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人\">{{form.inquiry.linker}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系电话\">{{form.inquiry.linkphone}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"邮箱\">{{form.inquiry.email}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价标题\">{{form.inquiry.title}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需求描述\">{{form.inquiry.description}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"付款方式\">{{form.inquiry.paymentStr}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"账期天数\">{{form.inquiry.period}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"币种\">{{form.inquiry.currencyStr}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"开票类型\">{{form.inquiry.invoice_typeStr}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"截止日期\">{{form.inquiry.deadline}}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <div v-if=\"form.offers && form.offers.length>0\" style=\"font-size: 16px;font-weight: bold;margin-top: 20px;margin-bottom: 20px;\">全部报价</div>\r\n      <el-tabs  type=\"card\" v-if=\"form.offers && form.offers.length>0\">\r\n        <el-tab-pane v-for=\"item in form.offers\" :label=\"item.offer.enterprise_name\" :name=\"'tab'+item.offer.id\">\r\n          <el-descriptions style=\"margin-top: 20px;\" class=\"margin-top\" title=\"报价信息\" :column=\"3\" direction=\"horizontal\"\r\n            border>\r\n            <el-descriptions-item label=\"报价日期\">{{item.offer.offer_date}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"报价单号\">{{item.offer.offer_no}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"报价公司\">{{item.offer.enterprise_name}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"联系人\">{{item.offer.linker}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"联系电话\">{{item.offer.linkphone}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"报价版本\">{{item.offer.version}}次报价</el-descriptions-item>\r\n            <el-descriptions-item label=\"备注\">{{item.offer.remark}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"初次报价其他费用\" v-if=\"item.offer.other_fee\">\r\n              <span v-for=\"item in item.offer.other_fee\" style=\"margin-right: 10px;\">\r\n                {{item.key}}:{{item.value}}\r\n              </span>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"二次报价其他费用\" v-if=\"item.offer.other_fee_v2\">\r\n              <span v-for=\"item in item.offer.other_fee_v2\" style=\"margin-right: 10px;\">\r\n                {{item.key}}:{{item.value}}\r\n              </span>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <div v-for=\"(values,key) in item.items\">\r\n            <div style=\"font-size: 16px;font-weight: bold;margin-top: 20px;margin-bottom: 20px;\">{{key}}次报价</div>\r\n            <el-table :data=\"values\">\r\n              <el-table-column label=\"物料分类\" align=\"center\" width=\"150\">\r\n                <template slot-scope=\"scope\">\r\n                  {{scope.row.classify_name}}-{{scope.row.classify2_name}}-{{scope.row.classify3_name}}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"物料名称\" align=\"center\" width=\"150\" prop=\"material_name\">\r\n              </el-table-column>\r\n              <el-table-column label=\"物料规格\" align=\"center\" width=\"120\" prop=\"specs\">\r\n              </el-table-column>\r\n              <el-table-column label=\"物料数量\" align=\"center\" width=\"100\" prop=\"quantity\">\r\n              </el-table-column>\r\n              <el-table-column label=\"物料单位\" align=\"center\" width=\"100\" prop=\"unit\">\r\n              </el-table-column>\r\n              <el-table-column label=\"物料品牌\" align=\"center\" width=\"100\" prop=\"brand\">\r\n              </el-table-column>\r\n              <el-table-column label=\"物料单价\" align=\"center\" width=\"100\" prop=\"tax_price\">\r\n              </el-table-column>\r\n              <el-table-column label=\"期望交货日期\" align=\"center\" width=\"120\" prop=\"delivery_date\">\r\n              </el-table-column>\r\n              <el-table-column label=\"含税价\" align=\"center\" width=\"100\" prop=\"offer_tax_price\">\r\n              </el-table-column>\r\n              <el-table-column label=\"税率\" align=\"center\" width=\"100\" prop=\"offer_tax_rate\">\r\n              </el-table-column>\r\n              <el-table-column label=\"总价\" align=\"center\" width=\"100\" prop=\"offer_total_price\">\r\n              </el-table-column>\r\n              <el-table-column label=\"交货日期\" align=\"center\" width=\"120\" prop=\"offer_delivery_date\">\r\n              </el-table-column>\r\n              <el-table-column label=\"发货仓库\" align=\"center\" width=\"120\" prop=\"offer_warehouse\">\r\n              </el-table-column>\r\n              <el-table-column label=\"运费\" align=\"center\" width=\"100\" prop=\"offer_freight\">\r\n              </el-table-column>\r\n              <el-table-column label=\"询价附件\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div v-for=\"file in scope.row.attachment\" v-if=\"scope.row.attachment\">\r\n                    <a :href=\"file.url\" target=\"_blank\">{{file.name}}</a>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"报价附件\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div v-for=\"file in scope.row.offer_attachment\" v-if=\"scope.row.offer_attachment\">\r\n                    <a :href=\"file.url\" target=\"_blank\">{{file.name}}</a>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\n  import {\r\n    getData\r\n  } from '@/api/project/inquiry';\r\n  export default {\r\n    data() {\r\n      return {\r\n        dialogVisible: false,\r\n        form: {\r\n          \"inquiry\": {},\r\n          \"offers\": []\r\n        }\r\n      };\r\n    },\r\n    methods: {\r\n      open(inquiryId) {\r\n        this.dialogVisible = true;\r\n        getData(inquiryId).then(res => {\r\n          this.form = res.data;\r\n        })\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style scoped>\r\n  .el-descriptions-item__cell {\r\n    max-width: 300px;\r\n  }\r\n</style>\r\n"]}]}