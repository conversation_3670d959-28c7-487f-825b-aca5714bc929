
085f4a450d610d29ea5848d1bdec0db1e8708d79	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Ftinymce-5-dark\\u002Fcontent.css\",\"contentHash\":\"33b4a37d4d9e52cec60095fd7dc1d59b\"}","integrity":"sha512-UTDDehs5/UOfINAfjlPkNddpewBpH0Kkxp1eRi+dlxAJSDn8uxnMrIKymr9SMjrQ9092F/d6fSpT6dWOchKIVA==","time":1750496064272,"size":2240}