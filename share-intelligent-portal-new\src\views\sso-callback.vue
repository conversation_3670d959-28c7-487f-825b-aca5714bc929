<template>
  <div class="sso-callback-container">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
  </div>
</template>

<script>
import { handleSSOCallback } from "@/api/login";

export default {
  name: "SSOCallback",
  data() {
    return {
      loadingText: "正在处理SSO登录..."
    };
  },
  mounted() {
    this.handleCallback();
  },
  methods: {
    handleCallback() {
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');
      const key = urlParams.get('key');
      const redirect = urlParams.get('redirect');
      const code = urlParams.get('code');
      const state = urlParams.get('state');

      // 如果有key参数，说明是从后端SSO回调重定向过来的，需要用key获取token
      if (key) {
        this.loadingText = "正在获取登录凭证...";

        // 调用后端API用key换取token
        this.$http.get(`/prod-api/auth/sso/token?key=${key}`)
          .then(response => {
            if (response.data.code === 200 && response.data.data) {
              const tokenData = response.data.data;

              this.loadingText = "正在设置登录状态...";

              try {
                // 设置token到store
                this.$store.commit("SET_TOKEN", tokenData.access_token);

                this.loadingText = "登录成功，正在跳转...";
                this.$message.success("SSO登录成功");

                // 跳转到目标页面或首页
                const redirectUrl = redirect || '/';
                setTimeout(() => {
                  this.$router.push(redirectUrl);
                }, 1000);

              } catch (error) {
                console.error("设置登录状态失败:", error);
                this.loadingText = "登录状态设置失败";
                this.$message.error("登录状态设置失败");
                setTimeout(() => {
                  this.$router.push('/login');
                }, 2000);
              }
            } else {
              this.loadingText = "获取登录凭证失败";
              this.$message.error(response.data.msg || "获取登录凭证失败");
              setTimeout(() => {
                this.$router.push('/login');
              }, 2000);
            }
          })
          .catch(error => {
            console.error("获取登录凭证异常:", error);
            this.loadingText = "获取登录凭证异常";
            this.$message.error("获取登录凭证服务异常");
            setTimeout(() => {
              this.$router.push('/login');
            }, 2000);
          });
        return;
      }

      // 如果有token参数，说明是直接传递token的方式
      if (token) {
        this.loadingText = "正在设置登录状态...";

        try {
          // 设置token到store
          this.$store.commit("SET_TOKEN", token);

          this.loadingText = "登录成功，正在跳转...";
          this.$message.success("SSO登录成功");

          // 跳转到目标页面或首页
          const redirectUrl = redirect || '/';
          setTimeout(() => {
            this.$router.push(redirectUrl);
          }, 1000);

        } catch (error) {
          console.error("设置登录状态失败:", error);
          this.loadingText = "登录状态设置失败";
          this.$message.error("登录状态设置失败");
          setTimeout(() => {
            this.$router.push('/login');
          }, 2000);
        }
        return;
      }

      // 如果没有token但有code，说明是旧的API回调方式
      if (code) {
        this.loadingText = "正在验证授权码...";

        handleSSOCallback(code, state)
          .then(response => {
            if (response.code === 200) {
              this.loadingText = "登录成功，正在跳转...";
              this.$message.success("SSO登录成功");

              // 设置token
              if (response.data && response.data.access_token) {
                this.$store.commit("SET_TOKEN", response.data.access_token);
              }

              // 跳转到目标页面或首页
              const redirectUrl = state || '/';
              setTimeout(() => {
                this.$router.push(redirectUrl);
              }, 1000);
            } else {
              this.loadingText = "SSO登录失败";
              this.$message.error(response.msg || "SSO登录失败");
              setTimeout(() => {
                this.$router.push('/login');
              }, 2000);
            }
          })
          .catch(error => {
            console.error("SSO回调处理失败:", error);
            this.loadingText = "SSO登录异常";
            this.$message.error("SSO登录服务异常");
            setTimeout(() => {
              this.$router.push('/login');
            }, 2000);
          });
        return;
      }

      // 既没有token也没有code
      this.loadingText = "SSO登录失败：缺少必要参数";
      this.$message.error("SSO登录失败：缺少必要参数");
      setTimeout(() => {
        this.$router.push('/login');
      }, 2000);
    }
  }
};
</script>

<style scoped>
.sso-callback-container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 18px;
  font-weight: 500;
}
</style>
