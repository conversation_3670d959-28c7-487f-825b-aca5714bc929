{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\gen\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\gen\\index.vue", "mtime": 1750151094313}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_gen", "require", "_importTable", "_interopRequireDefault", "_highlight", "hljs", "registerLanguage", "_default", "exports", "default", "name", "components", "importTable", "data", "loading", "uniqueId", "ids", "tableNames", "single", "multiple", "showSearch", "total", "tableList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "tableName", "undefined", "tableComment", "preview", "open", "title", "activeName", "created", "getList", "activated", "time", "$route", "query", "t", "Number", "methods", "_this", "listTable", "addDateRange", "then", "response", "rows", "handleQuery", "handleGenTable", "row", "_this2", "$modal", "msgError", "genType", "genCode", "msgSuccess", "gen<PERSON><PERSON>", "$download", "zip", "handleSynchDb", "_this3", "confirm", "synchDb", "catch", "openImportTable", "$refs", "import", "show", "reset<PERSON><PERSON>y", "resetForm", "handlePreview", "_this4", "previewTable", "tableId", "highlightedCode", "code", "key", "vmName", "substring", "lastIndexOf", "indexOf", "language", "length", "result", "highlight", "value", "clipboardSuccess", "handleSelectionChange", "selection", "map", "item", "handleEditTable", "params", "$tab", "openPage", "handleDelete", "_this5", "tableIds", "delTable"], "sources": ["src/views/tool/gen/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"表名称\" prop=\"tableName\">\r\n        <el-input\r\n          v-model=\"queryParams.tableName\"\r\n          placeholder=\"请输入表名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"表描述\" prop=\"tableComment\">\r\n        <el-input\r\n          v-model=\"queryParams.tableComment\"\r\n          placeholder=\"请输入表描述\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleGenTable\"\r\n          v-hasPermi=\"['tool:gen:code']\"\r\n        >生成</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-upload\"\r\n          size=\"mini\"\r\n          @click=\"openImportTable\"\r\n          v-hasPermi=\"['tool:gen:import']\"\r\n        >导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleEditTable\"\r\n          v-hasPermi=\"['tool:gen:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['tool:gen:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" align=\"center\" width=\"55\"></el-table-column>\r\n      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"表名称\"\r\n        align=\"center\"\r\n        prop=\"tableName\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        label=\"表描述\"\r\n        align=\"center\"\r\n        prop=\"tableComment\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        label=\"实体\"\r\n        align=\"center\"\r\n        prop=\"className\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\" />\r\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\" width=\"160\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handlePreview(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:preview']\"\r\n          >预览</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleEditTable(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:edit']\"\r\n          >编辑</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:remove']\"\r\n          >删除</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleSynchDb(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:edit']\"\r\n          >同步</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-download\"\r\n            @click=\"handleGenTable(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:code']\"\r\n          >生成代码</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n    <!-- 预览界面 -->\r\n    <el-dialog :title=\"preview.title\" :visible.sync=\"preview.open\" width=\"80%\" top=\"5vh\" append-to-body class=\"scrollbar\">\r\n      <el-tabs v-model=\"preview.activeName\">\r\n        <el-tab-pane\r\n          v-for=\"(value, key) in preview.data\"\r\n          :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.vm'))\"\r\n          :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.vm'))\"\r\n          :key=\"key\"\r\n        >\r\n          <el-link :underline=\"false\" icon=\"el-icon-document-copy\" v-clipboard:copy=\"value\" v-clipboard:success=\"clipboardSuccess\" style=\"float:right\">复制</el-link>\r\n          <pre><code class=\"hljs\" v-html=\"highlightedCode(value, key)\"></code></pre>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n    <import-table ref=\"import\" @ok=\"handleQuery\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTable, previewTable, delTable, genCode, synchDb } from \"@/api/tool/gen\";\r\nimport importTable from \"./importTable\";\r\nimport hljs from \"highlight.js/lib/highlight\";\r\nimport \"highlight.js/styles/github-gist.css\";\r\nhljs.registerLanguage(\"java\", require(\"highlight.js/lib/languages/java\"));\r\nhljs.registerLanguage(\"xml\", require(\"highlight.js/lib/languages/xml\"));\r\nhljs.registerLanguage(\"html\", require(\"highlight.js/lib/languages/xml\"));\r\nhljs.registerLanguage(\"vue\", require(\"highlight.js/lib/languages/xml\"));\r\nhljs.registerLanguage(\"javascript\", require(\"highlight.js/lib/languages/javascript\"));\r\nhljs.registerLanguage(\"sql\", require(\"highlight.js/lib/languages/sql\"));\r\n\r\nexport default {\r\n  name: \"Gen\",\r\n  components: { importTable },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 唯一标识符\r\n      uniqueId: \"\",\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中表数组\r\n      tableNames: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表数据\r\n      tableList: [],\r\n      // 日期范围\r\n      dateRange: \"\",\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        tableName: undefined,\r\n        tableComment: undefined\r\n      },\r\n      // 预览参数\r\n      preview: {\r\n        open: false,\r\n        title: \"代码预览\",\r\n        data: {},\r\n        activeName: \"domain.java\"\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  activated() {\r\n    const time = this.$route.query.t;\r\n    if (time != null && time != this.uniqueId) {\r\n      this.uniqueId = time;\r\n      this.queryParams.pageNum = Number(this.$route.query.pageNum);\r\n      this.getList();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询表集合 */\r\n    getList() {\r\n      this.loading = true;\r\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.tableList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 生成代码操作 */\r\n    handleGenTable(row) {\r\n      const tableNames = row.tableName || this.tableNames;\r\n      if (tableNames == \"\") {\r\n        this.$modal.msgError(\"请选择要生成的数据\");\r\n        return;\r\n      }\r\n      if(row.genType === \"1\") {\r\n        genCode(row.tableName).then(response => {\r\n          this.$modal.msgSuccess(\"成功生成到自定义路径：\" + row.genPath);\r\n        });\r\n      } else {\r\n        this.$download.zip(\"/code/gen/batchGenCode?tables=\" + tableNames, \"ruoyi\");\r\n      }\r\n    },\r\n    /** 同步数据库操作 */\r\n    handleSynchDb(row) {\r\n      const tableName = row.tableName;\r\n      this.$modal.confirm('确认要强制同步\"' + tableName + '\"表结构吗？').then(function() {\r\n        return synchDb(tableName);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"同步成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 打开导入表弹窗 */\r\n    openImportTable() {\r\n      this.$refs.import.show();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 预览按钮 */\r\n    handlePreview(row) {\r\n      previewTable(row.tableId).then(response => {\r\n        this.preview.data = response.data;\r\n        this.preview.open = true;\r\n        this.preview.activeName = \"domain.java\";\r\n      });\r\n    },\r\n    /** 高亮显示 */\r\n    highlightedCode(code, key) {\r\n      const vmName = key.substring(key.lastIndexOf(\"/\") + 1, key.indexOf(\".vm\"));\r\n      var language = vmName.substring(vmName.indexOf(\".\") + 1, vmName.length);\r\n      const result = hljs.highlight(language, code || \"\", true);\r\n      return result.value || '&nbsp;';\r\n    },\r\n    /** 复制代码成功 */\r\n    clipboardSuccess(){\r\n      this.$modal.msgSuccess(\"复制成功\");\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.tableId);\r\n      this.tableNames = selection.map(item => item.tableName);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleEditTable(row) {\r\n      const tableId = row.tableId || this.ids[0];\r\n      const tableName = row.tableName || this.tableNames[0];\r\n      const params = { pageNum: this.queryParams.pageNum };\r\n      this.$tab.openPage(\"修改[\" + tableName + \"]生成配置\", '/tool/gen-edit/index/' + tableId, params);\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const tableIds = row.tableId || this.ids;\r\n      this.$modal.confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项？').then(function() {\r\n        return delTable(tableIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AAkLA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAD,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACAI,kBAAA,CAAAC,gBAAA,SAAAL,OAAA;AACAI,kBAAA,CAAAC,gBAAA,QAAAL,OAAA;AACAI,kBAAA,CAAAC,gBAAA,SAAAL,OAAA;AACAI,kBAAA,CAAAC,gBAAA,QAAAL,OAAA;AACAI,kBAAA,CAAAC,gBAAA,eAAAL,OAAA;AACAI,kBAAA,CAAAC,gBAAA,QAAAL,OAAA;AAAA,IAAAM,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,QAAA;MACA;MACAC,GAAA;MACA;MACAC,UAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,YAAA,EAAAD;MACA;MACA;MACAE,OAAA;QACAC,IAAA;QACAC,KAAA;QACAnB,IAAA;QACAoB,UAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,CAAA;IACA,IAAAH,IAAA,YAAAA,IAAA,SAAAtB,QAAA;MACA,KAAAA,QAAA,GAAAsB,IAAA;MACA,KAAAb,WAAA,CAAAC,OAAA,GAAAgB,MAAA,MAAAH,MAAA,CAAAC,KAAA,CAAAd,OAAA;MACA,KAAAU,OAAA;IACA;EACA;EACAO,OAAA;IACA,YACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,KAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,cAAA,OAAAC,YAAA,MAAArB,WAAA,OAAAD,SAAA,GAAAuB,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAArB,SAAA,GAAAyB,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAtB,KAAA,GAAA0B,QAAA,CAAA1B,KAAA;QACAsB,KAAA,CAAA7B,OAAA;MACA,CACA;IACA;IACA,aACAmC,WAAA,WAAAA,YAAA;MACA,KAAAzB,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACA,aACAe,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAnC,UAAA,GAAAkC,GAAA,CAAAxB,SAAA,SAAAV,UAAA;MACA,IAAAA,UAAA;QACA,KAAAoC,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAH,GAAA,CAAAI,OAAA;QACA,IAAAC,YAAA,EAAAL,GAAA,CAAAxB,SAAA,EAAAmB,IAAA,WAAAC,QAAA;UACAK,MAAA,CAAAC,MAAA,CAAAI,UAAA,iBAAAN,GAAA,CAAAO,OAAA;QACA;MACA;QACA,KAAAC,SAAA,CAAAC,GAAA,oCAAA3C,UAAA;MACA;IACA;IACA,cACA4C,aAAA,WAAAA,cAAAV,GAAA;MAAA,IAAAW,MAAA;MACA,IAAAnC,SAAA,GAAAwB,GAAA,CAAAxB,SAAA;MACA,KAAA0B,MAAA,CAAAU,OAAA,cAAApC,SAAA,aAAAmB,IAAA;QACA,WAAAkB,YAAA,EAAArC,SAAA;MACA,GAAAmB,IAAA;QACAgB,MAAA,CAAAT,MAAA,CAAAI,UAAA;MACA,GAAAQ,KAAA;IACA;IACA,cACAC,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,IAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAA/C,SAAA;MACA,KAAAgD,SAAA;MACA,KAAAtB,WAAA;IACA;IACA,WACAuB,aAAA,WAAAA,cAAArB,GAAA;MAAA,IAAAsB,MAAA;MACA,IAAAC,iBAAA,EAAAvB,GAAA,CAAAwB,OAAA,EAAA7B,IAAA,WAAAC,QAAA;QACA0B,MAAA,CAAA3C,OAAA,CAAAjB,IAAA,GAAAkC,QAAA,CAAAlC,IAAA;QACA4D,MAAA,CAAA3C,OAAA,CAAAC,IAAA;QACA0C,MAAA,CAAA3C,OAAA,CAAAG,UAAA;MACA;IACA;IACA,WACA2C,eAAA,WAAAA,gBAAAC,IAAA,EAAAC,GAAA;MACA,IAAAC,MAAA,GAAAD,GAAA,CAAAE,SAAA,CAAAF,GAAA,CAAAG,WAAA,WAAAH,GAAA,CAAAI,OAAA;MACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAC,SAAA,CAAAD,MAAA,CAAAG,OAAA,WAAAH,MAAA,CAAAK,MAAA;MACA,IAAAC,MAAA,GAAAhF,kBAAA,CAAAiF,SAAA,CAAAH,QAAA,EAAAN,IAAA;MACA,OAAAQ,MAAA,CAAAE,KAAA;IACA;IACA,aACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAnC,MAAA,CAAAI,UAAA;IACA;IACA;IACAgC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1E,GAAA,GAAA0E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjB,OAAA;MAAA;MACA,KAAA1D,UAAA,GAAAyE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjE,SAAA;MAAA;MACA,KAAAT,MAAA,GAAAwE,SAAA,CAAAN,MAAA;MACA,KAAAjE,QAAA,IAAAuE,SAAA,CAAAN,MAAA;IACA;IACA,aACAS,eAAA,WAAAA,gBAAA1C,GAAA;MACA,IAAAwB,OAAA,GAAAxB,GAAA,CAAAwB,OAAA,SAAA3D,GAAA;MACA,IAAAW,SAAA,GAAAwB,GAAA,CAAAxB,SAAA,SAAAV,UAAA;MACA,IAAA6E,MAAA;QAAArE,OAAA,OAAAD,WAAA,CAAAC;MAAA;MACA,KAAAsE,IAAA,CAAAC,QAAA,SAAArE,SAAA,sCAAAgD,OAAA,EAAAmB,MAAA;IACA;IACA,aACAG,YAAA,WAAAA,aAAA9C,GAAA;MAAA,IAAA+C,MAAA;MACA,IAAAC,QAAA,GAAAhD,GAAA,CAAAwB,OAAA,SAAA3D,GAAA;MACA,KAAAqC,MAAA,CAAAU,OAAA,iBAAAoC,QAAA,aAAArD,IAAA;QACA,WAAAsD,aAAA,EAAAD,QAAA;MACA,GAAArD,IAAA;QACAoD,MAAA,CAAA/D,OAAA;QACA+D,MAAA,CAAA7C,MAAA,CAAAI,UAAA;MACA,GAAAQ,KAAA;IACA;EACA;AACA", "ignoreList": []}]}