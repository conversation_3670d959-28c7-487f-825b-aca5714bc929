package com.ruoyi.sso.service;

import com.ruoyi.sso.domain.SSOUser;
import com.ruoyi.system.api.domain.Member;

/**
 * SSO用户管理服务接口
 * 负责SSO用户的创建、更新和同步
 *
 * <AUTHOR>
 */
public interface SSOUserManagementService {

    /**
     * 从主系统Member创建SSO用户
     * 主系统注册时调用，只维护SSO表
     *
     * @param member 主系统用户
     * @return SSO用户
     */
    SSOUser createFromMember(Member member);

    /**
     * 从主系统Member更新SSO用户信息
     *
     * @param member 主系统用户
     * @return 是否成功
     */
    boolean updateFromMember(Member member);

    /**
     * 检查并创建SSO用户（如果不存在）
     * SSO登录时的兜底机制
     *
     * @param phone 手机号
     * @return SSO用户
     */
    SSOUser ensureSSOUserExists(String phone);

    /**
     * 为主系统注册提供的接口
     * 主系统注册成功后调用此接口创建SSO用户
     *
     * @param memberPhone 手机号
     * @param memberRealName 真实姓名
     * @param password 密码（已加密）
     * @return 是否成功
     */
    boolean createSSOUserForRegistration(String memberPhone, String memberRealName, String password);


    /**
     * 更新SSO用户密码
     * 主系统密码重置时调用
     *
     * @param phone 手机号
     * @param password 新密码（已加密）
     * @return 是否成功
     */
    boolean updateSSOUserPassword(String phone, String password);

    /**
     * 批量同步现有主系统用户到SSO
     * 用于数据迁移
     *
     * @return 同步数量
     */
    int batchSyncMembersToSSO();
}
