{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\login.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\login.js", "mtime": 1750151093955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "login", "username", "password", "code", "uuid", "request", "url", "method", "data", "register", "headers", "isToken", "refreshToken", "getInfo", "logout", "getCodeImg", "timeout"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/login.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 登录方法\r\n// 登录方法\r\nexport function login(username, password, code, uuid) {\r\n  return request({\r\n    url: '/auth/login',\r\n    method: 'post',\r\n    data: { username, password, code, uuid }\r\n  })\r\n}\r\n\r\n// 注册方法\r\nexport function register(data) {\r\n  return request({\r\n    url: '/auth/register',\r\n    headers: {\r\n      isToken: false\r\n    },\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 刷新方法\r\nexport function refreshToken() {\r\n  return request({\r\n    url: '/auth/refresh',\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 获取用户详细信息\r\nexport function getInfo() {\r\n  return request({\r\n    url: '/system/user/getInfo',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 退出方法\r\nexport function logout() {\r\n  return request({\r\n    url: '/auth/logout',\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获取验证码\r\nexport function getCodeImg() {\r\n  return request({\r\n    url: '/code',\r\n    headers: {\r\n      isToken: false\r\n    },\r\n    method: 'get',\r\n    timeout: 20000\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACA;AACO,SAASC,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACpD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE;MAAEP,QAAQ,EAARA,QAAQ;MAAEC,QAAQ,EAARA,QAAQ;MAAEC,IAAI,EAAJA,IAAI;MAAEC,IAAI,EAAJA;IAAK;EACzC,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,QAAQA,CAACD,IAAI,EAAE;EAC7B,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBI,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDJ,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,OAAOA,CAAA,EAAG;EACxB,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,MAAMA,CAAA,EAAG;EACvB,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,OAAO;IACZI,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDJ,MAAM,EAAE,KAAK;IACbS,OAAO,EAAE;EACX,CAAC,CAAC;AACJ", "ignoreList": []}]}