{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\components\\productdetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\components\\productdetail.vue", "mtime": 1750151094281}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfcHJvZHVjdCA9IHJlcXVpcmUoIkAvYXBpL3N0b3JlL3Byb2R1Y3QiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgc2hvdzogZmFsc2UsCiAgICAgIG9wRm9ybTogewogICAgICAgIG9waWQ6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZCwKICAgICAgICByZW1hcms6ICcnCiAgICAgIH0sCiAgICAgIGZvcm06IHt9CiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgb3BlbjogZnVuY3Rpb24gb3BlbihpZCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLm9wRm9ybS5vcGlkID0gaWQ7CiAgICAgIHRoaXMuc2hvdyA9IHRydWU7CiAgICAgICgwLCBfcHJvZHVjdC5nZXREYXRhKShpZCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMuZm9ybSA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRsZVN1Ym1pdChzdGF0dXMpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMub3BGb3JtLnN0YXR1cyA9IHN0YXR1czsKICAgICAgKDAsIF9wcm9kdWN0Lm9wRGF0YSkodGhpcy5vcEZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMi4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5oiQ5YqfJwogICAgICAgIH0pOwogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgX3RoaXMyLnNob3cgPSBmYWxzZTsKICAgICAgICBfdGhpczIuJGVtaXQoInJlZnJlc2giKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_product", "require", "data", "loading", "show", "opForm", "opid", "undefined", "status", "remark", "form", "methods", "open", "id", "_this", "getData", "then", "res", "handleSubmit", "_this2", "opData", "$message", "type", "message", "$emit"], "sources": ["src/views/store/components/productdetail.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog title=\"产品详情\" :visible.sync=\"show\" width=\"80%\" center>\r\n      <el-descriptions class=\"margin-top\" title=\"基本信息\" :column=\"3\" direction=\"horizontal\" border>\r\n        <el-descriptions-item label=\"产品类型\">\r\n          <el-tag type=\"primary\">{{form.typeStr}}</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审核状态\">\r\n          <el-tag type=\"warning\">{{form.statusStr}}</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"产品分类\">\r\n          {{form.classify_name}}{{form.classify2_name}}{{form.classify3_name}}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"所属企业\">{{form.enterprise_name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"产品编号\">{{form.product_no}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"产品名称\">{{form.name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"产品描述\">{{form.description}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"产品封面\">\r\n          <el-image v-if=\"form.cover\" style=\"width: 100px; height: 100px\" :src=\"form.cover\"\r\n            :preview-src-list=\"[form.cover]\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"含税单价\">{{form.tax_price}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"税率\">{{form.tax_rate}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"运费\">{{form.freight}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"产品单位\">{{form.unit}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"品牌\">{{form.brand}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"库存\">{{form.stock}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"销量\">{{form.sales}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"型号\">{{form.model}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"最小起订量\">{{form.start_order}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"最小包装量\">{{form.start_pack}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"批次\">{{form.batchno}}</el-descriptions-item>\r\n\r\n        <el-descriptions-item label=\"申请人\">{{form.create_by}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"申请时间\">{{form.create_time}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"审核备注\">{{form.remark || ''}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"产品规格书\">\r\n          <span v-if=\"form.normurl\">\r\n             <a target=\"_blank\" :href=\"form.normurl\">{{form.normfile}}</a>\r\n          </span>\r\n          <span v-if=\"!form.normfile\">\r\n            暂无\r\n          </span>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions v-if=\"form.type=='CENTRAL'\" style=\"margin-top:20px\" title=\"集采详情\" :column=\"3\"\r\n        direction=\"horizontal\" border>\r\n        <el-descriptions-item label=\"集采定金比例\">{{form.payment_rate}}%</el-descriptions-item>\r\n        <el-descriptions-item label=\"截止日期\">{{form.deadline}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"交货日期\">{{form.deliver_start}}到{{form.deliver_end}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采状态\">\r\n          <el-tag type=\"warning\">{{form.central_statusStr}}</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"非集采价\">{{form.sale_price}}</el-descriptions-item>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"集采目标数量\">{{form.central_goal}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采实际数量\">{{form.central_real}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采展示比例\">{{form.central_percent}}%</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采规则\">{{form.central_rule}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采取消原因\">{{form.central_note || ''}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采附件\"><a target=\"_blank\" :href=\"form.file_path\">{{form.file_name}}</a>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions v-if=\"form.type=='GROUP'\" style=\"margin-top:20px\" title=\"团购详情\" :column=\"3\" direction=\"horizontal\"\r\n        border>\r\n        <el-descriptions-item label=\"截止日期\">{{form.deadline}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"团购状态\">\r\n          <el-tag type=\"warning\">{{form.group_statusStr}}</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"非团购价\">{{form.sale_price}}</el-descriptions-item>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions v-if=\"form.pictures\" style=\"margin-top:20px\" title=\"产品图片\" :column=\"1\" direction=\"horizontal\"\r\n        border>\r\n        <el-descriptions-item label=\"图片预览\">\r\n          <el-image style=\"width: 100px; height: 100px\" :src=\"item\" v-for=\"(item,index) in form.pictures\" :key=\"index\"\r\n            :preview-src-list=\"form.pictures\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions v-if=\"form.details\" style=\"margin-top:20px\" title=\"产品详情\" :column=\"1\" direction=\"horizontal\"\r\n        border>\r\n        <el-descriptions-item label=\"图片预览\">\r\n          <el-image style=\"width: 100px; height: 100px\" :src=\"item\" v-for=\"(item,index) in form.details\" :key=\"index\"\r\n            :preview-src-list=\"form.details\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-row v-if=\"form.status=='WAIT'\" style=\"margin-top: 20px;\">\r\n        <el-form size=\"small\" label-width=\"68px\">\r\n          <el-form-item label=\"审核备注\" prop=\"remark\">\r\n            <el-input clearable type='textarea' v-model=\"opForm.remark\" placeholder=\"请输入备注\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\" style=\"text-align: center;\">\r\n        <el-button @click=\"show = false\">取 消</el-button>\r\n        <el-button v-if=\"form.status=='WAIT'\" type=\"primary\" :loading=\"loading\" @click=\"handleSubmit('ONLINE')\">通 过\r\n        </el-button>\r\n        <el-button v-if=\"form.status=='WAIT'\" type=\"danger\" :loading=\"loading\" @click=\"handleSubmit('DENY')\">驳 回\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\n  import {\r\n    getData,\r\n    opData\r\n  } from '@/api/store/product';\r\n  export default {\r\n    data() {\r\n      return {\r\n        loading: false,\r\n        show: false,\r\n        opForm: {\r\n          opid: undefined,\r\n          status: undefined,\r\n          remark: ''\r\n        },\r\n        form: {}\r\n      };\r\n    },\r\n    methods: {\r\n      open(id) {\r\n        this.opForm.opid = id\r\n        this.show = true;\r\n        getData(id).then(res => {\r\n          this.form = res.data;\r\n        })\r\n      },\r\n      handleSubmit(status) {\r\n        this.loading = true\r\n        this.opForm.status = status\r\n        opData(this.opForm).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '操作成功'\r\n          })\r\n          this.loading = false\r\n          this.show = false;\r\n          this.$emit(\"refresh\")\r\n        })\r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .el-descriptions-item__cell {\r\n    max-width: 300px;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;AA2GA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAIA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,MAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD,SAAA;QACAE,MAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAC,EAAA;MAAA,IAAAC,KAAA;MACA,KAAAT,MAAA,CAAAC,IAAA,GAAAO,EAAA;MACA,KAAAT,IAAA;MACA,IAAAW,gBAAA,EAAAF,EAAA,EAAAG,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAJ,IAAA,GAAAO,GAAA,CAAAf,IAAA;MACA;IACA;IACAgB,YAAA,WAAAA,aAAAV,MAAA;MAAA,IAAAW,MAAA;MACA,KAAAhB,OAAA;MACA,KAAAE,MAAA,CAAAG,MAAA,GAAAA,MAAA;MACA,IAAAY,eAAA,OAAAf,MAAA,EAAAW,IAAA,WAAAC,GAAA;QACAE,MAAA,CAAAE,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACAJ,MAAA,CAAAhB,OAAA;QACAgB,MAAA,CAAAf,IAAA;QACAe,MAAA,CAAAK,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}