{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\list.vue", "mtime": 1750151094268}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgb3JkZXJEZXRhaWxzIGZyb20gIi4vY29tcG9uZW50cy9vcmRlckRldGFpbHMudnVlIjsNCmltcG9ydCB7IGxpc3RFbnVtIH0gZnJvbSAiQC9hcGkvdG9vbC91dGlsIjsNCmltcG9ydCB7IGxpc3REYXRhLCBjb25maXJtRGF0YSB9IGZyb20gIkAvYXBpL29yZGVyL2xpc3QiOw0KZXhwb3J0IGRlZmF1bHQgew0KICBsYXlvdXQ6ICJvcmRlciIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBvcmRlckRldGFpbHMsDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGZvcm06IHt9LA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIHR5cGVPcHRpb25zOiBbXSwNCiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLA0KICAgICAgY2VudHJhbE9wdGlvbnM6IFtdLA0KICAgICAgcGF5T3B0aW9uczogW10sDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHRvdGFsOiAxLA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBvcmRlcl90eXBlOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIGNlbnRyYWxfc3RhdHVzOiBudWxsLA0KICAgICAgICBjZW50cmFsX3BheV9zdGF0dXM6IG51bGwsDQogICAgICAgIHN0YXJ0X3RpbWU6IG51bGwsDQogICAgICAgIGVuZF90aW1lOiBudWxsLA0KICAgICAgICBvcmRlcl9ubzogbnVsbCwNCiAgICAgICAgZGVtYW5kX25hbWU6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g6KGo5qC85pWw5o2uDQogICAgICBsaXN0OiBbXSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0RW51bXMoKTsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldEVudW1zKCkgew0KICAgICAgbGlzdEVudW0oKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5jZW50cmFsT3B0aW9ucyA9IHJlcy5kYXRhLmNlbnRyYWxPcmRlclN0YXR1czsNCiAgICAgICAgdGhpcy5wYXlPcHRpb25zID0gcmVzLmRhdGEuY2VudHJhbFBheVN0YXR1czsNCiAgICAgICAgdGhpcy50eXBlT3B0aW9ucyA9IHJlcy5kYXRhLm9yZGVyVHlwZTsNCiAgICAgICAgdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzLmRhdGEub3JkZXJTdGF0dXM7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldExpc3QoKSB7DQogICAgICBpZiAodGhpcy5kYXRlUmFuZ2UgJiYgdGhpcy5kYXRlUmFuZ2UubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0X3RpbWUgPSB0aGlzLmRhdGVSYW5nZVswXTsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRfdGltZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydF90aW1lID0gIiI7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kX3RpbWUgPSAiIjsNCiAgICAgIH0NCiAgICAgIGxpc3REYXRhKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmxpc3QgPSByZXMuZGF0YTsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5jb3VudDsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICBoYW5kbGVEZXRhaWwocm93KSB7DQogICAgICB0aGlzLiRyZWZzLm9yZGVyRGV0YWlscy5vcGVuKHJvdy5pZCk7DQogICAgfSwNCiAgICBoYW5kbGVDb25maXJtKHJvdykgew0KICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k6K+l6K6i5Y2VPyIsICLmj5DnpLoiLCB7DQogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBsZXQgZGF0YSA9IHsNCiAgICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICB9Ow0KICAgICAgICBjb25maXJtRGF0YShkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8hIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/order", "sourcesContent": ["<!-- 订单列表汇总 -->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-form\r\n        :model=\"queryParams\"\r\n        ref=\"queryForm\"\r\n        :inline=\"true\"\r\n        size=\"small\"\r\n        label-width=\"68px\"\r\n      >\r\n        <el-form-item label=\"\" prop=\"order_type\">\r\n          <el-select\r\n            clearable\r\n            v-model=\"queryParams.order_type\"\r\n            placeholder=\"订单类型\"\r\n            style=\"width: 120px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in typeOptions\"\r\n              :key=\"item.key\"\r\n              :label=\"item.value\"\r\n              :value=\"item.key\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"status\">\r\n          <el-select\r\n            clearable\r\n            v-model=\"queryParams.status\"\r\n            placeholder=\"订单状态\"\r\n            style=\"width: 120px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in statusOptions\"\r\n              :key=\"item.key\"\r\n              :label=\"item.value\"\r\n              :value=\"item.key\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"central_status\">\r\n          <el-select\r\n            clearable\r\n            v-model=\"queryParams.central_status\"\r\n            placeholder=\"集采订单状态\"\r\n            style=\"width: 140px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in centralOptions\"\r\n              :key=\"item.key\"\r\n              :label=\"item.value\"\r\n              :value=\"item.key\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"central_pay_status\">\r\n          <el-select\r\n            clearable\r\n            v-model=\"queryParams.central_pay_status\"\r\n            placeholder=\"集采支付状态\"\r\n            style=\"width: 140px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in payOptions\"\r\n              :key=\"item.key\"\r\n              :label=\"item.value\"\r\n              :value=\"item.key\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"order_no\">\r\n          <el-input\r\n            v-model=\"queryParams.order_no\"\r\n            placeholder=\"输入订单号\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"demand_name\">\r\n          <el-input\r\n            v-model=\"queryParams.demand_name\"\r\n            placeholder=\"输入需方\"\r\n            :maxlength=\"50\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"下单时间\">\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            style=\"width: 240px\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"daterange\"\r\n            range-separator=\"-\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n          ></el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n            >重置</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-row>\r\n    <el-table :data=\"list\" height=\"500\" style=\"width: 100%\">\r\n      <el-table-column prop=\"id\" label=\"序号\" align=\"center\" width=\"55\" />\r\n      <el-table-column\r\n        prop=\"order_no\"\r\n        label=\"订单号\"\r\n        align=\"center\"\r\n        width=\"160\"\r\n      />\r\n      <el-table-column\r\n        prop=\"create_time\"\r\n        label=\"下单时间\"\r\n        align=\"center\"\r\n        width=\"160\"\r\n      />\r\n      <el-table-column\r\n        prop=\"order_type\"\r\n        label=\"订单类型\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        prop=\"demand_name\"\r\n        label=\"需方\"\r\n        align=\"center\"\r\n        width=\"240\"\r\n        :show-overflow-tooltip=\"true\"\r\n      />\r\n      <el-table-column\r\n        prop=\"supply_name\"\r\n        label=\"供方\"\r\n        align=\"center\"\r\n        width=\"240\"\r\n        :show-overflow-tooltip=\"true\"\r\n      />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag size=\"mini\" type=\"warning\">{{ scope.row.statusStr }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"支付方式\"\r\n        align=\"center\"\r\n        prop=\"payment\"\r\n        width=\"120\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag size=\"mini\" type=\"primary\">{{ scope.row.paymentStr }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"total_price\"\r\n        label=\"订单总金额\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        label=\"集采状态\"\r\n        align=\"center\"\r\n        prop=\"central_status\"\r\n        width=\"120\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            size=\"mini\"\r\n            v-if=\"scope.row.central_statusStr\"\r\n            type=\"warning\"\r\n            >{{ scope.row.central_statusStr }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"集采支付状态\"\r\n        align=\"center\"\r\n        prop=\"central_pay_status\"\r\n        width=\"120\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            size=\"mini\"\r\n            v-if=\"scope.row.central_pay_statusStr\"\r\n            type=\"warning\"\r\n            >{{ scope.row.central_pay_statusStr }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"logistics_no\"\r\n        label=\"物流单号\"\r\n        align=\"center\"\r\n        width=\"240\"\r\n        :show-overflow-tooltip=\"true\"\r\n      />\r\n      <el-table-column\r\n        prop=\"operator\"\r\n        label=\"操作员\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"130\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"mini\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >详情</el-button\r\n          >\r\n          <!-- <el-button v-if=\"scope.row.status=='CONFIRM'\" type=\"text\" size=\"mini\" icon=\"el-icon-edit\" @click=\"handleConfirm(scope.row)\">确认</el-button> -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <Pagination\r\n      @pagination=\"getList\"\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n    >\r\n    </Pagination>\r\n    <!-- 订单详情 -->\r\n    <orderDetails ref=\"orderDetails\"></orderDetails>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport orderDetails from \"./components/orderDetails.vue\";\r\nimport { listEnum } from \"@/api/tool/util\";\r\nimport { listData, confirmData } from \"@/api/order/list\";\r\nexport default {\r\n  layout: \"order\",\r\n  components: {\r\n    orderDetails,\r\n  },\r\n  data() {\r\n    return {\r\n      form: {},\r\n      dateRange: [],\r\n      typeOptions: [],\r\n      statusOptions: [],\r\n      centralOptions: [],\r\n      payOptions: [],\r\n      dialogVisible: false,\r\n      total: 1,\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        order_type: null,\r\n        status: null,\r\n        central_status: null,\r\n        central_pay_status: null,\r\n        start_time: null,\r\n        end_time: null,\r\n        order_no: null,\r\n        demand_name: null,\r\n      },\r\n      // 表格数据\r\n      list: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getEnums();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getEnums() {\r\n      listEnum().then((res) => {\r\n        this.centralOptions = res.data.centralOrderStatus;\r\n        this.payOptions = res.data.centralPayStatus;\r\n        this.typeOptions = res.data.orderType;\r\n        this.statusOptions = res.data.orderStatus;\r\n      });\r\n    },\r\n    getList() {\r\n      if (this.dateRange && this.dateRange.length > 0) {\r\n        this.queryParams.start_time = this.dateRange[0];\r\n        this.queryParams.end_time = this.dateRange[1];\r\n      } else {\r\n        this.queryParams.start_time = \"\";\r\n        this.queryParams.end_time = \"\";\r\n      }\r\n      listData(this.queryParams).then((res) => {\r\n        this.list = res.data;\r\n        this.total = res.count;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleDetail(row) {\r\n      this.$refs.orderDetails.open(row.id);\r\n    },\r\n    handleConfirm(row) {\r\n      this.$confirm(\"是否确认该订单?\", \"提示\", {\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        let data = {\r\n          id: row.id,\r\n        };\r\n        confirmData(data).then((res) => {\r\n          this.$message({\r\n            type: \"success\",\r\n            message: \"操作成功!\",\r\n          });\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scope>\r\n.w160 {\r\n  width: 160px;\r\n}\r\n</style>\r\n"]}]}