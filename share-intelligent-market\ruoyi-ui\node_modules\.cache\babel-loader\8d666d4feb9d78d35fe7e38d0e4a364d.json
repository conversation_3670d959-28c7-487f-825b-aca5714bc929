{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\RuoYi\\Git\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\RuoYi\\Git\\index.vue", "mtime": 1750151094150}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnUnVvWWlHaXQnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICdodHRwczovL2dpdGVlLmNvbS95X3Byb2plY3QvUnVvWWktQ2xvdWQnCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgZ290bzogZnVuY3Rpb24gZ290bygpIHsKICAgICAgd2luZG93Lm9wZW4odGhpcy51cmwpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "url", "methods", "goto", "window", "open"], "sources": ["src/components/RuoYi/Git/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon icon-class=\"github\" @click=\"goto\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RuoYiGit',\r\n  data() {\r\n    return {\r\n      url: 'https://gitee.com/y_project/RuoYi-Cloud'\r\n    }\r\n  },\r\n  methods: {\r\n    goto() {\r\n      window.open(this.url)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;iCAOA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACAC,MAAA,CAAAC,IAAA,MAAAJ,GAAA;IACA;EACA;AACA", "ignoreList": []}]}