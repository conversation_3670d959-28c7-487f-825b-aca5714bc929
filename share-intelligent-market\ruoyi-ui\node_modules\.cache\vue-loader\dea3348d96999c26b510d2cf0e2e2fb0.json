{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\product.vue?vue&type=template&id=a0405cd2", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\product.vue", "mtime": 1750151094284}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}