
b8d4931cc7f5869b9c19161672880d10d6287dcf	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Fwriter\\u002Fcontent.css\",\"contentHash\":\"955b4990eb5e2ab005327493dc5d9c90\"}","integrity":"sha512-V67ub6N03ee7gt/TVI7tRoeDCa0tktBo/EC0YvpIP+7gurnwbaBqV+6SaWA613CdVOVpaoSLrKP2gWCp8wCr1A==","time":1750496064273,"size":2165}