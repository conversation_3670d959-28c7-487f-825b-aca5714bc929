{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\banner\\index.vue?vue&type=template&id=dc3c3f1a", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\banner\\index.vue", "mtime": 1750151094252}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}