{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\demand_joint.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\demand_joint.js", "mtime": 1750151093996}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGREZW1hbmRfam9pbnQgPSBhZGREZW1hbmRfam9pbnQ7CmV4cG9ydHMuZGVsRGVtYW5kX2pvaW50ID0gZGVsRGVtYW5kX2pvaW50OwpleHBvcnRzLmdldERlbWFuZF9qb2ludCA9IGdldERlbWFuZF9qb2ludDsKZXhwb3J0cy5saXN0RGVtYW5kX2pvaW50ID0gbGlzdERlbWFuZF9qb2ludDsKZXhwb3J0cy51cGRhdGVEZW1hbmRfam9pbnQgPSB1cGRhdGVEZW1hbmRfam9pbnQ7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LpnIDmsYLlkIjkvZzliJfooagKZnVuY3Rpb24gbGlzdERlbWFuZF9qb2ludChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9kZW1hbmRfam9pbnQvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LpnIDmsYLlkIjkvZzor6bnu4YKZnVuY3Rpb24gZ2V0RGVtYW5kX2pvaW50KGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvdXVjL2RlbWFuZF9qb2ludC8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinumcgOaxguWQiOS9nApmdW5jdGlvbiBhZGREZW1hbmRfam9pbnQoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9kZW1hbmRfam9pbnQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuemcgOaxguWQiOS9nApmdW5jdGlvbiB1cGRhdGVEZW1hbmRfam9pbnQoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9kZW1hbmRfam9pbnQnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk6ZyA5rGC5ZCI5L2cCmZ1bmN0aW9uIGRlbERlbWFuZF9qb2ludChpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9kZW1hbmRfam9pbnQvJyArIGlkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDemand_joint", "query", "request", "url", "method", "params", "getDemand_joint", "id", "addDemand_joint", "data", "updateDemand_joint", "delDemand_joint"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/uuc/demand_joint.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询需求合作列表\r\nexport function listDemand_joint(query) {\r\n  return request({\r\n    url: '/uuc/demand_joint/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询需求合作详细\r\nexport function getDemand_joint(id) {\r\n  return request({\r\n    url: '/uuc/demand_joint/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增需求合作\r\nexport function addDemand_joint(data) {\r\n  return request({\r\n    url: '/uuc/demand_joint',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改需求合作\r\nexport function updateDemand_joint(data) {\r\n  return request({\r\n    url: '/uuc/demand_joint',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除需求合作\r\nexport function delDemand_joint(id) {\r\n  return request({\r\n    url: '/uuc/demand_joint/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,kBAAkBA,CAACD,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,eAAeA,CAACJ,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}