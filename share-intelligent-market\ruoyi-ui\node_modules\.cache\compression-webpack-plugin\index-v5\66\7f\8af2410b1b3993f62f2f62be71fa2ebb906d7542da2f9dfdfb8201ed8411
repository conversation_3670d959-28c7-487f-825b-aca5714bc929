
6f19ae9108fa426dc1ff20a768fa48b7b28bade8	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"index.html\",\"contentHash\":\"4e89713852ddf6c06fff119f6b2bd478\"}","integrity":"sha512-VzIna9Ry9CgoanEQ+uRqMPCFF/wbmwbT8PvjIRkJVSuIeV6WlV6VxjrhMISG3GM6iNJ8nhcbFs9RV38voG5QcA==","time":1750496064277,"size":4979}