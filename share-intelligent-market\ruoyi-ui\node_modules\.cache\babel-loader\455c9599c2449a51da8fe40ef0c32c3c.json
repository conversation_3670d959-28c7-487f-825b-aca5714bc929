{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\user\\authRole.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\user\\authRole.vue", "mtime": 1750151094301}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "name", "data", "loading", "total", "pageNum", "pageSize", "roleIds", "roles", "form", "created", "_this", "userId", "$route", "params", "getAuthRole", "then", "response", "user", "length", "$nextTick", "for<PERSON>ach", "row", "flag", "$refs", "table", "toggleRowSelection", "methods", "clickRow", "handleSelectionChange", "selection", "map", "item", "roleId", "getRowKey", "submitForm", "_this2", "join", "updateAuthRole", "$modal", "msgSuccess", "close", "obj", "path", "$tab", "closeOpenPage"], "sources": ["src/views/system/user/authRole.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <h4 class=\"form-header h4\">基本信息</h4>\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-col :span=\"8\" :offset=\"2\">\r\n          <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n            <el-input v-model=\"form.nickName\" disabled />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\" :offset=\"2\">\r\n          <el-form-item label=\"登录账号\" prop=\"userName\">\r\n            <el-input  v-model=\"form.userName\" disabled />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <h4 class=\"form-header h4\">角色信息</h4>\r\n    <el-table v-loading=\"loading\" :row-key=\"getRowKey\" @row-click=\"clickRow\" ref=\"table\" @selection-change=\"handleSelectionChange\" :data=\"roles.slice((pageNum-1)*pageSize,pageNum*pageSize)\">\r\n      <el-table-column label=\"序号\" type=\"index\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column type=\"selection\" :reserve-selection=\"true\" width=\"55\"></el-table-column>\r\n      <el-table-column label=\"角色编号\" align=\"center\" prop=\"roleId\" />\r\n      <el-table-column label=\"角色名称\" align=\"center\" prop=\"roleName\" />\r\n      <el-table-column label=\"权限字符\" align=\"center\" prop=\"roleKey\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"pageNum\" :limit.sync=\"pageSize\" />\r\n\r\n    <el-form label-width=\"100px\">\r\n      <el-form-item style=\"text-align: center;margin-left:-120px;margin-top:30px;\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\r\n        <el-button @click=\"close()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getAuthRole, updateAuthRole } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"AuthRole\",\r\n  data() {\r\n    return {\r\n       // 遮罩层\r\n      loading: true,\r\n      // 分页信息\r\n      total: 0,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      // 选中角色编号\r\n      roleIds:[],\r\n      // 角色信息\r\n      roles: [],\r\n      // 用户信息\r\n      form: {}\r\n    };\r\n  },\r\n  created() {\r\n    const userId = this.$route.params && this.$route.params.userId;\r\n    if (userId) {\r\n      this.loading = true;\r\n      getAuthRole(userId).then((response) => {\r\n        this.form = response.user;\r\n        this.roles = response.roles;\r\n        this.total = this.roles.length;\r\n        this.$nextTick(() => {\r\n          this.roles.forEach((row) => {\r\n            if (row.flag) {\r\n              this.$refs.table.toggleRowSelection(row);\r\n            }\r\n          });\r\n        });\r\n        this.loading = false;\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /** 单击选中行数据 */\r\n    clickRow(row) {\r\n      this.$refs.table.toggleRowSelection(row);\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.roleIds = selection.map((item) => item.roleId);\r\n    },\r\n    // 保存选中的数据编号\r\n    getRowKey(row) {\r\n      return row.roleId;\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const userId = this.form.userId;\r\n      const roleIds = this.roleIds.join(\",\");\r\n      updateAuthRole({ userId: userId, roleIds: roleIds }).then((response) => {\r\n        this.$modal.msgSuccess(\"授权成功\");\r\n        this.close();\r\n      });\r\n    },\r\n    /** 关闭按钮 */\r\n    close() {\r\n      const obj = { path: \"/system/user\" };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n  },\r\n};\r\n</script>"], "mappings": ";;;;;;;;;;;;;AAgDA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,QAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,MAAA;IACA,IAAAA,MAAA;MACA,KAAAT,OAAA;MACA,IAAAY,iBAAA,EAAAH,MAAA,EAAAI,IAAA,WAAAC,QAAA;QACAN,KAAA,CAAAF,IAAA,GAAAQ,QAAA,CAAAC,IAAA;QACAP,KAAA,CAAAH,KAAA,GAAAS,QAAA,CAAAT,KAAA;QACAG,KAAA,CAAAP,KAAA,GAAAO,KAAA,CAAAH,KAAA,CAAAW,MAAA;QACAR,KAAA,CAAAS,SAAA;UACAT,KAAA,CAAAH,KAAA,CAAAa,OAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAZ,KAAA,CAAAa,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAJ,GAAA;YACA;UACA;QACA;QACAX,KAAA,CAAAR,OAAA;MACA;IACA;EACA;EACAwB,OAAA;IACA,cACAC,QAAA,WAAAA,SAAAN,GAAA;MACA,KAAAE,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAJ,GAAA;IACA;IACA;IACAO,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAvB,OAAA,GAAAuB,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;IACA;IACA;IACAC,SAAA,WAAAA,UAAAZ,GAAA;MACA,OAAAA,GAAA,CAAAW,MAAA;IACA;IACA,WACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAxB,MAAA,QAAAH,IAAA,CAAAG,MAAA;MACA,IAAAL,OAAA,QAAAA,OAAA,CAAA8B,IAAA;MACA,IAAAC,oBAAA;QAAA1B,MAAA,EAAAA,MAAA;QAAAL,OAAA,EAAAA;MAAA,GAAAS,IAAA,WAAAC,QAAA;QACAmB,MAAA,CAAAG,MAAA,CAAAC,UAAA;QACAJ,MAAA,CAAAK,KAAA;MACA;IACA;IACA,WACAA,KAAA,WAAAA,MAAA;MACA,IAAAC,GAAA;QAAAC,IAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAH,GAAA;IACA;EACA;AACA", "ignoreList": []}]}