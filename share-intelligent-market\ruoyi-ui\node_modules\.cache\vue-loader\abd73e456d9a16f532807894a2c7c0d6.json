{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\user\\profile\\userInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\user\\profile\\userInfo.vue", "mtime": 1750151094305}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyB1cGRhdGVVc2VyUHJvZmlsZSB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIHVzZXI6IHsNCiAgICAgIHR5cGU6IE9iamVjdA0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBuaWNrTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLfmmLXnp7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBlbWFpbDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpgq7nrrHlnLDlnYDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAiZW1haWwiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOmCrueuseWcsOWdgCIsDQogICAgICAgICAgICB0cmlnZ2VyOiBbImJsdXIiLCAiY2hhbmdlIl0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIHBob25lbnVtYmVyOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJi+acuuWPt+eggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHN1Ym1pdCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHVwZGF0ZVVzZXJQcm9maWxlKHRoaXMudXNlcikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBjbG9zZSgpIHsNCiAgICAgIHRoaXMuJHRhYi5jbG9zZVBhZ2UoKTsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["userInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userInfo.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"user\" :rules=\"rules\" label-width=\"80px\">\r\n    <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n      <el-input v-model=\"user.nickName\" maxlength=\"30\" />\r\n    </el-form-item> \r\n    <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n      <el-input v-model=\"user.phonenumber\" maxlength=\"11\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"邮箱\" prop=\"email\">\r\n      <el-input v-model=\"user.email\" maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"性别\">\r\n      <el-radio-group v-model=\"user.sex\">\r\n        <el-radio label=\"0\">男</el-radio>\r\n        <el-radio label=\"1\">女</el-radio>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item>\r\n      <el-button type=\"primary\" size=\"mini\" @click=\"submit\">保存</el-button>\r\n      <el-button type=\"danger\" size=\"mini\" @click=\"close\">关闭</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { updateUserProfile } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  props: {\r\n    user: {\r\n      type: Object\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 表单校验\r\n      rules: {\r\n        nickName: [\r\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          { required: true, message: \"邮箱地址不能为空\", trigger: \"blur\" },\r\n          {\r\n            type: \"email\",\r\n            message: \"请输入正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phonenumber: [\r\n          { required: true, message: \"手机号码不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          updateUserProfile(this.user).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    close() {\r\n      this.$tab.closePage();\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}