{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\post.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\post.js", "mtime": 1750151093997}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRQb3N0ID0gYWRkUG9zdDsKZXhwb3J0cy5kZWxQb3N0ID0gZGVsUG9zdDsKZXhwb3J0cy5nZXRQb3N0ID0gZ2V0UG9zdDsKZXhwb3J0cy5saXN0UG9zdCA9IGxpc3RQb3N0OwpleHBvcnRzLnVwZGF0ZVBvc3QgPSB1cGRhdGVQb3N0Owp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i5Lq65omN5oub6IGY5YiX6KGoCmZ1bmN0aW9uIGxpc3RQb3N0KHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvdXVjL3Bvc3QvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LkurrmiY3mi5vogZjor6bnu4YKZnVuY3Rpb24gZ2V0UG9zdChwb3N0SWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy91dWMvcG9zdC8nICsgcG9zdElkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7kurrmiY3mi5vogZgKZnVuY3Rpb24gYWRkUG9zdChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvdXVjL3Bvc3QnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueS6uuaJjeaLm+iBmApmdW5jdGlvbiB1cGRhdGVQb3N0KGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy91dWMvcG9zdCcsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTkurrmiY3mi5vogZgKZnVuY3Rpb24gZGVsUG9zdChwb3N0SWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy91dWMvcG9zdC8nICsgcG9zdElkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPost", "query", "request", "url", "method", "params", "getPost", "postId", "addPost", "data", "updatePost", "delPost"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/uuc/post.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询人才招聘列表\r\nexport function listPost(query) {\r\n  return request({\r\n    url: '/uuc/post/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询人才招聘详细\r\nexport function getPost(postId) {\r\n  return request({\r\n    url: '/uuc/post/' + postId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增人才招聘\r\nexport function addPost(data) {\r\n  return request({\r\n    url: '/uuc/post',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改人才招聘\r\nexport function updatePost(data) {\r\n  return request({\r\n    url: '/uuc/post',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除人才招聘\r\nexport function delPost(postId) {\r\n  return request({\r\n    url: '/uuc/post/' + postId,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGI,MAAM;IAC1BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGI,MAAM;IAC1BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}