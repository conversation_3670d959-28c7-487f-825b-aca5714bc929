
40c5dfe40568046c8ec3be9a0ae384e91383c1d4	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Ftinymce-5\\u002Fcontent.min.css\",\"contentHash\":\"2236fef815e866a7f0f2b9f8477ea4ce\"}","integrity":"sha512-EKGt9xzBI8kAcd8NnuxYpH30R/0K0Oicto8ae6qm8KMvU5fHit5Iav/FEWJnfVa6+X/aJ4D9Q4goDNhHtUYxaQ==","time":1750496064278,"size":1687}