{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\service\\quick.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\service\\quick.vue", "mtime": 1750151094280}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3REYXRhLA0KICAgIHNldFN0YXR1cywNCiAgICBkZWxEYXRhLA0KICAgIGFkZERhdGEsDQogICAgZWRpdERhdGEsDQp9IGZyb20gIkAvYXBpL3NlcnZpY2UvcXVpY2siOw0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJJbmZvciIsDQogICAgZGF0YSgpIHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHBpY3R1cmU6ICIiLA0KICAgICAgICAgICAgbm9ybXNMaXN0OiBbXSwNCiAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgICB0aXRsZTogIiIsDQogICAgICAgICAgICBmb3JtOiB7DQogICAgICAgICAgICAgICAgc3RhdHVzOjENCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBydWxlczogew0KICAgICAgICAgICAgICAgIG5hbWU6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5ZCN56ewIiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIHN0YXR1czogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nnirbmgIEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgaWNvbjogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fkuIrkvKDlm77moIciLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICB9LA0KDQogICAgICAgICAgICAvLyDpga7nvanlsYINCiAgICAgICAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAgICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgICAgICAgIGlkczogW10sDQogICAgICAgICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgICAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAgICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgICAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAgICAgICAvLyDmgLvmnaHmlbANCiAgICAgICAgICAgIHRvdGFsOiAwLA0KICAgICAgICAgICAgLy8g5YWs5ZGK6KGo5qC85pWw5o2uDQogICAgICAgICAgICBpbmZvckxpc3Q6IFtdLA0KICAgICAgICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICAgICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICAgICAgICAgIHRpdGxlOiB1bmRlZmluZWQsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZm9ybToge30sDQogICAgICAgIH07DQogICAgfSwNCiAgICBjcmVhdGVkKCkgew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIG1ldGhvZHM6IHsNCiAgICAgICAgLy8g5L+u5pS554q25oCBDQogICAgICAgIHNldFN0YXR1cyhyb3csIHR5cGUpIHsNCiAgICAgICAgICAgIHNldFN0YXR1cyh7DQogICAgICAgICAgICAgICAgb3BpZDogcm93LmlkLA0KICAgICAgICAgICAgICAgIHN0YXR1czogdHlwZSwNCiAgICAgICAgICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UubXNnLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIHVwbG9hZFN1Y2Nlc3MoZXZlbnQsIGl0ZW0pIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5pY29uID0gZXZlbnQ7DQogICAgICAgIH0sDQoNCiAgICAgICAgLyoqIOafpeivouWFrOWRiuWIl+ihqCAqLw0KICAgICAgICBnZXRMaXN0KCkgew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgIC8vICAgIOafpeivouWPguaVsHNpemXmlLnmiJBsaW1pdA0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zaXplID0gdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZTsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMubGltaXQgPSB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW07DQoNCiAgICAgICAgICAgIGxpc3REYXRhKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5pbmZvckxpc3QgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS5jb3VudDsNCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgICAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICAgICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpOw0KICAgICAgICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7DQogICAgICAgICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlQWRkKCkgew0KICAgICAgICAgICAgdGhpcy5hZGQoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICAgICAgICBjb25zdCBpbmZvcklkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgICAgICAgdGhpcy5mb3JtID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyb3cpKTsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5zdGF0dXMgPSBOdW1iZXIodGhpcy5mb3JtLnN0YXR1cykNCg0KICAgICAgICAgICAgZGVsZXRlIHRoaXMuZm9ybS5zb3J0cw0KICAgICAgICAgICAgdGhpcy5mb3JtID0gT2JqZWN0LmFzc2lnbih7fSx0aGlzLmZvcm0pDQogICAgICAgICAgICB0aGlzLnRpdGxlID0gIue8lui+kSI7DQogICAgICAgICAgICB0aGlzLnNob3cgPSB0cnVlOw0KDQogICAgICAgICAgICAvLyBnZXREYXRhKGluZm9ySWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAvLyAgICAgdGhpcy5lZGl0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICAgICAgLy8gfSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgICAgICAgY29uc3QgaW5mb3JJZHMgPSByb3cuaWQgfHwgdGhpcy5pZHMuam9pbigiLCIpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk57yW5Y+35Li6IicgKyBpbmZvcklkcyArICci55qE5pWw5o2u6aG577yfJykNCiAgICAgICAgICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkZWxEYXRhKGluZm9ySWRzKTsNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICAgICAgfSwNCiAgICAgICAgaGFuZGxlQ29weShyb3cpIHsNCiAgICAgICAgICAgIGNvbnN0IGNsaXBib2FyZE9iaiA9IG5hdmlnYXRvci5jbGlwYm9hcmQ7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6ZO+5o6l5bey5aSN5Yi2IiwNCiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIGNsaXBib2FyZE9iai53cml0ZVRleHQoDQogICAgICAgICAgICAgICAgImh0dHBzOi8vc2MuY251ZGouY29tL2luZm9yP2lkPSIgKyByb3cuaWQNCiAgICAgICAgICAgICk7DQogICAgICAgIH0sDQogICAgICAgIHJlc2V0KCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICAgIGlkOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgdGl0bGU6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgICAgICBjb250ZW50OiB1bmRlZmluZWQsDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICAgICAgfSwNCiAgICAgICAgYWRkKCkgew0KICAgICAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqAiOw0KICAgICAgICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsNCiAgICAgICAgfSwNCiAgICAgICAgZWRpdChkYXRhKSB7DQogICAgICAgICAgICB0aGlzLnRpdGxlID0gIue8lui+kSI7DQogICAgICAgICAgICB0aGlzLnNob3cgPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy5mb3JtID0gZGF0YTsNCiAgICAgICAgfSwNCiAgICAgICAgaGFuZGxlU3VibWl0KCkgew0KICAgICAgICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCh2YWxpZGF0ZSkgPT4gew0KICAgICAgICAgICAgICAgIGlmICh2YWxpZGF0ZSkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5pZCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgYWRkRGF0YSh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgicmVmcmVzaCIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICBlZGl0RGF0YSh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgicmVmcmVzaCIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+35a6M5ZaE5L+h5oGv5YaN5o+Q5LqkISIpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgIH0sDQp9Ow0K"}, {"version": 3, "sources": ["quick.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "quick.vue", "sourceRoot": "src/views/service", "sourcesContent": ["// 快捷入口管理\r\n<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n        >\r\n            <el-form-item label=\"标题\" prop=\"name\">\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.name\"\r\n                    style=\"width: 300px\"\r\n                    placeholder=\"请输入标题\"\r\n                    :maxlength=\"60\"\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"inforList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                    <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"图标\"\r\n                align=\"center\"\r\n                width=\"400\"\r\n                prop=\"title\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-image\r\n                        style=\"width: 100px; height: 100px\"\r\n                        :src=\"scope.row.icon\"\r\n                        fit=\"fill\"\r\n                    ></el-image>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"名称\"\r\n                align=\"center\"\r\n                prop=\"name\"\r\n                width=\"100\"\r\n            />\r\n            <el-table-column\r\n                label=\"入口地址\"\r\n                align=\"center\"\r\n                prop=\"url\"\r\n                width=\"160\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-link\r\n                        :underline=\"false\"\r\n                        type=\"primary\"\r\n                        :href=\"scope.row.url\"\r\n                        target=\"_blank\"\r\n                        >{{ scope.row.url }}</el-link\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column\r\n                label=\"排序\"\r\n                align=\"center\"\r\n                prop=\"sorts\"\r\n                width=\"100\"\r\n            /> -->\r\n            <el-table-column\r\n                label=\"状态\"\r\n                align=\"center\"\r\n                prop=\"create_by\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <!-- 开启 -->\r\n                    <!-- <el-switch v-model=\"form.delivery\"></el-switch> -->\r\n                    <el-tag type=\"success\" v-if=\"scope.row.status == 1\"\r\n                        >启用</el-tag\r\n                    >\r\n                    <el-tag type=\"danger\" v-else>禁用</el-tag>\r\n                </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                fixed=\"right\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        style=\"color: #85ce61\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"setStatus(scope.row, 1)\"\r\n                        >启用</el-button\r\n                    >\r\n                    <el-button\r\n                        style=\"color: #ebb563\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"setStatus(scope.row, 0)\"\r\n                        >禁用</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n        <!-- 添加弹窗 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"show\"\r\n            width=\"70%\"\r\n            :before-close=\"() => (show = false)\"\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" :rules=\"rules\">\r\n                <el-form-item label=\"标题\" prop=\"name\">\r\n                    <el-input\r\n                        clearable\r\n                        v-model=\"form.name\"\r\n                        placeholder=\"请输入标题\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\" prop=\"status\">\r\n                    <el-switch\r\n                        v-model=\"form.status\"\r\n                        :active-value=\"1\"\r\n                        :inactive-value=\"0\"\r\n                    >\r\n                    </el-switch>\r\n                </el-form-item>\r\n                <el-form-item label=\"入口地址\">\r\n                    <el-input\r\n                        clearable\r\n                        v-model=\"form.url\"\r\n                        placeholder=\"请输入口地址\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <!-- <el-form-item label=\"排序\">\r\n                    <el-input\r\n                        type=\"number\"\r\n                        clearable\r\n                        v-model=\"form.sorts\"\r\n                        placeholder=\"请输入排序\"\r\n                    ></el-input>\r\n                </el-form-item> -->\r\n                <el-form-item label=\"图标\" prop=\"icon\">\r\n                    <ImageUpload\r\n                        @input=\"uploadSuccess($event, item)\"\r\n                        sizeTxt=\"1920X412\"\r\n                        style=\"width: 100%\"\r\n                        :value=\"form.icon\"\r\n                        :limit=\"1\"\r\n                    ></ImageUpload>\r\n                </el-form-item>\r\n            </el-form>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"show = false\">取 消</el-button>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    :loading=\"loading\"\r\n                    @click=\"handleSubmit\"\r\n                    >确 定</el-button\r\n                >\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listData,\r\n    setStatus,\r\n    delData,\r\n    addData,\r\n    editData,\r\n} from \"@/api/service/quick\";\r\nexport default {\r\n    name: \"Infor\",\r\n    data() {\r\n        return {\r\n            picture: \"\",\r\n            normsList: [],\r\n            loading: false,\r\n            show: false,\r\n            title: \"\",\r\n            form: {\r\n                status:1\r\n            },\r\n            rules: {\r\n                name: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请填写名称\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                status: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请选择状态\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                icon: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请上传图标\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 公告表格数据\r\n            inforList: [],\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                title: undefined,\r\n            },\r\n            form: {},\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        // 修改状态\r\n        setStatus(row, type) {\r\n            setStatus({\r\n                opid: row.id,\r\n                status: type,\r\n            }).then((response) => {\r\n                if (response.code == 200) {\r\n                    this.$message({\r\n                        message: response.msg,\r\n                        type: \"success\",\r\n                    });\r\n                    this.getList();\r\n                }\r\n            });\r\n        },\r\n        uploadSuccess(event, item) {\r\n            this.form.icon = event;\r\n        },\r\n\r\n        /** 查询公告列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            //    查询参数size改成limit\r\n            this.queryParams.size = this.queryParams.pageSize;\r\n            this.queryParams.limit = this.queryParams.pageNum;\r\n\r\n            listData(this.queryParams).then((response) => {\r\n                this.inforList = response.data;\r\n                this.total = response.count;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length != 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.add();\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            const inforId = row.id || this.ids;\r\n            this.form = JSON.parse(JSON.stringify(row));\r\n            this.form.status = Number(this.form.status)\r\n\r\n            delete this.form.sorts\r\n            this.form = Object.assign({},this.form)\r\n            this.title = \"编辑\";\r\n            this.show = true;\r\n\r\n            // getData(inforId).then((response) => {\r\n            //     this.edit(response.data);\r\n            // });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const inforIds = row.id || this.ids.join(\",\");\r\n            this.$modal\r\n                .confirm('是否确认删除编号为\"' + inforIds + '\"的数据项？')\r\n                .then(function () {\r\n                    return delData(inforIds);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        handleCopy(row) {\r\n            const clipboardObj = navigator.clipboard;\r\n            this.$message({\r\n                message: \"链接已复制\",\r\n                type: \"success\",\r\n            });\r\n            clipboardObj.writeText(\r\n                \"https://sc.cnudj.com/infor?id=\" + row.id\r\n            );\r\n        },\r\n        reset() {\r\n            this.form = {\r\n                id: undefined,\r\n                title: undefined,\r\n                content: undefined,\r\n            };\r\n            this.resetForm(\"form\");\r\n        },\r\n        add() {\r\n            this.reset();\r\n            this.title = \"添加\";\r\n            this.show = true;\r\n        },\r\n        edit(data) {\r\n            this.title = \"编辑\";\r\n            this.show = true;\r\n            this.form = data;\r\n        },\r\n        handleSubmit() {\r\n            this.$refs.form.validate((validate) => {\r\n                if (validate) {\r\n                    this.loading = true;\r\n                    if (!this.form.id) {\r\n                        addData(this.form).then((response) => {\r\n                            this.$message({\r\n                                type: \"success\",\r\n                                message: \"操作成功!\",\r\n                            });\r\n                            this.loading = false;\r\n                            this.show = false;\r\n                            this.getList();\r\n\r\n                            this.$emit(\"refresh\");\r\n                        });\r\n                    } else {\r\n                        editData(this.form).then((response) => {\r\n                            this.$message({\r\n                                type: \"success\",\r\n                                message: \"操作成功!\",\r\n                            });\r\n                            this.loading = false;\r\n                            this.show = false;\r\n                            this.getList();\r\n\r\n                            this.$emit(\"refresh\");\r\n                        });\r\n                    }\r\n                } else {\r\n                    this.$modal.msgError(\"请完善信息再提交!\");\r\n                }\r\n            });\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}