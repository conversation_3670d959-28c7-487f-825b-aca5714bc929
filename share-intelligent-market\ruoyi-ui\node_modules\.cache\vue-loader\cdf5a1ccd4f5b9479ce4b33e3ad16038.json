{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\library.vue?vue&type=style&index=0&id=371d393a&scoped=true&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\library.vue", "mtime": 1750151094283}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLml0ZW0tZm9ybSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCn0NCi53aXJlIHsNCiAgICBiYWNrZ3JvdW5kOiByZ2IoMjE5LCAyMTksIDIxOSk7DQogICAgaGVpZ2h0OiAxcHg7DQogICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCi5mb3JtMS1ib3ggew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQp9DQouZm9ybTEtc2VhcmNoIHsNCiAgICBwYWRkaW5nOiAxMHB4IDA7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtZXZlbmx5Ow0KfQ0K"}, {"version": 3, "sources": ["library.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "library.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row>\r\n            <!--用户数据-->\r\n            <el-col :span=\"24\" :xs=\"24\">\r\n                <el-form\r\n                    :model=\"queryParams\"\r\n                    ref=\"queryForm\"\r\n                    :inline=\"true\"\r\n                    label-width=\"68px\"\r\n                >\r\n                    <el-form-item label=\"\">\r\n                        <el-select\r\n                            v-model=\"queryParams.product_id\"\r\n                            placeholder=\"请选择商品\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in shopList\"\r\n                                :key=\"item.value\"\r\n                                :label=\"item.label\"\r\n                                :value=\"item.value\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"\">\r\n                        <el-select\r\n                            v-model=\"queryParams.type\"\r\n                            placeholder=\"请选择出库/入库\"\r\n                        >\r\n                            <el-option\r\n                                v-for=\"item in options\"\r\n                                :key=\"item.value\"\r\n                                :label=\"item.label\"\r\n                                :value=\"item.value\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item>\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            icon=\"el-icon-search\"\r\n                            size=\"mini\"\r\n                            @click=\"getList\"\r\n                            >搜索</el-button\r\n                        >\r\n                        <el-button\r\n                            icon=\"el-icon-refresh\"\r\n                            size=\"mini\"\r\n                            @click=\"resetQuery\"\r\n                            >重置</el-button\r\n                        >\r\n                    </el-form-item>\r\n                </el-form>\r\n\r\n                <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n                    <right-toolbar\r\n                        :showSearch.sync=\"showSearch\"\r\n                        @queryTable=\"getList\"\r\n                    ></right-toolbar>\r\n                </el-row> -->\r\n\r\n                <el-table v-loading=\"loading\" border :data=\"list\">\r\n                    <el-table-column label=\"序号\" align=\"center\" width=\"50px\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.$index + 1 }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                        label=\"商品名称\"\r\n                        align=\"center\"\r\n                        prop=\"product_name\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"数量\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"num\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"操作类型\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"inquiry_no\"\r\n                    >\r\n                    <template slot-scope=\"scope\">\r\n                        <div v-if=\"scope.row.type == 0\">入库</div>\r\n                        <div v-if=\"scope.row.type == 1\">出库</div>\r\n                    </template>\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                        label=\"操作人\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"create_by\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"收方名称/入方名称\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"debit\"\r\n                    />\r\n                    <el-table-column\r\n                        label=\"操作时间\"\r\n                        align=\"center\"\r\n                        width=\"200\"\r\n                        prop=\"create_time\"\r\n                    />\r\n                </el-table>\r\n\r\n                <pagination\r\n                    v-show=\"total > 0\"\r\n                    :total=\"total\"\r\n                    :page.sync=\"queryParams.page\"\r\n                    :limit.sync=\"queryParams.limit\"\r\n                    @pagination=\"getList\"\r\n                />\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { listData, stockRecord } from \"@/api/store/product\";\r\nexport default {\r\n    layout: \"supply\",\r\n\r\n    data() {\r\n        return {\r\n            // 详情\r\n            open: false,\r\n            // 总条数\r\n            total: 0,\r\n            // 表单参数\r\n            form: {},\r\n            // 查询参数\r\n            queryParams: {\r\n                page: 1,\r\n                limit: 10,\r\n                type: undefined,\r\n                product_id: undefined,\r\n            },\r\n            // 搜索商品列表\r\n            shopList: [],\r\n            // 表格数据\r\n            list: [],\r\n            // 取消弹窗显示\r\n            dialogVisible: false,\r\n            // 弹窗标题\r\n            title: \"\",\r\n            loading: false,\r\n            //0入库 1出库\r\n            options: [\r\n                {\r\n                    label: '入库',\r\n                    value: 0,\r\n                },\r\n                {\r\n                    label: '出库',\r\n                    value: 1,\r\n                },\r\n            ],\r\n        };\r\n    },\r\n    created() {\r\n        this.getList()\r\n        this.remoteMethod()\r\n    },\r\n    methods: {\r\n        remoteMethod(query) {\r\n            this.loading = true;\r\n            listData({\r\n                pageNum: 1,\r\n                pageSize: 10000,\r\n                classify2_id: -1,\r\n                classify3_id: -1,\r\n            }).then((res) => {\r\n                console.log(res);\r\n                res.data.map(item=>{\r\n                    this.shopList.push({\r\n                        value:item.id,\r\n                        label:item.name\r\n                    })\r\n                })\r\n                this.loading = false;\r\n            });\r\n        },\r\n        getList() {\r\n            stockRecord(this.queryParams).then(res=>{\r\n                console.log(res)\r\n                this.list = res.data\r\n                this.total = res.count\r\n            })\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            (this.queryParams = {\r\n                page: 1,\r\n                limit: 10,\r\n                type: undefined,\r\n                product_id: undefined,\r\n                enterprise_id: null,\r\n            }),\r\n                (this.list = []);\r\n            this.getList();\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.item-form {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n}\r\n.wire {\r\n    background: rgb(219, 219, 219);\r\n    height: 1px;\r\n    margin-bottom: 20px;\r\n}\r\n.form1-box {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n.form1-search {\r\n    padding: 10px 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-evenly;\r\n}\r\n</style>\r\n"]}]}