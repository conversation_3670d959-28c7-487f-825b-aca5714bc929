{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\partner\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\partner\\index.vue", "mtime": 1750151094258}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_partner", "require", "name", "dicts", "data", "checkPhone", "rule", "value", "callback", "reg", "test", "Error", "loading", "ids", "single", "multiple", "showSearch", "total", "partnerList", "title", "open", "queryParams", "pageNum", "pageSize", "fieldName", "contact", "phone", "form", "rules", "required", "message", "trigger", "type", "validator", "created", "getList", "methods", "_this", "list<PERSON><PERSON>ner", "then", "response", "rows", "cancel", "reset", "id", "fieldCode", "thoughts", "btypeCode", "btypeName", "remark", "createBy", "createTime", "updateBy", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "<PERSON><PERSON><PERSON><PERSON>", "submitForm", "_this3", "$refs", "validate", "valid", "update<PERSON><PERSON><PERSON>", "$modal", "msgSuccess", "<PERSON><PERSON><PERSON><PERSON>", "handleDelete", "_this4", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/ningmengdou/partner/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"领域名称\" prop=\"fieldName\">\r\n                <el-select\r\n                    v-model=\"queryParams.fieldName\"\r\n                    placeholder=\"请选择领域名称\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_collaborative_areas\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"contact\">\r\n                <el-input\r\n                    v-model=\"queryParams.contact\"\r\n                    placeholder=\"请输入联系人\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                <el-input\r\n                    v-model=\"queryParams.phone\"\r\n                    placeholder=\"请输入联系电话\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:partner:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:partner:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:partner:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['uuc:partner:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"partnerList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n            <el-table-column label=\"领域名称\" align=\"center\" prop=\"fieldName\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_collaborative_areas\"\r\n                        :value=\"scope.row.fieldName\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"合作思路\" align=\"center\" prop=\"thoughts\" />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"contact\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"phone\" />\r\n            <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:partner:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:partner:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改合作伙伴对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n                <el-form-item label=\"领域名称\" prop=\"fieldName\">\r\n                    <el-select\r\n                        v-model=\"form.fieldName\"\r\n                        placeholder=\"请选择领域名称\"\r\n                    >\r\n                        <el-option\r\n                            v-for=\"dict in dict.type.uuc_collaborative_areas\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.label\"\r\n                            :value=\"dict.value\"\r\n                        ></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合作思路\" prop=\"thoughts\">\r\n                    <el-input\r\n                        v-model=\"form.thoughts\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"255\"\r\n                        placeholder=\"请输入合作思路\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" prop=\"contact\">\r\n                    <el-input\r\n                        v-model=\"form.contact\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入联系人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                    <el-input\r\n                        v-model=\"form.phone\"\r\n                        maxlength=\"20\"\r\n                        type=\"number\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"500\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listPartner,\r\n    getPartner,\r\n    delPartner,\r\n    addPartner,\r\n    updatePartner,\r\n} from \"@/api/uuc/partner\";\r\n\r\nexport default {\r\n    name: \"Partner\",\r\n    dicts: [\"uuc_collaborative_areas\"],\r\n    data() {\r\n        let checkPhone = (rule, value, callback) => {\r\n            let reg = /^1[345789]\\d{9}$/;\r\n            if (!reg.test(value)) {\r\n                callback(new Error(\"请输入11位手机号\"));\r\n            } else {\r\n                callback();\r\n            }\r\n        };\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 合作伙伴表格数据\r\n            partnerList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                fieldName: null,\r\n                contact: null,\r\n                phone: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            // 表单校验\r\n            rules: {\r\n                // fieldName: [\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"领域名称不能为空\",\r\n                //         trigger: \"change\",\r\n                //     },\r\n                // ],\r\n                // thoughts: [\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"合作思路不能为空\",\r\n                //         trigger: \"blur\",\r\n                //     },\r\n                // ],\r\n                contact: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                phone: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系电话不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    {\r\n                        type: \"number\",\r\n                        validator: checkPhone,\r\n                        message: \"请输入正确的手机号\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        /** 查询合作伙伴列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listPartner(this.queryParams).then((response) => {\r\n                this.partnerList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                fieldCode: null,\r\n                fieldName: null,\r\n                thoughts: null,\r\n                btypeCode: null,\r\n                btypeName: null,\r\n                contact: null,\r\n                phone: null,\r\n                remark: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加合作伙伴\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getPartner(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改合作伙伴\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.id != null) {\r\n                        updatePartner(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addPartner(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除合作伙伴编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delPartner(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/partner/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `partner_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAgOA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAQA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,UAAA,YAAAA,WAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,GAAA;MACA,KAAAA,GAAA,CAAAC,IAAA,CAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACA;MACA;MACAI,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAH,OAAA,GACA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAL,KAAA,GACA;UACAG,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,GACA;UACAC,IAAA;UACAC,SAAA,EAAA5B,UAAA;UACAyB,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAzB,OAAA;MACA,IAAA0B,oBAAA,OAAAjB,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAnB,WAAA,GAAAsB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAApB,KAAA,GAAAuB,QAAA,CAAAvB,KAAA;QACAoB,KAAA,CAAAzB,OAAA;MACA;IACA;IACA;IACA8B,MAAA,WAAAA,OAAA;MACA,KAAAtB,IAAA;MACA,KAAAuB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAhB,IAAA;QACAiB,EAAA;QACAC,SAAA;QACArB,SAAA;QACAsB,QAAA;QACAC,SAAA;QACAC,SAAA;QACAvB,OAAA;QACAC,KAAA;QACAuB,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlC,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAqB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7C,GAAA,GAAA6C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhB,EAAA;MAAA;MACA,KAAA9B,MAAA,GAAA4C,SAAA,CAAAG,MAAA;MACA,KAAA9C,QAAA,IAAA2C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAnB,KAAA;MACA,KAAAvB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA4C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAtB,KAAA;MACA,IAAAC,EAAA,GAAAoB,GAAA,CAAApB,EAAA,SAAA/B,GAAA;MACA,IAAAqD,mBAAA,EAAAtB,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAAtC,IAAA,GAAAa,QAAA,CAAApC,IAAA;QACA6D,MAAA,CAAA7C,IAAA;QACA6C,MAAA,CAAA9C,KAAA;MACA;IACA;IACA,WACAgD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAzC,IAAA,CAAAiB,EAAA;YACA,IAAA4B,sBAAA,EAAAJ,MAAA,CAAAzC,IAAA,EAAAY,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAhD,IAAA;cACAgD,MAAA,CAAAjC,OAAA;YACA;UACA;YACA,IAAAwC,mBAAA,EAAAP,MAAA,CAAAzC,IAAA,EAAAY,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAhD,IAAA;cACAgD,MAAA,CAAAjC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAyC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAhE,GAAA,GAAAmD,GAAA,CAAApB,EAAA,SAAA/B,GAAA;MACA,KAAA4D,MAAA,CACAK,OAAA,oBAAAjE,GAAA,aACA0B,IAAA;QACA,WAAAwC,mBAAA,EAAAlE,GAAA;MACA,GACA0B,IAAA;QACAsC,MAAA,CAAA1C,OAAA;QACA0C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,0BAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA/D,WAAA,cAAAgE,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}