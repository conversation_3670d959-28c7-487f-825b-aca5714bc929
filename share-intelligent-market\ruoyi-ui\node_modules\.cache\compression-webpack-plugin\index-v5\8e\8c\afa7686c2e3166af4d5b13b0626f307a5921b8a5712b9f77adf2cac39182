
6f26e55cddeaf788a6b7cf0bbf36b35e6f0e8f69	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Fdark\\u002Fcontent.css\",\"contentHash\":\"0f376b041bb04830308bc51ed44863be\"}","integrity":"sha512-K6kXZr64aRtjXPdEq4cZfRHpBi7FQCSVj0/CFPCO1tiB5Bal/dT77N6Bry4KJTigzsFXiMNMb5xNJ9ntbQaZKw==","time":1750496064272,"size":2222}