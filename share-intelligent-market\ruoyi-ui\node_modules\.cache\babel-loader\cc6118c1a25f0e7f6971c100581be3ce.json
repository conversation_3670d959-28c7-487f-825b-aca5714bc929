{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\product_joint.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\product_joint.js", "mtime": 1750151093999}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRQcm9kdWN0X2pvaW50ID0gYWRkUHJvZHVjdF9qb2ludDsKZXhwb3J0cy5kZWxQcm9kdWN0X2pvaW50ID0gZGVsUHJvZHVjdF9qb2ludDsKZXhwb3J0cy5nZXRQcm9kdWN0X2pvaW50ID0gZ2V0UHJvZHVjdF9qb2ludDsKZXhwb3J0cy5saXN0UHJvZHVjdF9qb2ludCA9IGxpc3RQcm9kdWN0X2pvaW50OwpleHBvcnRzLnVwZGF0ZVByb2R1Y3Rfam9pbnQgPSB1cGRhdGVQcm9kdWN0X2pvaW50Owp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i5Lqn5ZOB5ZCI5L2c5YiX6KGoCmZ1bmN0aW9uIGxpc3RQcm9kdWN0X2pvaW50KHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvdXVjL3Byb2R1Y3Rfam9pbnQvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lkuqflk4HlkIjkvZzor6bnu4YKZnVuY3Rpb24gZ2V0UHJvZHVjdF9qb2ludChpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9wcm9kdWN0X2pvaW50LycgKyBpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5Lqn5ZOB5ZCI5L2cCmZ1bmN0aW9uIGFkZFByb2R1Y3Rfam9pbnQoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9wcm9kdWN0X2pvaW50JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnkuqflk4HlkIjkvZwKZnVuY3Rpb24gdXBkYXRlUHJvZHVjdF9qb2ludChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvdXVjL3Byb2R1Y3Rfam9pbnQnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5Lqn5ZOB5ZCI5L2cCmZ1bmN0aW9uIGRlbFByb2R1Y3Rfam9pbnQoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy91dWMvcHJvZHVjdF9qb2ludC8nICsgaWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listProduct_joint", "query", "request", "url", "method", "params", "getProduct_joint", "id", "addProduct_joint", "data", "updateProduct_joint", "delProduct_joint"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/uuc/product_joint.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询产品合作列表\r\nexport function listProduct_joint(query) {\r\n  return request({\r\n    url: '/uuc/product_joint/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询产品合作详细\r\nexport function getProduct_joint(id) {\r\n  return request({\r\n    url: '/uuc/product_joint/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增产品合作\r\nexport function addProduct_joint(data) {\r\n  return request({\r\n    url: '/uuc/product_joint',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改产品合作\r\nexport function updateProduct_joint(data) {\r\n  return request({\r\n    url: '/uuc/product_joint',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除产品合作\r\nexport function delProduct_joint(id) {\r\n  return request({\r\n    url: '/uuc/product_joint/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACC,EAAE,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,EAAE;IAC/BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,gBAAgBA,CAACC,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,mBAAmBA,CAACD,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,gBAAgBA,CAACJ,EAAE,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,EAAE;IAC/BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}