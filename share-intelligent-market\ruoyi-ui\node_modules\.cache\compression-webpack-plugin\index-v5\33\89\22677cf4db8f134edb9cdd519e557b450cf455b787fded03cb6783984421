
9194ad6eb67058768bfdd22cde9e584382bc2c9c	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"d2ae34ea1b967f7418cd53b13902edf5\"}","integrity":"sha512-fZ2/NjzWzEOKRfGVweHNFGd4wfHSzLaH8d1KhISInTjI31ZhIhLeIZ6thCBfWH6Qtk4I6evR5ueizyDB/mbBCQ==","time":1750496064720,"size":4847909}