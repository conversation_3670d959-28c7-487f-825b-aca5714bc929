{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\apply.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\apply.vue", "mtime": 1750151094285}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["apply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "apply.vue", "sourceRoot": "src/views/supply", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\" prop='name'>\r\n            <el-input clearable v-model=\"queryParams.name\" placeholder=\"输入公司名称\" :maxlength=\"50\" size='small'\r\n              style=\"width: 300px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='business_no'>\r\n            <el-input clearable v-model=\"queryParams.business_no\" placeholder=\"输入营业执照\" :maxlength=\"18\" size='small'\r\n              style=\"width: 180px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='linker'>\r\n            <el-input clearable v-model=\"queryParams.linker\" placeholder=\"输入联系人\" :maxlength=\"20\" size='small'\r\n              style=\"width: 140px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='linkphone'>\r\n            <el-input clearable v-model=\"queryParams.linkphone\" placeholder=\"输入联系电话\" :maxlength=\"11\" size='small'\r\n              style=\"width: 140px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"审核状态\" clearable size=\"small\" style=\"width: 120px\">\r\n              <el-option v-for=\"item in statusOptions\" :key=\"item.key\" :label='item.value' :value=\"item.key\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleInvite\">邀请入驻</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"50\" />\r\n          <el-table-column label=\"企业名称\" align=\"center\" prop=\"name\" width=\"280\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"营业执照号\" align=\"center\" prop=\"business_no\" width=\"180\" />\r\n          <el-table-column label=\"公司授权文件\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.certification\"\r\n                @click=\"handlePreview(scope.row.certification)\" :preview-src-list=\"srcList\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"营业执照\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.business_image\"\r\n                @click=\"handlePreview(scope.row.business_image)\" :preview-src-list=\"srcList\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"品牌\" align=\"center\" prop=\"brand\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"联系人\" align=\"center\" prop=\"linker\" width=\"120\" />\r\n          <el-table-column label=\"联系电话\" align=\"center\" prop=\"linkphone\" width=\"140\" />\r\n          <el-table-column label=\"职务\" align=\"center\" prop=\"post\" width=\"120\" />\r\n          <el-table-column label=\"邮箱\" align=\"center\" prop=\"email\" width=\"120\" />\r\n          <el-table-column label=\"公司地址\" align=\"center\" prop=\"location\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"申请者\" align=\"center\" prop=\"create_by\" width=\"100\" />\r\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n          <el-table-column label=\"操作\" align=\"center\" width=\"80\" fixed='right'>\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" v-if=\"scope.row.status=='NEW'\" icon=\"el-icon-user\" size=\"mini\"\r\n                @click=\"handleDetail(scope.row)\">审核\r\n              </el-button>\r\n              <el-button type=\"text\" v-if=\"scope.row.status!='NEW'\" icon=\"el-icon-view\" size=\"mini\"\r\n                @click=\"handleDetail(scope.row)\">详情\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <enterprise-detail ref='detail' :form=\"form\" @refresh='getList'></enterprise-detail>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import enterpriseDetail from './components/enterprise-detail';\r\n  import {\r\n    listEnum\r\n  } from '@/api/tool/util';\r\n  // 新增弹窗\r\n  import {\r\n    listData,\r\n    getData\r\n  } from \"@/api/enterprise/apply\";\r\n  export default {\r\n    components: {\r\n      enterpriseDetail\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        statusOptions: [],\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          business_no: undefined,\r\n          linker: undefined,\r\n          linkphone: undefined,\r\n          status: undefined,\r\n        },\r\n        // 列表数据\r\n        list: [],\r\n        // 图片预览地址\r\n        srcList: [],\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n      this.getEnums();\r\n    },\r\n    methods: {\r\n      getEnums() {\r\n        listEnum().then(res => {\r\n          this.statusOptions = res.data.enterpriseApplyStatus;\r\n        })\r\n      },\r\n      /** 查询列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listData(this.queryParams).then(response => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 表单搜索 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      handlePreview(url) {\r\n        this.srcList = [url];\r\n      },\r\n      handleInvite() {\r\n        const clipboardObj = navigator.clipboard;\r\n        this.$message({\r\n          message: '链接已复制，快去找朋友分享吧~',\r\n          type: 'success'\r\n        })\r\n        clipboardObj.writeText('https://sc.cnudj.com/login');\r\n      },\r\n      // 重置\r\n      resetQuery() {\r\n        this.queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          business_no: undefined,\r\n          linker: undefined,\r\n          linkphone: undefined,\r\n          status: undefined,\r\n        };\r\n        this.reserForm('queryForm')\r\n      },\r\n      /** 修改按钮操作 */\r\n      handleDetail(row) {\r\n        getData(row.id).then(response => {\r\n          this.form = response.data;\r\n          this.$refs.detail.open()\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n"]}]}