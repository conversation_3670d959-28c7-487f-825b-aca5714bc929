{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\member\\components\\setLabel.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\member\\components\\setLabel.vue", "mtime": 1750151094241}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["setLabel.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "setLabel.vue", "sourceRoot": "src/views/member/components", "sourcesContent": ["<template>\r\n    <div>\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"show\"\r\n            width=\"70%\"\r\n            :before-close=\"() => (show = false)\"\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" :rules=\"rules\">\r\n                <el-form-item   label=\"标签\" prop=\"value\">\r\n                    <el-select\r\n                        multiple\r\n                        clearable\r\n                        v-model=\"form.value\"\r\n                        placeholder=\"请选择标签\"\r\n                    >\r\n                        <el-option\r\n                            v-for=\"item in optionsGradeType\"\r\n                            :key=\"item.id\"\r\n                            :label=\"item.name\"\r\n                            :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n            </el-form>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"show = false\">取 消</el-button>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    :loading=\"loading\"\r\n                    @click=\"handleSubmit\"\r\n                    >确 定</el-button\r\n                >\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { labelList ,setLable,getData  } from \"@/api/member/list\";\r\nexport default {\r\n    data() {\r\n        return {\r\n            form1:{},\r\n            show: false,\r\n            value: \"\",\r\n            loading: false,\r\n            optionsGradeType: [],\r\n            title: \"\",\r\n            form: {},\r\n            rules: {\r\n                value: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请选择标签\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    methods: {\r\n        labelList() {\r\n            labelList({\r\n                pageNum: 1,\r\n                pageSize: 100,\r\n                // name: this.form.enterprise_name,\r\n                status: 1,\r\n            })\r\n                .then((res) => {\r\n                    this.optionsGradeType = res.data;\r\n                })\r\n                .catch((err) => {});\r\n        },\r\n        async open(title, data) {\r\n            this.title = title || \"设置\";\r\n            var inforId =  await this.getData(data.id);\r\n            let ids =  inforId.labels.map(item=>{\r\n              return item.id\r\n            })\r\n            data.value = ids\r\n            this.form = JSON.parse(JSON.stringify(data)) || {}\r\n            this.show = true;\r\n            setTimeout(() => {\r\n                this.$refs.form.clearValidate();\r\n            }, 100);\r\n            this.labelList();\r\n        },\r\n        getData(inforId){\r\n         return getData(inforId).then((response) => {\r\n              return response.data\r\n          });\r\n        },\r\n        handleSubmit() {\r\n            this.$refs.form.validate((validate) => {\r\n                if (validate) {\r\n                    this.loading = true;\r\n\r\n                    setLable({\r\n                        id:this.form.id,\r\n                        grade_id:this.form.value  //标签id\r\n                    }).then((response) => {\r\n                        this.$message({\r\n                            type: \"success\",\r\n                            message: \"操作成功!\",\r\n                        });\r\n                        this.loading = false;\r\n                        this.show = false;\r\n                        this.$emit(\"refresh\");\r\n                    });\r\n                } else {\r\n                    this.$modal.msgError(\"请完善信息再提交!\");\r\n                }\r\n            });\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}