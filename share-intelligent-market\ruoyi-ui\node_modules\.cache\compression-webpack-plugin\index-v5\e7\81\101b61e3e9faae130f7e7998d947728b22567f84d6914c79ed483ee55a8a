
e26b7275d41038ac34314edb892bbde82db2db4e	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Flangs\\u002Fzh-Hans.js\",\"contentHash\":\"8712961fdee04a81e058260b35e12878\"}","integrity":"sha512-+cKxQkgtuSytHR0m48hcWJuNKFFpNGLnzKKZwp0eY7fbmfw2FO2aziXUTe3oUWgfwIF5kxLhUz9NBiekiXyZGw==","time":1750496064273,"size":22494}