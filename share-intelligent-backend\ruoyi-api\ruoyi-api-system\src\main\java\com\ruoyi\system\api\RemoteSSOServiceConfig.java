package com.ruoyi.system.api;

import feign.Logger;
import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SSO远程服务配置
 * 专门为SSO服务优化的Feign配置
 *
 * <AUTHOR>
 */
@Configuration
public class RemoteSSOServiceConfig {

    /**
     * SSO服务专用超时配置
     * 针对SSO服务的特点进行优化
     */
    @Bean
    public Request.Options ssoFeignOptions() {
        return new Request.Options(
            3000,  // 连接超时：3秒（SSO服务通常响应较快）
            15000, // 读取超时：15秒（给数据库操作足够时间）
            true   // 跟随重定向
        );
    }

    /**
     * SSO服务日志级别
     * 便于调试SSO调用问题
     */
    @Bean
    public Logger.Level ssoLoggerLevel() {
        // 开发环境使用FULL，生产环境使用BASIC
        return Logger.Level.BASIC;
    }
}
