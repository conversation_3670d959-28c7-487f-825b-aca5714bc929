{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\App.vue", "mtime": 1750151093947}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCAgew0KICBuYW1lOiAgJ0FwcCcsDQogICAgbWV0YUluZm8oKSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgICB0aXRsZTogdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MuZHluYW1pY1RpdGxlICYmIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRpdGxlLA0KICAgICAgICAgICAgdGl0bGVUZW1wbGF0ZTogdGl0bGUgPT4gew0KICAgICAgICAgICAgICAgIHJldHVybiB0aXRsZSA/IGAke3RpdGxlfSAtICR7cHJvY2Vzcy5lbnYuVlVFX0FQUF9USVRMRX1gIDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9USVRMRQ0KICAgICAgICAgICAgfQ0KICAgICAgICB9DQogICAgfQ0KfQ0K"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default  {\r\n  name:  'App',\r\n    metaInfo() {\r\n        return {\r\n            title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,\r\n            titleTemplate: title => {\r\n                return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\r\n            }\r\n        }\r\n    }\r\n}\r\n</script>\r\n"]}]}