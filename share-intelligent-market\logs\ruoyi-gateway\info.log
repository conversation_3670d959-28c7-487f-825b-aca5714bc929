11:48:41.502 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:48:42.948 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0
11:48:43.049 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 57 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:43.101 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:43.115 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:43.320 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 202 ms to scan 234 urls, producing 0 keys and 0 values 
11:48:43.331 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:43.348 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:43.360 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:43.568 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 205 ms to scan 234 urls, producing 0 keys and 0 values 
11:48:43.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:43.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428527783
11:48:43.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/423109432
11:48:43.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:43.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:43.589 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:45.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477725472_127.0.0.1_64104
11:48:45.816 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] Notify connected event to listeners.
11:48:45.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:45.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf94e5c9-d66c-46f9-9b3c-0062bdadcdc8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
11:48:45.977 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:48:54.297 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:48:54.836 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4f427df7-d581-4c1b-9e92-4c11c2f75418
11:48:54.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] RpcClient init label, labels = {module=naming, source=sdk}
11:48:54.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:48:54.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:48:54.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:48:54.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:54.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477734857_127.0.0.1_64260
11:48:54.965 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Notify connected event to listeners.
11:48:54.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:54.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
11:48:55.490 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:48:56.338 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0
11:48:56.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428527783
11:48:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/423109432
11:48:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:56.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:56.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477736347_127.0.0.1_64267
11:48:56.460 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] Notify connected event to listeners.
11:48:56.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:56.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a0acf2-4869-47c6-973e-e4fe978ab21d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1829287142
11:48:57.270 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 49
11:48:57.273 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 49
11:48:57.785 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
11:48:57.832 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 17.459 seconds (JVM running for 19.273)
11:48:57.838 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
11:48:57.840 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
11:48:57.840 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
11:48:58.304 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 50
11:48:58.312 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 50
11:49:27.630 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 56
11:49:27.631 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 56
11:49:27.632 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 55
11:49:27.632 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 55
11:49:27.636 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 58
11:49:27.638 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 58
11:49:27.639 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 57
11:49:27.640 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 57
11:50:57.611 [nacos-grpc-client-executor-82] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 60
11:50:57.613 [nacos-grpc-client-executor-82] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 60
13:15:19.433 [nacos-grpc-client-executor-1861] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 65
13:15:19.434 [nacos-grpc-client-executor-1861] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 65
13:15:36.594 [nacos-grpc-client-executor-1872] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 66
13:15:36.597 [nacos-grpc-client-executor-1872] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 66
14:10:01.451 [nacos-grpc-client-executor-3048] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Receive server push request, request = NotifySubscriberRequest, requestId = 86
14:10:01.452 [nacos-grpc-client-executor-3048] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f427df7-d581-4c1b-9e92-4c11c2f75418] Ack server push request, request = NotifySubscriberRequest, requestId = 86
14:10:02.716 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:10:02.718 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:10:03.057 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:10:03.058 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6b11e795[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:10:03.058 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750477734857_127.0.0.1_64260
14:10:03.062 [nacos-grpc-client-executor-3051] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750477734857_127.0.0.1_64260]Ignore complete event,isRunning:false,isAbandon=false
14:10:03.082 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40373068[Running, pool size = 9, active threads = 0, queued tasks = 0, completed tasks = 3052]
14:10:09.505 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
14:10:10.414 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0
14:10:10.485 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
14:10:10.520 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:10:10.536 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
14:10:10.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 157 ms to scan 234 urls, producing 0 keys and 0 values 
14:10:10.706 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:10:10.720 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
14:10:10.730 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:10:10.892 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 160 ms to scan 234 urls, producing 0 keys and 0 values 
14:10:10.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:10.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1329492804
14:10:10.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/2013853240
14:10:10.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:10.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:10.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:10:12.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750486212184_127.0.0.1_62578
14:10:12.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Notify connected event to listeners.
14:10:12.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:12.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1444500451
14:10:12.563 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
14:10:18.556 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:10:18.993 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 48f064de-ed62-48bf-bf37-d6adef4f7f9c
14:10:18.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] RpcClient init label, labels = {module=naming, source=sdk}
14:10:18.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:18.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:18.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:18.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:10:19.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750486219003_127.0.0.1_62627
14:10:19.119 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Notify connected event to listeners.
14:10:19.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:19.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1444500451
14:10:19.529 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:10:20.326 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b215111c-26eb-48d7-ad67-4a70e89e106f_config-0
14:10:20.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:20.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1329492804
14:10:20.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/2013853240
14:10:20.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:20.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:20.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:10:20.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750486220333_127.0.0.1_62638
14:10:20.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:20.444 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] Notify connected event to listeners.
14:10:20.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b215111c-26eb-48d7-ad67-4a70e89e106f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1444500451
14:10:21.286 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Receive server push request, request = NotifySubscriberRequest, requestId = 88
14:10:21.299 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Ack server push request, request = NotifySubscriberRequest, requestId = 88
14:10:21.466 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Receive server push request, request = NotifySubscriberRequest, requestId = 89
14:10:21.466 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Ack server push request, request = NotifySubscriberRequest, requestId = 89
14:10:21.470 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Receive server push request, request = NotifySubscriberRequest, requestId = 90
14:10:21.472 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Ack server push request, request = NotifySubscriberRequest, requestId = 90
14:10:21.473 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Receive server push request, request = NotifySubscriberRequest, requestId = 91
14:10:21.474 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Ack server push request, request = NotifySubscriberRequest, requestId = 91
14:10:21.476 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Receive server push request, request = NotifySubscriberRequest, requestId = 93
14:10:21.477 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Ack server push request, request = NotifySubscriberRequest, requestId = 93
14:10:21.479 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Receive server push request, request = NotifySubscriberRequest, requestId = 92
14:10:21.479 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Ack server push request, request = NotifySubscriberRequest, requestId = 92
14:10:21.937 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
14:10:21.977 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.21 seconds (JVM running for 14.683)
14:10:21.984 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
14:10:21.985 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
14:10:21.986 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
14:10:22.447 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Receive server push request, request = NotifySubscriberRequest, requestId = 94
14:10:22.450 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48f064de-ed62-48bf-bf37-d6adef4f7f9c] Ack server push request, request = NotifySubscriberRequest, requestId = 94
14:21:30.334 [nacos-grpc-client-executor-147] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 100
14:21:30.336 [nacos-grpc-client-executor-147] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec026904-6d7d-48f9-b846-97e3f7a7bcc1_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 100
14:21:40.097 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:21:40.098 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:21:40.436 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:21:40.437 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6f3ef281[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:21:40.437 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750486219003_127.0.0.1_62627
14:21:40.439 [nacos-grpc-client-executor-340] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750486219003_127.0.0.1_62627]Ignore complete event,isRunning:false,isAbandon=false
14:21:40.462 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1f014f17[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 341]
14:21:46.183 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
14:21:47.116 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0
14:21:47.183 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
14:21:47.227 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:21:47.239 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:21:47.423 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 181 ms to scan 234 urls, producing 0 keys and 0 values 
14:21:47.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
14:21:47.457 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
14:21:47.478 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
14:21:47.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 164 ms to scan 234 urls, producing 0 keys and 0 values 
14:21:47.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:21:47.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1203142603
14:21:47.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/731870416
14:21:47.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:21:47.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:21:47.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:21:49.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750486909170_127.0.0.1_49251
14:21:49.430 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] Notify connected event to listeners.
14:21:49.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:49.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d72e5204-c54b-4ab4-b704-4c9a68724d9a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1180383462
14:21:49.543 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
14:21:55.256 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:21:55.855 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a718e95c-6616-435e-8db8-5d23743b59b3
14:21:55.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] RpcClient init label, labels = {module=naming, source=sdk}
14:21:55.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:21:55.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:21:55.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:21:55.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:21:55.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750486915868_127.0.0.1_49288
14:21:55.972 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Notify connected event to listeners.
14:21:55.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:55.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1180383462
14:21:56.373 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:21:57.078 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0
14:21:57.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:21:57.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1203142603
14:21:57.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/731870416
14:21:57.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:21:57.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:21:57.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:21:57.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750486917084_127.0.0.1_49299
14:21:57.192 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] Notify connected event to listeners.
14:21:57.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:57.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3fc1df85-013c-4491-ae17-4c3b6d7a6c28_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1180383462
14:21:57.949 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Receive server push request, request = NotifySubscriberRequest, requestId = 101
14:21:57.949 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Ack server push request, request = NotifySubscriberRequest, requestId = 101
14:21:58.211 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Receive server push request, request = NotifySubscriberRequest, requestId = 103
14:21:58.211 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Ack server push request, request = NotifySubscriberRequest, requestId = 103
14:21:58.214 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Receive server push request, request = NotifySubscriberRequest, requestId = 102
14:21:58.215 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Ack server push request, request = NotifySubscriberRequest, requestId = 102
14:21:58.216 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Receive server push request, request = NotifySubscriberRequest, requestId = 106
14:21:58.217 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Ack server push request, request = NotifySubscriberRequest, requestId = 106
14:21:58.219 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Receive server push request, request = NotifySubscriberRequest, requestId = 104
14:21:58.219 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Ack server push request, request = NotifySubscriberRequest, requestId = 104
14:21:58.219 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Receive server push request, request = NotifySubscriberRequest, requestId = 105
14:21:58.219 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Ack server push request, request = NotifySubscriberRequest, requestId = 105
14:21:58.405 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
14:21:58.452 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 12.959 seconds (JVM running for 14.493)
14:21:58.461 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
14:21:58.462 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
14:21:58.463 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
14:21:58.972 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Receive server push request, request = NotifySubscriberRequest, requestId = 107
14:21:58.976 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a718e95c-6616-435e-8db8-5d23743b59b3] Ack server push request, request = NotifySubscriberRequest, requestId = 107
