{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\dept\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\dept\\index.vue", "mtime": 1750151094292}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGVwdCwgZ2V0RGVwdCwgZGVsRGVwdCwgYWRkRGVwdCwgdXBkYXRlRGVwdCwgbGlzdERlcHRFeGNsdWRlQ2hpbGQgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVwdCI7DQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7DQppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRGVwdCIsDQogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLA0KICBjb21wb25lbnRzOiB7IFRyZWVzZWxlY3QgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g6KGo5qC85qCR5pWw5o2uDQogICAgICBkZXB0TGlzdDogW10sDQogICAgICAvLyDpg6jpl6jmoJHpgInpobkNCiAgICAgIGRlcHRPcHRpb25zOiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpuWxleW8gO+8jOm7mOiupOWFqOmDqOWxleW8gA0KICAgICAgaXNFeHBhbmRBbGw6IHRydWUsDQogICAgICAvLyDph43mlrDmuLLmn5PooajmoLznirbmgIENCiAgICAgIHJlZnJlc2hUYWJsZTogdHJ1ZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgZGVwdE5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBwYXJlbnRJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuIrnuqfpg6jpl6jkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBkZXB0TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpg6jpl6jlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBvcmRlck51bTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmmL7npLrmjpLluo/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBlbWFpbDogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICJlbWFpbCIsDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwNCiAgICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGhvbmU6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIg0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i6YOo6Zeo5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0RGVwdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kZXB0TGlzdCA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAiZGVwdElkIik7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6L2s5o2i6YOo6Zeo5pWw5o2u57uT5p6EICovDQogICAgbm9ybWFsaXplcihub2RlKSB7DQogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW47DQogICAgICB9DQogICAgICByZXR1cm4gew0KICAgICAgICBpZDogbm9kZS5kZXB0SWQsDQogICAgICAgIGxhYmVsOiBub2RlLmRlcHROYW1lLA0KICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbg0KICAgICAgfTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkLA0KICAgICAgICBwYXJlbnRJZDogdW5kZWZpbmVkLA0KICAgICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLA0KICAgICAgICBvcmRlck51bTogdW5kZWZpbmVkLA0KICAgICAgICBsZWFkZXI6IHVuZGVmaW5lZCwNCiAgICAgICAgcGhvbmU6IHVuZGVmaW5lZCwNCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwNCiAgICAgICAgc3RhdHVzOiAiMCINCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBpZiAocm93ICE9IHVuZGVmaW5lZCkgew0KICAgICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSByb3cuZGVwdElkOw0KICAgICAgfQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6YOo6ZeoIjsNCg0KICAgICAgLy8gY29uc3QgZGVwdHMgPSB7IGRlcHRJZDogMCwgZGVwdE5hbWU6ICfkuLvnsbvnm64nLCBjaGlsZHJlbjogW10gfTsNCiAgICAgIA0KICAgICAgLy8gbGlzdERlcHQoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgLy8gdGhpcy5kZXB0T3B0aW9ucyA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAiZGVwdElkIik7DQogICAgICAvLyAgIGRlcHRzLmNoaWxkcmVuID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJtZW51SWQiKTsNCiAgICAgIC8vICAgdGhpcy5kZXB0T3B0aW9ucy5wdXNoKGRlcHRzKTsNCiAgICAgIC8vIH0pOw0KDQogICAgICBsaXN0RGVwdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJkZXB0SWQiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWxleW8gC/mipjlj6Dmk43kvZwgKi8NCiAgICB0b2dnbGVFeHBhbmRBbGwoKSB7DQogICAgICB0aGlzLnJlZnJlc2hUYWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5pc0V4cGFuZEFsbCA9ICF0aGlzLmlzRXhwYW5kQWxsOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLnJlZnJlc2hUYWJsZSA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBnZXREZXB0KHJvdy5kZXB0SWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuemDqOmXqCI7DQogICAgICB9KTsNCiAgICAgIGxpc3REZXB0RXhjbHVkZUNoaWxkKHJvdy5kZXB0SWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJkZXB0SWQiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5kZXB0SWQgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICB1cGRhdGVEZXB0KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkRGVwdCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlkI3np7DkuLoiJyArIHJvdy5kZXB0TmFtZSArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbERlcHQocm93LmRlcHRJZCk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/dept", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"部门名称\" prop=\"deptName\">\r\n        <el-input\r\n          v-model=\"queryParams.deptName\"\r\n          placeholder=\"请输入部门名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"部门状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:dept:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-sort\"\r\n          size=\"mini\"\r\n          @click=\"toggleExpandAll\"\r\n        >展开/折叠</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-if=\"refreshTable\"\r\n      v-loading=\"loading\"\r\n      :data=\"deptList\"\r\n      row-key=\"deptId\"\r\n      :default-expand-all=\"isExpandAll\"\r\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n    >\r\n      <el-table-column prop=\"deptName\" label=\"部门名称\" width=\"260\"></el-table-column>\r\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"200\"></el-table-column>\r\n      <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:dept:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd(scope.row)\"\r\n            v-hasPermi=\"['system:dept:add']\"\r\n          >新增</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.parentId != 0\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:dept:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 添加或修改部门对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"24\" v-if=\"form.parentId !== 0\">\r\n            <el-form-item label=\"上级部门\" prop=\"parentId\">\r\n              <treeselect v-model=\"form.parentId\" :options=\"deptOptions\" :normalizer=\"normalizer\" placeholder=\"选择上级部门\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门名称\" prop=\"deptName\">\r\n              <el-input v-model=\"form.deptName\" placeholder=\"请输入部门名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人\" prop=\"leader\">\r\n              <el-input v-model=\"form.leader\" placeholder=\"请输入负责人\" maxlength=\"20\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话\" prop=\"phone\">\r\n              <el-input v-model=\"form.phone\" placeholder=\"请输入联系电话\" maxlength=\"11\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮箱\" prop=\"email\">\r\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from \"@/api/system/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Dept\",\r\n  dicts: ['sys_normal_disable'],\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 表格树数据\r\n      deptList: [],\r\n      // 部门树选项\r\n      deptOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部展开\r\n      isExpandAll: true,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        deptName: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parentId: [\r\n          { required: true, message: \"上级部门不能为空\", trigger: \"blur\" }\r\n        ],\r\n        deptName: [\r\n          { required: true, message: \"部门名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        orderNum: [\r\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          {\r\n            type: \"email\",\r\n            message: \"请输入正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phone: [\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询部门列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDept(this.queryParams).then(response => {\r\n        this.deptList = this.handleTree(response.data, \"deptId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 转换部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.deptId,\r\n        label: node.deptName,\r\n        children: node.children\r\n      };\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        deptId: undefined,\r\n        parentId: undefined,\r\n        deptName: undefined,\r\n        orderNum: undefined,\r\n        leader: undefined,\r\n        phone: undefined,\r\n        email: undefined,\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      if (row != undefined) {\r\n        this.form.parentId = row.deptId;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加部门\";\r\n\r\n      // const depts = { deptId: 0, deptName: '主类目', children: [] };\r\n      \r\n      // listDept().then(response => {\r\n      //   // this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n      //   depts.children = this.handleTree(response.data, \"menuId\");\r\n      //   this.deptOptions.push(depts);\r\n      // });\r\n\r\n      listDept().then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n      });\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      getDept(row.deptId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改部门\";\r\n      });\r\n      listDeptExcludeChild(row.deptId).then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.deptId != undefined) {\r\n            updateDept(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDept(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除名称为\"' + row.deptName + '\"的数据项？').then(function() {\r\n        return delDept(row.deptId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}