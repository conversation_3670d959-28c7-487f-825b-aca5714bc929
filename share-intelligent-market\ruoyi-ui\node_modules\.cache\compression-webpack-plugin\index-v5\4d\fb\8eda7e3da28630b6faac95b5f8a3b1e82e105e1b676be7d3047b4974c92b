
d84b8ff438a158afc225965dc9f3598622a278fd	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"85e2df2fca9c7bea1b938db391803e2b\"}","integrity":"sha512-Or+OCCQ8TyEcTPnOCB+/LSEfLhEr+EmFfRYjyJwKAPlNipjlr/woiZl2een8rJwCWGCihB2WVC+FgvmYxlF0sA==","time":1750496065137,"size":10872950}