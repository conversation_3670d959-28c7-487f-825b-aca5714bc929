{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\msg\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\msg\\list.vue", "mtime": 1750151094247}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdERhdGEsCiAgc2V0U3RhdHVzLAogIGRlbERhdGEsCiAgYWRkRGF0YSwKICBnZXREYXRhCn0gZnJvbSAiQC9hcGkvbXNnL2xpc3QuanMiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkluZm9yIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgb3B0aW9uc1N0YXR1czogW3sKICAgICAgICAgIHZhbHVlOiAxLAogICAgICAgICAgbGFiZWw6ICLlt7Lor7siLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6IDAsCiAgICAgICAgICBsYWJlbDogIuacquivuyIsCiAgICAgICAgfSwKICAgICAgXSwKICAgICAgbm9ybXNMaXN0OiBbXSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNob3c6IGZhbHNlLAogICAgICB0aXRsZTogIiIsCiAgICAgIGZvcm06IHt9LAogICAgICBydWxlczogewogICAgICAgIGNvbnRlbnQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7floavlhpnlhoXlrrkiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiLAogICAgICAgIH0sIF0sCgogICAgICB9LAoKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOihqOagvOaVsOaNrgogICAgICBsaXN0OiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgaXNyZWFkOiAiIgogICAgICB9LAogICAgICBmb3JtOiB7CiAgICAgIH0sCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g56Gu6K6k5bey6K+7CiAgICBoYW5kbGVSZWFkZShyb3cpIHsKICAgICAgc2V0U3RhdHVzKHsKICAgICAgICBvcGlkOiByb3cuaWQsCiAgICAgICAgc3RhdHVzOiAxCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICB9KQogICAgfSwKICAgIC8vIOS/ruaUueeKtuaAgQogICAgc2V0U3RhdHVzKHJvdywgdHlwZSkgewogICAgICBzZXRTdGF0dXMoewogICAgICAgIG9waWQ6IHJvdy5pZCwKICAgICAgICBzdGF0dXM6IHR5cGUKICAgICAgfSkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5tc2csCiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgIH0pOwogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICB1cGxvYWROb3JtKGZpbGVMaXN0KSB7CiAgICAgIGxldCBuYW1lID0gdW5kZWZpbmVkOwogICAgICBsZXQgdXJsID0gdW5kZWZpbmVkOwogICAgICBpZiAoZmlsZUxpc3QubGVuZ3RoKSB7CiAgICAgICAgbmFtZSA9IGZpbGVMaXN0WzBdLm5hbWU7CiAgICAgICAgdXJsID0gZmlsZUxpc3RbMF0udXJsOwogICAgICB9CiAgICAgIHRoaXMuZm9ybS5ub3JtZmlsZSA9IG5hbWU7CiAgICAgIHRoaXMuZm9ybS5ub3JtdXJsID0gdXJsOwogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0pOwogICAgfSwKICAgIC8qKiDmn6Xor6LlhazlkYrliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3REYXRhKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuY291bnQ7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGlzcmVhZDogIiIKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS50YXJnZXRfaWQpOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5hZGQoKTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIGNvbnN0IGluZm9ySWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7CgogICAgICB0aGlzLmZvcm0gPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJvdykpOwogICAgICB0aGlzLnRpdGxlID0gIuafpeeci+ivpuaDhSI7CiAgICAgIHRoaXMuc2hvdyA9IHRydWU7CiAgICAgIGdldERhdGEoaW5mb3JJZCkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICB0aGlzLmVkaXQocmVzcG9uc2UuZGF0YSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgaW5mb3JJZHMgPSByb3cudGFyZ2V0X2lkIHx8IHRoaXMuaWRzLmpvaW4oIiwiKTsKICAgICAgdGhpcy4kbW9kYWwKICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk57yW5Y+35Li6IicgKyBpbmZvcklkcyArICci55qE5pWw5o2u6aG577yfJykKICAgICAgICAudGhlbihmdW5jdGlvbigpIHsKICAgICAgICAgIHJldHVybiBkZWxEYXRhKGluZm9ySWRzKTsKICAgICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIGhhbmRsZUNvcHkocm93KSB7CiAgICAgIGNvbnN0IGNsaXBib2FyZE9iaiA9IG5hdmlnYXRvci5jbGlwYm9hcmQ7CiAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgIG1lc3NhZ2U6ICLpk77mjqXlt7LlpI3liLYiLAogICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgfSk7CiAgICAgIGNsaXBib2FyZE9iai53cml0ZVRleHQoCiAgICAgICAgImh0dHBzOi8vc2MuY251ZGouY29tL2luZm9yP2lkPSIgKyByb3cuaWQKICAgICAgKTsKICAgIH0sCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewoKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICBhZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqAiOwogICAgICB0aGlzLnNob3cgPSB0cnVlOwogICAgfSwKICAgIGVkaXQoZGF0YSkgewogICAgICB0aGlzLnRpdGxlID0gIue8lui+kSI7CiAgICAgIHRoaXMuc2hvdyA9IHRydWU7CiAgICAgIHRoaXMuZm9ybSA9IGRhdGE7CiAgICB9LAogICAgaGFuZGxlU3VibWl0KCkgewogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGlkYXRlKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkYXRlKSB7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgaWYgKCF0aGlzLmZvcm0uaWQpIHsKICAgICAgICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtKTsKICAgICAgICAgICAgYWRkRGF0YSh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfISIsCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5zaG93ID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgICAgICB0aGlzLiRlbWl0KCJyZWZyZXNoIik7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+35a6M5ZaE5L+h5oGv5YaN5o+Q5LqkISIpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogIH0sCn07Cg=="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/msg", "sourcesContent": ["// 消息管理\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"是否已读\" prop=\"name\">\r\n          <el-select clearable  v-model=\"queryParams.isread\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"item in optionsStatus\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\">\r\n            </el-option>\r\n          </el-select>\r\n        <!-- <el-input clearable v-model=\"queryParams.name\" style=\"width: 300px\" placeholder=\"请输入名称\" :maxlength=\"60\"\r\n          @keyup.enter.native=\"handleQuery\" /> -->\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col>\r\n\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\">删除\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.$index + 1 }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标题\" align=\"center\" prop=\"title\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"内容\" align=\"center\" prop=\"content\" />\r\n\r\n      <el-table-column label=\"时间\" align=\"center\" prop=\"create_time\" sortable />\r\n      <el-table-column label=\"是否已读\" align=\"center\" prop=\"create_by\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag type=\"success\" v-if=\"scope.row.is_read == 1\">已读</el-tag>\r\n          <el-tag type=\"danger\" v-else>未读</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button v-if=\"scope.row.is_read != 1\" size=\"mini\" type=\"text\" @click=\"handleReade(scope.row)\">已读\r\n          </el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\">查看详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" style=\"color: red;\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n    <!-- 添加弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"70%\" :before-close=\"() => (show = false)\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" :rules=\"rules\">\r\n\r\n        <el-form-item label=\"内容\" prop=\"content\">\r\n          <el-input :disabled=\"form.id?true:false\" :autosize=\"{ minRows: 2, maxRows: 5}\" clearable v-model=\"form.content\" placeholder=\"请输入名称\"\r\n            type=\"textarea\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"时间\" prop=\"create_time\" v-if=\"form.id\">\r\n          <el-input :disabled=\"form.id?true:false\" v-model=\"form.create_time\" placeholder=\"请输入名称\"\r\n            type=\"text\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否已读\" prop=\"create_time\" v-if=\"form.id\">\r\n          <el-tag type=\"success\" v-if=\"form.is_read == 1\">已读</el-tag>\r\n          <el-tag type=\"danger\" v-else>未读</el-tag>\r\n        </el-form-item>\r\n\r\n\r\n\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"show = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit\" v-if=\"!form.id\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    listData,\r\n    setStatus,\r\n    delData,\r\n    addData,\r\n    getData\r\n  } from \"@/api/msg/list.js\";\r\n  export default {\r\n    name: \"Infor\",\r\n    data() {\r\n      return {\r\n        optionsStatus: [{\r\n            value: 1,\r\n            label: \"已读\",\r\n          },\r\n          {\r\n            value: 0,\r\n            label: \"未读\",\r\n          },\r\n        ],\r\n        normsList: [],\r\n        loading: false,\r\n        show: false,\r\n        title: \"\",\r\n        form: {},\r\n        rules: {\r\n          content: [{\r\n            required: true,\r\n            message: \"请填写内容\",\r\n            trigger: \"blur\",\r\n          }, ],\r\n\r\n        },\r\n\r\n        // 遮罩层\r\n        loading: true,\r\n        // 选中数组\r\n        ids: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表格数据\r\n        list: [],\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          isread: \"\"\r\n        },\r\n        form: {\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.getList();\r\n    },\r\n    methods: {\r\n      // 确认已读\r\n      handleReade(row) {\r\n        setStatus({\r\n          opid: row.id,\r\n          status: 1\r\n        }).then(res => {\r\n          this.getList()\r\n        })\r\n      },\r\n      // 修改状态\r\n      setStatus(row, type) {\r\n        setStatus({\r\n          opid: row.id,\r\n          status: type\r\n        }).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message({\r\n              message: response.msg,\r\n              type: \"success\",\r\n            });\r\n            this.getList();\r\n          }\r\n        });\r\n      },\r\n      uploadNorm(fileList) {\r\n        let name = undefined;\r\n        let url = undefined;\r\n        if (fileList.length) {\r\n          name = fileList[0].name;\r\n          url = fileList[0].url;\r\n        }\r\n        this.form.normfile = name;\r\n        this.form.normurl = url;\r\n        console.log(this.form);\r\n      },\r\n      /** 查询公告列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listData(this.queryParams).then((response) => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          isread: \"\"\r\n        };\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n      // 多选框选中数据\r\n      handleSelectionChange(selection) {\r\n        this.ids = selection.map((item) => item.target_id);\r\n        this.single = selection.length != 1;\r\n        this.multiple = !selection.length;\r\n      },\r\n      /** 新增按钮操作 */\r\n      handleAdd() {\r\n        this.add();\r\n      },\r\n      /** 修改按钮操作 */\r\n      handleUpdate(row) {\r\n        const inforId = row.id || this.ids;\r\n\r\n        this.form = JSON.parse(JSON.stringify(row));\r\n        this.title = \"查看详情\";\r\n        this.show = true;\r\n        getData(inforId).then((response) => {\r\n          this.edit(response.data);\r\n        });\r\n      },\r\n      /** 删除按钮操作 */\r\n      handleDelete(row) {\r\n        const inforIds = row.target_id || this.ids.join(\",\");\r\n        this.$modal\r\n          .confirm('是否确认删除编号为\"' + inforIds + '\"的数据项？')\r\n          .then(function() {\r\n            return delData(inforIds);\r\n          })\r\n          .then(() => {\r\n            this.getList();\r\n            this.$modal.msgSuccess(\"删除成功\");\r\n          })\r\n          .catch(() => {});\r\n      },\r\n      handleCopy(row) {\r\n        const clipboardObj = navigator.clipboard;\r\n        this.$message({\r\n          message: \"链接已复制\",\r\n          type: \"success\",\r\n        });\r\n        clipboardObj.writeText(\r\n          \"https://sc.cnudj.com/infor?id=\" + row.id\r\n        );\r\n      },\r\n      reset() {\r\n        this.form = {\r\n\r\n        };\r\n        this.resetForm(\"form\");\r\n      },\r\n      add() {\r\n        this.reset();\r\n        this.title = \"添加\";\r\n        this.show = true;\r\n      },\r\n      edit(data) {\r\n        this.title = \"编辑\";\r\n        this.show = true;\r\n        this.form = data;\r\n      },\r\n      handleSubmit() {\r\n        this.$refs.form.validate((validate) => {\r\n          if (validate) {\r\n            this.loading = true;\r\n            if (!this.form.id) {\r\n              console.log(this.form);\r\n              addData(this.form).then((response) => {\r\n                this.$message({\r\n                  type: \"success\",\r\n                  message: \"操作成功!\",\r\n                });\r\n                this.loading = false;\r\n                this.show = false;\r\n                this.getList()\r\n                this.$emit(\"refresh\");\r\n              });\r\n            }\r\n          } else {\r\n            this.$modal.msgError(\"请完善信息再提交!\");\r\n          }\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n"]}]}