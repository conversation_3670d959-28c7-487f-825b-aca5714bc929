{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\components\\lessDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\components\\lessDetails.vue", "mtime": 1750151094272}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0RGF0YQp9IGZyb20gJ0AvYXBpL3Byb2plY3Qvb2ZmZXInOwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBmb3JtOiB7CiAgICAgICAgImlucXVpcnkiOnt9LAogICAgICAgICJvZmZlciI6e30sCiAgICAgICAgIml0ZW1zIjpbXQogICAgICB9CiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgb3BlbihvZmZlcklkKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIGdldERhdGEob2ZmZXJJZCkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlcy5kYXRhOwogICAgICB9KQogICAgfQogIH0sCn07Cg=="}, {"version": 3, "sources": ["lessDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "lessDetails.vue", "sourceRoot": "src/views/project/offer/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog title=\"极速报价详情\" :visible.sync=\"dialogVisible\" width=\"80%\" center>\r\n      <el-descriptions class=\"margin-top\" title=\"询价信息\" :column=\"3\" direction=\"horizontal\" border>\r\n        <el-descriptions-item label=\"询价公司\">{{form.inquiry.enterprise_name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价单号\">{{form.inquiry.inquiry_no}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价日期\">{{form.inquiry.inquiry_date}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人\">{{form.inquiry.linker}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系电话\">{{form.inquiry.linkphone}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价标题\">{{form.inquiry.title}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需求描述\">{{form.inquiry.description}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"截止日期\">{{form.inquiry.deadline}}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions style=\"margin-top: 20px;\" class=\"margin-top\" title=\"报价信息\" :column=\"3\" direction=\"horizontal\"\r\n        border>\r\n        <el-descriptions-item label=\"报价日期\">{{form.offer.offer_date}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报价单号\">{{form.offer.offer_no}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报价公司\">{{form.offer.enterprise_name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人\">{{form.offer.linker}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系电话\">{{form.offer.linkphone}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报价版本\">{{form.offer.version}}次报价</el-descriptions-item>\r\n        <el-descriptions-item label=\"备注\">{{form.offer.remark}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"初次报价其他费用\" v-if=\"form.offer.other_fee\">\r\n          <span v-for=\"item in form.offer.other_fee\" style=\"margin-right: 10px;\">\r\n            {{item.key}}:{{item.value}}\r\n          </span>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"二次报价其他费用\" v-if=\"form.offer.other_fee_v2\">\r\n          <span v-for=\"item in form.offer.other_fee_v2\" style=\"margin-right: 10px;\">\r\n            {{item.key}}:{{item.value}}\r\n          </span>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions style=\"margin-top: 20px;\" v-for=\"(values,key) in form.items\" class=\"margin-top\"\r\n        :title=\"key+'次报价'\" :column=\"3\" direction=\"horizontal\" border>\r\n        <el-descriptions-item label=\"报价明细\">\r\n          <el-table :data=\"values\">\r\n            <el-table-column label=\"物料分类\" align=\"center\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                {{scope.row.classify_name}}-{{scope.row.classify2_name}}-{{scope.row.classify3_name}}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"物料名称\" align=\"center\" width=\"150\" prop=\"material_name\">\r\n            </el-table-column>\r\n            <el-table-column label=\"物料单价\" align=\"center\" width=\"150\" prop=\"offer_tax_price\">\r\n            </el-table-column>\r\n            <el-table-column label=\"询价附件\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div v-for=\"file in scope.row.attachment\" v-if=\"scope.row.attachment\">\r\n                  <a :href=\"file.url\" target=\"_blank\">{{file.name}}</a>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"报价附件\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div v-for=\"file in scope.row.offer_attachment\" v-if=\"scope.row.offer_attachment\">\r\n                  <a :href=\"file.url\" target=\"_blank\">{{file.name}}</a>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\n  import {\r\n    getData\r\n  } from '@/api/project/offer';\r\n  export default {\r\n    data() {\r\n      return {\r\n        dialogVisible: false,\r\n        form: {\r\n          \"inquiry\":{},\r\n          \"offer\":{},\r\n          \"items\":[]\r\n        }\r\n      };\r\n    },\r\n    methods: {\r\n      open(offerId) {\r\n        this.dialogVisible = true;\r\n        getData(offerId).then(res => {\r\n          this.form = res.data;\r\n        })\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style scoped>\r\n  .el-descriptions-item__cell {\r\n    max-width: 300px;\r\n  }\r\n</style>\r\n"]}]}