{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\dashboard\\mixins\\resize.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\dashboard\\mixins\\resize.js", "mtime": 1750151094234}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_utils", "require", "_default", "exports", "default", "data", "$_sidebarElm", "$_resizeHandler", "mounted", "initListener", "activated", "resize", "<PERSON><PERSON><PERSON><PERSON>", "destroyListener", "deactivated", "methods", "$_sidebarResizeHandler", "e", "propertyName", "_this", "debounce", "window", "addEventListener", "document", "getElementsByClassName", "removeEventListener", "chart"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/views/dashboard/mixins/resize.js"], "sourcesContent": ["import { debounce } from '@/utils'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      $_sidebarElm: null,\r\n      $_resizeHandler: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initListener()\r\n  },\r\n  activated() {\r\n    if (!this.$_resizeHandler) {\r\n      // avoid duplication init\r\n      this.initListener()\r\n    }\r\n\r\n    // when keep-alive chart activated, auto resize\r\n    this.resize()\r\n  },\r\n  beforeD<PERSON>roy() {\r\n    this.destroyListener()\r\n  },\r\n  deactivated() {\r\n    this.destroyListener()\r\n  },\r\n  methods: {\r\n    // use $_ for mixins properties\r\n    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential\r\n    $_sidebarResizeHandler(e) {\r\n      if (e.propertyName === 'width') {\r\n        this.$_resizeHandler()\r\n      }\r\n    },\r\n    initListener() {\r\n      this.$_resizeHandler = debounce(() => {\r\n        this.resize()\r\n      }, 100)\r\n      window.addEventListener('resize', this.$_resizeHandler)\r\n\r\n      this.$_sidebarElm = document.getElementsByClassName('sidebar-container')[0]\r\n      this.$_sidebarElm && this.$_sidebarElm.addEventListener('transitionend', this.$_sidebarResizeHandler)\r\n    },\r\n    destroyListener() {\r\n      window.removeEventListener('resize', this.$_resizeHandler)\r\n      this.$_resizeHandler = null\r\n\r\n      this.$_sidebarElm && this.$_sidebarElm.removeEventListener('transitionend', this.$_sidebarResizeHandler)\r\n    },\r\n    resize() {\r\n      const { chart } = this\r\n      chart && chart.resize()\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAkC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEnB;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,IAAI;MAClBC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,YAAY,CAAC,CAAC;EACrB,CAAC;EACDC,SAAS,WAATA,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACH,eAAe,EAAE;MACzB;MACA,IAAI,CAACE,YAAY,CAAC,CAAC;IACrB;;IAEA;IACA,IAAI,CAACE,MAAM,CAAC,CAAC;EACf,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACD,eAAe,CAAC,CAAC;EACxB,CAAC;EACDE,OAAO,EAAE;IACP;IACA;IACAC,sBAAsB,WAAtBA,sBAAsBA,CAACC,CAAC,EAAE;MACxB,IAAIA,CAAC,CAACC,YAAY,KAAK,OAAO,EAAE;QAC9B,IAAI,CAACX,eAAe,CAAC,CAAC;MACxB;IACF,CAAC;IACDE,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAU,KAAA;MACb,IAAI,CAACZ,eAAe,GAAG,IAAAa,eAAQ,EAAC,YAAM;QACpCD,KAAI,CAACR,MAAM,CAAC,CAAC;MACf,CAAC,EAAE,GAAG,CAAC;MACPU,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACf,eAAe,CAAC;MAEvD,IAAI,CAACD,YAAY,GAAGiB,QAAQ,CAACC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;MAC3E,IAAI,CAAClB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACgB,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACN,sBAAsB,CAAC;IACvG,CAAC;IACDH,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChBQ,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAClB,eAAe,CAAC;MAC1D,IAAI,CAACA,eAAe,GAAG,IAAI;MAE3B,IAAI,CAACD,YAAY,IAAI,IAAI,CAACA,YAAY,CAACmB,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACT,sBAAsB,CAAC;IAC1G,CAAC;IACDL,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAQe,KAAK,GAAK,IAAI,CAAdA,KAAK;MACbA,KAAK,IAAIA,KAAK,CAACf,MAAM,CAAC,CAAC;IACzB;EACF;AACF,CAAC", "ignoreList": []}]}