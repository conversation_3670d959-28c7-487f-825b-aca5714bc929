{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\project\\offer.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\project\\offer.js", "mtime": 1750151093967}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5nZXREYXRhID0gZ2V0RGF0YTsKZXhwb3J0cy5saXN0RGF0YSA9IGxpc3REYXRhOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIik7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyBpbnF1aXJ5CgovLyDojrflj5bliJfooajmlbDmja4KZnVuY3Rpb24gbGlzdERhdGEocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICJzaG9wL2FkbWluL29mZmVyL2xpc3QvIi5jb25jYXQocGFyYW1zLnBhZ2VOdW0sICIvIikuY29uY2F0KHBhcmFtcy5wYWdlU2l6ZSksCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQoKLy8g6I635Y+W6K+m5oOF5pWw5o2uCmZ1bmN0aW9uIGdldERhdGEoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogInNob3AvYWRtaW4vb2ZmZXIvZGV0YWlsLyIuY29uY2F0KGlkKSwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listData", "params", "request", "url", "concat", "pageNum", "pageSize", "method", "getData", "id"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/project/offer.js"], "sourcesContent": ["// inquiry\r\nimport request from '@/utils/request'\r\n\r\n// 获取列表数据\r\nexport function listData(params) {\r\n  return request({\r\n    url: `shop/admin/offer/list/${params.pageNum}/${params.pageSize}`,\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取详情数据\r\nexport function getData(id) {\r\n  return request({\r\n    url: `shop/admin/offer/detail/${id}`,\r\n    method: 'get',\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AACA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AADA;;AAGA;AACO,SAASC,QAAQA,CAACC,MAAM,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,2BAAAC,MAAA,CAA2BH,MAAM,CAACI,OAAO,OAAAD,MAAA,CAAIH,MAAM,CAACK,QAAQ,CAAE;IACjEC,MAAM,EAAE,KAAK;IACbN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,OAAOA,CAACC,EAAE,EAAE;EAC1B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,6BAAAC,MAAA,CAA6BK,EAAE,CAAE;IACpCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}