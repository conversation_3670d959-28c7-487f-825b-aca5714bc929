{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\plugins\\auth.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\plugins\\auth.js", "mtime": 1750151094187}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "authPermission", "permission", "all_permission", "permissions", "store", "getters", "length", "some", "v", "authRole", "role", "super_admin", "roles", "_default", "exports", "default", "<PERSON><PERSON><PERSON><PERSON>", "hasPermiOr", "item", "hasPermiAnd", "every", "hasRole", "hasRoleOr", "hasRoleAnd"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/plugins/auth.js"], "sourcesContent": ["import store from '@/store'\r\n\r\nfunction authPermission(permission) {\r\n  const all_permission = \"*:*:*\";\r\n  const permissions = store.getters && store.getters.permissions\r\n  \r\n  if (permission && permission.length > 0) {\r\n    return permissions.some(v => {\r\n      return all_permission === v || v === permission\r\n    })\r\n  } else {\r\n    return false\r\n  }\r\n}\r\n\r\nfunction authRole(role) {\r\n  const super_admin = \"admin\";\r\n  const roles = store.getters && store.getters.roles\r\n  if (role && role.length > 0) {\r\n    return roles.some(v => {\r\n      return super_admin === v || v === role\r\n    })\r\n  } else {\r\n    return false\r\n  }\r\n}\r\n\r\nexport default {\r\n  // 验证用户是否具备某权限\r\n  hasPermi(permission) {\r\n    return authPermission(permission);\r\n  },\r\n  // 验证用户是否含有指定权限，只需包含其中一个\r\n  hasPermiOr(permissions) {\r\n    return permissions.some(item => {\r\n      return authPermission(item)\r\n    })\r\n  },\r\n  // 验证用户是否含有指定权限，必须全部拥有\r\n  hasPermiAnd(permissions) {\r\n    return permissions.every(item => {\r\n      return authPermission(item)\r\n    })\r\n  },\r\n  // 验证用户是否具备某角色\r\n  hasRole(role) {\r\n    return authRole(role);\r\n  },\r\n  // 验证用户是否含有指定角色，只需包含其中一个\r\n  hasRoleOr(roles) {\r\n    return roles.some(item => {\r\n      return authRole(item)\r\n    })\r\n  },\r\n  // 验证用户是否含有指定角色，必须全部拥有\r\n  hasRoleAnd(roles) {\r\n    return roles.every(item => {\r\n      return authRole(item)\r\n    })\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,IAAMC,cAAc,GAAG,OAAO;EAC9B,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;EAE9D,IAAIF,UAAU,IAAIA,UAAU,CAACK,MAAM,GAAG,CAAC,EAAE;IACvC,OAAOH,WAAW,CAACI,IAAI,CAAC,UAAAC,CAAC,EAAI;MAC3B,OAAON,cAAc,KAAKM,CAAC,IAAIA,CAAC,KAAKP,UAAU;IACjD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAEA,SAASQ,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAMC,WAAW,GAAG,OAAO;EAC3B,IAAMC,KAAK,GAAGR,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACO,KAAK;EAClD,IAAIF,IAAI,IAAIA,IAAI,CAACJ,MAAM,GAAG,CAAC,EAAE;IAC3B,OAAOM,KAAK,CAACL,IAAI,CAAC,UAAAC,CAAC,EAAI;MACrB,OAAOG,WAAW,KAAKH,CAAC,IAAIA,CAAC,KAAKE,IAAI;IACxC,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACb;EACAC,QAAQ,WAARA,QAAQA,CAACf,UAAU,EAAE;IACnB,OAAOD,cAAc,CAACC,UAAU,CAAC;EACnC,CAAC;EACD;EACAgB,UAAU,WAAVA,UAAUA,CAACd,WAAW,EAAE;IACtB,OAAOA,WAAW,CAACI,IAAI,CAAC,UAAAW,IAAI,EAAI;MAC9B,OAAOlB,cAAc,CAACkB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;EACAC,WAAW,WAAXA,WAAWA,CAAChB,WAAW,EAAE;IACvB,OAAOA,WAAW,CAACiB,KAAK,CAAC,UAAAF,IAAI,EAAI;MAC/B,OAAOlB,cAAc,CAACkB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;EACAG,OAAO,WAAPA,OAAOA,CAACX,IAAI,EAAE;IACZ,OAAOD,QAAQ,CAACC,IAAI,CAAC;EACvB,CAAC;EACD;EACAY,SAAS,WAATA,SAASA,CAACV,KAAK,EAAE;IACf,OAAOA,KAAK,CAACL,IAAI,CAAC,UAAAW,IAAI,EAAI;MACxB,OAAOT,QAAQ,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACD;EACAK,UAAU,WAAVA,UAAUA,CAACX,KAAK,EAAE;IAChB,OAAOA,KAAK,CAACQ,KAAK,CAAC,UAAAF,IAAI,EAAI;MACzB,OAAOT,QAAQ,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}]}