{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\login.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\login.js", "mtime": 1750476205678}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "loginCode", "username", "smsCode", "password", "request", "url", "headers", "isToken", "method", "data", "userType", "timeout", "ssologinCode", "login", "code", "uuid", "loginByPassword", "ssologin", "register", "refreshToken", "getInfo", "logout", "getCodeImg", "getCommonCode", "params", "setPassword", "accessToken", "getSSOLoginUrl", "redirect", "handleSSOCallback", "state", "getSSOTokenByKey", "key"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/api/login.js"], "sourcesContent": ["/*\r\n * @Author: jhy\r\n * @Date: 2023-01-28 08:57:34\r\n * @LastEditors: jhy\r\n * @LastEditTime: 2023-01-29 15:07:26\r\n */\r\nimport request from \"@/utils/request\";\r\n\r\n// 短信验证码登录、密码设置\r\nexport function loginCode(username, smsCode, password) {\r\n  return request({\r\n    url: \"/auth/loginBySmsCode\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"post\",\r\n    data: { username, smsCode, password, userType: \"01\" },\r\n    timeout: 45000, // 验证码登录可能涉及用户创建和SSO同步，设置为45秒\r\n  });\r\n}\r\n\r\n// 短信验证码登录、密码设置\r\nexport function ssologinCode(username, smsCode, password) {\r\n  return request({\r\n    url: \"/auth/chiWeb/loginBySmsCode\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"post\",\r\n    data: { username, smsCode, password, userType: \"01\" },\r\n    timeout: 45000, // SSO验证码登录可能涉及用户创建和同步，设置为45秒\r\n  });\r\n}\r\n\r\n// 账号密码登录（需要验证码）\r\nexport function login(username, password, code, uuid) {\r\n  return request({\r\n    url: \"/auth/portallogin\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"post\",\r\n    data: { username, password, smsCode: code, uuid },\r\n  });\r\n}\r\n\r\n// 纯账号密码登录（不需要验证码）\r\nexport function loginByPassword(username, password) {\r\n  return request({\r\n    url: \"/auth/portalloginByPassword\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"post\",\r\n    data: { username, password },\r\n  });\r\n}\r\n\r\n// 账号密码登录\r\n// export function login(username, password, code, uuid) {\r\n//   return request({\r\n//     url: \"/register\",\r\n//     headers: {\r\n//       isToken: false,\r\n//     },\r\n//     method: \"post\",\r\n//     data: { username, password, code, uuid, userType: \"01\" },\r\n//   });\r\n// }\r\n\r\n// sso账号密码登录\r\nexport function ssologin(username, password, code, uuid) {\r\n  return request({\r\n    url: \"/auth/chiWeb/loginByPwd\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"post\",\r\n    data: { username, password, code, uuid, userType: \"01\" },\r\n  });\r\n}\r\n\r\n// 注册方法\r\nexport function register(data) {\r\n  return request({\r\n    url: \"/auth/register\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"post\",\r\n    data: data,\r\n  });\r\n}\r\n\r\n// 刷新方法\r\nexport function refreshToken() {\r\n  return request({\r\n    url: \"/auth/refresh\",\r\n    method: \"post\",\r\n  });\r\n}\r\n\r\n// // 获取用户详细信息\r\n// export function getInfo() {\r\n//   return request({\r\n//     url: \"/system/user/getInfo\",\r\n//     method: \"get\",\r\n//   });\r\n// }\r\n// 获取用户详细信息\r\nexport function getInfo() {\r\n  return request({\r\n    url: \"/portalweb/Member/getMemberInfo\",\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 退出方法\r\nexport function logout() {\r\n  return request({\r\n    url: \"/auth/portallogout\",\r\n    method: \"delete\",\r\n  });\r\n}\r\n\r\n// 获取图形验证码\r\nexport function getCodeImg() {\r\n  return request({\r\n    url: \"/code\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"get\",\r\n    timeout: 20000,\r\n  });\r\n}\r\n\r\n// 获取手机号验证码\r\nexport function getCommonCode(params) {\r\n  return request({\r\n    // url: \"/auth/single/util/get_common_code\",\r\n    url: \"/auth/single/util/get_qwt_code\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 设置密码\r\nexport function setPassword(username, smsCode, password) {\r\n  return request({\r\n    url: \"/auth/portalpassword\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"post\",\r\n    data: { username, smsCode, password },\r\n    timeout: 60000, // 密码设置操作需要更长时间，设置为60秒\r\n  });\r\n}\r\n\r\n// sso单点登录 通过票据 调用接口获取token\r\nexport function accessToken(params) {\r\n  return request({\r\n    url: \"/auth/chiWeb/getToken\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// SSO登录 - 获取SSO登录地址\r\nexport function getSSOLoginUrl(redirect) {\r\n  return request({\r\n    url: \"/sso/loginUrl\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"get\",\r\n    params: { redirect },\r\n  });\r\n}\r\n\r\n// SSO登录 - 处理SSO回调\r\nexport function handleSSOCallback(code, state) {\r\n  return request({\r\n    url: \"/sso/callback\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"get\",\r\n    params: { code, state },\r\n  });\r\n}\r\n\r\n// SSO登录 - 通过临时key获取JWT token\r\nexport function getSSOTokenByKey(key) {\r\n  return request({\r\n    url: \"/sso/token\",\r\n    headers: {\r\n      isToken: false,\r\n    },\r\n    method: \"get\",\r\n    params: { key },\r\n    timeout: 30000, // SSO token获取可能需要更长时间\r\n  });\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAMA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AANA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACO,SAASC,SAASA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACrD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE;MAAER,QAAQ,EAARA,QAAQ;MAAEC,OAAO,EAAPA,OAAO;MAAEC,QAAQ,EAARA,QAAQ;MAAEO,QAAQ,EAAE;IAAK,CAAC;IACrDC,OAAO,EAAE,KAAK,CAAE;EAClB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACX,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACxD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE;MAAER,QAAQ,EAARA,QAAQ;MAAEC,OAAO,EAAPA,OAAO;MAAEC,QAAQ,EAARA,QAAQ;MAAEO,QAAQ,EAAE;IAAK,CAAC;IACrDC,OAAO,EAAE,KAAK,CAAE;EAClB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,KAAKA,CAACZ,QAAQ,EAAEE,QAAQ,EAAEW,IAAI,EAAEC,IAAI,EAAE;EACpD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE;MAAER,QAAQ,EAARA,QAAQ;MAAEE,QAAQ,EAARA,QAAQ;MAAED,OAAO,EAAEY,IAAI;MAAEC,IAAI,EAAJA;IAAK;EAClD,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACf,QAAQ,EAAEE,QAAQ,EAAE;EAClD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE;MAAER,QAAQ,EAARA,QAAQ;MAAEE,QAAQ,EAARA;IAAS;EAC7B,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,SAASc,QAAQA,CAAChB,QAAQ,EAAEE,QAAQ,EAAEW,IAAI,EAAEC,IAAI,EAAE;EACvD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE;MAAER,QAAQ,EAARA,QAAQ;MAAEE,QAAQ,EAARA,QAAQ;MAAEW,IAAI,EAAJA,IAAI;MAAEC,IAAI,EAAJA,IAAI;MAAEL,QAAQ,EAAE;IAAK;EACzD,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,QAAQA,CAACT,IAAI,EAAE;EAC7B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBG,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASY,OAAOA,CAAA,EAAG;EACxB,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCG,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,MAAMA,CAAA,EAAG;EACvB,OAAO,IAAAjB,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBG,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAlB,gBAAO,EAAC;IACbC,GAAG,EAAE,OAAO;IACZC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,KAAK;IACbG,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAO,IAAApB,gBAAO,EAAC;IACb;IACAC,GAAG,EAAE,gCAAgC;IACrCG,MAAM,EAAE,KAAK;IACbgB,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACxB,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACvD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE;MAAER,QAAQ,EAARA,QAAQ;MAAEC,OAAO,EAAPA,OAAO;MAAEC,QAAQ,EAARA;IAAS,CAAC;IACrCQ,OAAO,EAAE,KAAK,CAAE;EAClB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,WAAWA,CAACF,MAAM,EAAE;EAClC,OAAO,IAAApB,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BG,MAAM,EAAE,KAAK;IACbgB,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,cAAcA,CAACC,QAAQ,EAAE;EACvC,OAAO,IAAAxB,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,KAAK;IACbgB,MAAM,EAAE;MAAEI,QAAQ,EAARA;IAAS;EACrB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACf,IAAI,EAAEgB,KAAK,EAAE;EAC7C,OAAO,IAAA1B,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,KAAK;IACbgB,MAAM,EAAE;MAAEV,IAAI,EAAJA,IAAI;MAAEgB,KAAK,EAALA;IAAM;EACxB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EACpC,OAAO,IAAA5B,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,KAAK;IACbgB,MAAM,EAAE;MAAEQ,GAAG,EAAHA;IAAI,CAAC;IACfrB,OAAO,EAAE,KAAK,CAAE;EAClB,CAAC,CAAC;AACJ", "ignoreList": []}]}