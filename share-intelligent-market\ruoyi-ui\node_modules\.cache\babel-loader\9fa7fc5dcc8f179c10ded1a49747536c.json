{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\TopNav\\index.vue", "mtime": 1750151094156}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_router", "require", "hideList", "_default", "exports", "default", "data", "visibleNumber", "currentIndex", "undefined", "computed", "theme", "$store", "state", "settings", "topMenus", "routers", "map", "menu", "hidden", "path", "push", "children", "permission", "topbarRouters", "childrenMenus", "_this", "router", "item", "parentPath", "ishttp", "constantRoutes", "concat", "activeMenu", "$route", "activePath", "lastIndexOf", "indexOf", "tmpPath", "substring", "length", "dispatch", "activeRoutes", "beforeMount", "window", "addEventListener", "setVisibleNumber", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "mounted", "methods", "width", "document", "body", "getBoundingClientRect", "parseInt", "handleSelect", "key", "keyP<PERSON>", "route", "find", "open", "$router", "routes", "commit", "url"], "sources": ["src/components/TopNav/index.vue"], "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item :style=\"{'--theme': theme}\" :index=\"item.path\" :key=\"index\" v-if=\"index < visibleNumber\"\r\n        ><svg-icon :icon-class=\"item.meta.icon\" />\r\n        {{ item.meta.title }}</el-menu-item\r\n      >\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu :style=\"{'--theme': theme}\" index=\"more\" v-if=\"topMenus.length > visibleNumber\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          :index=\"item.path\"\r\n          :key=\"index\"\r\n          v-if=\"index >= visibleNumber\"\r\n          ><svg-icon :icon-class=\"item.meta.icon\" />\r\n          {{ item.meta.title }}</el-menu-item\r\n        >\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from \"@/router\";\r\n\r\n// 隐藏侧边栏路由\r\nconst hideList = ['/index', '/user/profile'];\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined\r\n    };\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      let topMenus = [];\r\n      this.routers.map((menu) => {\r\n        if (menu.hidden !== true) {\r\n          // 兼容顶部栏一级菜单内部跳转\r\n          if (menu.path === \"/\") {\r\n              topMenus.push(menu.children[0]);\r\n          } else {\r\n              topMenus.push(menu);\r\n          }\r\n        }\r\n      });\r\n      return topMenus;\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters;\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = [];\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            if(router.path === \"/\") {\r\n              router.children[item].path = \"/\" + router.children[item].path;\r\n            } else {\r\n              if(!this.ishttp(router.children[item].path)) {\r\n                router.children[item].path = router.path + \"/\" + router.children[item].path;\r\n              }\r\n            }\r\n            router.children[item].parentPath = router.path;\r\n          }\r\n          childrenMenus.push(router.children[item]);\r\n        }\r\n      });\r\n      return constantRoutes.concat(childrenMenus);\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path;\r\n      let activePath = path;\r\n      if (path !== undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) === -1) {\r\n        const tmpPath = path.substring(1, path.length);\r\n        activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"));\r\n        this.$store.dispatch('app/toggleSideBarHide', false);\r\n      } else if(!this.$route.children) {\r\n        activePath = path;\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n      this.activeRoutes(activePath);\r\n      return activePath;\r\n    },\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber();\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width / 3;\r\n      this.visibleNumber = parseInt(width / 85);\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      this.currentIndex = key;\r\n      const route = this.routers.find(item => item.path === key);\r\n      if (this.ishttp(key)) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, \"_blank\");\r\n      } else if (!route || !route.children) {\r\n        // 没有子路由路径内部打开\r\n        this.$router.push({ path: key });\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      } else {\r\n        // 显示左侧联动菜单\r\n        this.activeRoutes(key);\r\n        this.$store.dispatch('app/toggleSideBarHide', false);\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = [];\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\r\n            routes.push(item);\r\n          }\r\n        });\r\n      }\r\n      if(routes.length > 0) {\r\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes);\r\n      }\r\n    },\r\n    ishttp(url) {\r\n      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.topmenu-container.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\r\n  border-bottom: 2px solid #{'var(--theme)'} !important;\r\n  color: #303133;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA8BA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA,IAAAC,QAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,YAAA,EAAAC;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;IACA;IACA;IACAI,QAAA,WAAAA,SAAA;MACA,IAAAA,QAAA;MACA,KAAAC,OAAA,CAAAC,GAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,MAAA;UACA;UACA,IAAAD,IAAA,CAAAE,IAAA;YACAL,QAAA,CAAAM,IAAA,CAAAH,IAAA,CAAAI,QAAA;UACA;YACAP,QAAA,CAAAM,IAAA,CAAAH,IAAA;UACA;QACA;MACA;MACA,OAAAH,QAAA;IACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAJ,MAAA,CAAAC,KAAA,CAAAU,UAAA,CAAAC,aAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,aAAA;MACA,KAAAT,OAAA,CAAAC,GAAA,WAAAU,MAAA;QACA,SAAAC,IAAA,IAAAD,MAAA,CAAAL,QAAA;UACA,IAAAK,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAC,UAAA,KAAApB,SAAA;YACA,IAAAkB,MAAA,CAAAP,IAAA;cACAO,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAR,IAAA,SAAAO,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAR,IAAA;YACA;cACA,KAAAM,KAAA,CAAAI,MAAA,CAAAH,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAR,IAAA;gBACAO,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAR,IAAA,GAAAO,MAAA,CAAAP,IAAA,SAAAO,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAR,IAAA;cACA;YACA;YACAO,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAC,UAAA,GAAAF,MAAA,CAAAP,IAAA;UACA;UACAK,aAAA,CAAAJ,IAAA,CAAAM,MAAA,CAAAL,QAAA,CAAAM,IAAA;QACA;MACA;MACA,OAAAG,sBAAA,CAAAC,MAAA,CAAAP,aAAA;IACA;IACA;IACAQ,UAAA,WAAAA,WAAA;MACA,IAAAb,IAAA,QAAAc,MAAA,CAAAd,IAAA;MACA,IAAAe,UAAA,GAAAf,IAAA;MACA,IAAAA,IAAA,KAAAX,SAAA,IAAAW,IAAA,CAAAgB,WAAA,aAAAlC,QAAA,CAAAmC,OAAA,CAAAjB,IAAA;QACA,IAAAkB,OAAA,GAAAlB,IAAA,CAAAmB,SAAA,IAAAnB,IAAA,CAAAoB,MAAA;QACAL,UAAA,SAAAG,OAAA,CAAAC,SAAA,IAAAD,OAAA,CAAAD,OAAA;QACA,KAAAzB,MAAA,CAAA6B,QAAA;MACA,iBAAAP,MAAA,CAAAZ,QAAA;QACAa,UAAA,GAAAf,IAAA;QACA,KAAAR,MAAA,CAAA6B,QAAA;MACA;MACA,KAAAC,YAAA,CAAAP,UAAA;MACA,OAAAA,UAAA;IACA;EACA;EACAQ,WAAA,WAAAA,YAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,gBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,gBAAA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAH,gBAAA;EACA;EACAI,OAAA;IACA;IACAJ,gBAAA,WAAAA,iBAAA;MACA,IAAAK,KAAA,GAAAC,QAAA,CAAAC,IAAA,CAAAC,qBAAA,GAAAH,KAAA;MACA,KAAA5C,aAAA,GAAAgD,QAAA,CAAAJ,KAAA;IACA;IACA;IACAK,YAAA,WAAAA,aAAAC,GAAA,EAAAC,OAAA;MACA,KAAAlD,YAAA,GAAAiD,GAAA;MACA,IAAAE,KAAA,QAAA3C,OAAA,CAAA4C,IAAA,WAAAhC,IAAA;QAAA,OAAAA,IAAA,CAAAR,IAAA,KAAAqC,GAAA;MAAA;MACA,SAAA3B,MAAA,CAAA2B,GAAA;QACA;QACAb,MAAA,CAAAiB,IAAA,CAAAJ,GAAA;MACA,YAAAE,KAAA,KAAAA,KAAA,CAAArC,QAAA;QACA;QACA,KAAAwC,OAAA,CAAAzC,IAAA;UAAAD,IAAA,EAAAqC;QAAA;QACA,KAAA7C,MAAA,CAAA6B,QAAA;MACA;QACA;QACA,KAAAC,YAAA,CAAAe,GAAA;QACA,KAAA7C,MAAA,CAAA6B,QAAA;MACA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAe,GAAA;MACA,IAAAM,MAAA;MACA,SAAAtC,aAAA,SAAAA,aAAA,CAAAe,MAAA;QACA,KAAAf,aAAA,CAAAR,GAAA,WAAAW,IAAA;UACA,IAAA6B,GAAA,IAAA7B,IAAA,CAAAC,UAAA,IAAA4B,GAAA,qBAAA7B,IAAA,CAAAR,IAAA;YACA2C,MAAA,CAAA1C,IAAA,CAAAO,IAAA;UACA;QACA;MACA;MACA,IAAAmC,MAAA,CAAAvB,MAAA;QACA,KAAA5B,MAAA,CAAAoD,MAAA,wBAAAD,MAAA;MACA;IACA;IACAjC,MAAA,WAAAA,OAAAmC,GAAA;MACA,OAAAA,GAAA,CAAA5B,OAAA,sBAAA4B,GAAA,CAAA5B,OAAA;IACA;EACA;AACA", "ignoreList": []}]}