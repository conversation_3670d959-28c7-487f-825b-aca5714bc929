{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\settings.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\settings.js", "mtime": 1750151094194}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSB7CiAgLyoqDQogICAqIOS+p+i+ueagj+S4u+mimCDmt7HoibLkuLvpoph0aGVtZS1kYXJr77yM5rWF6Imy5Li76aKYdGhlbWUtbGlnaHQNCiAgICovCiAgc2lkZVRoZW1lOiAndGhlbWUtZGFyaycsCiAgLyoqDQogICAqIOaYr+WQpuezu+e7n+W4g+WxgOmFjee9rg0KICAgKi8KICBzaG93U2V0dGluZ3M6IGZhbHNlLAogIC8qKg0KICAgKiDmmK/lkKbmmL7npLrpobbpg6jlr7zoiKoNCiAgICovCiAgdG9wTmF2OiBmYWxzZSwKICAvKioNCiAgICog5piv5ZCm5pi+56S6IHRhZ3NWaWV3DQogICAqLwogIHRhZ3NWaWV3OiB0cnVlLAogIC8qKg0KICAgKiDmmK/lkKblm7rlrprlpLTpg6gNCiAgICovCiAgZml4ZWRIZWFkZXI6IGZhbHNlLAogIC8qKg0KICAgKiDmmK/lkKbmmL7npLpsb2dvDQogICAqLwogIHNpZGViYXJMb2dvOiB0cnVlLAogIC8qKg0KICAgKiDmmK/lkKbmmL7npLrliqjmgIHmoIfpopgNCiAgICovCiAgZHluYW1pY1RpdGxlOiBmYWxzZSwKICAvKioNCiAgICogQHR5cGUge3N0cmluZyB8IGFycmF5fSAncHJvZHVjdGlvbicgfCBbJ3Byb2R1Y3Rpb24nLCAnZGV2ZWxvcG1lbnQnXQ0KICAgKiBAZGVzY3JpcHRpb24gTmVlZCBzaG93IGVyciBsb2dzIGNvbXBvbmVudC4NCiAgICogVGhlIGRlZmF1bHQgaXMgb25seSB1c2VkIGluIHRoZSBwcm9kdWN0aW9uIGVudg0KICAgKiBJZiB5b3Ugd2FudCB0byBhbHNvIHVzZSBpdCBpbiBkZXYsIHlvdSBjYW4gcGFzcyBbJ3Byb2R1Y3Rpb24nLCAnZGV2ZWxvcG1lbnQnXQ0KICAgKi8KICBlcnJvckxvZzogJ3Byb2R1Y3Rpb24nCn07"}, {"version": 3, "names": ["module", "exports", "sideTheme", "showSettings", "topNav", "tagsView", "fixedHeader", "sidebarLogo", "dynamicTitle", "errorLog"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/settings.js"], "sourcesContent": ["module.exports = {\r\n  /**\r\n   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light\r\n   */\r\n  sideTheme: 'theme-dark',\r\n\r\n  /**\r\n   * 是否系统布局配置\r\n   */\r\n  showSettings: false,\r\n\r\n  /**\r\n   * 是否显示顶部导航\r\n   */\r\n  topNav: false,\r\n\r\n  /**\r\n   * 是否显示 tagsView\r\n   */\r\n  tagsView: true,\r\n\r\n  /**\r\n   * 是否固定头部\r\n   */\r\n  fixedHeader: false,\r\n\r\n  /**\r\n   * 是否显示logo\r\n   */\r\n  sidebarLogo: true,\r\n\r\n  /**\r\n   * 是否显示动态标题\r\n   */\r\n  dynamicTitle: false,\r\n\r\n  /**\r\n   * @type {string | array} 'production' | ['production', 'development']\r\n   * @description Need show err logs component.\r\n   * The default is only used in the production env\r\n   * If you want to also use it in dev, you can pass ['production', 'development']\r\n   */\r\n  errorLog: 'production'\r\n}\r\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EACf;AACF;AACA;EACEC,SAAS,EAAE,YAAY;EAEvB;AACF;AACA;EACEC,YAAY,EAAE,KAAK;EAEnB;AACF;AACA;EACEC,MAAM,EAAE,KAAK;EAEb;AACF;AACA;EACEC,QAAQ,EAAE,IAAI;EAEd;AACF;AACA;EACEC,WAAW,EAAE,KAAK;EAElB;AACF;AACA;EACEC,WAAW,EAAE,IAAI;EAEjB;AACF;AACA;EACEC,YAAY,EAAE,KAAK;EAEnB;AACF;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}]}