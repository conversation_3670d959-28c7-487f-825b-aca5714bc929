16:29:12.188 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
16:29:13.173 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2a4425ac-c9f1-49e3-b266-9a020305a273_config-0
16:29:13.241 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 6 values 
16:29:13.281 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
16:29:13.293 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
16:29:13.470 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 174 ms to scan 222 urls, producing 0 keys and 0 values 
16:29:13.479 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:29:13.496 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
16:29:13.506 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
16:29:13.703 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 194 ms to scan 222 urls, producing 0 keys and 0 values 
16:29:13.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:29:13.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/777970377
16:29:13.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1436944861
16:29:13.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:29:13.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:29:13.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:29:15.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750494555184_127.0.0.1_57423
16:29:15.449 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] Notify connected event to listeners.
16:29:15.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:29:15.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a4425ac-c9f1-49e3-b266-9a020305a273_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1048274391
16:29:15.549 [main] INFO  c.r.g.RuoYiGenApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
16:29:18.617 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9202"]
16:29:18.618 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:29:18.618 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
16:29:18.935 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:29:19.503 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
16:59:38.160 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
16:59:39.297 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0
16:59:39.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
16:59:39.416 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
16:59:39.429 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
16:59:39.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 192 ms to scan 222 urls, producing 0 keys and 0 values 
16:59:39.635 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
16:59:39.653 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
16:59:39.664 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:59:39.853 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 187 ms to scan 222 urls, producing 0 keys and 0 values 
16:59:39.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:59:39.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/788592721
16:59:39.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1107779742
16:59:39.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:59:39.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:59:39.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:59:41.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750496381298_127.0.0.1_60404
16:59:41.570 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] Notify connected event to listeners.
16:59:41.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:59:41.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb030328-3cdc-4c19-b1a3-81aeeec408f2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1919497442
16:59:41.671 [main] INFO  c.r.g.RuoYiGenApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
16:59:44.767 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9202"]
16:59:44.769 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:59:44.769 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
16:59:45.113 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:59:45.619 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:00:22.179 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:00:23.195 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0
17:00:23.280 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
17:00:23.323 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
17:00:23.336 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
17:00:23.530 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 191 ms to scan 222 urls, producing 0 keys and 0 values 
17:00:23.539 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
17:00:23.550 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
17:00:23.562 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
17:00:23.731 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 168 ms to scan 222 urls, producing 0 keys and 0 values 
17:00:23.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:00:23.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1783418615
17:00:23.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1765350920
17:00:23.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:00:23.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:00:23.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:00:25.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750496425218_127.0.0.1_60662
17:00:25.501 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] Notify connected event to listeners.
17:00:25.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:00:25.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7132c1e-9ef0-4676-a23d-7890e56005c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1895203464
17:00:25.608 [main] INFO  c.r.g.RuoYiGenApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "prod"
17:00:28.814 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9202"]
17:00:28.815 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:00:28.815 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:00:29.143 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:00:29.636 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
