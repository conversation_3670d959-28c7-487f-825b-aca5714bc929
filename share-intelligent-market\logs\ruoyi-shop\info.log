11:48:32.381 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:48:33.609 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 83987024-180c-4e8d-97ec-8955a1533f67_config-0
11:48:33.699 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:33.771 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:33.793 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:34.155 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 357 ms to scan 293 urls, producing 0 keys and 0 values 
11:48:34.177 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:34.202 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:34.225 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:34.472 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 244 ms to scan 293 urls, producing 0 keys and 0 values 
11:48:34.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:34.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/538375433
11:48:34.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1864007931
11:48:34.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:34.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:34.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:36.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477716413_127.0.0.1_63860
11:48:36.765 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] Notify connected event to listeners.
11:48:36.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:36.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83987024-180c-4e8d-97ec-8955a1533f67_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/355222677
11:48:36.975 [main] INFO  c.r.s.RuoYiShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:48:41.895 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,53] - Automatically configure Seata
11:48:41.926 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is registry
11:48:42.164 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
11:48:42.199 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is file.conf
11:48:42.201 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
11:48:42.535 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,189] - Initializing Global Transaction Clients ... 
11:48:42.870 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
11:48:42.871 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,197] - Transaction Manager Client is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
11:48:42.904 [main] INFO  i.s.r.d.AsyncWorker - [<init>,71] - Async Commit Buffer Limit: 10000
11:48:42.905 [main] INFO  i.s.r.d.x.ResourceManagerXA - [init,40] - ResourceManagerXA init ...
11:48:42.920 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
11:48:42.920 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,202] - Resource Manager is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
11:48:42.920 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,206] - Global Transaction Clients are initialized. 
11:48:45.117 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9707"]
11:48:45.118 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:48:45.118 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:48:45.491 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:48:47.079 [main] INFO  i.s.s.a.d.SeataAutoDataSourceProxyCreator - [getAdvicesAndAdvisorsForBean,47] - Auto proxy of [dataSource]
11:48:47.857 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
11:48:58.093 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:48:59.162 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 97d40ce4-4160-446a-8190-4814372cbde5
11:48:59.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] RpcClient init label, labels = {module=naming, source=sdk}
11:48:59.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:48:59.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:48:59.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:48:59.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:59.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477739177_127.0.0.1_64318
11:48:59.291 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Notify connected event to listeners.
11:48:59.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:59.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/355222677
11:49:03.483 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9707"]
11:49:03.547 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-shop 192.168.0.68:9707 register finished
11:49:03.938 [main] INFO  c.r.s.RuoYiShopApplication - [logStarted,61] - Started RuoYiShopApplication in 32.449 seconds (JVM running for 34.028)
11:49:04.137 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop-dev.yaml, group=DEFAULT_GROUP
11:49:04.139 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop, group=DEFAULT_GROUP
11:49:04.139 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop.yaml, group=DEFAULT_GROUP
11:49:04.234 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Receive server push request, request = NotifySubscriberRequest, requestId = 54
11:49:04.239 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97d40ce4-4160-446a-8190-4814372cbde5] Ack server push request, request = NotifySubscriberRequest, requestId = 54
11:49:05.141 [RMI TCP Connection(11)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
