{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\role\\authUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\role\\authUser.vue", "mtime": 1750151094298}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["authUser.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "authUser.vue", "sourceRoot": "src/views/system/role", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n     <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"用户名称\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户名称\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n        <el-input\r\n          v-model=\"queryParams.phonenumber\"\r\n          placeholder=\"请输入手机号码\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"openSelectUser\"\r\n          v-hasPermi=\"['system:role:add']\"\r\n        >添加用户</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-circle-close\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"cancelAuthUserAll\"\r\n          v-hasPermi=\"['system:role:remove']\"\r\n        >批量取消授权</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          @click=\"handleClose\"\r\n        >关闭</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"用户名称\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"用户昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"手机\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-circle-close\"\r\n            @click=\"cancelAuthUser(scope.row)\"\r\n            v-hasPermi=\"['system:role:remove']\"\r\n          >取消授权</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n    <select-user ref=\"select\" :roleId=\"queryParams.roleId\" @ok=\"handleQuery\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { allocatedUserList, authUserCancel, authUserCancelAll } from \"@/api/system/role\";\r\nimport selectUser from \"./selectUser\";\r\n\r\nexport default {\r\n  name: \"AuthUser\",\r\n  dicts: ['sys_normal_disable'],\r\n  components: { selectUser },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中用户组\r\n      userIds: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        roleId: undefined,\r\n        userName: undefined,\r\n        phonenumber: undefined\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    const roleId = this.$route.params && this.$route.params.roleId;\r\n    if (roleId) {\r\n      this.queryParams.roleId = roleId;\r\n      this.getList();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询授权用户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      allocatedUserList(this.queryParams).then(response => {\r\n          this.userList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    // 返回按钮\r\n    handleClose() {\r\n      const obj = { path: \"/system/role\" };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.userIds = selection.map(item => item.userId)\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 打开授权用户表弹窗 */\r\n    openSelectUser() {\r\n      this.$refs.select.show();\r\n    },\r\n    /** 取消授权按钮操作 */\r\n    cancelAuthUser(row) {\r\n      const roleId = this.queryParams.roleId;\r\n      this.$modal.confirm('确认要取消该用户\"' + row.userName + '\"角色吗？').then(function() {\r\n        return authUserCancel({ userId: row.userId, roleId: roleId });\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"取消授权成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量取消授权按钮操作 */\r\n    cancelAuthUserAll(row) {\r\n      const roleId = this.queryParams.roleId;\r\n      const userIds = this.userIds.join(\",\");\r\n      this.$modal.confirm('是否取消选中用户授权数据项？').then(function() {\r\n        return authUserCancelAll({ roleId: roleId, userIds: userIds });\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"取消授权成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>"]}]}