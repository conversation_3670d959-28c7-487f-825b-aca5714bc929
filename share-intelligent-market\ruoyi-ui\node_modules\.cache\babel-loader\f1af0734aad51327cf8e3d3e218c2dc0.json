{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\offer.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\offer.vue", "mtime": 1750151094273}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_util", "require", "_offer", "_lessDetails", "_interopRequireDefault", "_moreDetails", "components", "lessDetails", "moreDetails", "data", "classifyOptions", "classify", "loading", "showSearch", "total", "form", "queryParams", "pageNum", "pageSize", "inquiry_no", "undefined", "offer_no", "enterprise_name", "list", "created", "getClassify", "getList", "methods", "handleDetail", "row", "type", "$refs", "open", "id", "_this", "listClassify", "then", "res", "_this2", "classify_id", "classify2_id", "length", "classify3_id", "listData", "response", "count", "handleQuery", "reset<PERSON><PERSON>y", "resetForm"], "sources": ["src/views/project/offer/offer.vue"], "sourcesContent": ["<template>\r\n  <!-- 报价单管理 -->\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" size='small' v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\">\r\n            <el-cascader \r\n              filterable \r\n              clearable \r\n              placeholder=\"选择产品分类\" \r\n              v-model=\"classify\" \r\n              :options=\"classifyOptions\"\r\n              :props='{label: \"name\", value: \"id\",checkStrictly: true}'></el-cascader>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"inquiry_no\">\r\n            <el-input clearable v-model=\"queryParams.inquiry_no\" placeholder=\"输入询价单号\" style=\"width: 200px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"offer_no\">\r\n            <el-input clearable v-model=\"queryParams.offer_no\" placeholder=\"输入报价单号\" style=\"width: 200px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"enterprise_name\">\r\n            <el-input \r\n              clearable \r\n              v-model=\"queryParams.enterprise_name\" \r\n              placeholder=\"输入报价公司\" :maxlength='50'\r\n              style=\"width: 300px\">\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"50\" />\r\n          <el-table-column label=\"报价日期\" align=\"center\" width=\"120\" prop=\"offer_date\" />\r\n          <el-table-column label=\"报价单编号\" align=\"center\" width=\"140\" prop=\"offer_no\" />\r\n          <el-table-column label=\"报价公司\" align=\"center\" width=\"280\" prop=\"enterprise_name\" />\r\n          <el-table-column label=\"联系人\" align=\"center\" width=\"120\" prop=\"linker\" />\r\n          <el-table-column label=\"联系电话\" align=\"center\" width=\"120\" prop=\"linkphone\" />\r\n          <el-table-column label=\"操作者\" align=\"center\" width=\"120\" prop=\"operator\" />\r\n          <el-table-column label=\"报价信息\" align=\"center\" prop=\"offer_version\">\r\n            <template slot-scope=\"scope\">\r\n              {{scope.row.offer_version}}次报价\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"询价截止日期\" width=\"120\" align=\"center\" prop=\"deadline\" />\r\n          <el-table-column label=\"询价标题\" width=\"280\" align=\"center\" prop=\"inquiry_title\" />\r\n          <el-table-column label=\"询价公司\" width=\"280\" align=\"center\" prop=\"inquiry_enterprise_name\" />\r\n          <el-table-column label=\"询价类型\" width=\"120\" align=\"center\" prop=\"typeStr\" />\r\n          <el-table-column label=\"询价状态\" width=\"120\" align=\"center\" prop=\"status\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\" size=\"mini\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 急速报价详情 -->\r\n    <lessDetails ref=\"lessDetails\"></lessDetails>\r\n    <!-- 精准报价详情 -->\r\n    <moreDetails ref=\"moreDetails\"></moreDetails>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    listClassify\r\n  } from '@/api/tool/util';\r\n  import {\r\n    listData\r\n  } from '@/api/project/offer';\r\n  import lessDetails from \"./components/lessDetails.vue\";\r\n  import moreDetails from \"./components/moreDetails.vue\";\r\n  export default {\r\n    components: {\r\n      lessDetails,\r\n      moreDetails,\r\n    },\r\n    data() {\r\n      return {\r\n        classifyOptions: [],\r\n        classify:{},\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          inquiry_no: undefined,\r\n          offer_no: undefined,\r\n          enterprise_name: undefined\r\n        },\r\n        // 表格数据\r\n        list: [],\r\n      };\r\n    },\r\n    created() {\r\n      this.getClassify()\r\n      this.getList()\r\n    },\r\n    methods: {\r\n      handleDetail(row) {\r\n        if(row.type=='NORMAL'){\r\n          this.$refs.lessDetails.open(row.id)\r\n        }\r\n        else{\r\n          this.$refs.moreDetails.open(row.id)\r\n        }\r\n      },\r\n      getClassify() {\r\n        listClassify().then(res => {\r\n          this.classifyOptions = res.data;\r\n        })\r\n      },\r\n      /** 查询企业信息列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        if (this.classify) {\r\n          this.queryParams.classify_id = this.classify[0]\r\n          this.queryParams.classify2_id = this.classify.length > 1 ? this.classify[1] : -1\r\n          this.queryParams.classify3_id = this.classify.length > 2 ? this.classify[2] : -1\r\n        } else {\r\n          this.queryParams.classify_id = -1\r\n          this.queryParams.classify2_id = -1\r\n          this.queryParams.classify3_id = -1\r\n        }\r\n        listData(this.queryParams).then((response) => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n</style>\r\n"], "mappings": ";;;;;;;AAgFA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAD,OAAA;AAGA,IAAAE,YAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,YAAA,GAAAD,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAK,UAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA;MACAC,QAAA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,eAAA,EAAAF;MACA;MACA;MACAG,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA,KAAAC,KAAA,CAAAxB,WAAA,CAAAyB,IAAA,CAAAH,GAAA,CAAAI,EAAA;MACA,OACA;QACA,KAAAF,KAAA,CAAAvB,WAAA,CAAAwB,IAAA,CAAAH,GAAA,CAAAI,EAAA;MACA;IACA;IACAR,WAAA,WAAAA,YAAA;MAAA,IAAAS,KAAA;MACA,IAAAC,kBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAxB,eAAA,GAAA2B,GAAA,CAAA5B,IAAA;MACA;IACA;IACA,eACAiB,OAAA,WAAAA,QAAA;MAAA,IAAAY,MAAA;MACA,KAAA1B,OAAA;MACA,SAAAD,QAAA;QACA,KAAAK,WAAA,CAAAuB,WAAA,QAAA5B,QAAA;QACA,KAAAK,WAAA,CAAAwB,YAAA,QAAA7B,QAAA,CAAA8B,MAAA,YAAA9B,QAAA;QACA,KAAAK,WAAA,CAAA0B,YAAA,QAAA/B,QAAA,CAAA8B,MAAA,YAAA9B,QAAA;MACA;QACA,KAAAK,WAAA,CAAAuB,WAAA;QACA,KAAAvB,WAAA,CAAAwB,YAAA;QACA,KAAAxB,WAAA,CAAA0B,YAAA;MACA;MACA,IAAAC,eAAA,OAAA3B,WAAA,EAAAoB,IAAA,WAAAQ,QAAA;QACAN,MAAA,CAAAf,IAAA,GAAAqB,QAAA,CAAAnC,IAAA;QACA6B,MAAA,CAAAxB,KAAA,GAAA8B,QAAA,CAAAC,KAAA;QACAP,MAAA,CAAA1B,OAAA;MACA;IACA;IACA,aACAkC,WAAA,WAAAA,YAAA;MACA,KAAA9B,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IACA,aACAqB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;EACA;AACA", "ignoreList": []}]}