{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\components\\orderDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\components\\orderDetails.vue", "mtime": 1750151094267}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_list", "require", "data", "btnload", "form", "dform", "logistics_no", "linker", "proof", "linkphone", "pays", "activetab", "delivers", "dialogVisible", "deliverOpen", "rules", "shipped_qty", "required", "message", "trigger", "created", "methods", "get<PERSON>ogo", "e", "$refs", "clearValidate", "open", "orderId", "_this", "getData", "then", "res", "payData", "order_id", "deliverData", "handlePay", "row", "_this2", "$confirm", "type", "id", "confirmPay", "$message", "pay_status", "pay_statusStr", "handleDeliver", "order_item_id", "left_qty", "quantity", "shiped_quantity", "opDeliver", "_this3", "validate", "confirmDeliver", "$modal", "msgError"], "sources": ["src/views/order/components/orderDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-dialog\r\n      width=\"80%\"\r\n      title=\"订单详情\"\r\n      :visible.sync=\"dialogVisible\"\r\n      append-to-body\r\n      center\r\n    >\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        title=\"基本信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"订单类型\">{{\r\n          form.order_type_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单状态\">\r\n          <el-tag size=\"mini\" type=\"primary\">{{ form.status_str }}</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"订单号\">{{\r\n          form.order_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"下单时间\">{{\r\n          form.create_time\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"付款方式\">{{\r\n          form.payment_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采状态\">{{\r\n          form.central_status_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采付款状态\">{{\r\n          form.central_pay_status_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"操作员\">{{\r\n          form.operator\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单总价\">{{\r\n          form.total_price\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采定金\">{{\r\n          form.deposit\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"物流单号\">{{\r\n          form.logistics_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"合同编号\">{{\r\n          form.contract_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"地址\">{{\r\n          form.address\r\n        }}</el-descriptions-item>\r\n        <!-- <el-descriptions-item label=\"订单条款\">{{\r\n                    form.terms\r\n                }}</el-descriptions-item> -->\r\n        <el-descriptions-item label=\"订单备注\">{{\r\n          form.remark\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        style=\"margin-top: 20px\"\r\n        title=\"需方信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"需方名称\">{{\r\n          form.demand_name\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方代理\">{{\r\n          form.demand_proxy\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方电话\">{{\r\n          form.demand_phone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方地址\">{{\r\n          form.demand_location\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        style=\"margin-top: 20px\"\r\n        title=\"供方信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"供方名称\">{{\r\n          form.supply_name\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方代理\">{{\r\n          form.supply_proxy\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方电话\">{{\r\n          form.supply_phone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方地址\">{{\r\n          form.supply_location\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-tabs v-model=\"activetab\" type=\"card\" style=\"margin-top: 20px\">\r\n        <el-tab-pane label=\"订单明细\" name=\"items\">\r\n          <el-table :data=\"form.items\">\r\n            <el-table-column label=\"物料分类\" align=\"center\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.classify_name }}-{{ scope.row.classify2_name }}-{{\r\n                  scope.row.classify3_name\r\n                }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料编号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_name\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"规格\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"specs\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"单位\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"unit\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物品数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"已发数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"shiped_quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"品牌\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"brand\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"包装\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pack\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"税率\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"tax_rate\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"商品总价\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"total_price\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"运费\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"freight\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"明细总价\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"total_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"期望交货日期\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"delivery_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"交货日期\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"delivered_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              fixed=\"right\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.shiped_quantity < scope.row.quantity\"\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleDeliver(scope.row)\"\r\n                  >确认发货</el-button\r\n                >\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"付款申请单\" name=\"pay\">\r\n          <el-table :data=\"pays\">\r\n            <el-table-column\r\n              label=\"申请单号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"request_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"申请时间\"\r\n              align=\"center\"\r\n              width=\"160\"\r\n              prop=\"create_time\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"申请金额\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"total_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付方式\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"paymentStr\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"币种\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"currency\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付比例\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_ratio\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付金额\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付状态\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_statusStr\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付时间\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"pay_time\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付凭证\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"pay_proof\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-image\r\n                  v-if=\"scope.row.pay_proof\"\r\n                  style=\"width: 100px; height: 100px\"\r\n                  :src=\"scope.row.pay_proof\"\r\n                  :preview-src-list=\"[scope.row.pay_proof]\"\r\n                >\r\n                </el-image>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              align=\"center\"\r\n              width=\"80\"\r\n              fixed=\"right\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"\r\n                    scope.row.pay_status == 'WAIT' &&\r\n                    (scope.row.payment == 'OFFLINE' ||\r\n                      scope.row.payment == 'PERIOD')\r\n                  \"\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handlePay(scope.row)\"\r\n                  >确认</el-button\r\n                >\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"发货记录\" name=\"deliver\">\r\n          <el-table :data=\"delivers\">\r\n            <el-table-column\r\n              label=\"物流单号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"logistics_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"车牌号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"carnno\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"司机名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"linker\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"司机电话\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"linkphone\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              align=\"center\"\r\n              property=\"proof\"\r\n              label=\"发货凭证\"\r\n              width=\"100px\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"scope.row.proof\">\r\n                  <el-image\r\n                    style=\"width: 60px; height: 60px\"\r\n                    :src=\"scope.row.proof\"\r\n                    :preview-src-list=\"[scope.row.proof]\"\r\n                  >\r\n                  </el-image>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              align=\"center\"\r\n              property=\"proof\"\r\n              label=\"签收凭证\"\r\n              width=\"100px\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"scope.row.sproof\">\r\n                  <el-image\r\n                    style=\"width: 60px; height: 60px\"\r\n                    :src=\"scope.row.sproof\"\r\n                    :preview-src-list=\"[scope.row.sproof]\"\r\n                  >\r\n                  </el-image>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_name\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"单位\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"unit\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"规格\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"specs\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"品牌\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"brand\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"包装\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pack\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"期望交货日期\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"delivery_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"交货日期\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"delivered_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"发货数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"shipped_qty\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_qty\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收人\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_by\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收时间\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_time\"\r\n            >\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n    <el-dialog\r\n      width=\"40%\"\r\n      title=\"发货\"\r\n      :visible.sync=\"deliverOpen\"\r\n      append-to-body\r\n      center\r\n    >\r\n      <el-form\r\n        ref=\"dform\"\r\n        :rules=\"rules\"\r\n        :model=\"dform\"\r\n        label-width=\"110px\"\r\n        label-position=\"left\"\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"发货数量\" prop=\"shipped_qty\">\r\n              <el-input\r\n                type=\"number\"\r\n                v-model=\"dform.shipped_qty\"\r\n                placeholder=\"请输入发货数量\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物流号\" prop=\"logistics_no\">\r\n              <el-input\r\n                clearable\r\n                v-model=\"dform.logistics_no\"\r\n                placeholder=\"请输入物流号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"司机名称\" prop=\"linker\">\r\n              <el-input\r\n                v-model=\"dform.linker\"\r\n                placeholder=\"请输入司机名称\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"司机电话\" prop=\"linkphone\">\r\n              <el-input\r\n                clearable\r\n                v-model=\"dform.linkphone\"\r\n                placeholder=\"请输入物流号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"车牌号\" prop=\"carno\">\r\n              <el-input\r\n                v-model=\"dform.carno\"\r\n                placeholder=\"请输入车牌号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"发货凭证\" prop=\"proof\">\r\n              <ImageUpload\r\n                v-model=\"dform.proof\"\r\n                @input=\"getLogo\"\r\n                :limit=\"1\"\r\n                :isShowTip=\"false\"\r\n              ></ImageUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"deliverOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"opDeliver\" :loading=\"btnload\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getData,\r\n  payData,\r\n  deliverData,\r\n  confirmPay,\r\n  confirmDeliver,\r\n} from \"@/api/order/list\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      btnload: false,\r\n      form: {},\r\n      dform: {\r\n        logistics_no: \"\",\r\n        linker: \"\",\r\n        proof: \"\",\r\n        linkphone: \"\",\r\n      },\r\n      pays: [],\r\n      activetab: \"items\",\r\n      delivers: [],\r\n      dialogVisible: false,\r\n      deliverOpen: false,\r\n      rules: {\r\n        shipped_qty: [\r\n          {\r\n            required: true,\r\n            message: \"发货数量不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        logistics_no: [\r\n          {\r\n            required: true,\r\n            message: \"物料号不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {},\r\n  methods: {\r\n    getLogo(e) {\r\n      this.dform.proof = e;\r\n      if (this.dform.proof) {\r\n        this.$refs.dform.clearValidate(\"proof\");\r\n      }\r\n    },\r\n    open(orderId) {\r\n      this.dialogVisible = true;\r\n      getData(orderId).then((res) => {\r\n        this.form = res.data;\r\n      });\r\n      payData({\r\n        order_id: orderId,\r\n      }).then((res) => {\r\n        this.pays = res.data;\r\n      });\r\n      deliverData({\r\n        order_id: orderId,\r\n      }).then((res) => {\r\n        this.delivers = res.data;\r\n      });\r\n    },\r\n    handlePay(row) {\r\n      this.$confirm(\"是否确认该付款申请单?\", \"提示\", {\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        let data = {\r\n          id: row.id,\r\n        };\r\n        confirmPay(data).then((res) => {\r\n          this.$message({\r\n            type: \"success\",\r\n            message: \"操作成功!\",\r\n          });\r\n          row.pay_status = \"CONFIRM\";\r\n          row.pay_statusStr = \"已确认\";\r\n        });\r\n      });\r\n    },\r\n    handleDeliver(row) {\r\n      this.dform.order_item_id = row.id;\r\n      this.dform.order_id = row.order_id;\r\n      this.dform.left_qty = row.quantity - row.shiped_quantity;\r\n      this.dform.shipped_qty = row.quantity - row.shiped_quantity;\r\n      this.dform.logistics_no = \"\";\r\n      this.dform.linker = \"\";\r\n      this.dform.linkphone = \"\";\r\n      this.deliverOpen = true;\r\n    },\r\n    opDeliver() {\r\n      this.$refs.dform.validate((validate) => {\r\n        if (validate) {\r\n          if (this.dform.shipped_qty > this.dform.left_qty) {\r\n            this.$message({\r\n              type: \"error\",\r\n              message: \"发货数量不得多余总数量!\",\r\n            });\r\n            return;\r\n          }\r\n          this.btnload = true;\r\n          confirmDeliver(this.dform).then(() => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n            this.btnload = false;\r\n            this.deliverOpen = false;\r\n            this.open(this.dform.order_id);\r\n          });\r\n        } else {\r\n          this.$modal.msgError(\"请完善信息再提交!\");\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.item-form {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.wire {\r\n  background: #ccc;\r\n  height: 1px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.red {\r\n  color: red;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AA4jBA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAOA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;QACAC,YAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;MACA;MACAC,IAAA;MACAC,SAAA;MACAC,QAAA;MACAC,aAAA;MACAC,WAAA;MACAC,KAAA;QACAC,WAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAb,YAAA,GACA;UACAW,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,OAAA,WAAAA,QAAAC,CAAA;MACA,KAAAlB,KAAA,CAAAG,KAAA,GAAAe,CAAA;MACA,SAAAlB,KAAA,CAAAG,KAAA;QACA,KAAAgB,KAAA,CAAAnB,KAAA,CAAAoB,aAAA;MACA;IACA;IACAC,IAAA,WAAAA,KAAAC,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAf,aAAA;MACA,IAAAgB,aAAA,EAAAF,OAAA,EAAAG,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAxB,IAAA,GAAA2B,GAAA,CAAA7B,IAAA;MACA;MACA,IAAA8B,aAAA;QACAC,QAAA,EAAAN;MACA,GAAAG,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAlB,IAAA,GAAAqB,GAAA,CAAA7B,IAAA;MACA;MACA,IAAAgC,iBAAA;QACAD,QAAA,EAAAN;MACA,GAAAG,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAhB,QAAA,GAAAmB,GAAA,CAAA7B,IAAA;MACA;IACA;IACAiC,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,IAAA;MACA,GAAAT,IAAA;QACA,IAAA5B,IAAA;UACAsC,EAAA,EAAAJ,GAAA,CAAAI;QACA;QACA,IAAAC,gBAAA,EAAAvC,IAAA,EAAA4B,IAAA,WAAAC,GAAA;UACAM,MAAA,CAAAK,QAAA;YACAH,IAAA;YACArB,OAAA;UACA;UACAkB,GAAA,CAAAO,UAAA;UACAP,GAAA,CAAAQ,aAAA;QACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAAT,GAAA;MACA,KAAA/B,KAAA,CAAAyC,aAAA,GAAAV,GAAA,CAAAI,EAAA;MACA,KAAAnC,KAAA,CAAA4B,QAAA,GAAAG,GAAA,CAAAH,QAAA;MACA,KAAA5B,KAAA,CAAA0C,QAAA,GAAAX,GAAA,CAAAY,QAAA,GAAAZ,GAAA,CAAAa,eAAA;MACA,KAAA5C,KAAA,CAAAW,WAAA,GAAAoB,GAAA,CAAAY,QAAA,GAAAZ,GAAA,CAAAa,eAAA;MACA,KAAA5C,KAAA,CAAAC,YAAA;MACA,KAAAD,KAAA,CAAAE,MAAA;MACA,KAAAF,KAAA,CAAAI,SAAA;MACA,KAAAK,WAAA;IACA;IACAoC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAA3B,KAAA,CAAAnB,KAAA,CAAA+C,QAAA,WAAAA,QAAA;QACA,IAAAA,QAAA;UACA,IAAAD,MAAA,CAAA9C,KAAA,CAAAW,WAAA,GAAAmC,MAAA,CAAA9C,KAAA,CAAA0C,QAAA;YACAI,MAAA,CAAAT,QAAA;cACAH,IAAA;cACArB,OAAA;YACA;YACA;UACA;UACAiC,MAAA,CAAAhD,OAAA;UACA,IAAAkD,oBAAA,EAAAF,MAAA,CAAA9C,KAAA,EAAAyB,IAAA;YACAqB,MAAA,CAAAT,QAAA;cACAxB,OAAA;cACAqB,IAAA;YACA;YACAY,MAAA,CAAAhD,OAAA;YACAgD,MAAA,CAAArC,WAAA;YACAqC,MAAA,CAAAzB,IAAA,CAAAyB,MAAA,CAAA9C,KAAA,CAAA4B,QAAA;UACA;QACA;UACAkB,MAAA,CAAAG,MAAA,CAAAC,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}