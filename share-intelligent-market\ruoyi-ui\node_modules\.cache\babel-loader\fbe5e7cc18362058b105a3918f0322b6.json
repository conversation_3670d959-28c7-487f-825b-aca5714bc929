{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\RightPanel.vue", "mtime": 1750151094309}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_util", "require", "_vuedraggable", "_interopRequireDefault", "_TreeNodeDialog", "_index", "_IconsDialog", "_config", "dateTimeFormat", "date", "week", "month", "year", "datetime", "daterange", "monthrange", "datetimerange", "_default", "exports", "default", "components", "draggable", "TreeNodeDialog", "IconsDialog", "props", "data", "currentTab", "currentNode", "dialogVisible", "iconsVisible", "currentIconModel", "dateTypeOptions", "label", "value", "dateRangeTypeOptions", "colorFormatOptions", "justifyOptions", "layoutTreeProps", "node", "componentName", "concat", "vModel", "computed", "documentLink", "activeData", "document", "dateOptions", "type", "undefined", "tag", "tagList", "options", "inputComponents", "selectComponents", "methods", "addReg", "regList", "push", "pattern", "message", "addSelectItem", "addTreeItem", "idGlobal", "renderContent", "h", "_ref", "_this", "store", "click", "append", "remove", "children", "$set", "parent", "index", "findIndex", "d", "id", "splice", "addNode", "setOptionValue", "item", "val", "isNumberStr", "setDefaultValue", "Array", "isArray", "join", "indexOf", "onDefaultValueInput", "str", "defaultValue", "split", "map", "JSON", "parse", "onSwitchValueInput", "name", "setTimeValue", "valueFormat", "spanChange", "formConf", "span", "multipleChange", "dateTypeChange", "rangeChange", "min", "max", "rateTextChange", "rateScoreChange", "colorFormatChange", "<PERSON><PERSON><PERSON>", "Date", "openIconsDialog", "model", "setIcon", "tagChange", "tagIcon", "target", "find", "$emit"], "sources": ["src/views/tool/build/RightPanel.vue"], "sourcesContent": ["<template>\r\n  <div class=\"right-board\">\r\n    <el-tabs v-model=\"currentTab\" class=\"center-tabs\">\r\n      <el-tab-pane label=\"组件属性\" name=\"field\" />\r\n      <el-tab-pane label=\"表单属性\" name=\"form\" />\r\n    </el-tabs>\r\n    <div class=\"field-box\">\r\n      <a class=\"document-link\" target=\"_blank\" :href=\"documentLink\" title=\"查看组件文档\">\r\n        <i class=\"el-icon-link\" />\r\n      </a>\r\n      <el-scrollbar class=\"right-scrollbar\">\r\n        <!-- 组件属性 -->\r\n        <el-form v-show=\"currentTab==='field' && showField\" size=\"small\" label-width=\"90px\">\r\n          <el-form-item v-if=\"activeData.changeTag\" label=\"组件类型\">\r\n            <el-select\r\n              v-model=\"activeData.tagIcon\"\r\n              placeholder=\"请选择组件类型\"\r\n              :style=\"{width: '100%'}\"\r\n              @change=\"tagChange\"\r\n            >\r\n              <el-option-group v-for=\"group in tagList\" :key=\"group.label\" :label=\"group.label\">\r\n                <el-option\r\n                  v-for=\"item in group.options\"\r\n                  :key=\"item.label\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.tagIcon\"\r\n                >\r\n                  <svg-icon class=\"node-icon\" :icon-class=\"item.tagIcon\" />\r\n                  <span> {{ item.label }}</span>\r\n                </el-option>\r\n              </el-option-group>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.vModel!==undefined\" label=\"字段名\">\r\n            <el-input v-model=\"activeData.vModel\" placeholder=\"请输入字段名（v-model）\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.componentName!==undefined\" label=\"组件名\">\r\n            {{ activeData.componentName }}\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.label!==undefined\" label=\"标题\">\r\n            <el-input v-model=\"activeData.label\" placeholder=\"请输入标题\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.placeholder!==undefined\" label=\"占位提示\">\r\n            <el-input v-model=\"activeData.placeholder\" placeholder=\"请输入占位提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['start-placeholder']!==undefined\" label=\"开始占位\">\r\n            <el-input v-model=\"activeData['start-placeholder']\" placeholder=\"请输入占位提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['end-placeholder']!==undefined\" label=\"结束占位\">\r\n            <el-input v-model=\"activeData['end-placeholder']\" placeholder=\"请输入占位提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.span!==undefined\" label=\"表单栅格\">\r\n            <el-slider v-model=\"activeData.span\" :max=\"24\" :min=\"1\" :marks=\"{12:''}\" @change=\"spanChange\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.layout==='rowFormItem'\" label=\"栅格间隔\">\r\n            <el-input-number v-model=\"activeData.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.layout==='rowFormItem'\" label=\"布局模式\">\r\n            <el-radio-group v-model=\"activeData.type\">\r\n              <el-radio-button label=\"default\" />\r\n              <el-radio-button label=\"flex\" />\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.justify!==undefined&&activeData.type==='flex'\" label=\"水平排列\">\r\n            <el-select v-model=\"activeData.justify\" placeholder=\"请选择水平排列\" :style=\"{width: '100%'}\">\r\n              <el-option\r\n                v-for=\"(item, index) in justifyOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.align!==undefined&&activeData.type==='flex'\" label=\"垂直排列\">\r\n            <el-radio-group v-model=\"activeData.align\">\r\n              <el-radio-button label=\"top\" />\r\n              <el-radio-button label=\"middle\" />\r\n              <el-radio-button label=\"bottom\" />\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.labelWidth!==undefined\" label=\"标签宽度\">\r\n            <el-input v-model.number=\"activeData.labelWidth\" type=\"number\" placeholder=\"请输入标签宽度\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.style&&activeData.style.width!==undefined\" label=\"组件宽度\">\r\n            <el-input v-model=\"activeData.style.width\" placeholder=\"请输入组件宽度\" clearable />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.vModel!==undefined\" label=\"默认值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData.defaultValue)\"\r\n              placeholder=\"请输入默认值\"\r\n              @input=\"onDefaultValueInput\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag==='el-checkbox-group'\" label=\"至少应选\">\r\n            <el-input-number\r\n              :value=\"activeData.min\"\r\n              :min=\"0\"\r\n              placeholder=\"至少应选\"\r\n              @input=\"$set(activeData, 'min', $event?$event:undefined)\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag==='el-checkbox-group'\" label=\"最多可选\">\r\n            <el-input-number\r\n              :value=\"activeData.max\"\r\n              :min=\"0\"\r\n              placeholder=\"最多可选\"\r\n              @input=\"$set(activeData, 'max', $event?$event:undefined)\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.prepend!==undefined\" label=\"前缀\">\r\n            <el-input v-model=\"activeData.prepend\" placeholder=\"请输入前缀\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.append!==undefined\" label=\"后缀\">\r\n            <el-input v-model=\"activeData.append\" placeholder=\"请输入后缀\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['prefix-icon']!==undefined\" label=\"前图标\">\r\n            <el-input v-model=\"activeData['prefix-icon']\" placeholder=\"请输入前图标名称\">\r\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('prefix-icon')\">\r\n                选择\r\n              </el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['suffix-icon'] !== undefined\" label=\"后图标\">\r\n            <el-input v-model=\"activeData['suffix-icon']\" placeholder=\"请输入后图标名称\">\r\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('suffix-icon')\">\r\n                选择\r\n              </el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"选项分隔符\">\r\n            <el-input v-model=\"activeData.separator\" placeholder=\"请输入选项分隔符\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最小行数\">\r\n            <el-input-number v-model=\"activeData.autosize.minRows\" :min=\"1\" placeholder=\"最小行数\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最大行数\">\r\n            <el-input-number v-model=\"activeData.autosize.maxRows\" :min=\"1\" placeholder=\"最大行数\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.min !== undefined\" label=\"最小值\">\r\n            <el-input-number v-model=\"activeData.min\" placeholder=\"最小值\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.max !== undefined\" label=\"最大值\">\r\n            <el-input-number v-model=\"activeData.max\" placeholder=\"最大值\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.step !== undefined\" label=\"步长\">\r\n            <el-input-number v-model=\"activeData.step\" placeholder=\"步数\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"精度\">\r\n            <el-input-number v-model=\"activeData.precision\" :min=\"0\" placeholder=\"精度\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"按钮位置\">\r\n            <el-radio-group v-model=\"activeData['controls-position']\">\r\n              <el-radio-button label=\"\">\r\n                默认\r\n              </el-radio-button>\r\n              <el-radio-button label=\"right\">\r\n                右侧\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.maxlength !== undefined\" label=\"最多输入\">\r\n            <el-input v-model=\"activeData.maxlength\" placeholder=\"请输入字符长度\">\r\n              <template slot=\"append\">\r\n                个字符\r\n              </template>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-text'] !== undefined\" label=\"开启提示\">\r\n            <el-input v-model=\"activeData['active-text']\" placeholder=\"请输入开启提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-text'] !== undefined\" label=\"关闭提示\">\r\n            <el-input v-model=\"activeData['inactive-text']\" placeholder=\"请输入关闭提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-value'] !== undefined\" label=\"开启值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData['active-value'])\"\r\n              placeholder=\"请输入开启值\"\r\n              @input=\"onSwitchValueInput($event, 'active-value')\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-value'] !== undefined\" label=\"关闭值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData['inactive-value'])\"\r\n              placeholder=\"请输入关闭值\"\r\n              @input=\"onSwitchValueInput($event, 'inactive-value')\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.type !== undefined && 'el-date-picker' === activeData.tag\"\r\n            label=\"时间类型\"\r\n          >\r\n            <el-select\r\n              v-model=\"activeData.type\"\r\n              placeholder=\"请选择时间类型\"\r\n              :style=\"{ width: '100%' }\"\r\n              @change=\"dateTypeChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in dateOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.name !== undefined\" label=\"文件字段名\">\r\n            <el-input v-model=\"activeData.name\" placeholder=\"请输入上传文件字段名\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.accept !== undefined\" label=\"文件类型\">\r\n            <el-select\r\n              v-model=\"activeData.accept\"\r\n              placeholder=\"请选择文件类型\"\r\n              :style=\"{ width: '100%' }\"\r\n              clearable\r\n            >\r\n              <el-option label=\"图片\" value=\"image/*\" />\r\n              <el-option label=\"视频\" value=\"video/*\" />\r\n              <el-option label=\"音频\" value=\"audio/*\" />\r\n              <el-option label=\"excel\" value=\".xls,.xlsx\" />\r\n              <el-option label=\"word\" value=\".doc,.docx\" />\r\n              <el-option label=\"pdf\" value=\".pdf\" />\r\n              <el-option label=\"txt\" value=\".txt\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.fileSize !== undefined\" label=\"文件大小\">\r\n            <el-input v-model.number=\"activeData.fileSize\" placeholder=\"请输入文件大小\">\r\n              <el-select slot=\"append\" v-model=\"activeData.sizeUnit\" :style=\"{ width: '66px' }\">\r\n                <el-option label=\"KB\" value=\"KB\" />\r\n                <el-option label=\"MB\" value=\"MB\" />\r\n                <el-option label=\"GB\" value=\"GB\" />\r\n              </el-select>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.action !== undefined\" label=\"上传地址\">\r\n            <el-input v-model=\"activeData.action\" placeholder=\"请输入上传地址\" clearable />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['list-type'] !== undefined\" label=\"列表类型\">\r\n            <el-radio-group v-model=\"activeData['list-type']\" size=\"small\">\r\n              <el-radio-button label=\"text\">\r\n                text\r\n              </el-radio-button>\r\n              <el-radio-button label=\"picture\">\r\n                picture\r\n              </el-radio-button>\r\n              <el-radio-button label=\"picture-card\">\r\n                picture-card\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.buttonText !== undefined\"\r\n            v-show=\"'picture-card' !== activeData['list-type']\"\r\n            label=\"按钮文字\"\r\n          >\r\n            <el-input v-model=\"activeData.buttonText\" placeholder=\"请输入按钮文字\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['range-separator'] !== undefined\" label=\"分隔符\">\r\n            <el-input v-model=\"activeData['range-separator']\" placeholder=\"请输入分隔符\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['picker-options'] !== undefined\" label=\"时间段\">\r\n            <el-input\r\n              v-model=\"activeData['picker-options'].selectableRange\"\r\n              placeholder=\"请输入时间段\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.format !== undefined\" label=\"时间格式\">\r\n            <el-input\r\n              :value=\"activeData.format\"\r\n              placeholder=\"请输入时间格式\"\r\n              @input=\"setTimeValue($event)\"\r\n            />\r\n          </el-form-item>\r\n          <template v-if=\"['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(activeData.tag) > -1\">\r\n            <el-divider>选项</el-divider>\r\n            <draggable\r\n              :list=\"activeData.options\"\r\n              :animation=\"340\"\r\n              group=\"selectItem\"\r\n              handle=\".option-drag\"\r\n            >\r\n              <div v-for=\"(item, index) in activeData.options\" :key=\"index\" class=\"select-item\">\r\n                <div class=\"select-line-icon option-drag\">\r\n                  <i class=\"el-icon-s-operation\" />\r\n                </div>\r\n                <el-input v-model=\"item.label\" placeholder=\"选项名\" size=\"small\" />\r\n                <el-input\r\n                  placeholder=\"选项值\"\r\n                  size=\"small\"\r\n                  :value=\"item.value\"\r\n                  @input=\"setOptionValue(item, $event)\"\r\n                />\r\n                <div class=\"close-btn select-line-icon\" @click=\"activeData.options.splice(index, 1)\">\r\n                  <i class=\"el-icon-remove-outline\" />\r\n                </div>\r\n              </div>\r\n            </draggable>\r\n            <div style=\"margin-left: 20px;\">\r\n              <el-button\r\n                style=\"padding-bottom: 0\"\r\n                icon=\"el-icon-circle-plus-outline\"\r\n                type=\"text\"\r\n                @click=\"addSelectItem\"\r\n              >\r\n                添加选项\r\n              </el-button>\r\n            </div>\r\n            <el-divider />\r\n          </template>\r\n\r\n          <template v-if=\"['el-cascader'].indexOf(activeData.tag) > -1\">\r\n            <el-divider>选项</el-divider>\r\n            <el-form-item label=\"数据类型\">\r\n              <el-radio-group v-model=\"activeData.dataType\" size=\"small\">\r\n                <el-radio-button label=\"dynamic\">\r\n                  动态数据\r\n                </el-radio-button>\r\n                <el-radio-button label=\"static\">\r\n                  静态数据\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <template v-if=\"activeData.dataType === 'dynamic'\">\r\n              <el-form-item label=\"标签键名\">\r\n                <el-input v-model=\"activeData.labelKey\" placeholder=\"请输入标签键名\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"值键名\">\r\n                <el-input v-model=\"activeData.valueKey\" placeholder=\"请输入值键名\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"子级键名\">\r\n                <el-input v-model=\"activeData.childrenKey\" placeholder=\"请输入子级键名\" />\r\n              </el-form-item>\r\n            </template>\r\n\r\n            <el-tree\r\n              v-if=\"activeData.dataType === 'static'\"\r\n              draggable\r\n              :data=\"activeData.options\"\r\n              node-key=\"id\"\r\n              :expand-on-click-node=\"false\"\r\n              :render-content=\"renderContent\"\r\n            />\r\n            <div v-if=\"activeData.dataType === 'static'\" style=\"margin-left: 20px\">\r\n              <el-button\r\n                style=\"padding-bottom: 0\"\r\n                icon=\"el-icon-circle-plus-outline\"\r\n                type=\"text\"\r\n                @click=\"addTreeItem\"\r\n              >\r\n                添加父级\r\n              </el-button>\r\n            </div>\r\n            <el-divider />\r\n          </template>\r\n\r\n          <el-form-item v-if=\"activeData.optionType !== undefined\" label=\"选项样式\">\r\n            <el-radio-group v-model=\"activeData.optionType\">\r\n              <el-radio-button label=\"default\">\r\n                默认\r\n              </el-radio-button>\r\n              <el-radio-button label=\"button\">\r\n                按钮\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-color'] !== undefined\" label=\"开启颜色\">\r\n            <el-color-picker v-model=\"activeData['active-color']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-color'] !== undefined\" label=\"关闭颜色\">\r\n            <el-color-picker v-model=\"activeData['inactive-color']\" />\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"activeData['allow-half'] !== undefined\" label=\"允许半选\">\r\n            <el-switch v-model=\"activeData['allow-half']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-text'] !== undefined\" label=\"辅助文字\">\r\n            <el-switch v-model=\"activeData['show-text']\" @change=\"rateTextChange\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-score'] !== undefined\" label=\"显示分数\">\r\n            <el-switch v-model=\"activeData['show-score']\" @change=\"rateScoreChange\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-stops'] !== undefined\" label=\"显示间断点\">\r\n            <el-switch v-model=\"activeData['show-stops']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.range !== undefined\" label=\"范围选择\">\r\n            <el-switch v-model=\"activeData.range\" @change=\"rangeChange\" />\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.border !== undefined && activeData.optionType === 'default'\"\r\n            label=\"是否带边框\"\r\n          >\r\n            <el-switch v-model=\"activeData.border\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-color-picker'\" label=\"颜色格式\">\r\n            <el-select\r\n              v-model=\"activeData['color-format']\"\r\n              placeholder=\"请选择颜色格式\"\r\n              :style=\"{ width: '100%' }\"\r\n              @change=\"colorFormatChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in colorFormatOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.size !== undefined &&\r\n              (activeData.optionType === 'button' ||\r\n                activeData.border ||\r\n                activeData.tag === 'el-color-picker')\"\r\n            label=\"选项尺寸\"\r\n          >\r\n            <el-radio-group v-model=\"activeData.size\">\r\n              <el-radio-button label=\"medium\">\r\n                中等\r\n              </el-radio-button>\r\n              <el-radio-button label=\"small\">\r\n                较小\r\n              </el-radio-button>\r\n              <el-radio-button label=\"mini\">\r\n                迷你\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-word-limit'] !== undefined\" label=\"输入统计\">\r\n            <el-switch v-model=\"activeData['show-word-limit']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"严格步数\">\r\n            <el-switch v-model=\"activeData['step-strictly']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"是否多选\">\r\n            <el-switch v-model=\"activeData.props.props.multiple\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"展示全路径\">\r\n            <el-switch v-model=\"activeData['show-all-levels']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"可否筛选\">\r\n            <el-switch v-model=\"activeData.filterable\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.clearable !== undefined\" label=\"能否清空\">\r\n            <el-switch v-model=\"activeData.clearable\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.showTip !== undefined\" label=\"显示提示\">\r\n            <el-switch v-model=\"activeData.showTip\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.multiple !== undefined\" label=\"多选文件\">\r\n            <el-switch v-model=\"activeData.multiple\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['auto-upload'] !== undefined\" label=\"自动上传\">\r\n            <el-switch v-model=\"activeData['auto-upload']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.readonly !== undefined\" label=\"是否只读\">\r\n            <el-switch v-model=\"activeData.readonly\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.disabled !== undefined\" label=\"是否禁用\">\r\n            <el-switch v-model=\"activeData.disabled\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-select'\" label=\"是否可搜索\">\r\n            <el-switch v-model=\"activeData.filterable\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-select'\" label=\"是否多选\">\r\n            <el-switch v-model=\"activeData.multiple\" @change=\"multipleChange\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.required !== undefined\" label=\"是否必填\">\r\n            <el-switch v-model=\"activeData.required\" />\r\n          </el-form-item>\r\n\r\n          <template v-if=\"activeData.layoutTree\">\r\n            <el-divider>布局结构树</el-divider>\r\n            <el-tree\r\n              :data=\"[activeData]\"\r\n              :props=\"layoutTreeProps\"\r\n              node-key=\"renderKey\"\r\n              default-expand-all\r\n              draggable\r\n            >\r\n              <span slot-scope=\"{ node, data }\">\r\n                <span class=\"node-label\">\r\n                  <svg-icon class=\"node-icon\" :icon-class=\"data.tagIcon\" />\r\n                  {{ node.label }}\r\n                </span>\r\n              </span>\r\n            </el-tree>\r\n          </template>\r\n\r\n          <template v-if=\"activeData.layout === 'colFormItem'\">\r\n            <el-divider>正则校验</el-divider>\r\n            <div\r\n              v-for=\"(item, index) in activeData.regList\"\r\n              :key=\"index\"\r\n              class=\"reg-item\"\r\n            >\r\n              <span class=\"close-btn\" @click=\"activeData.regList.splice(index, 1)\">\r\n                <i class=\"el-icon-close\" />\r\n              </span>\r\n              <el-form-item label=\"表达式\">\r\n                <el-input v-model=\"item.pattern\" placeholder=\"请输入正则\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"错误提示\" style=\"margin-bottom:0\">\r\n                <el-input v-model=\"item.message\" placeholder=\"请输入错误提示\" />\r\n              </el-form-item>\r\n            </div>\r\n            <div style=\"margin-left: 20px\">\r\n              <el-button icon=\"el-icon-circle-plus-outline\" type=\"text\" @click=\"addReg\">\r\n                添加规则\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-form>\r\n        <!-- 表单属性 -->\r\n        <el-form v-show=\"currentTab === 'form'\" size=\"small\" label-width=\"90px\">\r\n          <el-form-item label=\"表单名\">\r\n            <el-input v-model=\"formConf.formRef\" placeholder=\"请输入表单名（ref）\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"表单模型\">\r\n            <el-input v-model=\"formConf.formModel\" placeholder=\"请输入数据模型\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"校验模型\">\r\n            <el-input v-model=\"formConf.formRules\" placeholder=\"请输入校验模型\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"表单尺寸\">\r\n            <el-radio-group v-model=\"formConf.size\">\r\n              <el-radio-button label=\"medium\">\r\n                中等\r\n              </el-radio-button>\r\n              <el-radio-button label=\"small\">\r\n                较小\r\n              </el-radio-button>\r\n              <el-radio-button label=\"mini\">\r\n                迷你\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"标签对齐\">\r\n            <el-radio-group v-model=\"formConf.labelPosition\">\r\n              <el-radio-button label=\"left\">\r\n                左对齐\r\n              </el-radio-button>\r\n              <el-radio-button label=\"right\">\r\n                右对齐\r\n              </el-radio-button>\r\n              <el-radio-button label=\"top\">\r\n                顶部对齐\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"标签宽度\">\r\n            <el-input-number v-model=\"formConf.labelWidth\" placeholder=\"标签宽度\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"栅格间隔\">\r\n            <el-input-number v-model=\"formConf.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"禁用表单\">\r\n            <el-switch v-model=\"formConf.disabled\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"表单按钮\">\r\n            <el-switch v-model=\"formConf.formBtns\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"显示未选中组件边框\">\r\n            <el-switch v-model=\"formConf.unFocusedComponentBorder\" />\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <treeNode-dialog :visible.sync=\"dialogVisible\" title=\"添加选项\" @commit=\"addNode\" />\r\n    <icons-dialog :visible.sync=\"iconsVisible\" :current=\"activeData[currentIconModel]\" @select=\"setIcon\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { isArray } from 'util'\r\nimport draggable from 'vuedraggable'\r\nimport TreeNodeDialog from './TreeNodeDialog'\r\nimport { isNumberStr } from '@/utils/index'\r\nimport IconsDialog from './IconsDialog'\r\nimport {\r\n  inputComponents,\r\n  selectComponents,\r\n  layoutComponents\r\n} from '@/utils/generator/config'\r\n\r\nconst dateTimeFormat = {\r\n  date: 'yyyy-MM-dd',\r\n  week: 'yyyy 第 WW 周',\r\n  month: 'yyyy-MM',\r\n  year: 'yyyy',\r\n  datetime: 'yyyy-MM-dd HH:mm:ss',\r\n  daterange: 'yyyy-MM-dd',\r\n  monthrange: 'yyyy-MM',\r\n  datetimerange: 'yyyy-MM-dd HH:mm:ss'\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    draggable,\r\n    TreeNodeDialog,\r\n    IconsDialog\r\n  },\r\n  props: ['showField', 'activeData', 'formConf'],\r\n  data() {\r\n    return {\r\n      currentTab: 'field',\r\n      currentNode: null,\r\n      dialogVisible: false,\r\n      iconsVisible: false,\r\n      currentIconModel: null,\r\n      dateTypeOptions: [\r\n        {\r\n          label: '日(date)',\r\n          value: 'date'\r\n        },\r\n        {\r\n          label: '周(week)',\r\n          value: 'week'\r\n        },\r\n        {\r\n          label: '月(month)',\r\n          value: 'month'\r\n        },\r\n        {\r\n          label: '年(year)',\r\n          value: 'year'\r\n        },\r\n        {\r\n          label: '日期时间(datetime)',\r\n          value: 'datetime'\r\n        }\r\n      ],\r\n      dateRangeTypeOptions: [\r\n        {\r\n          label: '日期范围(daterange)',\r\n          value: 'daterange'\r\n        },\r\n        {\r\n          label: '月范围(monthrange)',\r\n          value: 'monthrange'\r\n        },\r\n        {\r\n          label: '日期时间范围(datetimerange)',\r\n          value: 'datetimerange'\r\n        }\r\n      ],\r\n      colorFormatOptions: [\r\n        {\r\n          label: 'hex',\r\n          value: 'hex'\r\n        },\r\n        {\r\n          label: 'rgb',\r\n          value: 'rgb'\r\n        },\r\n        {\r\n          label: 'rgba',\r\n          value: 'rgba'\r\n        },\r\n        {\r\n          label: 'hsv',\r\n          value: 'hsv'\r\n        },\r\n        {\r\n          label: 'hsl',\r\n          value: 'hsl'\r\n        }\r\n      ],\r\n      justifyOptions: [\r\n        {\r\n          label: 'start',\r\n          value: 'start'\r\n        },\r\n        {\r\n          label: 'end',\r\n          value: 'end'\r\n        },\r\n        {\r\n          label: 'center',\r\n          value: 'center'\r\n        },\r\n        {\r\n          label: 'space-around',\r\n          value: 'space-around'\r\n        },\r\n        {\r\n          label: 'space-between',\r\n          value: 'space-between'\r\n        }\r\n      ],\r\n      layoutTreeProps: {\r\n        label(data, node) {\r\n          return data.componentName || `${data.label}: ${data.vModel}`\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    documentLink() {\r\n      return (\r\n        this.activeData.document\r\n        || 'https://element.eleme.cn/#/zh-CN/component/installation'\r\n      )\r\n    },\r\n    dateOptions() {\r\n      if (\r\n        this.activeData.type !== undefined\r\n        && this.activeData.tag === 'el-date-picker'\r\n      ) {\r\n        if (this.activeData['start-placeholder'] === undefined) {\r\n          return this.dateTypeOptions\r\n        }\r\n        return this.dateRangeTypeOptions\r\n      }\r\n      return []\r\n    },\r\n    tagList() {\r\n      return [\r\n        {\r\n          label: '输入型组件',\r\n          options: inputComponents\r\n        },\r\n        {\r\n          label: '选择型组件',\r\n          options: selectComponents\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    addReg() {\r\n      this.activeData.regList.push({\r\n        pattern: '',\r\n        message: ''\r\n      })\r\n    },\r\n    addSelectItem() {\r\n      this.activeData.options.push({\r\n        label: '',\r\n        value: ''\r\n      })\r\n    },\r\n    addTreeItem() {\r\n      ++this.idGlobal\r\n      this.dialogVisible = true\r\n      this.currentNode = this.activeData.options\r\n    },\r\n    renderContent(h, { node, data, store }) {\r\n      return (\r\n        <div class=\"custom-tree-node\">\r\n          <span>{node.label}</span>\r\n          <span class=\"node-operation\">\r\n            <i on-click={() => this.append(data)}\r\n              class=\"el-icon-plus\"\r\n              title=\"添加\"\r\n            ></i>\r\n            <i on-click={() => this.remove(node, data)}\r\n              class=\"el-icon-delete\"\r\n              title=\"删除\"\r\n            ></i>\r\n          </span>\r\n        </div>\r\n      )\r\n    },\r\n    append(data) {\r\n      if (!data.children) {\r\n        this.$set(data, 'children', [])\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentNode = data.children\r\n    },\r\n    remove(node, data) {\r\n      const { parent } = node\r\n      const children = parent.data.children || parent.data\r\n      const index = children.findIndex(d => d.id === data.id)\r\n      children.splice(index, 1)\r\n    },\r\n    addNode(data) {\r\n      this.currentNode.push(data)\r\n    },\r\n    setOptionValue(item, val) {\r\n      item.value = isNumberStr(val) ? +val : val\r\n    },\r\n    setDefaultValue(val) {\r\n      if (Array.isArray(val)) {\r\n        return val.join(',')\r\n      }\r\n      if (['string', 'number'].indexOf(val) > -1) {\r\n        return val\r\n      }\r\n      if (typeof val === 'boolean') {\r\n        return `${val}`\r\n      }\r\n      return val\r\n    },\r\n    onDefaultValueInput(str) {\r\n      if (isArray(this.activeData.defaultValue)) {\r\n        // 数组\r\n        this.$set(\r\n          this.activeData,\r\n          'defaultValue',\r\n          str.split(',').map(val => (isNumberStr(val) ? +val : val))\r\n        )\r\n      } else if (['true', 'false'].indexOf(str) > -1) {\r\n        // 布尔\r\n        this.$set(this.activeData, 'defaultValue', JSON.parse(str))\r\n      } else {\r\n        // 字符串和数字\r\n        this.$set(\r\n          this.activeData,\r\n          'defaultValue',\r\n          isNumberStr(str) ? +str : str\r\n        )\r\n      }\r\n    },\r\n    onSwitchValueInput(val, name) {\r\n      if (['true', 'false'].indexOf(val) > -1) {\r\n        this.$set(this.activeData, name, JSON.parse(val))\r\n      } else {\r\n        this.$set(this.activeData, name, isNumberStr(val) ? +val : val)\r\n      }\r\n    },\r\n    setTimeValue(val, type) {\r\n      const valueFormat = type === 'week' ? dateTimeFormat.date : val\r\n      this.$set(this.activeData, 'defaultValue', null)\r\n      this.$set(this.activeData, 'value-format', valueFormat)\r\n      this.$set(this.activeData, 'format', val)\r\n    },\r\n    spanChange(val) {\r\n      this.formConf.span = val\r\n    },\r\n    multipleChange(val) {\r\n      this.$set(this.activeData, 'defaultValue', val ? [] : '')\r\n    },\r\n    dateTypeChange(val) {\r\n      this.setTimeValue(dateTimeFormat[val], val)\r\n    },\r\n    rangeChange(val) {\r\n      this.$set(\r\n        this.activeData,\r\n        'defaultValue',\r\n        val ? [this.activeData.min, this.activeData.max] : this.activeData.min\r\n      )\r\n    },\r\n    rateTextChange(val) {\r\n      if (val) this.activeData['show-score'] = false\r\n    },\r\n    rateScoreChange(val) {\r\n      if (val) this.activeData['show-text'] = false\r\n    },\r\n    colorFormatChange(val) {\r\n      this.activeData.defaultValue = null\r\n      this.activeData['show-alpha'] = val.indexOf('a') > -1\r\n      this.activeData.renderKey = +new Date() // 更新renderKey,重新渲染该组件\r\n    },\r\n    openIconsDialog(model) {\r\n      this.iconsVisible = true\r\n      this.currentIconModel = model\r\n    },\r\n    setIcon(val) {\r\n      this.activeData[this.currentIconModel] = val\r\n    },\r\n    tagChange(tagIcon) {\r\n      let target = inputComponents.find(item => item.tagIcon === tagIcon)\r\n      if (!target) target = selectComponents.find(item => item.tagIcon === tagIcon)\r\n      this.$emit('tag-change', target)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.right-board {\r\n  width: 350px;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  padding-top: 3px;\r\n  .field-box {\r\n    position: relative;\r\n    height: calc(100vh - 42px);\r\n    box-sizing: border-box;\r\n    overflow: hidden;\r\n  }\r\n  .el-scrollbar {\r\n    height: 100%;\r\n  }\r\n}\r\n.select-item {\r\n  display: flex;\r\n  border: 1px dashed #fff;\r\n  box-sizing: border-box;\r\n  & .close-btn {\r\n    cursor: pointer;\r\n    color: #f56c6c;\r\n  }\r\n  & .el-input + .el-input {\r\n    margin-left: 4px;\r\n  }\r\n}\r\n.select-item + .select-item {\r\n  margin-top: 4px;\r\n}\r\n.select-item.sortable-chosen {\r\n  border: 1px dashed #409eff;\r\n}\r\n.select-line-icon {\r\n  line-height: 32px;\r\n  font-size: 22px;\r\n  padding: 0 4px;\r\n  color: #777;\r\n}\r\n.option-drag {\r\n  cursor: move;\r\n}\r\n.time-range {\r\n  .el-date-editor {\r\n    width: 227px;\r\n  }\r\n  ::v-deep .el-icon-time {\r\n    display: none;\r\n  }\r\n}\r\n.document-link {\r\n  position: absolute;\r\n  display: block;\r\n  width: 26px;\r\n  height: 26px;\r\n  top: 0;\r\n  left: 0;\r\n  cursor: pointer;\r\n  background: #409eff;\r\n  z-index: 1;\r\n  border-radius: 0 0 6px 0;\r\n  text-align: center;\r\n  line-height: 26px;\r\n  color: #fff;\r\n  font-size: 18px;\r\n}\r\n.node-label{\r\n  font-size: 14px;\r\n}\r\n.node-icon{\r\n  color: #bebfc3;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA8jBA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,OAAA,GAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAAO,cAAA;EACAC,IAAA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA;EACAC,QAAA;EACAC,SAAA;EACAC,UAAA;EACAC,aAAA;AACA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,UAAA;IACAC,SAAA,EAAAA,qBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,eAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,oBAAA,GACA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAE,kBAAA,GACA;QACAH,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAG,cAAA,GACA;QACAJ,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAI,eAAA;QACAL,KAAA,WAAAA,MAAAP,IAAA,EAAAa,IAAA;UACA,OAAAb,IAAA,CAAAc,aAAA,OAAAC,MAAA,CAAAf,IAAA,CAAAO,KAAA,QAAAQ,MAAA,CAAAf,IAAA,CAAAgB,MAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,OACA,KAAAC,UAAA,CAAAC,QAAA,IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IACA,KAAAF,UAAA,CAAAG,IAAA,KAAAC,SAAA,IACA,KAAAJ,UAAA,CAAAK,GAAA,uBACA;QACA,SAAAL,UAAA,0BAAAI,SAAA;UACA,YAAAjB,eAAA;QACA;QACA,YAAAG,oBAAA;MACA;MACA;IACA;IACAgB,OAAA,WAAAA,QAAA;MACA,QACA;QACAlB,KAAA;QACAmB,OAAA,EAAAC;MACA,GACA;QACApB,KAAA;QACAmB,OAAA,EAAAE;MACA,EACA;IACA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAX,UAAA,CAAAY,OAAA,CAAAC,IAAA;QACAC,OAAA;QACAC,OAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAhB,UAAA,CAAAO,OAAA,CAAAM,IAAA;QACAzB,KAAA;QACAC,KAAA;MACA;IACA;IACA4B,WAAA,WAAAA,YAAA;MACA,OAAAC,QAAA;MACA,KAAAlC,aAAA;MACA,KAAAD,WAAA,QAAAiB,UAAA,CAAAO,OAAA;IACA;IACAY,aAAA,WAAAA,cAAAC,CAAA,EAAAC,IAAA;MAAA,IAAAC,KAAA;MAAA,IAAA5B,IAAA,GAAA2B,IAAA,CAAA3B,IAAA;QAAAb,IAAA,GAAAwC,IAAA,CAAAxC,IAAA;QAAA0C,KAAA,GAAAF,IAAA,CAAAE,KAAA;MACA,OAAAH,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA,UACA1B,IAAA,CAAAN,KAAA,IAAAgC,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA;QAAA;UAAA,SACA,SAAAI,MAAA;YAAA,OAAAF,KAAA,CAAAG,MAAA,CAAA5C,IAAA;UAAA;QAAA;QAAA,SACA;QAAA;UAAA,SACA;QAAA;MAAA,IAAAuC,CAAA;QAAA;UAAA,SAEA,SAAAI,MAAA;YAAA,OAAAF,KAAA,CAAAI,MAAA,CAAAhC,IAAA,EAAAb,IAAA;UAAA;QAAA;QAAA,SACA;QAAA;UAAA,SACA;QAAA;MAAA;IAKA;IACA4C,MAAA,WAAAA,OAAA5C,IAAA;MACA,KAAAA,IAAA,CAAA8C,QAAA;QACA,KAAAC,IAAA,CAAA/C,IAAA;MACA;MACA,KAAAG,aAAA;MACA,KAAAD,WAAA,GAAAF,IAAA,CAAA8C,QAAA;IACA;IACAD,MAAA,WAAAA,OAAAhC,IAAA,EAAAb,IAAA;MACA,IAAAgD,MAAA,GAAAnC,IAAA,CAAAmC,MAAA;MACA,IAAAF,QAAA,GAAAE,MAAA,CAAAhD,IAAA,CAAA8C,QAAA,IAAAE,MAAA,CAAAhD,IAAA;MACA,IAAAiD,KAAA,GAAAH,QAAA,CAAAI,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,KAAApD,IAAA,CAAAoD,EAAA;MAAA;MACAN,QAAA,CAAAO,MAAA,CAAAJ,KAAA;IACA;IACAK,OAAA,WAAAA,QAAAtD,IAAA;MACA,KAAAE,WAAA,CAAA8B,IAAA,CAAAhC,IAAA;IACA;IACAuD,cAAA,WAAAA,eAAAC,IAAA,EAAAC,GAAA;MACAD,IAAA,CAAAhD,KAAA,OAAAkD,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;IACA;IACAE,eAAA,WAAAA,gBAAAF,GAAA;MACA,IAAAG,KAAA,CAAAC,OAAA,CAAAJ,GAAA;QACA,OAAAA,GAAA,CAAAK,IAAA;MACA;MACA,yBAAAC,OAAA,CAAAN,GAAA;QACA,OAAAA,GAAA;MACA;MACA,WAAAA,GAAA;QACA,UAAA1C,MAAA,CAAA0C,GAAA;MACA;MACA,OAAAA,GAAA;IACA;IACAO,mBAAA,WAAAA,oBAAAC,GAAA;MACA,QAAAJ,aAAA,OAAA1C,UAAA,CAAA+C,YAAA;QACA;QACA,KAAAnB,IAAA,CACA,KAAA5B,UAAA,EACA,gBACA8C,GAAA,CAAAE,KAAA,MAAAC,GAAA,WAAAX,GAAA;UAAA,WAAAC,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;QAAA,EACA;MACA,6BAAAM,OAAA,CAAAE,GAAA;QACA;QACA,KAAAlB,IAAA,MAAA5B,UAAA,kBAAAkD,IAAA,CAAAC,KAAA,CAAAL,GAAA;MACA;QACA;QACA,KAAAlB,IAAA,CACA,KAAA5B,UAAA,EACA,gBACA,IAAAuC,kBAAA,EAAAO,GAAA,KAAAA,GAAA,GAAAA,GACA;MACA;IACA;IACAM,kBAAA,WAAAA,mBAAAd,GAAA,EAAAe,IAAA;MACA,sBAAAT,OAAA,CAAAN,GAAA;QACA,KAAAV,IAAA,MAAA5B,UAAA,EAAAqD,IAAA,EAAAH,IAAA,CAAAC,KAAA,CAAAb,GAAA;MACA;QACA,KAAAV,IAAA,MAAA5B,UAAA,EAAAqD,IAAA,MAAAd,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;MACA;IACA;IACAgB,YAAA,WAAAA,aAAAhB,GAAA,EAAAnC,IAAA;MACA,IAAAoD,WAAA,GAAApD,IAAA,cAAAvC,cAAA,CAAAC,IAAA,GAAAyE,GAAA;MACA,KAAAV,IAAA,MAAA5B,UAAA;MACA,KAAA4B,IAAA,MAAA5B,UAAA,kBAAAuD,WAAA;MACA,KAAA3B,IAAA,MAAA5B,UAAA,YAAAsC,GAAA;IACA;IACAkB,UAAA,WAAAA,WAAAlB,GAAA;MACA,KAAAmB,QAAA,CAAAC,IAAA,GAAApB,GAAA;IACA;IACAqB,cAAA,WAAAA,eAAArB,GAAA;MACA,KAAAV,IAAA,MAAA5B,UAAA,kBAAAsC,GAAA;IACA;IACAsB,cAAA,WAAAA,eAAAtB,GAAA;MACA,KAAAgB,YAAA,CAAA1F,cAAA,CAAA0E,GAAA,GAAAA,GAAA;IACA;IACAuB,WAAA,WAAAA,YAAAvB,GAAA;MACA,KAAAV,IAAA,CACA,KAAA5B,UAAA,EACA,gBACAsC,GAAA,SAAAtC,UAAA,CAAA8D,GAAA,OAAA9D,UAAA,CAAA+D,GAAA,SAAA/D,UAAA,CAAA8D,GACA;IACA;IACAE,cAAA,WAAAA,eAAA1B,GAAA;MACA,IAAAA,GAAA,OAAAtC,UAAA;IACA;IACAiE,eAAA,WAAAA,gBAAA3B,GAAA;MACA,IAAAA,GAAA,OAAAtC,UAAA;IACA;IACAkE,iBAAA,WAAAA,kBAAA5B,GAAA;MACA,KAAAtC,UAAA,CAAA+C,YAAA;MACA,KAAA/C,UAAA,iBAAAsC,GAAA,CAAAM,OAAA;MACA,KAAA5C,UAAA,CAAAmE,SAAA,QAAAC,IAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAArF,YAAA;MACA,KAAAC,gBAAA,GAAAoF,KAAA;IACA;IACAC,OAAA,WAAAA,QAAAjC,GAAA;MACA,KAAAtC,UAAA,MAAAd,gBAAA,IAAAoD,GAAA;IACA;IACAkC,SAAA,WAAAA,UAAAC,OAAA;MACA,IAAAC,MAAA,GAAAlE,uBAAA,CAAAmE,IAAA,WAAAtC,IAAA;QAAA,OAAAA,IAAA,CAAAoC,OAAA,KAAAA,OAAA;MAAA;MACA,KAAAC,MAAA,EAAAA,MAAA,GAAAjE,wBAAA,CAAAkE,IAAA,WAAAtC,IAAA;QAAA,OAAAA,IAAA,CAAAoC,OAAA,KAAAA,OAAA;MAAA;MACA,KAAAG,KAAA,eAAAF,MAAA;IACA;EACA;AACA", "ignoreList": []}]}