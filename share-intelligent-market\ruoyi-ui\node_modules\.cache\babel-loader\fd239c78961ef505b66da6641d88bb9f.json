{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\SvgIcon\\index.vue", "mtime": 1750151094154}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfdmFsaWRhdGUgPSByZXF1aXJlKCJAL3V0aWxzL3ZhbGlkYXRlIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnU3ZnSWNvbicsCiAgcHJvcHM6IHsKICAgIGljb25DbGFzczogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgY2xhc3NOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc0V4dGVybmFsOiBmdW5jdGlvbiBpc0V4dGVybmFsKCkgewogICAgICByZXR1cm4gKDAsIF92YWxpZGF0ZS5pc0V4dGVybmFsKSh0aGlzLmljb25DbGFzcyk7CiAgICB9LAogICAgaWNvbk5hbWU6IGZ1bmN0aW9uIGljb25OYW1lKCkgewogICAgICByZXR1cm4gIiNpY29uLSIuY29uY2F0KHRoaXMuaWNvbkNsYXNzKTsKICAgIH0sCiAgICBzdmdDbGFzczogZnVuY3Rpb24gc3ZnQ2xhc3MoKSB7CiAgICAgIGlmICh0aGlzLmNsYXNzTmFtZSkgewogICAgICAgIHJldHVybiAnc3ZnLWljb24gJyArIHRoaXMuY2xhc3NOYW1lOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnc3ZnLWljb24nOwogICAgICB9CiAgICB9LAogICAgc3R5bGVFeHRlcm5hbEljb246IGZ1bmN0aW9uIHN0eWxlRXh0ZXJuYWxJY29uKCkgewogICAgICByZXR1cm4gewogICAgICAgIG1hc2s6ICJ1cmwoIi5jb25jYXQodGhpcy5pY29uQ2xhc3MsICIpIG5vLXJlcGVhdCA1MCUgNTAlIiksCiAgICAgICAgJy13ZWJraXQtbWFzayc6ICJ1cmwoIi5jb25jYXQodGhpcy5pY29uQ2xhc3MsICIpIG5vLXJlcGVhdCA1MCUgNTAlIikKICAgICAgfTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_validate", "require", "name", "props", "iconClass", "type", "String", "required", "className", "default", "computed", "isExternal", "iconName", "concat", "svgClass", "styleExternalIcon", "mask"], "sources": ["src/components/SvgIcon/index.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\r\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\r\n    <use :xlink:href=\"iconName\" />\r\n  </svg>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  name: 'SvgIcon',\r\n  props: {\r\n    iconClass: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    className: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isExternal() {\r\n      return isExternal(this.iconClass)\r\n    },\r\n    iconName() {\r\n      return `#icon-${this.iconClass}`\r\n    },\r\n    svgClass() {\r\n      if (this.className) {\r\n        return 'svg-icon ' + this.className\r\n      } else {\r\n        return 'svg-icon'\r\n      }\r\n    },\r\n    styleExternalIcon() {\r\n      return {\r\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\r\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.svg-icon {\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: -0.15em;\r\n  fill: currentColor;\r\n  overflow: hidden;\r\n}\r\n\r\n.svg-external-icon {\r\n  background-color: currentColor;\r\n  mask-size: cover!important;\r\n  display: inline-block;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAQA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,WAAAA,oBAAA,OAAAP,SAAA;IACA;IACAQ,QAAA,WAAAA,SAAA;MACA,gBAAAC,MAAA,MAAAT,SAAA;IACA;IACAU,QAAA,WAAAA,SAAA;MACA,SAAAN,SAAA;QACA,0BAAAA,SAAA;MACA;QACA;MACA;IACA;IACAO,iBAAA,WAAAA,kBAAA;MACA;QACAC,IAAA,SAAAH,MAAA,MAAAT,SAAA;QACA,uBAAAS,MAAA,MAAAT,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}