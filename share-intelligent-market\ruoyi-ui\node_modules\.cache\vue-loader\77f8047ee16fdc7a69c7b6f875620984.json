{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\user\\profile\\userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\user\\profile\\userAvatar.vue", "mtime": 1750151094305}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userAvatar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userAvatar.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"user-info-head\" @click=\"editCropper()\"><img v-bind:src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" /></div>\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body @opened=\"modalOpened\"  @close=\"closeDialog\">\r\n      <el-row>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <vue-cropper\r\n            ref=\"cropper\"\r\n            :img=\"options.img\"\r\n            :info=\"true\"\r\n            :autoCrop=\"options.autoCrop\"\r\n            :autoCropWidth=\"options.autoCropWidth\"\r\n            :autoCropHeight=\"options.autoCropHeight\"\r\n            :fixedBox=\"options.fixedBox\"\r\n            @realTime=\"realTime\"\r\n            v-if=\"visible\"\r\n          />\r\n        </el-col>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <div class=\"avatar-upload-preview\">\r\n            <img :src=\"previews.url\" :style=\"previews.img\" />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <br />\r\n      <el-row>\r\n        <el-col :lg=\"2\" :md=\"2\">\r\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\r\n            <el-button size=\"small\">\r\n              选择\r\n              <i class=\"el-icon-upload el-icon--right\"></i>\r\n            </el-button>\r\n          </el-upload>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 2}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 2, offset: 6}\" :md=\"2\">\r\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from \"@/store\";\r\nimport { VueCropper } from \"vue-cropper\";\r\nimport { uploadAvatar,getUserProfile,updateUserProfile } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  components: { VueCropper },\r\n  props: {\r\n    user: {\r\n      type: Object\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示cropper\r\n      visible: false,\r\n      // 弹出层标题\r\n      title: \"修改头像\",\r\n      options: {\r\n        img: store.getters.avatar, //裁剪图片的地址\r\n        autoCrop: true, // 是否默认生成截图框\r\n        autoCropWidth: 200, // 默认生成截图框宽度\r\n        autoCropHeight: 200, // 默认生成截图框高度\r\n        fixedBox: true // 固定截图框大小 不允许改变\r\n      },\r\n      previews: {}\r\n    };\r\n  },\r\n  methods: {\r\n    // 编辑头像\r\n    editCropper() {\r\n      this.open = true;\r\n    },\r\n    // 打开弹出层结束时的回调\r\n    modalOpened() {\r\n      this.visible = true;\r\n    },\r\n    // 覆盖默认的上传行为\r\n    requestUpload() {\r\n    },\r\n    // 向左旋转\r\n    rotateLeft() {\r\n      this.$refs.cropper.rotateLeft();\r\n    },\r\n    // 向右旋转\r\n    rotateRight() {\r\n      this.$refs.cropper.rotateRight();\r\n    },\r\n    // 图片缩放\r\n    changeScale(num) {\r\n      num = num || 1;\r\n      this.$refs.cropper.changeScale(num);\r\n    },\r\n    // 上传预处理\r\n    beforeUpload(file) {\r\n      if (file.type.indexOf(\"image/\") == -1) {\r\n        this.$modal.msgError(\"文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。\");\r\n      } else {\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          this.options.img = reader.result;\r\n        };\r\n      }\r\n    },\r\n    // 上传图片\r\n    uploadImg() {\r\n      this.$refs.cropper.getCropBlob(data => {\r\n        let formData = new FormData();\r\n        formData.append(\"avatarfile\", data);\r\n        uploadAvatar(formData).then(response => {\r\n          if(response.status == 0){\r\n            updateUserProfile({\r\n                userId: this.user.userId,\r\n                avatar: response.url\r\n            }).then(res => {\r\n              this.open = false;\r\n              this.options.img = response.url;\r\n              store.commit('SET_AVATAR', this.options.img);\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.visible = false;\r\n            })\r\n          }\r\n        });\r\n      });\r\n    },\r\n    // 实时预览\r\n    realTime(data) {\r\n      this.previews = data;\r\n    },\r\n    // 关闭窗口\r\n    closeDialog() {\r\n      this.options.img = store.getters.avatar\r\n      this.visible = false;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.user-info-head {\r\n  position: relative;\r\n  display: inline-block;\r\n  height: 120px;\r\n}\r\n\r\n.user-info-head:hover:after {\r\n  content: '+';\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  color: #eee;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  font-size: 24px;\r\n  font-style: normal;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  cursor: pointer;\r\n  line-height: 110px;\r\n  border-radius: 50%;\r\n}\r\n</style>"]}]}