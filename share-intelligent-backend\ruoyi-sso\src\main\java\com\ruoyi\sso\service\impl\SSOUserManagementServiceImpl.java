package com.ruoyi.sso.service.impl;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.sso.domain.SSOUser;
import com.ruoyi.sso.service.SSOUserManagementService;
import com.ruoyi.sso.service.SSOUserService;
import com.ruoyi.system.api.RemoteMemberService;
import com.ruoyi.system.api.domain.Member;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * SSO用户管理服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SSOUserManagementServiceImpl implements SSOUserManagementService {

    private static final Logger log = LoggerFactory.getLogger(SSOUserManagementServiceImpl.class);

    @Autowired
    private SSOUserService ssoUserService;

    @Autowired
    private RemoteMemberService remoteMemberService;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public SSOUser createFromMember(Member member) {
        try {
            SSOUser ssoUser = new SSOUser();
            ssoUser.setUsername(member.getMemberPhone());
            ssoUser.setNickname(member.getMemberRealName());
            ssoUser.setPhone(member.getMemberPhone());
            ssoUser.setPassword(member.getMemberPassword()); // 已加密的密码
            ssoUser.setStatus(1); // 正常状态
            ssoUser.setCreateTime(new Date());
            
            int result = ssoUserService.insertSSOUser(ssoUser);
            if (result > 0) {
                log.info("从Member创建SSO用户成功: {}", member.getMemberPhone());
                return ssoUser;
            } else {
                log.warn("从Member创建SSO用户失败: {}", member.getMemberPhone());
                return null;
            }
        } catch (Exception e) {
            log.error("从Member创建SSO用户异常: {}", member.getMemberPhone(), e);
            return null;
        }
    }

    @Override
    public boolean updateFromMember(Member member) {
        try {
            SSOUser existingUser = ssoUserService.selectSSOUserByUsername(member.getMemberPhone());
            if (existingUser == null) {
                log.warn("SSO用户不存在，无法更新: {}", member.getMemberPhone());
                return false;
            }

            // 更新基础信息
            existingUser.setNickname(member.getMemberRealName());
            existingUser.setPhone(member.getMemberPhone());
            // 注意：通常不在这里更新密码
            
            int result = ssoUserService.updateSSOUser(existingUser);
            if (result > 0) {
                log.info("更新SSO用户成功: {}", member.getMemberPhone());
                return true;
            } else {
                log.warn("更新SSO用户失败: {}", member.getMemberPhone());
                return false;
            }
        } catch (Exception e) {
            log.error("更新SSO用户异常: {}", member.getMemberPhone(), e);
            return false;
        }
    }

    @Override
    public SSOUser ensureSSOUserExists(String phone) {
        try {
            // 先检查SSO用户是否存在
            SSOUser ssoUser = ssoUserService.selectSSOUserByUsername(phone);
            if (ssoUser != null) {
                return ssoUser;
            }

            // SSO用户不存在，从主系统查询Member信息
            R<Member> memberResult = remoteMemberService.getMemberInfo(phone, "inner");
            if (memberResult != null && memberResult.getData() != null) {
                Member member = memberResult.getData();
                return createFromMember(member);
            } else {
                log.warn("主系统中也不存在该用户: {}", phone);
                return null;
            }
        } catch (Exception e) {
            log.error("确保SSO用户存在时异常: {}", phone, e);
            return null;
        }
    }

    @Override
    public boolean createSSOUserForRegistration(String memberPhone, String memberRealName, String password) {
        long startTime = System.currentTimeMillis();
        log.info("=== 开始创建SSO用户: {} ===", memberPhone);
        log.info("参数 - 手机号: {}, 姓名: {}, 密码长度: {}",
            memberPhone, memberRealName, password != null ? password.length() : 0);

        try {
            // 检查用户是否已存在（优化：先检查缓存）
            long checkStartTime = System.currentTimeMillis();
            SSOUser existingUser = ssoUserService.selectSSOUserByUsername(memberPhone);
            long checkEndTime = System.currentTimeMillis();
            log.info("检查用户存在性耗时: {}ms, 用户: {}, 结果: {}",
                (checkEndTime - checkStartTime), memberPhone, existingUser != null ? "存在" : "不存在");

            if (existingUser != null) {
                log.warn("SSO用户已存在，跳过创建: {}", memberPhone);
                return true; // 已存在也算成功
            }

            // 创建新的SSO用户
            long createStartTime = System.currentTimeMillis();
            SSOUser ssoUser = new SSOUser();
            ssoUser.setUsername(memberPhone);
            ssoUser.setNickname(memberRealName);
            ssoUser.setPhone(memberPhone);
            ssoUser.setPassword(password); // 使用传入的已加密密码
            ssoUser.setStatus(1); // 正常状态
            ssoUser.setCreateTime(new Date());

            int result = ssoUserService.insertSSOUser(ssoUser);
            long createEndTime = System.currentTimeMillis();
            log.info("插入SSO用户耗时: {}ms, 用户: {}", (createEndTime - createStartTime), memberPhone);

            if (result > 0) {
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("为注册创建SSO用户成功: {}, 总耗时: {}ms", memberPhone, totalTime);
                return true;
            } else {
                log.warn("为注册创建SSO用户失败: {}", memberPhone);
                return false;
            }
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("为注册创建SSO用户异常: {}, 耗时: {}ms", memberPhone, totalTime, e);
            return false;
        }
    }

    @Override
    public boolean updateSSOUserPassword(String phone, String password) {
        try {
            if (phone == null || password == null) {
                log.warn("更新SSO用户密码参数不完整: phone={}, password={}", phone, password != null ? "***" : "null");
                return false;
            }

            // 查找SSO用户
            SSOUser ssoUser = ssoUserService.selectSSOUserByUsername(phone);
            if (ssoUser == null) {
                // 尝试用手机号查询
                ssoUser = ssoUserService.selectSSOUserByPhone(phone);
            }

            if (ssoUser == null) {
                log.warn("SSO用户不存在，无法更新密码: {}", phone);
                return false;
            }

            // 更新密码
            ssoUser.setPassword(password); // 传入的密码已经是加密后的
            int result = ssoUserService.updateSSOUser(ssoUser);

            if (result > 0) {
                log.info("SSO用户密码更新成功: {}", phone);
                return true;
            } else {
                log.warn("SSO用户密码更新失败: {}", phone);
                return false;
            }
        } catch (Exception e) {
            log.error("更新SSO用户密码异常: {}", phone, e);
            return false;
        }
    }

    @Override
    public int batchSyncMembersToSSO() {
        try {
            log.info("开始批量同步Member到SSO");

            // TODO: 实现批量同步逻辑
            // 1. 查询所有Member
            // 2. 检查对应的SSO用户是否存在
            // 3. 不存在则创建

            log.info("批量同步Member到SSO暂未实现");
            return 0;
        } catch (Exception e) {
            log.error("批量同步Member到SSO异常", e);
            return 0;
        }
    }
}
