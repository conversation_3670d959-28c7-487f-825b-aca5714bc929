{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue", "mtime": 1750476192303}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "_auth", "name", "data", "loadingText", "mounted", "handleCallback", "methods", "_this", "urlParams", "URLSearchParams", "window", "location", "search", "token", "get", "key", "redirect", "code", "state", "getSSOTokenByKey", "then", "response", "tokenData", "access_token", "$store", "commit", "setToken", "expires_in", "setExpiresIn", "$message", "success", "redirectUrl", "startsWith", "url", "URL", "pathname", "hash", "e", "console", "warn", "setTimeout", "$router", "push", "error", "msg", "catch", "handleSSOCallback"], "sources": ["src/views/sso-callback.vue"], "sourcesContent": ["<template>\n  <div class=\"sso-callback-container\">\n    <div class=\"loading-content\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">{{ loadingText }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { handleSSOCallback, getSSOTokenByKey } from \"@/api/login\";\nimport { setToken, setExpiresIn } from \"@/utils/auth\";\n\nexport default {\n  name: \"SSOCallback\",\n  data() {\n    return {\n      loadingText: \"正在处理SSO登录...\"\n    };\n  },\n  mounted() {\n    this.handleCallback();\n  },\n  methods: {\n    handleCallback() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const token = urlParams.get('token');\n      const key = urlParams.get('key');\n      const redirect = urlParams.get('redirect');\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n\n      // 如果有key参数，说明是从后端SSO回调重定向过来的，需要用key获取token\n      if (key) {\n        this.loadingText = \"正在获取登录凭证...\";\n\n        // 调用后端API用key换取token\n        getSSOTokenByKey(key)\n          .then(response => {\n            if (response.code === 200 && response.data) {\n              const tokenData = response.data;\n\n              this.loadingText = \"正在设置登录状态...\";\n\n              try {\n                // 设置token到store和cookies\n                const token = tokenData.access_token || tokenData.token;\n                this.$store.commit(\"SET_TOKEN\", token);\n                setToken(token);\n\n                // 设置过期时间\n                if (tokenData.expires_in) {\n                  this.$store.commit(\"SET_EXPIRES_IN\", tokenData.expires_in);\n                  setExpiresIn(tokenData.expires_in);\n                }\n\n                this.loadingText = \"登录成功，正在跳转...\";\n                this.$message.success(\"SSO登录成功\");\n\n                // 跳转到目标页面或首页\n                let redirectUrl = redirect || '/index';\n                // 如果redirect是完整URL，提取路径部分\n                if (redirectUrl.startsWith('http')) {\n                  try {\n                    const url = new URL(redirectUrl);\n                    redirectUrl = url.pathname + url.search + url.hash;\n                    // 如果提取的路径是根路径，跳转到首页\n                    if (redirectUrl === '/' || redirectUrl === '/login') {\n                      redirectUrl = '/index';\n                    }\n                  } catch (e) {\n                    console.warn('解析redirect URL失败，使用默认路径:', e);\n                    redirectUrl = '/index';\n                  }\n                }\n                setTimeout(() => {\n                  this.$router.push(redirectUrl);\n                }, 1000);\n\n              } catch (error) {\n                console.error(\"设置登录状态失败:\", error);\n                this.loadingText = \"登录状态设置失败\";\n                this.$message.error(\"登录状态设置失败\");\n                setTimeout(() => {\n                  this.$router.push('/login');\n                }, 2000);\n              }\n            } else {\n              this.loadingText = \"获取登录凭证失败\";\n              this.$message.error(response.msg || \"获取登录凭证失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"获取登录凭证异常:\", error);\n            this.loadingText = \"获取登录凭证异常\";\n            this.$message.error(\"获取登录凭证服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 如果有token参数，说明是直接传递token的方式\n      if (token) {\n        this.loadingText = \"正在设置登录状态...\";\n\n        try {\n          // 设置token到store和cookies\n          this.$store.commit(\"SET_TOKEN\", token);\n          setToken(token);\n\n          this.loadingText = \"登录成功，正在跳转...\";\n          this.$message.success(\"SSO登录成功\");\n\n          // 跳转到目标页面或首页\n          let redirectUrl = redirect || '/';\n          // 如果redirect是完整URL，提取路径部分\n          if (redirectUrl.startsWith('http')) {\n            try {\n              const url = new URL(redirectUrl);\n              redirectUrl = url.pathname + url.search + url.hash;\n            } catch (e) {\n              console.warn('解析redirect URL失败，使用默认路径:', e);\n              redirectUrl = '/';\n            }\n          }\n          setTimeout(() => {\n            this.$router.push(redirectUrl);\n          }, 1000);\n\n        } catch (error) {\n          console.error(\"设置登录状态失败:\", error);\n          this.loadingText = \"登录状态设置失败\";\n          this.$message.error(\"登录状态设置失败\");\n          setTimeout(() => {\n            this.$router.push('/login');\n          }, 2000);\n        }\n        return;\n      }\n\n      // 如果没有token但有code，说明是旧的API回调方式\n      if (code) {\n        this.loadingText = \"正在验证授权码...\";\n\n        handleSSOCallback(code, state)\n          .then(response => {\n            if (response.code === 200) {\n              this.loadingText = \"登录成功，正在跳转...\";\n              this.$message.success(\"SSO登录成功\");\n\n              // 设置token到store和cookies\n              if (response.data && response.data.access_token) {\n                this.$store.commit(\"SET_TOKEN\", response.data.access_token);\n                setToken(response.data.access_token);\n\n                // 设置过期时间\n                if (response.data.expires_in) {\n                  this.$store.commit(\"SET_EXPIRES_IN\", response.data.expires_in);\n                  setExpiresIn(response.data.expires_in);\n                }\n              }\n\n              // 跳转到目标页面或首页\n              let redirectUrl = state || '/';\n              // 如果redirect是完整URL，提取路径部分\n              if (redirectUrl.startsWith('http')) {\n                try {\n                  const url = new URL(redirectUrl);\n                  redirectUrl = url.pathname + url.search + url.hash;\n                } catch (e) {\n                  console.warn('解析redirect URL失败，使用默认路径:', e);\n                  redirectUrl = '/';\n                }\n              }\n              setTimeout(() => {\n                this.$router.push(redirectUrl);\n              }, 1000);\n            } else {\n              this.loadingText = \"SSO登录失败\";\n              this.$message.error(response.msg || \"SSO登录失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"SSO回调处理失败:\", error);\n            this.loadingText = \"SSO登录异常\";\n            this.$message.error(\"SSO登录服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 既没有token也没有code\n      this.loadingText = \"SSO登录失败：缺少必要参数\";\n      this.$message.error(\"SSO登录失败：缺少必要参数\");\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.sso-callback-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 18px;\n  font-weight: 500;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AAUA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,SAAA,OAAAC,eAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA;MACA,IAAAC,KAAA,GAAAL,SAAA,CAAAM,GAAA;MACA,IAAAC,GAAA,GAAAP,SAAA,CAAAM,GAAA;MACA,IAAAE,QAAA,GAAAR,SAAA,CAAAM,GAAA;MACA,IAAAG,IAAA,GAAAT,SAAA,CAAAM,GAAA;MACA,IAAAI,KAAA,GAAAV,SAAA,CAAAM,GAAA;;MAEA;MACA,IAAAC,GAAA;QACA,KAAAZ,WAAA;;QAEA;QACA,IAAAgB,uBAAA,EAAAJ,GAAA,EACAK,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAJ,IAAA,YAAAI,QAAA,CAAAnB,IAAA;YACA,IAAAoB,SAAA,GAAAD,QAAA,CAAAnB,IAAA;YAEAK,KAAA,CAAAJ,WAAA;YAEA;cACA;cACA,IAAAU,MAAA,GAAAS,SAAA,CAAAC,YAAA,IAAAD,SAAA,CAAAT,KAAA;cACAN,KAAA,CAAAiB,MAAA,CAAAC,MAAA,cAAAZ,MAAA;cACA,IAAAa,cAAA,EAAAb,MAAA;;cAEA;cACA,IAAAS,SAAA,CAAAK,UAAA;gBACApB,KAAA,CAAAiB,MAAA,CAAAC,MAAA,mBAAAH,SAAA,CAAAK,UAAA;gBACA,IAAAC,kBAAA,EAAAN,SAAA,CAAAK,UAAA;cACA;cAEApB,KAAA,CAAAJ,WAAA;cACAI,KAAA,CAAAsB,QAAA,CAAAC,OAAA;;cAEA;cACA,IAAAC,WAAA,GAAAf,QAAA;cACA;cACA,IAAAe,WAAA,CAAAC,UAAA;gBACA;kBACA,IAAAC,GAAA,OAAAC,GAAA,CAAAH,WAAA;kBACAA,WAAA,GAAAE,GAAA,CAAAE,QAAA,GAAAF,GAAA,CAAArB,MAAA,GAAAqB,GAAA,CAAAG,IAAA;kBACA;kBACA,IAAAL,WAAA,YAAAA,WAAA;oBACAA,WAAA;kBACA;gBACA,SAAAM,CAAA;kBACAC,OAAA,CAAAC,IAAA,6BAAAF,CAAA;kBACAN,WAAA;gBACA;cACA;cACAS,UAAA;gBACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA,CAAAX,WAAA;cACA;YAEA,SAAAY,KAAA;cACAL,OAAA,CAAAK,KAAA,cAAAA,KAAA;cACApC,KAAA,CAAAJ,WAAA;cACAI,KAAA,CAAAsB,QAAA,CAAAc,KAAA;cACAH,UAAA;gBACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA;cACA;YACA;UACA;YACAnC,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAsB,QAAA,CAAAc,KAAA,CAAAtB,QAAA,CAAAuB,GAAA;YACAJ,UAAA;cACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA;YACA;UACA;QACA,GACAG,KAAA,WAAAF,KAAA;UACAL,OAAA,CAAAK,KAAA,cAAAA,KAAA;UACApC,KAAA,CAAAJ,WAAA;UACAI,KAAA,CAAAsB,QAAA,CAAAc,KAAA;UACAH,UAAA;YACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAA7B,KAAA;QACA,KAAAV,WAAA;QAEA;UACA;UACA,KAAAqB,MAAA,CAAAC,MAAA,cAAAZ,KAAA;UACA,IAAAa,cAAA,EAAAb,KAAA;UAEA,KAAAV,WAAA;UACA,KAAA0B,QAAA,CAAAC,OAAA;;UAEA;UACA,IAAAC,WAAA,GAAAf,QAAA;UACA;UACA,IAAAe,WAAA,CAAAC,UAAA;YACA;cACA,IAAAC,GAAA,OAAAC,GAAA,CAAAH,WAAA;cACAA,WAAA,GAAAE,GAAA,CAAAE,QAAA,GAAAF,GAAA,CAAArB,MAAA,GAAAqB,GAAA,CAAAG,IAAA;YACA,SAAAC,CAAA;cACAC,OAAA,CAAAC,IAAA,6BAAAF,CAAA;cACAN,WAAA;YACA;UACA;UACAS,UAAA;YACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA,CAAAX,WAAA;UACA;QAEA,SAAAY,KAAA;UACAL,OAAA,CAAAK,KAAA,cAAAA,KAAA;UACA,KAAAxC,WAAA;UACA,KAAA0B,QAAA,CAAAc,KAAA;UACAH,UAAA;YACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAAzB,IAAA;QACA,KAAAd,WAAA;QAEA,IAAA2C,wBAAA,EAAA7B,IAAA,EAAAC,KAAA,EACAE,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAJ,IAAA;YACAV,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAsB,QAAA,CAAAC,OAAA;;YAEA;YACA,IAAAT,QAAA,CAAAnB,IAAA,IAAAmB,QAAA,CAAAnB,IAAA,CAAAqB,YAAA;cACAhB,KAAA,CAAAiB,MAAA,CAAAC,MAAA,cAAAJ,QAAA,CAAAnB,IAAA,CAAAqB,YAAA;cACA,IAAAG,cAAA,EAAAL,QAAA,CAAAnB,IAAA,CAAAqB,YAAA;;cAEA;cACA,IAAAF,QAAA,CAAAnB,IAAA,CAAAyB,UAAA;gBACApB,KAAA,CAAAiB,MAAA,CAAAC,MAAA,mBAAAJ,QAAA,CAAAnB,IAAA,CAAAyB,UAAA;gBACA,IAAAC,kBAAA,EAAAP,QAAA,CAAAnB,IAAA,CAAAyB,UAAA;cACA;YACA;;YAEA;YACA,IAAAI,YAAA,GAAAb,KAAA;YACA;YACA,IAAAa,YAAA,CAAAC,UAAA;cACA;gBACA,IAAAC,IAAA,OAAAC,GAAA,CAAAH,YAAA;gBACAA,YAAA,GAAAE,IAAA,CAAAE,QAAA,GAAAF,IAAA,CAAArB,MAAA,GAAAqB,IAAA,CAAAG,IAAA;cACA,SAAAC,CAAA;gBACAC,OAAA,CAAAC,IAAA,6BAAAF,CAAA;gBACAN,YAAA;cACA;YACA;YACAS,UAAA;cACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA,CAAAX,YAAA;YACA;UACA;YACAxB,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAsB,QAAA,CAAAc,KAAA,CAAAtB,QAAA,CAAAuB,GAAA;YACAJ,UAAA;cACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA;YACA;UACA;QACA,GACAG,KAAA,WAAAF,KAAA;UACAL,OAAA,CAAAK,KAAA,eAAAA,KAAA;UACApC,KAAA,CAAAJ,WAAA;UACAI,KAAA,CAAAsB,QAAA,CAAAc,KAAA;UACAH,UAAA;YACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,KAAAvC,WAAA;MACA,KAAA0B,QAAA,CAAAc,KAAA;MACAH,UAAA;QACAjC,KAAA,CAAAkC,OAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}