{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue", "mtime": 1750416009613}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "name", "data", "loadingText", "mounted", "handleCallback", "methods", "_this", "urlParams", "URLSearchParams", "window", "location", "search", "token", "get", "key", "redirect", "code", "state", "getSSOTokenByKey", "then", "response", "tokenData", "$store", "commit", "access_token", "$message", "success", "redirectUrl", "setTimeout", "$router", "push", "error", "console", "msg", "catch", "handleSSOCallback"], "sources": ["src/views/sso-callback.vue"], "sourcesContent": ["<template>\n  <div class=\"sso-callback-container\">\n    <div class=\"loading-content\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">{{ loadingText }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { handleSSOCallback, getSSOTokenByKey } from \"@/api/login\";\n\nexport default {\n  name: \"SSOCallback\",\n  data() {\n    return {\n      loadingText: \"正在处理SSO登录...\"\n    };\n  },\n  mounted() {\n    this.handleCallback();\n  },\n  methods: {\n    handleCallback() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const token = urlParams.get('token');\n      const key = urlParams.get('key');\n      const redirect = urlParams.get('redirect');\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n\n      // 如果有key参数，说明是从后端SSO回调重定向过来的，需要用key获取token\n      if (key) {\n        this.loadingText = \"正在获取登录凭证...\";\n\n        // 调用后端API用key换取token\n        getSSOTokenByKey(key)\n          .then(response => {\n            if (response.code === 200 && response.data) {\n              const tokenData = response.data;\n\n              this.loadingText = \"正在设置登录状态...\";\n\n              try {\n                // 设置token到store\n                this.$store.commit(\"SET_TOKEN\", tokenData.access_token || tokenData.token);\n\n                this.loadingText = \"登录成功，正在跳转...\";\n                this.$message.success(\"SSO登录成功\");\n\n                // 跳转到目标页面或首页\n                const redirectUrl = redirect || '/';\n                setTimeout(() => {\n                  this.$router.push(redirectUrl);\n                }, 1000);\n\n              } catch (error) {\n                console.error(\"设置登录状态失败:\", error);\n                this.loadingText = \"登录状态设置失败\";\n                this.$message.error(\"登录状态设置失败\");\n                setTimeout(() => {\n                  this.$router.push('/login');\n                }, 2000);\n              }\n            } else {\n              this.loadingText = \"获取登录凭证失败\";\n              this.$message.error(response.msg || \"获取登录凭证失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"获取登录凭证异常:\", error);\n            this.loadingText = \"获取登录凭证异常\";\n            this.$message.error(\"获取登录凭证服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 如果有token参数，说明是直接传递token的方式\n      if (token) {\n        this.loadingText = \"正在设置登录状态...\";\n\n        try {\n          // 设置token到store\n          this.$store.commit(\"SET_TOKEN\", token);\n\n          this.loadingText = \"登录成功，正在跳转...\";\n          this.$message.success(\"SSO登录成功\");\n\n          // 跳转到目标页面或首页\n          const redirectUrl = redirect || '/';\n          setTimeout(() => {\n            this.$router.push(redirectUrl);\n          }, 1000);\n\n        } catch (error) {\n          console.error(\"设置登录状态失败:\", error);\n          this.loadingText = \"登录状态设置失败\";\n          this.$message.error(\"登录状态设置失败\");\n          setTimeout(() => {\n            this.$router.push('/login');\n          }, 2000);\n        }\n        return;\n      }\n\n      // 如果没有token但有code，说明是旧的API回调方式\n      if (code) {\n        this.loadingText = \"正在验证授权码...\";\n\n        handleSSOCallback(code, state)\n          .then(response => {\n            if (response.code === 200) {\n              this.loadingText = \"登录成功，正在跳转...\";\n              this.$message.success(\"SSO登录成功\");\n\n              // 设置token\n              if (response.data && response.data.access_token) {\n                this.$store.commit(\"SET_TOKEN\", response.data.access_token);\n              }\n\n              // 跳转到目标页面或首页\n              const redirectUrl = state || '/';\n              setTimeout(() => {\n                this.$router.push(redirectUrl);\n              }, 1000);\n            } else {\n              this.loadingText = \"SSO登录失败\";\n              this.$message.error(response.msg || \"SSO登录失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"SSO回调处理失败:\", error);\n            this.loadingText = \"SSO登录异常\";\n            this.$message.error(\"SSO登录服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 既没有token也没有code\n      this.loadingText = \"SSO登录失败：缺少必要参数\";\n      this.$message.error(\"SSO登录失败：缺少必要参数\");\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.sso-callback-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 18px;\n  font-weight: 500;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AAUA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,SAAA,OAAAC,eAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA;MACA,IAAAC,KAAA,GAAAL,SAAA,CAAAM,GAAA;MACA,IAAAC,GAAA,GAAAP,SAAA,CAAAM,GAAA;MACA,IAAAE,QAAA,GAAAR,SAAA,CAAAM,GAAA;MACA,IAAAG,IAAA,GAAAT,SAAA,CAAAM,GAAA;MACA,IAAAI,KAAA,GAAAV,SAAA,CAAAM,GAAA;;MAEA;MACA,IAAAC,GAAA;QACA,KAAAZ,WAAA;;QAEA;QACA,IAAAgB,uBAAA,EAAAJ,GAAA,EACAK,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAJ,IAAA,YAAAI,QAAA,CAAAnB,IAAA;YACA,IAAAoB,SAAA,GAAAD,QAAA,CAAAnB,IAAA;YAEAK,KAAA,CAAAJ,WAAA;YAEA;cACA;cACAI,KAAA,CAAAgB,MAAA,CAAAC,MAAA,cAAAF,SAAA,CAAAG,YAAA,IAAAH,SAAA,CAAAT,KAAA;cAEAN,KAAA,CAAAJ,WAAA;cACAI,KAAA,CAAAmB,QAAA,CAAAC,OAAA;;cAEA;cACA,IAAAC,WAAA,GAAAZ,QAAA;cACAa,UAAA;gBACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA,CAAAH,WAAA;cACA;YAEA,SAAAI,KAAA;cACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;cACAzB,KAAA,CAAAJ,WAAA;cACAI,KAAA,CAAAmB,QAAA,CAAAM,KAAA;cACAH,UAAA;gBACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA;cACA;YACA;UACA;YACAxB,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAmB,QAAA,CAAAM,KAAA,CAAAX,QAAA,CAAAa,GAAA;YACAL,UAAA;cACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA;YACA;UACA;QACA,GACAI,KAAA,WAAAH,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;UACAzB,KAAA,CAAAJ,WAAA;UACAI,KAAA,CAAAmB,QAAA,CAAAM,KAAA;UACAH,UAAA;YACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAAlB,KAAA;QACA,KAAAV,WAAA;QAEA;UACA;UACA,KAAAoB,MAAA,CAAAC,MAAA,cAAAX,KAAA;UAEA,KAAAV,WAAA;UACA,KAAAuB,QAAA,CAAAC,OAAA;;UAEA;UACA,IAAAC,WAAA,GAAAZ,QAAA;UACAa,UAAA;YACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA,CAAAH,WAAA;UACA;QAEA,SAAAI,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;UACA,KAAA7B,WAAA;UACA,KAAAuB,QAAA,CAAAM,KAAA;UACAH,UAAA;YACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAAd,IAAA;QACA,KAAAd,WAAA;QAEA,IAAAiC,wBAAA,EAAAnB,IAAA,EAAAC,KAAA,EACAE,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAJ,IAAA;YACAV,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAmB,QAAA,CAAAC,OAAA;;YAEA;YACA,IAAAN,QAAA,CAAAnB,IAAA,IAAAmB,QAAA,CAAAnB,IAAA,CAAAuB,YAAA;cACAlB,KAAA,CAAAgB,MAAA,CAAAC,MAAA,cAAAH,QAAA,CAAAnB,IAAA,CAAAuB,YAAA;YACA;;YAEA;YACA,IAAAG,YAAA,GAAAV,KAAA;YACAW,UAAA;cACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA,CAAAH,YAAA;YACA;UACA;YACArB,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAmB,QAAA,CAAAM,KAAA,CAAAX,QAAA,CAAAa,GAAA;YACAL,UAAA;cACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA;YACA;UACA;QACA,GACAI,KAAA,WAAAH,KAAA;UACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;UACAzB,KAAA,CAAAJ,WAAA;UACAI,KAAA,CAAAmB,QAAA,CAAAM,KAAA;UACAH,UAAA;YACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,KAAA5B,WAAA;MACA,KAAAuB,QAAA,CAAAM,KAAA;MACAH,UAAA;QACAtB,KAAA,CAAAuB,OAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}