{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue", "mtime": 1750413626109}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc2VhcmNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIudXJsLXNlYXJjaC1wYXJhbXMuanMiKTsKdmFyIF9sb2dpbiA9IHJlcXVpcmUoIkAvYXBpL2xvZ2luIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiU1NPQ2FsbGJhY2siLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nVGV4dDogIuato+WcqOWkhOeQhlNTT+eZu+W9lS4uLiIKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5oYW5kbGVDYWxsYmFjaygpOwogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlQ2FsbGJhY2s6IGZ1bmN0aW9uIGhhbmRsZUNhbGxiYWNrKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB2YXIgdXJsUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh3aW5kb3cubG9jYXRpb24uc2VhcmNoKTsKICAgICAgdmFyIHRva2VuID0gdXJsUGFyYW1zLmdldCgndG9rZW4nKTsKICAgICAgdmFyIHJlZGlyZWN0ID0gdXJsUGFyYW1zLmdldCgncmVkaXJlY3QnKTsKICAgICAgdmFyIGNvZGUgPSB1cmxQYXJhbXMuZ2V0KCdjb2RlJyk7CiAgICAgIHZhciBzdGF0ZSA9IHVybFBhcmFtcy5nZXQoJ3N0YXRlJyk7CgogICAgICAvLyDlpoLmnpzmnIl0b2tlbuWPguaVsO+8jOivtOaYjuaYr+S7juWQjuerr1NTT+Wbnuiwg+mHjeWumuWQkei/h+adpeeahAogICAgICBpZiAodG9rZW4pIHsKICAgICAgICB0aGlzLmxvYWRpbmdUZXh0ID0gIuato+WcqOiuvue9rueZu+W9leeKtuaAgS4uLiI7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIC8vIOiuvue9rnRva2Vu5Yiwc3RvcmUKICAgICAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgiU0VUX1RPS0VOIiwgdG9rZW4pOwogICAgICAgICAgdGhpcy5sb2FkaW5nVGV4dCA9ICLnmbvlvZXmiJDlip/vvIzmraPlnKjot7PovawuLi4iOwogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCJTU0/nmbvlvZXmiJDlip8iKTsKCiAgICAgICAgICAvLyDot7PovazliLDnm67moIfpobXpnaLmiJbpppbpobUKICAgICAgICAgIHZhciByZWRpcmVjdFVybCA9IHJlZGlyZWN0IHx8ICcvJzsKICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2gocmVkaXJlY3RVcmwpOwogICAgICAgICAgfSwgMTAwMCk7CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiuvue9rueZu+W9leeKtuaAgeWksei0pToiLCBlcnJvcik7CiAgICAgICAgICB0aGlzLmxvYWRpbmdUZXh0ID0gIueZu+W9leeKtuaAgeiuvue9ruWksei0pSI7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLnmbvlvZXnirbmgIHorr7nva7lpLHotKUiKTsKICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2goJy9sb2dpbicpOwogICAgICAgICAgfSwgMjAwMCk7CiAgICAgICAgfQogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5aaC5p6c5rKh5pyJdG9rZW7kvYbmnIljb2Rl77yM6K+05piO5piv5pen55qEQVBJ5Zue6LCD5pa55byPCiAgICAgIGlmIChjb2RlKSB7CiAgICAgICAgdGhpcy5sb2FkaW5nVGV4dCA9ICLmraPlnKjpqozor4HmjojmnYPnoIEuLi4iOwogICAgICAgICgwLCBfbG9naW4uaGFuZGxlU1NPQ2FsbGJhY2spKGNvZGUsIHN0YXRlKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICBfdGhpcy5sb2FkaW5nVGV4dCA9ICLnmbvlvZXmiJDlip/vvIzmraPlnKjot7PovawuLi4iOwogICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCJTU0/nmbvlvZXmiJDlip8iKTsKCiAgICAgICAgICAgIC8vIOiuvue9rnRva2VuCiAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuYWNjZXNzX3Rva2VuKSB7CiAgICAgICAgICAgICAgX3RoaXMuJHN0b3JlLmNvbW1pdCgiU0VUX1RPS0VOIiwgcmVzcG9uc2UuZGF0YS5hY2Nlc3NfdG9rZW4pOwogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyDot7PovazliLDnm67moIfpobXpnaLmiJbpppbpobUKICAgICAgICAgICAgdmFyIF9yZWRpcmVjdFVybCA9IHN0YXRlIHx8ICcvJzsKICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgX3RoaXMuJHJvdXRlci5wdXNoKF9yZWRpcmVjdFVybCk7CiAgICAgICAgICAgIH0sIDEwMDApOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMubG9hZGluZ1RleHQgPSAiU1NP55m75b2V5aSx6LSlIjsKICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICJTU0/nmbvlvZXlpLHotKUiKTsKICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgX3RoaXMuJHJvdXRlci5wdXNoKCcvbG9naW4nKTsKICAgICAgICAgICAgfSwgMjAwMCk7CiAgICAgICAgICB9CiAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCJTU0/lm57osIPlpITnkIblpLHotKU6IiwgZXJyb3IpOwogICAgICAgICAgX3RoaXMubG9hZGluZ1RleHQgPSAiU1NP55m75b2V5byC5bi4IjsKICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKCJTU0/nmbvlvZXmnI3liqHlvILluLgiKTsKICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2goJy9sb2dpbicpOwogICAgICAgICAgfSwgMjAwMCk7CiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDml6LmsqHmnIl0b2tlbuS5n+ayoeaciWNvZGUKICAgICAgdGhpcy5sb2FkaW5nVGV4dCA9ICJTU0/nmbvlvZXlpLHotKXvvJrnvLrlsJHlv4XopoHlj4LmlbAiOwogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJTU0/nmbvlvZXlpLHotKXvvJrnvLrlsJHlv4XopoHlj4LmlbAiKTsKICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMuJHJvdXRlci5wdXNoKCcvbG9naW4nKTsKICAgICAgfSwgMjAwMCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_login", "require", "name", "data", "loadingText", "mounted", "handleCallback", "methods", "_this", "urlParams", "URLSearchParams", "window", "location", "search", "token", "get", "redirect", "code", "state", "$store", "commit", "$message", "success", "redirectUrl", "setTimeout", "$router", "push", "error", "console", "handleSSOCallback", "then", "response", "access_token", "msg", "catch"], "sources": ["src/views/sso-callback.vue"], "sourcesContent": ["<template>\n  <div class=\"sso-callback-container\">\n    <div class=\"loading-content\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">{{ loadingText }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { handleSSOCallback } from \"@/api/login\";\n\nexport default {\n  name: \"SSOCallback\",\n  data() {\n    return {\n      loadingText: \"正在处理SSO登录...\"\n    };\n  },\n  mounted() {\n    this.handleCallback();\n  },\n  methods: {\n    handleCallback() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const token = urlParams.get('token');\n      const redirect = urlParams.get('redirect');\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n\n      // 如果有token参数，说明是从后端SSO回调重定向过来的\n      if (token) {\n        this.loadingText = \"正在设置登录状态...\";\n\n        try {\n          // 设置token到store\n          this.$store.commit(\"SET_TOKEN\", token);\n\n          this.loadingText = \"登录成功，正在跳转...\";\n          this.$message.success(\"SSO登录成功\");\n\n          // 跳转到目标页面或首页\n          const redirectUrl = redirect || '/';\n          setTimeout(() => {\n            this.$router.push(redirectUrl);\n          }, 1000);\n\n        } catch (error) {\n          console.error(\"设置登录状态失败:\", error);\n          this.loadingText = \"登录状态设置失败\";\n          this.$message.error(\"登录状态设置失败\");\n          setTimeout(() => {\n            this.$router.push('/login');\n          }, 2000);\n        }\n        return;\n      }\n\n      // 如果没有token但有code，说明是旧的API回调方式\n      if (code) {\n        this.loadingText = \"正在验证授权码...\";\n\n        handleSSOCallback(code, state)\n          .then(response => {\n            if (response.code === 200) {\n              this.loadingText = \"登录成功，正在跳转...\";\n              this.$message.success(\"SSO登录成功\");\n\n              // 设置token\n              if (response.data && response.data.access_token) {\n                this.$store.commit(\"SET_TOKEN\", response.data.access_token);\n              }\n\n              // 跳转到目标页面或首页\n              const redirectUrl = state || '/';\n              setTimeout(() => {\n                this.$router.push(redirectUrl);\n              }, 1000);\n            } else {\n              this.loadingText = \"SSO登录失败\";\n              this.$message.error(response.msg || \"SSO登录失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"SSO回调处理失败:\", error);\n            this.loadingText = \"SSO登录异常\";\n            this.$message.error(\"SSO登录服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 既没有token也没有code\n      this.loadingText = \"SSO登录失败：缺少必要参数\";\n      this.$message.error(\"SSO登录失败：缺少必要参数\");\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.sso-callback-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 18px;\n  font-weight: 500;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AAUA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,SAAA,OAAAC,eAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA;MACA,IAAAC,KAAA,GAAAL,SAAA,CAAAM,GAAA;MACA,IAAAC,QAAA,GAAAP,SAAA,CAAAM,GAAA;MACA,IAAAE,IAAA,GAAAR,SAAA,CAAAM,GAAA;MACA,IAAAG,KAAA,GAAAT,SAAA,CAAAM,GAAA;;MAEA;MACA,IAAAD,KAAA;QACA,KAAAV,WAAA;QAEA;UACA;UACA,KAAAe,MAAA,CAAAC,MAAA,cAAAN,KAAA;UAEA,KAAAV,WAAA;UACA,KAAAiB,QAAA,CAAAC,OAAA;;UAEA;UACA,IAAAC,WAAA,GAAAP,QAAA;UACAQ,UAAA;YACAhB,KAAA,CAAAiB,OAAA,CAAAC,IAAA,CAAAH,WAAA;UACA;QAEA,SAAAI,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;UACA,KAAAvB,WAAA;UACA,KAAAiB,QAAA,CAAAM,KAAA;UACAH,UAAA;YACAhB,KAAA,CAAAiB,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAAT,IAAA;QACA,KAAAb,WAAA;QAEA,IAAAyB,wBAAA,EAAAZ,IAAA,EAAAC,KAAA,EACAY,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAd,IAAA;YACAT,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAa,QAAA,CAAAC,OAAA;;YAEA;YACA,IAAAS,QAAA,CAAA5B,IAAA,IAAA4B,QAAA,CAAA5B,IAAA,CAAA6B,YAAA;cACAxB,KAAA,CAAAW,MAAA,CAAAC,MAAA,cAAAW,QAAA,CAAA5B,IAAA,CAAA6B,YAAA;YACA;;YAEA;YACA,IAAAT,YAAA,GAAAL,KAAA;YACAM,UAAA;cACAhB,KAAA,CAAAiB,OAAA,CAAAC,IAAA,CAAAH,YAAA;YACA;UACA;YACAf,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAa,QAAA,CAAAM,KAAA,CAAAI,QAAA,CAAAE,GAAA;YACAT,UAAA;cACAhB,KAAA,CAAAiB,OAAA,CAAAC,IAAA;YACA;UACA;QACA,GACAQ,KAAA,WAAAP,KAAA;UACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;UACAnB,KAAA,CAAAJ,WAAA;UACAI,KAAA,CAAAa,QAAA,CAAAM,KAAA;UACAH,UAAA;YACAhB,KAAA,CAAAiB,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,KAAAtB,WAAA;MACA,KAAAiB,QAAA,CAAAM,KAAA;MACAH,UAAA;QACAhB,KAAA,CAAAiB,OAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}