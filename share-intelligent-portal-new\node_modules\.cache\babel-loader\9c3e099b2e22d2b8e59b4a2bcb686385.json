{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue", "mtime": 1750474296593}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "name", "data", "loadingText", "mounted", "handleCallback", "methods", "_this", "urlParams", "URLSearchParams", "window", "location", "search", "token", "get", "key", "redirect", "code", "state", "getSSOTokenByKey", "then", "response", "tokenData", "$store", "commit", "access_token", "$message", "success", "redirectUrl", "startsWith", "url", "URL", "pathname", "hash", "e", "console", "warn", "setTimeout", "$router", "push", "error", "msg", "catch", "handleSSOCallback"], "sources": ["src/views/sso-callback.vue"], "sourcesContent": ["<template>\n  <div class=\"sso-callback-container\">\n    <div class=\"loading-content\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">{{ loadingText }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { handleSSOCallback, getSSOTokenByKey } from \"@/api/login\";\n\nexport default {\n  name: \"SSOCallback\",\n  data() {\n    return {\n      loadingText: \"正在处理SSO登录...\"\n    };\n  },\n  mounted() {\n    this.handleCallback();\n  },\n  methods: {\n    handleCallback() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const token = urlParams.get('token');\n      const key = urlParams.get('key');\n      const redirect = urlParams.get('redirect');\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n\n      // 如果有key参数，说明是从后端SSO回调重定向过来的，需要用key获取token\n      if (key) {\n        this.loadingText = \"正在获取登录凭证...\";\n\n        // 调用后端API用key换取token\n        getSSOTokenByKey(key)\n          .then(response => {\n            if (response.code === 200 && response.data) {\n              const tokenData = response.data;\n\n              this.loadingText = \"正在设置登录状态...\";\n\n              try {\n                // 设置token到store\n                this.$store.commit(\"SET_TOKEN\", tokenData.access_token || tokenData.token);\n\n                this.loadingText = \"登录成功，正在跳转...\";\n                this.$message.success(\"SSO登录成功\");\n\n                // 跳转到目标页面或首页\n                let redirectUrl = redirect || '/';\n                // 如果redirect是完整URL，提取路径部分\n                if (redirectUrl.startsWith('http')) {\n                  try {\n                    const url = new URL(redirectUrl);\n                    redirectUrl = url.pathname + url.search + url.hash;\n                  } catch (e) {\n                    console.warn('解析redirect URL失败，使用默认路径:', e);\n                    redirectUrl = '/';\n                  }\n                }\n                setTimeout(() => {\n                  this.$router.push(redirectUrl);\n                }, 1000);\n\n              } catch (error) {\n                console.error(\"设置登录状态失败:\", error);\n                this.loadingText = \"登录状态设置失败\";\n                this.$message.error(\"登录状态设置失败\");\n                setTimeout(() => {\n                  this.$router.push('/login');\n                }, 2000);\n              }\n            } else {\n              this.loadingText = \"获取登录凭证失败\";\n              this.$message.error(response.msg || \"获取登录凭证失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"获取登录凭证异常:\", error);\n            this.loadingText = \"获取登录凭证异常\";\n            this.$message.error(\"获取登录凭证服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 如果有token参数，说明是直接传递token的方式\n      if (token) {\n        this.loadingText = \"正在设置登录状态...\";\n\n        try {\n          // 设置token到store\n          this.$store.commit(\"SET_TOKEN\", token);\n\n          this.loadingText = \"登录成功，正在跳转...\";\n          this.$message.success(\"SSO登录成功\");\n\n          // 跳转到目标页面或首页\n          let redirectUrl = redirect || '/';\n          // 如果redirect是完整URL，提取路径部分\n          if (redirectUrl.startsWith('http')) {\n            try {\n              const url = new URL(redirectUrl);\n              redirectUrl = url.pathname + url.search + url.hash;\n            } catch (e) {\n              console.warn('解析redirect URL失败，使用默认路径:', e);\n              redirectUrl = '/';\n            }\n          }\n          setTimeout(() => {\n            this.$router.push(redirectUrl);\n          }, 1000);\n\n        } catch (error) {\n          console.error(\"设置登录状态失败:\", error);\n          this.loadingText = \"登录状态设置失败\";\n          this.$message.error(\"登录状态设置失败\");\n          setTimeout(() => {\n            this.$router.push('/login');\n          }, 2000);\n        }\n        return;\n      }\n\n      // 如果没有token但有code，说明是旧的API回调方式\n      if (code) {\n        this.loadingText = \"正在验证授权码...\";\n\n        handleSSOCallback(code, state)\n          .then(response => {\n            if (response.code === 200) {\n              this.loadingText = \"登录成功，正在跳转...\";\n              this.$message.success(\"SSO登录成功\");\n\n              // 设置token\n              if (response.data && response.data.access_token) {\n                this.$store.commit(\"SET_TOKEN\", response.data.access_token);\n              }\n\n              // 跳转到目标页面或首页\n              let redirectUrl = state || '/';\n              // 如果redirect是完整URL，提取路径部分\n              if (redirectUrl.startsWith('http')) {\n                try {\n                  const url = new URL(redirectUrl);\n                  redirectUrl = url.pathname + url.search + url.hash;\n                } catch (e) {\n                  console.warn('解析redirect URL失败，使用默认路径:', e);\n                  redirectUrl = '/';\n                }\n              }\n              setTimeout(() => {\n                this.$router.push(redirectUrl);\n              }, 1000);\n            } else {\n              this.loadingText = \"SSO登录失败\";\n              this.$message.error(response.msg || \"SSO登录失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"SSO回调处理失败:\", error);\n            this.loadingText = \"SSO登录异常\";\n            this.$message.error(\"SSO登录服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 既没有token也没有code\n      this.loadingText = \"SSO登录失败：缺少必要参数\";\n      this.$message.error(\"SSO登录失败：缺少必要参数\");\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.sso-callback-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 18px;\n  font-weight: 500;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AAUA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,SAAA,OAAAC,eAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA;MACA,IAAAC,KAAA,GAAAL,SAAA,CAAAM,GAAA;MACA,IAAAC,GAAA,GAAAP,SAAA,CAAAM,GAAA;MACA,IAAAE,QAAA,GAAAR,SAAA,CAAAM,GAAA;MACA,IAAAG,IAAA,GAAAT,SAAA,CAAAM,GAAA;MACA,IAAAI,KAAA,GAAAV,SAAA,CAAAM,GAAA;;MAEA;MACA,IAAAC,GAAA;QACA,KAAAZ,WAAA;;QAEA;QACA,IAAAgB,uBAAA,EAAAJ,GAAA,EACAK,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAJ,IAAA,YAAAI,QAAA,CAAAnB,IAAA;YACA,IAAAoB,SAAA,GAAAD,QAAA,CAAAnB,IAAA;YAEAK,KAAA,CAAAJ,WAAA;YAEA;cACA;cACAI,KAAA,CAAAgB,MAAA,CAAAC,MAAA,cAAAF,SAAA,CAAAG,YAAA,IAAAH,SAAA,CAAAT,KAAA;cAEAN,KAAA,CAAAJ,WAAA;cACAI,KAAA,CAAAmB,QAAA,CAAAC,OAAA;;cAEA;cACA,IAAAC,WAAA,GAAAZ,QAAA;cACA;cACA,IAAAY,WAAA,CAAAC,UAAA;gBACA;kBACA,IAAAC,GAAA,OAAAC,GAAA,CAAAH,WAAA;kBACAA,WAAA,GAAAE,GAAA,CAAAE,QAAA,GAAAF,GAAA,CAAAlB,MAAA,GAAAkB,GAAA,CAAAG,IAAA;gBACA,SAAAC,CAAA;kBACAC,OAAA,CAAAC,IAAA,6BAAAF,CAAA;kBACAN,WAAA;gBACA;cACA;cACAS,UAAA;gBACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA,CAAAX,WAAA;cACA;YAEA,SAAAY,KAAA;cACAL,OAAA,CAAAK,KAAA,cAAAA,KAAA;cACAjC,KAAA,CAAAJ,WAAA;cACAI,KAAA,CAAAmB,QAAA,CAAAc,KAAA;cACAH,UAAA;gBACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA;cACA;YACA;UACA;YACAhC,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAmB,QAAA,CAAAc,KAAA,CAAAnB,QAAA,CAAAoB,GAAA;YACAJ,UAAA;cACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA;YACA;UACA;QACA,GACAG,KAAA,WAAAF,KAAA;UACAL,OAAA,CAAAK,KAAA,cAAAA,KAAA;UACAjC,KAAA,CAAAJ,WAAA;UACAI,KAAA,CAAAmB,QAAA,CAAAc,KAAA;UACAH,UAAA;YACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAA1B,KAAA;QACA,KAAAV,WAAA;QAEA;UACA;UACA,KAAAoB,MAAA,CAAAC,MAAA,cAAAX,KAAA;UAEA,KAAAV,WAAA;UACA,KAAAuB,QAAA,CAAAC,OAAA;;UAEA;UACA,IAAAC,WAAA,GAAAZ,QAAA;UACA;UACA,IAAAY,WAAA,CAAAC,UAAA;YACA;cACA,IAAAC,GAAA,OAAAC,GAAA,CAAAH,WAAA;cACAA,WAAA,GAAAE,GAAA,CAAAE,QAAA,GAAAF,GAAA,CAAAlB,MAAA,GAAAkB,GAAA,CAAAG,IAAA;YACA,SAAAC,CAAA;cACAC,OAAA,CAAAC,IAAA,6BAAAF,CAAA;cACAN,WAAA;YACA;UACA;UACAS,UAAA;YACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA,CAAAX,WAAA;UACA;QAEA,SAAAY,KAAA;UACAL,OAAA,CAAAK,KAAA,cAAAA,KAAA;UACA,KAAArC,WAAA;UACA,KAAAuB,QAAA,CAAAc,KAAA;UACAH,UAAA;YACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAAtB,IAAA;QACA,KAAAd,WAAA;QAEA,IAAAwC,wBAAA,EAAA1B,IAAA,EAAAC,KAAA,EACAE,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAJ,IAAA;YACAV,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAmB,QAAA,CAAAC,OAAA;;YAEA;YACA,IAAAN,QAAA,CAAAnB,IAAA,IAAAmB,QAAA,CAAAnB,IAAA,CAAAuB,YAAA;cACAlB,KAAA,CAAAgB,MAAA,CAAAC,MAAA,cAAAH,QAAA,CAAAnB,IAAA,CAAAuB,YAAA;YACA;;YAEA;YACA,IAAAG,YAAA,GAAAV,KAAA;YACA;YACA,IAAAU,YAAA,CAAAC,UAAA;cACA;gBACA,IAAAC,IAAA,OAAAC,GAAA,CAAAH,YAAA;gBACAA,YAAA,GAAAE,IAAA,CAAAE,QAAA,GAAAF,IAAA,CAAAlB,MAAA,GAAAkB,IAAA,CAAAG,IAAA;cACA,SAAAC,CAAA;gBACAC,OAAA,CAAAC,IAAA,6BAAAF,CAAA;gBACAN,YAAA;cACA;YACA;YACAS,UAAA;cACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA,CAAAX,YAAA;YACA;UACA;YACArB,KAAA,CAAAJ,WAAA;YACAI,KAAA,CAAAmB,QAAA,CAAAc,KAAA,CAAAnB,QAAA,CAAAoB,GAAA;YACAJ,UAAA;cACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA;YACA;UACA;QACA,GACAG,KAAA,WAAAF,KAAA;UACAL,OAAA,CAAAK,KAAA,eAAAA,KAAA;UACAjC,KAAA,CAAAJ,WAAA;UACAI,KAAA,CAAAmB,QAAA,CAAAc,KAAA;UACAH,UAAA;YACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,KAAApC,WAAA;MACA,KAAAuB,QAAA,CAAAc,KAAA;MACAH,UAAA;QACA9B,KAAA,CAAA+B,OAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}