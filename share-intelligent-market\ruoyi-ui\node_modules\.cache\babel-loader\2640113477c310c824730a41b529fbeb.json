{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\user\\profile\\userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\user\\profile\\userAvatar.vue", "mtime": 1750151094305}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_vueCropper", "_user", "components", "VueCropper", "props", "user", "type", "Object", "data", "open", "visible", "title", "options", "img", "store", "getters", "avatar", "autoCrop", "autoCropWidth", "autoCropHeight", "fixedBox", "previews", "methods", "editCropper", "modalOpened", "requestUpload", "rotateLeft", "$refs", "cropper", "rotateRight", "changeScale", "num", "beforeUpload", "file", "_this", "indexOf", "$modal", "msgError", "reader", "FileReader", "readAsDataURL", "onload", "result", "uploadImg", "_this2", "getCropBlob", "formData", "FormData", "append", "uploadAvatar", "then", "response", "status", "updateUserProfile", "userId", "url", "res", "commit", "msgSuccess", "realTime", "closeDialog"], "sources": ["src/views/system/user/profile/userAvatar.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"user-info-head\" @click=\"editCropper()\"><img v-bind:src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" /></div>\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body @opened=\"modalOpened\"  @close=\"closeDialog\">\r\n      <el-row>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <vue-cropper\r\n            ref=\"cropper\"\r\n            :img=\"options.img\"\r\n            :info=\"true\"\r\n            :autoCrop=\"options.autoCrop\"\r\n            :autoCropWidth=\"options.autoCropWidth\"\r\n            :autoCropHeight=\"options.autoCropHeight\"\r\n            :fixedBox=\"options.fixedBox\"\r\n            @realTime=\"realTime\"\r\n            v-if=\"visible\"\r\n          />\r\n        </el-col>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <div class=\"avatar-upload-preview\">\r\n            <img :src=\"previews.url\" :style=\"previews.img\" />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <br />\r\n      <el-row>\r\n        <el-col :lg=\"2\" :md=\"2\">\r\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\r\n            <el-button size=\"small\">\r\n              选择\r\n              <i class=\"el-icon-upload el-icon--right\"></i>\r\n            </el-button>\r\n          </el-upload>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 2}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\"></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 2, offset: 6}\" :md=\"2\">\r\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from \"@/store\";\r\nimport { VueCropper } from \"vue-cropper\";\r\nimport { uploadAvatar,getUserProfile,updateUserProfile } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  components: { VueCropper },\r\n  props: {\r\n    user: {\r\n      type: Object\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示cropper\r\n      visible: false,\r\n      // 弹出层标题\r\n      title: \"修改头像\",\r\n      options: {\r\n        img: store.getters.avatar, //裁剪图片的地址\r\n        autoCrop: true, // 是否默认生成截图框\r\n        autoCropWidth: 200, // 默认生成截图框宽度\r\n        autoCropHeight: 200, // 默认生成截图框高度\r\n        fixedBox: true // 固定截图框大小 不允许改变\r\n      },\r\n      previews: {}\r\n    };\r\n  },\r\n  methods: {\r\n    // 编辑头像\r\n    editCropper() {\r\n      this.open = true;\r\n    },\r\n    // 打开弹出层结束时的回调\r\n    modalOpened() {\r\n      this.visible = true;\r\n    },\r\n    // 覆盖默认的上传行为\r\n    requestUpload() {\r\n    },\r\n    // 向左旋转\r\n    rotateLeft() {\r\n      this.$refs.cropper.rotateLeft();\r\n    },\r\n    // 向右旋转\r\n    rotateRight() {\r\n      this.$refs.cropper.rotateRight();\r\n    },\r\n    // 图片缩放\r\n    changeScale(num) {\r\n      num = num || 1;\r\n      this.$refs.cropper.changeScale(num);\r\n    },\r\n    // 上传预处理\r\n    beforeUpload(file) {\r\n      if (file.type.indexOf(\"image/\") == -1) {\r\n        this.$modal.msgError(\"文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。\");\r\n      } else {\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          this.options.img = reader.result;\r\n        };\r\n      }\r\n    },\r\n    // 上传图片\r\n    uploadImg() {\r\n      this.$refs.cropper.getCropBlob(data => {\r\n        let formData = new FormData();\r\n        formData.append(\"avatarfile\", data);\r\n        uploadAvatar(formData).then(response => {\r\n          if(response.status == 0){\r\n            updateUserProfile({\r\n                userId: this.user.userId,\r\n                avatar: response.url\r\n            }).then(res => {\r\n              this.open = false;\r\n              this.options.img = response.url;\r\n              store.commit('SET_AVATAR', this.options.img);\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.visible = false;\r\n            })\r\n          }\r\n        });\r\n      });\r\n    },\r\n    // 实时预览\r\n    realTime(data) {\r\n      this.previews = data;\r\n    },\r\n    // 关闭窗口\r\n    closeDialog() {\r\n      this.options.img = store.getters.avatar\r\n      this.visible = false;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.user-info-head {\r\n  position: relative;\r\n  display: inline-block;\r\n  height: 120px;\r\n}\r\n\r\n.user-info-head:hover:after {\r\n  content: '+';\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  color: #eee;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  font-size: 24px;\r\n  font-style: normal;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  cursor: pointer;\r\n  line-height: 110px;\r\n  border-radius: 50%;\r\n}\r\n</style>"], "mappings": ";;;;;;;AAuDA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACAC,OAAA;QACAC,GAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAC,MAAA;QAAA;QACAC,QAAA;QAAA;QACAC,aAAA;QAAA;QACAC,cAAA;QAAA;QACAC,QAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAd,IAAA;IACA;IACA;IACAe,WAAA,WAAAA,YAAA;MACA,KAAAd,OAAA;IACA;IACA;IACAe,aAAA,WAAAA,cAAA,GACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAF,UAAA;IACA;IACA;IACAG,WAAA,WAAAA,YAAA;MACA,KAAAF,KAAA,CAAAC,OAAA,CAAAC,WAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACAA,GAAA,GAAAA,GAAA;MACA,KAAAJ,KAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,GAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,IAAA,CAAA3B,IAAA,CAAA6B,OAAA;QACA,KAAAC,MAAA,CAAAC,QAAA;MACA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,aAAA,CAAAP,IAAA;QACAK,MAAA,CAAAG,MAAA;UACAP,KAAA,CAAAtB,OAAA,CAAAC,GAAA,GAAAyB,MAAA,CAAAI,MAAA;QACA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAjB,KAAA,CAAAC,OAAA,CAAAiB,WAAA,WAAArC,IAAA;QACA,IAAAsC,QAAA,OAAAC,QAAA;QACAD,QAAA,CAAAE,MAAA,eAAAxC,IAAA;QACA,IAAAyC,kBAAA,EAAAH,QAAA,EAAAI,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,MAAA;YACA,IAAAC,uBAAA;cACAC,MAAA,EAAAV,MAAA,CAAAvC,IAAA,CAAAiD,MAAA;cACAtC,MAAA,EAAAmC,QAAA,CAAAI;YACA,GAAAL,IAAA,WAAAM,GAAA;cACAZ,MAAA,CAAAnC,IAAA;cACAmC,MAAA,CAAAhC,OAAA,CAAAC,GAAA,GAAAsC,QAAA,CAAAI,GAAA;cACAzC,cAAA,CAAA2C,MAAA,eAAAb,MAAA,CAAAhC,OAAA,CAAAC,GAAA;cACA+B,MAAA,CAAAR,MAAA,CAAAsB,UAAA;cACAd,MAAA,CAAAlC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAiD,QAAA,WAAAA,SAAAnD,IAAA;MACA,KAAAa,QAAA,GAAAb,IAAA;IACA;IACA;IACAoD,WAAA,WAAAA,YAAA;MACA,KAAAhD,OAAA,CAAAC,GAAA,GAAAC,cAAA,CAAAC,OAAA,CAAAC,MAAA;MACA,KAAAN,OAAA;IACA;EACA;AACA", "ignoreList": []}]}