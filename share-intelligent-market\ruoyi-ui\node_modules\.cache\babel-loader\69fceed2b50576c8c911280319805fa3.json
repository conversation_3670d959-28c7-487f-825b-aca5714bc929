{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\components\\lessDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\components\\lessDetails.vue", "mtime": 1750151094272}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfb2ZmZXIgPSByZXF1aXJlKCJAL2FwaS9wcm9qZWN0L29mZmVyIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGZvcm06IHsKICAgICAgICAiaW5xdWlyeSI6IHt9LAogICAgICAgICJvZmZlciI6IHt9LAogICAgICAgICJpdGVtcyI6IFtdCiAgICAgIH0KICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBvcGVuOiBmdW5jdGlvbiBvcGVuKG9mZmVySWQpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgKDAsIF9vZmZlci5nZXREYXRhKShvZmZlcklkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpcy5mb3JtID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_offer", "require", "data", "dialogVisible", "form", "methods", "open", "offerId", "_this", "getData", "then", "res"], "sources": ["src/views/project/offer/components/lessDetails.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog title=\"极速报价详情\" :visible.sync=\"dialogVisible\" width=\"80%\" center>\r\n      <el-descriptions class=\"margin-top\" title=\"询价信息\" :column=\"3\" direction=\"horizontal\" border>\r\n        <el-descriptions-item label=\"询价公司\">{{form.inquiry.enterprise_name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价单号\">{{form.inquiry.inquiry_no}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价日期\">{{form.inquiry.inquiry_date}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人\">{{form.inquiry.linker}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系电话\">{{form.inquiry.linkphone}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"询价标题\">{{form.inquiry.title}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需求描述\">{{form.inquiry.description}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"截止日期\">{{form.inquiry.deadline}}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions style=\"margin-top: 20px;\" class=\"margin-top\" title=\"报价信息\" :column=\"3\" direction=\"horizontal\"\r\n        border>\r\n        <el-descriptions-item label=\"报价日期\">{{form.offer.offer_date}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报价单号\">{{form.offer.offer_no}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报价公司\">{{form.offer.enterprise_name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人\">{{form.offer.linker}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系电话\">{{form.offer.linkphone}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报价版本\">{{form.offer.version}}次报价</el-descriptions-item>\r\n        <el-descriptions-item label=\"备注\">{{form.offer.remark}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"初次报价其他费用\" v-if=\"form.offer.other_fee\">\r\n          <span v-for=\"item in form.offer.other_fee\" style=\"margin-right: 10px;\">\r\n            {{item.key}}:{{item.value}}\r\n          </span>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"二次报价其他费用\" v-if=\"form.offer.other_fee_v2\">\r\n          <span v-for=\"item in form.offer.other_fee_v2\" style=\"margin-right: 10px;\">\r\n            {{item.key}}:{{item.value}}\r\n          </span>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions style=\"margin-top: 20px;\" v-for=\"(values,key) in form.items\" class=\"margin-top\"\r\n        :title=\"key+'次报价'\" :column=\"3\" direction=\"horizontal\" border>\r\n        <el-descriptions-item label=\"报价明细\">\r\n          <el-table :data=\"values\">\r\n            <el-table-column label=\"物料分类\" align=\"center\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                {{scope.row.classify_name}}-{{scope.row.classify2_name}}-{{scope.row.classify3_name}}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"物料名称\" align=\"center\" width=\"150\" prop=\"material_name\">\r\n            </el-table-column>\r\n            <el-table-column label=\"物料单价\" align=\"center\" width=\"150\" prop=\"offer_tax_price\">\r\n            </el-table-column>\r\n            <el-table-column label=\"询价附件\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div v-for=\"file in scope.row.attachment\" v-if=\"scope.row.attachment\">\r\n                  <a :href=\"file.url\" target=\"_blank\">{{file.name}}</a>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"报价附件\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div v-for=\"file in scope.row.offer_attachment\" v-if=\"scope.row.offer_attachment\">\r\n                  <a :href=\"file.url\" target=\"_blank\">{{file.name}}</a>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\n  import {\r\n    getData\r\n  } from '@/api/project/offer';\r\n  export default {\r\n    data() {\r\n      return {\r\n        dialogVisible: false,\r\n        form: {\r\n          \"inquiry\":{},\r\n          \"offer\":{},\r\n          \"items\":[]\r\n        }\r\n      };\r\n    },\r\n    methods: {\r\n      open(offerId) {\r\n        this.dialogVisible = true;\r\n        getData(offerId).then(res => {\r\n          this.form = res.data;\r\n        })\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style scoped>\r\n  .el-descriptions-item__cell {\r\n    max-width: 300px;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;AAmEA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,IAAA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAC,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAL,aAAA;MACA,IAAAM,cAAA,EAAAF,OAAA,EAAAG,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAJ,IAAA,GAAAO,GAAA,CAAAT,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}