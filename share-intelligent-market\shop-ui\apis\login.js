import request from '@/utils/request'
import axios from 'axios'

// 创建专门用于SSO的axios实例
const ssoRequest = axios.create({
  baseURL: '/auth/',
  timeout: 20000,
  withCredentials: true,
  crossDomain: true
})

// 为SSO请求添加响应拦截器
ssoRequest.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('SSO请求失败:', error)
    return Promise.reject(error)
  }
)

// 登录页面--验证码登录
export const loginCode = (params) => {
  return request({
    url: '/data/login/code',
    method: 'post',
    params:params
  })
}



// 获取短信验证码
export const getLoginCode = (params) => {
  return request({
    url: '/data/util/code',
    method: 'get',
    params:params
  })
}


// 密码登录
export const login = (params) => {
  return request({
    url: '/data/login/pass',
    method: 'post',
    params:params
  })
}

// 获取权限
export const getPermi = (params) => {
  return request({
    url: '/purchase/user/permission',
    method: 'get',
    params:params
  })
}

// 获取图片验证码
export const getCodeImg = (params) => {
  return request({
    url: '/data/login/randomImage/'+params,
    method: 'get'
  })
}

// SSO登录 - 获取SSO登录地址
export const getSSOLoginUrl = (redirect) => {
  return ssoRequest({
    url: '/sso/loginUrl',
    method: 'get',
    params: { redirect }
  })
}

// SSO登录 - 处理SSO回调
export const handleSSOCallback = (code, state) => {
  return ssoRequest({
    url: '/sso/callback',
    method: 'get',
    params: { code, state }
  })
}

// SSO登录 - 通过临时key获取JWT token
export const getSSOTokenByKey = (key) => {
  return ssoRequest({
    url: '/sso/token',
    method: 'get',
    params: { key },
    timeout: 30000 // SSO token获取可能需要更长时间
  })
}
