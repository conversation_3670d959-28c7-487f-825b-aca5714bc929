
8ae8f291cf0873ca3db190111f80307f57766b45	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Ftinymce-5-dark\\u002Fcontent.min.css\",\"contentHash\":\"c8bd7d8728833752343f2a42aeaf639b\"}","integrity":"sha512-HutxNnNBb6+fk8aXq1BOPqIhvSkjt1XXx3Do4Y+bVUYkHFPfvdgnOD/N2lLFhikxszttGGd2OUV2QIVEC4S/qw==","time":1750496064273,"size":1813}