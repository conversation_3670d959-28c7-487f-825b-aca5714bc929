package com.ruoyi.auth.service;

import com.ruoyi.auth.config.SSOClientConfig;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.RemoteUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 从系统SSO客户端服务
 *
 * <AUTHOR>
 */
@Service
public class SSOClientService {

    private static final Logger log = LoggerFactory.getLogger(SSOClientService.class);

    @Autowired
    private SSOClientConfig.SSOClientProperties ssoClientProperties;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private TokenService tokenService;

    private static final String LOCAL_SESSION_PREFIX = "market_session:";
    private static final String LOCAL_TOKEN_PREFIX = "market_token:";
    private static final String TEMP_TOKEN_PREFIX = "market_temp_token:";
    private static final Long SESSION_EXPIRE_MINUTES = 480L;
    private static final Long LOCAL_TOKEN_EXPIRE = 480L;
    private static final Long TEMP_TOKEN_EXPIRE = 10L; // 临时token 10分钟过期

    /**
     * 使用授权码换取访问令牌
     *
     * @param authCode 授权码
     * @return 令牌信息
     */
    public Map<String, Object> exchangeToken(String authCode) {
        try {
            String tokenUrl = ssoClientProperties.getServerUrl() + "/sso/token";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("code", authCode);
            params.add("client_id", ssoClientProperties.getClientId());
            params.add("client_secret", ssoClientProperties.getClientSecret());

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                if (Integer.valueOf(200).equals(result.get("code"))) {
                    return (Map<String, Object>) result.get("data");
                } else {
                    log.error("令牌交换失败: {}", result.get("msg"));
                }
            }

            return null;
        } catch (Exception e) {
            log.error("令牌交换异常", e);
            return null;
        }
    }

    /**
     * 创建本地会话
     *
     * @param accessToken SSO访问令牌
     * @return 是否创建成功
     */
    public boolean createLocalSession(String accessToken) {
        try {
            // 使用访问令牌获取用户信息
            Map<String, Object> userInfo = getUserInfoFromSSO(accessToken);
            if (userInfo == null) {
                log.error("获取用户信息失败");
                return false;
            }

            // 获取或创建本地用户
            LoginUser loginUser = getOrCreateLocalUser(userInfo);
            if (loginUser == null) {
                log.error("获取或创建本地用户失败");
                return false;
            }

            // 生成本地Token
            String localToken = generateLocalToken(loginUser);

            // 设置登录状态
            setLoginStatus(localToken, loginUser);

            log.info("创建本地会话成功，用户: {}", loginUser.getUsername());
            return true;
        } catch (Exception e) {
            log.error("创建本地会话失败", e);
            return false;
        }
    }

    /**
     * 从SSO获取用户信息
     *
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    private Map<String, Object> getUserInfoFromSSO(String accessToken) {
        try {
            String userInfoUrl = ssoClientProperties.getServerUrl() + "/sso/userinfo";

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);

            HttpEntity<String> request = new HttpEntity<>(headers);
            ResponseEntity<Map> response = restTemplate.exchange(userInfoUrl,
                org.springframework.http.HttpMethod.GET, request, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                if (Integer.valueOf(200).equals(result.get("code"))) {
                    return (Map<String, Object>) result.get("data");
                } else {
                    log.error("获取用户信息失败: {}", result.get("msg"));
                }
            }

            return null;
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return null;
        }
    }

    /**
     * 获取或创建本地用户
     *
     * @param userInfo SSO用户信息
     * @return 本地登录用户
     */
    public LoginUser getOrCreateLocalUser(Map<String, Object> userInfo) {
        try {
            String ssoUserId = (String) userInfo.get("userId");
            String username = (String) userInfo.get("username");

            // 安全地转换角色和权限信息
            String[] roles = convertToStringArray(userInfo.get("roles"));
            String[] permissions = convertToStringArray(userInfo.get("permissions"));
            Boolean hasPermission = (Boolean) userInfo.get("hasPermission");

            // 查找本地用户
            R<LoginUser> userResult = remoteUserService.getUserInfo(username, "inner");
            SysUser localUser = null;
            if (userResult != null && userResult.getData() != null) {
                localUser = userResult.getData().getSysUser();
                log.info("找到本地用户: {}", username);
            }

            // 构造LoginUser对象（无论是否找到本地用户都创建）
            LoginUser loginUser = new LoginUser();

            if (localUser != null) {
                // 使用本地用户信息
                loginUser.setUserid(localUser.getUserId());
                loginUser.setUsername(localUser.getUserName());
                loginUser.setSysUser(localUser);
                log.info("使用本地用户信息: {}", username);
            } else {
                // 基于SSO信息创建临时用户信息
                SysUser tempUser = createTempUserFromSSO(userInfo);
                loginUser.setUserid(tempUser.getUserId());
                loginUser.setUsername(tempUser.getUserName());
                loginUser.setSysUser(tempUser);
                log.info("创建临时用户信息: {}", username);
            }

            // 设置权限信息（从SSO获取的权限映射）
            if (Boolean.TRUE.equals(hasPermission) && roles != null && roles.length > 0) {
                // 这里可以根据SSO返回的角色和权限设置本地权限
                log.info("用户 {} 拥有角色: {} 和权限: {}", username,
                        String.join(",", roles),
                        permissions != null ? String.join(",", permissions) : "无");
            }

            return loginUser;
        } catch (Exception e) {
            log.error("获取或创建本地用户失败", e);
            return null;
        }
    }

    /**
     * 基于SSO信息创建临时用户对象
     */
    private SysUser createTempUserFromSSO(Map<String, Object> ssoUserInfo) {
        SysUser user = new SysUser();

        // 设置基本信息
        String username = (String) ssoUserInfo.get("username");
        String ssoUserId = (String) ssoUserInfo.get("userId");

        user.setUserId(Long.valueOf(ssoUserId != null ? ssoUserId.hashCode() & 0x7FFFFFFF : username.hashCode() & 0x7FFFFFFF));
        user.setUserName(username);
        user.setNickName((String) ssoUserInfo.get("nickName"));
        user.setEmail((String) ssoUserInfo.get("email"));
        user.setPhonenumber((String) ssoUserInfo.get("phonenumber"));
        user.setSex((String) ssoUserInfo.get("sex"));
        user.setAvatar((String) ssoUserInfo.get("avatar"));
        user.setStatus("0");
        user.setRemark("SSO临时用户 - 来自复合材料共享智造平台");

        String[] roles = convertToStringArray(ssoUserInfo.get("roles"));
        if (roles != null && roles.length > 0) {
            if (java.util.Arrays.asList(roles).contains("admin")) {
                user.setRemark(user.getRemark() + " - 管理员用户");
            } else if (java.util.Arrays.asList(roles).contains("system_manager")) {
                user.setRemark(user.getRemark() + " - 系统管理员");
            } else {
                user.setRemark(user.getRemark() + " - 普通用户");
            }
        }

        log.info("基于SSO信息创建临时用户: {} (ID: {})", user.getUserName(), user.getUserId());
        return user;
    }

    /**
     * 更新本地用户信息
     * 
     * @param localUser 本地用户
     * @param userInfo 主系统用户信息
     */
    private void updateLocalUser(SysUser localUser, Map<String, Object> userInfo) {
        try {
            // 更新用户基本信息
            localUser.setNickName((String) userInfo.get("nickName"));
            localUser.setEmail((String) userInfo.get("email"));
            localUser.setPhonenumber((String) userInfo.get("phonenumber"));
            localUser.setSex((String) userInfo.get("sex"));
            localUser.setAvatar((String) userInfo.get("avatar"));
            
            // 调用用户服务更新用户
            // remoteUserService.updateUser(localUser);
            
            log.info("更新本地用户信息成功: {}", localUser.getUserName());
        } catch (Exception e) {
            log.error("更新本地用户信息失败", e);
        }
    }

    /**
     * 生成本地访问Token
     * 
     * @param loginUser 登录用户
     * @return 本地Token
     */
    public String generateLocalToken(LoginUser loginUser) {
        String token = IdUtils.fastSimpleUUID();
        loginUser.setToken(token);
        return token;
    }

    /**
     * 设置登录状态
     * 
     * @param token 本地Token
     * @param loginUser 登录用户
     */
    public void setLoginStatus(String token, LoginUser loginUser) {
        String redisKey = LOCAL_TOKEN_PREFIX + token;
        redisService.setCacheObject(redisKey, loginUser, LOCAL_TOKEN_EXPIRE, TimeUnit.MINUTES);
        log.info("设置用户 {} 登录状态成功", loginUser.getUsername());
    }

    /**
     * 清除本地用户会话
     * 
     * @param token 本地Token
     */
    public void clearLocalUserSession(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String redisKey = LOCAL_TOKEN_PREFIX + token;
            redisService.deleteObject(redisKey);
            log.info("清除本地用户会话: {}", token);
        }
    }

    /**
     * 通知主系统登出
     *
     * @param token 本地Token
     */
    public void notifyMainSystemLogout(String token) {
        try {
            String logoutUrl = ssoClientProperties.getMainSystemUrl() + "/sso/logout";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("token", token);
            params.add("system", ssoClientProperties.getClientId());

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            restTemplate.postForEntity(logoutUrl, request, Map.class);

            log.info("通知主系统登出成功");
        } catch (Exception e) {
            log.error("通知主系统登出失败", e);
        }
    }

    /**
     * 检查主系统登录状态
     *
     * @return 主系统登录状态
     */
    public boolean checkMainSystemStatus() {
        try {
            String statusUrl = ssoClientProperties.getMainSystemUrl() + "/sso/status";
            ResponseEntity<Map> response = restTemplate.getForEntity(statusUrl, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                if ("0".equals(String.valueOf(result.get("code")))) {
                    Map<String, Object> data = (Map<String, Object>) result.get("data");
                    return Boolean.TRUE.equals(data.get("isLogin"));
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查主系统登录状态失败", e);
            return false;
        }
    }

    /**
     * 获取主系统登录地址
     *
     * @param redirect 登录成功后的跳转地址
     * @return 主系统登录地址
     */
    public String getMainSystemLoginUrl(String redirect) {
        StringBuilder url = new StringBuilder();
        url.append(ssoClientProperties.getMainSystemUrl()).append(ssoClientProperties.getMainLoginUrl());
        url.append("?target=").append(ssoClientProperties.getClientId());

        if (StringUtils.isNotEmpty(redirect)) {
            try {
                url.append("&redirect=").append(java.net.URLEncoder.encode(redirect, "UTF-8"));
            } catch (Exception e) {
                log.error("URL编码失败", e);
            }
        }

        return url.toString();
    }

    /**
     * 同步用户信息
     * 
     * @param userInfo 用户信息
     * @return 同步结果
     */
    public boolean syncUserInfo(Map<String, Object> userInfo) {
        try {
            String username = (String) userInfo.get("username");
            R<LoginUser> userResult = remoteUserService.getUserInfo(username, "inner");
            SysUser localUser = null;
            if (userResult != null && userResult.getData() != null) {
                localUser = userResult.getData().getSysUser();
            }

            if (localUser != null) {
                updateLocalUser(localUser, userInfo);
                return true;
            } else {
                SysUser newUser = createTempUserFromSSO(userInfo);
                return newUser != null;
            }
        } catch (Exception e) {
            log.error("同步用户信息失败", e);
            return false;
        }
    }

    /**
     * 安全地将对象转换为String数组
     * 处理SSO返回的角色和权限信息可能是ArrayList的情况
     */
    @SuppressWarnings("unchecked")
    private String[] convertToStringArray(Object obj) {
        if (obj == null) {
            return new String[0];
        }

        if (obj instanceof String[]) {
            return (String[]) obj;
        }

        if (obj instanceof java.util.List) {
            java.util.List<String> list = (java.util.List<String>) obj;
            return list.toArray(new String[0]);
        }

        if (obj instanceof String) {
            return new String[]{(String) obj};
        }

        log.warn("无法转换对象为String数组: {}", obj.getClass().getName());
        return new String[0];
    }

    /**
     * 使用访问令牌获取用户信息并创建本地会话（新SSO服务接口）
     *
     * @param accessToken SSO访问令牌
     * @return JWT token信息
     */
    public Map<String, Object> createLocalSessionWithToken(String accessToken) {
        try {
            log.info("市场系统：开始使用访问令牌创建本地会话");

            // 使用访问令牌获取用户信息
            Map<String, Object> userInfo = getUserInfoFromSSO(accessToken);
            if (userInfo == null) {
                log.error("市场系统：获取用户信息失败");
                return null;
            }

            log.info("市场系统：获取到用户信息: {}", userInfo.get("username"));

            // 获取或创建本地用户
            LoginUser loginUser = getOrCreateLocalUser(userInfo);
            if (loginUser == null) {
                log.error("市场系统：获取或创建本地用户失败");
                return null;
            }

            // 使用TokenService创建JWT token（与主系统保持一致）
            log.info("市场系统：开始使用TokenService创建JWT token，用户: {}", loginUser.getUsername());
            Map<String, Object> tokenMap = tokenService.createToken(loginUser);
            log.info("市场系统：TokenService创建token完成，结果: {}", tokenMap);

            log.info("创建本地会话成功，用户: {}, JWT Token: {}",
                    loginUser.getUsername(),
                    tokenMap.get("access_token") != null ? "已生成" : "生成失败");

            return tokenMap;
        } catch (Exception e) {
            log.error("市场系统：创建本地会话失败", e);
            return null;
        }
    }

    /**
     * 存储临时token信息
     *
     * @param tokenInfo token信息
     * @return 临时key
     */
    public String storeTempToken(Map<String, Object> tokenInfo) {
        String tempKey = IdUtils.fastSimpleUUID();
        String redisKey = TEMP_TOKEN_PREFIX + tempKey;
        redisService.setCacheObject(redisKey, tokenInfo, TEMP_TOKEN_EXPIRE, TimeUnit.MINUTES);
        log.info("市场系统：存储临时token，key: {}", tempKey);
        return tempKey;
    }

    /**
     * 获取临时token信息
     *
     * @param tempKey 临时key
     * @return token信息
     */
    public Map<String, Object> getTempToken(String tempKey) {
        String redisKey = TEMP_TOKEN_PREFIX + tempKey;
        Map<String, Object> tokenInfo = redisService.getCacheObject(redisKey);
        if (tokenInfo != null) {
            // 获取后立即删除
            redisService.deleteObject(redisKey);
            log.info("市场系统：获取并删除临时token，key: {}", tempKey);
        } else {
            log.warn("市场系统：临时token不存在或已过期，key: {}", tempKey);
        }
        return tokenInfo;
    }
}
