package com.ruoyi.auth.service;

import com.ruoyi.auth.config.SSOClientConfig;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.RemoteUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 主系统SSO客户端服务
 *
 * <AUTHOR>
 */
@Service
public class SSOClientService {

    private static final Logger log = LoggerFactory.getLogger(SSOClientService.class);

    @Autowired
    private SSOClientConfig.SSOClientProperties ssoClientProperties;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private TokenService tokenService;

    private static final String LOCAL_SESSION_PREFIX = "backend_session:";
    private static final String LOCAL_TOKEN_PREFIX = "backend_token:";
    private static final Long SESSION_EXPIRE_MINUTES = 480L;
    private static final Long LOCAL_TOKEN_EXPIRE = 480L;

    /**
     * 使用授权码换取访问令牌
     *
     * @param authCode 授权码
     * @return 令牌信息
     */
    public Map<String, Object> exchangeToken(String authCode) {
        try {
            String tokenUrl = ssoClientProperties.getFullTokenUrl();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", "authorization_code");
            params.add("code", authCode);
            params.add("client_id", ssoClientProperties.getClientId());
            params.add("client_secret", ssoClientProperties.getClientSecret());
            params.add("redirect_uri", ssoClientProperties.getCallbackUrl());

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                // 检查响应格式，SSO服务返回的是AjaxResult格式
                if (result.get("code") != null && "200".equals(result.get("code").toString())) {
                    log.info("授权码换取令牌成功");
                    return (Map<String, Object>) result.get("data");
                } else {
                    log.warn("授权码换取令牌失败: {}", result.get("msg"));
                    return null;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("令牌交换异常", e);
            return null;
        }
    }

    /**
     * 创建本地会话
     *
     * @param accessToken SSO访问令牌
     * @return 是否创建成功
     */
    public boolean createLocalSession(String accessToken) {
        try {
            Map<String, Object> tokenInfo = createLocalSessionWithToken(accessToken);
            return tokenInfo != null && tokenInfo.get("access_token") != null;
        } catch (Exception e) {
            log.error("创建本地会话失败", e);
            return false;
        }
    }

    /**
     * 创建本地会话并返回JWT token
     *
     * @param accessToken SSO访问令牌
     * @return JWT token信息
     */
    public Map<String, Object> createLocalSessionWithToken(String accessToken) {
        try {
            // 使用访问令牌获取用户信息
            Map<String, Object> userInfo = getUserInfoFromSSO(accessToken);
            if (userInfo == null) {
                log.error("获取用户信息失败");
                return null;
            }

            // 获取或创建本地用户
            LoginUser loginUser = getOrCreateLocalUser(userInfo);
            if (loginUser == null) {
                log.error("获取或创建本地用户失败");
                return null;
            }

            // 使用TokenService创建JWT token
            Map<String, Object> tokenMap = tokenService.createToken(loginUser);

            log.info("创建本地会话成功，用户: {}, JWT Token: {}",
                    loginUser.getUsername(),
                    tokenMap.get("access_token") != null ? "已生成" : "生成失败");

            return tokenMap;
        } catch (Exception e) {
            log.error("创建本地会话失败", e);
            return null;
        }
    }

    /**
     * 从SSO获取用户信息
     *
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    private Map<String, Object> getUserInfoFromSSO(String accessToken) {
        try {
            String userInfoUrl = ssoClientProperties.getFullUserInfoUrl() + "?access_token=" + accessToken;

            ResponseEntity<Map> response = restTemplate.getForEntity(userInfoUrl, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                // 检查响应格式，SSO服务返回的是AjaxResult格式
                if (result.get("code") != null && "200".equals(result.get("code").toString())) {
                    log.debug("获取用户信息成功");
                    return (Map<String, Object>) result.get("data");
                } else {
                    log.warn("获取用户信息失败: {}", result.get("msg"));
                    return null;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return null;
        }
    }

    /**
     * 获取或创建本地用户
     *
     * @param ssoUserInfo SSO用户信息
     * @return 本地登录用户
     */
    public LoginUser getOrCreateLocalUser(Map<String, Object> ssoUserInfo) {
        try {
            String ssoUserId = (String) ssoUserInfo.get("userId");
            String username = (String) ssoUserInfo.get("username");

            // 安全地转换角色和权限信息
            String[] roles = convertToStringArray(ssoUserInfo.get("roles"));
            String[] permissions = convertToStringArray(ssoUserInfo.get("permissions"));
            Boolean hasPermission = (Boolean) ssoUserInfo.get("hasPermission");
            
            // 查找本地用户
            R<LoginUser> userResult = remoteUserService.getUserInfo(username, "inner");
            SysUser localUser = null;
            if (userResult != null && userResult.getData() != null) {
                localUser = userResult.getData().getSysUser();
                log.info("找到本地用户: {}", username);
            }

            // 构造LoginUser对象（无论是否找到本地用户都创建）
            LoginUser loginUser = new LoginUser();

            if (localUser != null) {
                // 使用本地用户信息
                loginUser.setUserid(localUser.getUserId());
                loginUser.setUsername(localUser.getUserName());
                loginUser.setSysUser(localUser);
                log.info("使用本地用户信息: {}", username);
            } else {
                // 基于SSO信息创建临时用户信息
                SysUser tempUser = createTempUserFromSSO(ssoUserInfo);
                loginUser.setUserid(tempUser.getUserId());
                loginUser.setUsername(tempUser.getUserName());
                loginUser.setSysUser(tempUser);
                log.info("创建临时用户信息: {}", username);
            }
            
            // 设置权限信息（从SSO获取的权限映射）
            if (Boolean.TRUE.equals(hasPermission) && roles != null && roles.length > 0) {
                // 这里可以根据SSO返回的角色和权限设置本地权限
                log.info("用户 {} 拥有角色: {} 和权限: {}", username, 
                        String.join(",", roles), 
                        permissions != null ? String.join(",", permissions) : "无");
            }
            
            // 缓存SSO用户映射关系
            String userCacheKey = LOCAL_SESSION_PREFIX + "mapping:" + ssoUserId;
            Map<String, Object> mappingInfo = new HashMap<>();
            mappingInfo.put("localUserId", loginUser.getUserid());
            mappingInfo.put("username", username);
            mappingInfo.put("roles", roles);
            mappingInfo.put("permissions", permissions);
            mappingInfo.put("hasPermission", hasPermission);
            redisService.setCacheObject(userCacheKey, mappingInfo, SESSION_EXPIRE_MINUTES, TimeUnit.MINUTES);

            return loginUser;

        } catch (Exception e) {
            log.error("获取或创建本地用户失败", e);
            return null;
        }
    }

    /**
     * 基于SSO信息创建临时用户对象
     */
    private SysUser createTempUserFromSSO(Map<String, Object> ssoUserInfo) {
        SysUser user = new SysUser();

        // 设置基本信息
        String username = (String) ssoUserInfo.get("username");
        String ssoUserId = (String) ssoUserInfo.get("userId");

        user.setUserId(Long.valueOf(ssoUserId != null ? ssoUserId.hashCode() & 0x7FFFFFFF : username.hashCode() & 0x7FFFFFFF));
        user.setUserName(username);
        user.setNickName(username);
        user.setStatus("0");
        user.setRemark("SSO临时用户 - 来自复合材料共享智造平台");

        String[] roles = convertToStringArray(ssoUserInfo.get("roles"));
        if (roles != null && roles.length > 0) {
            if (java.util.Arrays.asList(roles).contains("admin")) {
                user.setRemark(user.getRemark() + " - 管理员用户");
            } else if (java.util.Arrays.asList(roles).contains("system_manager")) {
                user.setRemark(user.getRemark() + " - 系统管理员");
            } else {
                user.setRemark(user.getRemark() + " - 普通用户");
            }
        }

        log.info("基于SSO信息创建临时用户: {} (ID: {})", user.getUserName(), user.getUserId());
        return user;
    }

    /**
     * 更新本地用户权限信息
     */
    private void updateLocalUserPermissions(SysUser localUser, Map<String, Object> ssoUserInfo) {
        String[] roles = convertToStringArray(ssoUserInfo.get("roles"));
        String[] permissions = convertToStringArray(ssoUserInfo.get("permissions"));
        
        log.info("更新用户 {} 的权限信息，角色: {}, 权限: {}", 
                localUser.getUserName(),
                roles != null ? String.join(",", roles) : "无",
                permissions != null ? String.join(",", permissions) : "无");
    }

    /**
     * 生成本地访问Token
     */
    public String generateLocalToken(LoginUser loginUser) {
        String token = IdUtils.fastSimpleUUID();
        loginUser.setToken(token);
        return token;
    }

    /**
     * 设置登录状态
     */
    public void setLoginStatus(String token, LoginUser loginUser) {
        String redisKey = LOCAL_TOKEN_PREFIX + token;
        redisService.setCacheObject(redisKey, loginUser, LOCAL_TOKEN_EXPIRE, TimeUnit.MINUTES);
        log.info("设置用户 {} 登录状态成功", loginUser.getUsername());
    }

    /**
     * 清除本地用户会话
     */
    public void clearLocalUserSession(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String redisKey = LOCAL_TOKEN_PREFIX + token;
            redisService.deleteObject(redisKey);
            log.info("清除本地用户会话: {}", token);
        }
    }

    /**
     * 通知SSO服务登出
     */
    public void notifySSOLogout(String token) {
        try {
            String logoutUrl = ssoClientProperties.getFullLogoutUrl();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            
            HttpEntity<String> request = new HttpEntity<>(headers);
            restTemplate.postForEntity(logoutUrl, request, Map.class);
            
            log.info("通知SSO服务登出成功");
        } catch (Exception e) {
            log.error("通知SSO服务登出失败", e);
        }
    }

    /**
     * 检查SSO服务状态
     */
    public boolean checkSSOServerStatus() {
        try {
            String statusUrl = ssoClientProperties.getServerUrl() + "/sso/status";
            ResponseEntity<Map> response = restTemplate.getForEntity(statusUrl, Map.class);
            
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.error("检查SSO服务状态失败", e);
            return false;
        }
    }

    /**
     * 获取SSO登录地址
     */
    public String getSSOLoginUrl(String redirect) {
        StringBuilder url = new StringBuilder();
        url.append(ssoClientProperties.getFullLoginUrl());
        url.append("?client_id=").append(ssoClientProperties.getClientId());
        url.append("&redirect_uri=").append(ssoClientProperties.getCallbackUrl());
        
        if (StringUtils.isNotEmpty(redirect)) {
            try {
                url.append("&state=").append(java.net.URLEncoder.encode(redirect, "UTF-8"));
            } catch (Exception e) {
                log.error("URL编码失败", e);
            }
        }
        
        return url.toString();
    }

    /**
     * 刷新用户权限信息
     */
    public boolean refreshUserPermissions(String token) {
        try {
            // 这里可以重新从SSO获取用户权限信息并更新本地缓存
            log.info("刷新用户权限信息: {}", token);
            return true;
        } catch (Exception e) {
            log.error("刷新用户权限信息失败", e);
            return false;
        }
    }

    /**
     * 安全地将对象转换为String数组
     * 处理SSO返回的角色和权限信息可能是ArrayList的情况
     */
    @SuppressWarnings("unchecked")
    private String[] convertToStringArray(Object obj) {
        if (obj == null) {
            return new String[0];
        }

        if (obj instanceof String[]) {
            return (String[]) obj;
        }

        if (obj instanceof java.util.List) {
            java.util.List<String> list = (java.util.List<String>) obj;
            return list.toArray(new String[0]);
        }

        if (obj instanceof String) {
            return new String[]{(String) obj};
        }

        log.warn("无法转换对象为String数组: {}", obj.getClass().getName());
        return new String[0];
    }
}
