09:11:26.018 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:11:27.969 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 17fd543e-028c-4887-9874-23d9549089c2_config-0
09:11:28.134 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 88 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:28.214 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 31 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:28.247 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:28.593 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 333 ms to scan 271 urls, producing 0 keys and 0 values 
09:11:28.620 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:28.638 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:28.659 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:28.991 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 328 ms to scan 271 urls, producing 0 keys and 0 values 
09:11:29.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:29.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1883652579
09:11:29.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/788592721
09:11:29.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:29.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:29.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:32.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381892175_127.0.0.1_49913
09:11:32.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] Notify connected event to listeners.
09:11:32.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:32.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17fd543e-028c-4887-9874-23d9549089c2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1432929903
09:11:33.046 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:11:42.224 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
09:11:42.226 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:11:42.227 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:11:42.921 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:11:46.029 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
09:11:46.035 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
09:11:46.035 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:11:55.421 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:11:56.207 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f903a798-d64b-474c-b220-cec2ca1f42ba
09:11:56.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] RpcClient init label, labels = {module=naming, source=sdk}
09:11:56.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:11:56.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:11:56.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:11:56.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:56.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381916222_127.0.0.1_50520
09:11:56.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Notify connected event to listeners.
09:11:56.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:56.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1432929903
09:11:59.595 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
09:11:59.648 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
09:11:59.966 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 35.503 seconds (JVM running for 37.858)
09:12:00.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
09:12:00.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
09:12:00.002 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
09:12:00.085 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:12:00.090 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f903a798-d64b-474c-b220-cec2ca1f42ba] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:12:00.541 [RMI TCP Connection(19)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:39:46.490 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:39:46.495 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:39:46.828 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:39:46.828 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2158980f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:39:46.829 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750381916222_127.0.0.1_50520
12:39:46.834 [nacos-grpc-client-executor-2514] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750381916222_127.0.0.1_50520]Ignore complete event,isRunning:false,isAbandon=false
12:39:46.914 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@16af994d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2515]
12:39:47.250 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,205] - dynamic-datasource start closing ....
12:39:47.259 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
12:39:47.301 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
12:39:47.302 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,209] - dynamic-datasource all closed success,bye
