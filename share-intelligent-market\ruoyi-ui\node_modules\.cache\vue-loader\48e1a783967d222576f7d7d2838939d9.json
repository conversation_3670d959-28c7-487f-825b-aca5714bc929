{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\list.vue?vue&type=style&index=0&id=96626e4e&scope=true&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\list.vue", "mtime": 1750151094268}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudzE2MCB7DQogIHdpZHRoOiAxNjBweDsNCn0NCg=="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8UA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/order", "sourcesContent": ["<!-- 订单列表汇总 -->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-form\r\n        :model=\"queryParams\"\r\n        ref=\"queryForm\"\r\n        :inline=\"true\"\r\n        size=\"small\"\r\n        label-width=\"68px\"\r\n      >\r\n        <el-form-item label=\"\" prop=\"order_type\">\r\n          <el-select\r\n            clearable\r\n            v-model=\"queryParams.order_type\"\r\n            placeholder=\"订单类型\"\r\n            style=\"width: 120px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in typeOptions\"\r\n              :key=\"item.key\"\r\n              :label=\"item.value\"\r\n              :value=\"item.key\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"status\">\r\n          <el-select\r\n            clearable\r\n            v-model=\"queryParams.status\"\r\n            placeholder=\"订单状态\"\r\n            style=\"width: 120px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in statusOptions\"\r\n              :key=\"item.key\"\r\n              :label=\"item.value\"\r\n              :value=\"item.key\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"central_status\">\r\n          <el-select\r\n            clearable\r\n            v-model=\"queryParams.central_status\"\r\n            placeholder=\"集采订单状态\"\r\n            style=\"width: 140px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in centralOptions\"\r\n              :key=\"item.key\"\r\n              :label=\"item.value\"\r\n              :value=\"item.key\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"central_pay_status\">\r\n          <el-select\r\n            clearable\r\n            v-model=\"queryParams.central_pay_status\"\r\n            placeholder=\"集采支付状态\"\r\n            style=\"width: 140px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in payOptions\"\r\n              :key=\"item.key\"\r\n              :label=\"item.value\"\r\n              :value=\"item.key\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"order_no\">\r\n          <el-input\r\n            v-model=\"queryParams.order_no\"\r\n            placeholder=\"输入订单号\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"demand_name\">\r\n          <el-input\r\n            v-model=\"queryParams.demand_name\"\r\n            placeholder=\"输入需方\"\r\n            :maxlength=\"50\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"下单时间\">\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            style=\"width: 240px\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"daterange\"\r\n            range-separator=\"-\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n          ></el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n            >重置</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-row>\r\n    <el-table :data=\"list\" height=\"500\" style=\"width: 100%\">\r\n      <el-table-column prop=\"id\" label=\"序号\" align=\"center\" width=\"55\" />\r\n      <el-table-column\r\n        prop=\"order_no\"\r\n        label=\"订单号\"\r\n        align=\"center\"\r\n        width=\"160\"\r\n      />\r\n      <el-table-column\r\n        prop=\"create_time\"\r\n        label=\"下单时间\"\r\n        align=\"center\"\r\n        width=\"160\"\r\n      />\r\n      <el-table-column\r\n        prop=\"order_type\"\r\n        label=\"订单类型\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        prop=\"demand_name\"\r\n        label=\"需方\"\r\n        align=\"center\"\r\n        width=\"240\"\r\n        :show-overflow-tooltip=\"true\"\r\n      />\r\n      <el-table-column\r\n        prop=\"supply_name\"\r\n        label=\"供方\"\r\n        align=\"center\"\r\n        width=\"240\"\r\n        :show-overflow-tooltip=\"true\"\r\n      />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag size=\"mini\" type=\"warning\">{{ scope.row.statusStr }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"支付方式\"\r\n        align=\"center\"\r\n        prop=\"payment\"\r\n        width=\"120\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag size=\"mini\" type=\"primary\">{{ scope.row.paymentStr }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"total_price\"\r\n        label=\"订单总金额\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        label=\"集采状态\"\r\n        align=\"center\"\r\n        prop=\"central_status\"\r\n        width=\"120\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            size=\"mini\"\r\n            v-if=\"scope.row.central_statusStr\"\r\n            type=\"warning\"\r\n            >{{ scope.row.central_statusStr }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"集采支付状态\"\r\n        align=\"center\"\r\n        prop=\"central_pay_status\"\r\n        width=\"120\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            size=\"mini\"\r\n            v-if=\"scope.row.central_pay_statusStr\"\r\n            type=\"warning\"\r\n            >{{ scope.row.central_pay_statusStr }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"logistics_no\"\r\n        label=\"物流单号\"\r\n        align=\"center\"\r\n        width=\"240\"\r\n        :show-overflow-tooltip=\"true\"\r\n      />\r\n      <el-table-column\r\n        prop=\"operator\"\r\n        label=\"操作员\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"130\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"mini\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >详情</el-button\r\n          >\r\n          <!-- <el-button v-if=\"scope.row.status=='CONFIRM'\" type=\"text\" size=\"mini\" icon=\"el-icon-edit\" @click=\"handleConfirm(scope.row)\">确认</el-button> -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <Pagination\r\n      @pagination=\"getList\"\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n    >\r\n    </Pagination>\r\n    <!-- 订单详情 -->\r\n    <orderDetails ref=\"orderDetails\"></orderDetails>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport orderDetails from \"./components/orderDetails.vue\";\r\nimport { listEnum } from \"@/api/tool/util\";\r\nimport { listData, confirmData } from \"@/api/order/list\";\r\nexport default {\r\n  layout: \"order\",\r\n  components: {\r\n    orderDetails,\r\n  },\r\n  data() {\r\n    return {\r\n      form: {},\r\n      dateRange: [],\r\n      typeOptions: [],\r\n      statusOptions: [],\r\n      centralOptions: [],\r\n      payOptions: [],\r\n      dialogVisible: false,\r\n      total: 1,\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        order_type: null,\r\n        status: null,\r\n        central_status: null,\r\n        central_pay_status: null,\r\n        start_time: null,\r\n        end_time: null,\r\n        order_no: null,\r\n        demand_name: null,\r\n      },\r\n      // 表格数据\r\n      list: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getEnums();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getEnums() {\r\n      listEnum().then((res) => {\r\n        this.centralOptions = res.data.centralOrderStatus;\r\n        this.payOptions = res.data.centralPayStatus;\r\n        this.typeOptions = res.data.orderType;\r\n        this.statusOptions = res.data.orderStatus;\r\n      });\r\n    },\r\n    getList() {\r\n      if (this.dateRange && this.dateRange.length > 0) {\r\n        this.queryParams.start_time = this.dateRange[0];\r\n        this.queryParams.end_time = this.dateRange[1];\r\n      } else {\r\n        this.queryParams.start_time = \"\";\r\n        this.queryParams.end_time = \"\";\r\n      }\r\n      listData(this.queryParams).then((res) => {\r\n        this.list = res.data;\r\n        this.total = res.count;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleDetail(row) {\r\n      this.$refs.orderDetails.open(row.id);\r\n    },\r\n    handleConfirm(row) {\r\n      this.$confirm(\"是否确认该订单?\", \"提示\", {\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        let data = {\r\n          id: row.id,\r\n        };\r\n        confirmData(data).then((res) => {\r\n          this.$message({\r\n            type: \"success\",\r\n            message: \"操作成功!\",\r\n          });\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scope>\r\n.w160 {\r\n  width: 160px;\r\n}\r\n</style>\r\n"]}]}