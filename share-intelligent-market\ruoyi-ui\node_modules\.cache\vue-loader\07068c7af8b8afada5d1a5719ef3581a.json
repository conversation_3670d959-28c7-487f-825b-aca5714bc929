{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\index.vue?vue&type=style&index=0&id=3fb1a7fa&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\index.vue", "mtime": 1750151094283}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNvbG9yLXJlZCB7CiAgY29sb3I6IHJlZCAhaW1wb3J0YW50OwogIGJvcmRlcjogMXB4IHNvbGlkIHJlZCAhaW1wb3J0YW50Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+SA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\" prop='enterprise_id'>\r\n            <el-select clearable style=\"width: 300px;\" v-model=\"enterprise\" size='small'\r\n              filterable remote reserve-keyword placeholder=\"请输入企业名称\"\r\n              :remote-method=\"remoteEnterprise\" @change='changeEnterprise' value-key='id' :loading=\"loading\">\r\n              <el-option v-for=\"item in enterpriseOptions\" :key=\"item.id\" :label=\"item.name\" :value=\"item\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='name'>\r\n            <el-input clearable v-model=\"queryParams.name\" placeholder=\"输入店铺名称\" :maxlength='50' size='small'\r\n              style=\"width: 300px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select clearable v-model=\"queryParams.status\" placeholder=\"状态\" size='small'>\r\n              <el-option v-for=\"item in statusOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"企业ID\" align=\"center\" prop=\"enterprise_id\" />\r\n          <el-table-column label=\"店铺名称\" align=\"center\" prop=\"name\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"店铺Logo\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.logo\"\r\n                :preview-src-list=\"[scope.row.logo]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"banner\" align=\"center\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 160px; height: 100px\" v-if='scope.row.banner'\r\n                :src=\"scope.row.banner\" :preview-src-list=\"[scope.row.banner]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"店铺状态\" align=\"center\" >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"店铺装修状态\" align=\"center\" width=\"120\"  prop=\"fitment_status\">\r\n           <template slot-scope=\"scope\">\r\n               <el-tag size=\"mini\" type='warning'>{{scope.row.fitment_status|filterStatus}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"开户行\" align=\"center\" prop=\"openbank\" width=\"140\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"开户地址\" align=\"center\" prop=\"bank\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"户头\" align=\"center\" prop=\"account\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"对公账户\" align=\"center\" prop=\"cardno\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"授权认证\" align=\"center\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 160px; height: 100px\" v-if='scope.row.certfile'\r\n                :src=\"scope.row.certfile\" :preview-src-list=\"[scope.row.certfile]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注信息\" align=\"center\" prop=\"remark\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"申请者\" align=\"center\" prop=\"create_by\" width=\"100\" />\r\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n          <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejection_reason\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n\r\n          <el-table-column label=\"操作\" align=\"center\" fixed='right' width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.status == 'OPEN'\" type=\"text\"  icon=\"el-icon-download\" size=\"mini\" @click=\"handleLower(scope.row)\">下线\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.status == 'CLOSE'\" type=\"text\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleUpper(scope.row)\">上线\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.status == 'WAIT'\" type=\"text\" icon=\"el-icon-user\" size=\"mini\" @click=\"handleOp(scope.row)\">审核\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.fitment_status == '1'\" type=\"text\" icon=\"el-icon-user\" size=\"mini\" @click=\"handleOp1(scope.row)\">装修审核\r\n              </el-button>\r\n\r\n\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog title=\"操作店铺\" :visible.sync=\"dialogVisible\" width=\"30%\" center>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"审核备注\" v-model=\"form.remark\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore('OPEN')\">确 定</el-button>\r\n        <el-button type=\"danger\" @click=\"opStore('DENY')\">驳回</el-button>\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog title=\"审核店铺装修\" :visible.sync=\"dialogVisible1\" width=\"80%\" center>\r\n      <div style=\"color:#333;font-weight:bold\">装修内容：</div>\r\n      <div v-html=\"form.content\"></div>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"驳回原因\" v-model=\"form.rejection_reason\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore1('2')\">通过</el-button>\r\n        <el-button type=\"danger\" @click=\"opStore1('3')\">驳回</el-button>\r\n        <el-button @click=\"dialogVisible1 = false\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog title=\"请输入下架原因\" :visible.sync=\"dialogVisibleClose\" width=\"30%\" center>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"下架原因\" v-model=\"form.remark\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore('CLOSE')\">确 定</el-button>\r\n        <el-button @click=\"dialogVisibleClose = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    searchData\r\n  } from '@/api/enterprise/apply';\r\n  import {\r\n    listEnum\r\n  } from '@/api/tool/util';\r\n  import { listData, opData,upStore } from '@/api/store/list'\r\n  export default {\r\n    data() {\r\n      return {\r\n        // 营业状态\r\n        statusOptions: [],\r\n        // 供应商列表\r\n        enterpriseOptions: [],\r\n        // 选中的供应商\r\n        enterprise: {},\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          enterprise_id:undefined,\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          status: undefined,\r\n        },\r\n        // 列表数据\r\n        list: [],\r\n        // 图片预览地址\r\n        srcList: [\r\n        ],\r\n        dialogVisible: false,\r\n        dialogVisibleClose:false,\r\n        status: '',\r\n        dialogVisible1:false\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n      this.getEnums();\r\n    },\r\n    filters:{\r\n      filterStatus(val){\r\n        if(val==1){\r\n          return '待审核'\r\n        }else if(val==2){\r\n          return '已审核'\r\n        }else if(val==3){\r\n          return '已驳回'\r\n        }\r\n      }\r\n    },\r\n    methods: {\r\n      /* 查询企业信息 */\r\n      remoteEnterprise(e) {\r\n        this.loading = true;\r\n        searchData(e).then(res => {\r\n          this.loading = false;\r\n          this.enterpriseOptions = res.data;\r\n        })\r\n      },\r\n      /* 切换企业信息 */\r\n      changeEnterprise(e) {\r\n        this.queryParams.enterprise_id = this.enterprise.id;\r\n      },\r\n      getEnums() {\r\n        listEnum().then(res => {\r\n          this.statusOptions = res.data.storeStatus;\r\n        })\r\n      },\r\n      reset() {\r\n        this.form = {\r\n          enterprise_id: undefined,\r\n          status: undefined,\r\n          remark: undefined\r\n        }\r\n      },\r\n      /** 查询用户列表 */\r\n      getList() {\r\n        this.loading = true\r\n        listData(this.queryParams).then(res => {\r\n          this.loading=false\r\n          this.list = res.data;\r\n          this.total = res.count;\r\n        })\r\n      },\r\n      /** 表单搜索 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      // 搜索重置\r\n      resetQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.resetForm('queryForm');\r\n        this.getList();\r\n      },\r\n      handlePreview(url) {\r\n        this.srcList = [url];\r\n      },\r\n      /** 下线 */\r\n      handleLower(row) {\r\n        this.reset()\r\n        this.$confirm(\"是否进行店铺下线？注：店铺对应商品同步下线\", \"提示\", {\r\n            confirmButtonText: \"确定\",\r\n            cancelButtonText: \"取消\",\r\n            type: \"warning\",\r\n            center: true\r\n          })\r\n          .then(() => {\r\n            this.form.enterprise_id = row.enterprise_id\r\n            this.dialogVisibleClose=true\r\n          })\r\n      },\r\n      handleUpper(row) {\r\n        this.reset()\r\n        this.$confirm(\"是否进行店铺上线？\", \"提示\", {\r\n            confirmButtonText: \"确定\",\r\n            cancelButtonText: \"取消\",\r\n            type: \"warning\",\r\n            center: true\r\n          })\r\n          .then(() => {\r\n            this.form.enterprise_id = row.enterprise_id\r\n            this.opStore('OPEN')\r\n          })\r\n      },\r\n      /** 审核 */\r\n      handleOp(row) {\r\n        this.form = row\r\n        this.dialogVisible = true;\r\n      },\r\n      /** 审核装修 */\r\n      handleOp1(row) {\r\n        this.form = row\r\n        this.dialogVisible1 = true;\r\n      },\r\n      // 弹窗确认\r\n      opStore(status) {\r\n        opData({\r\n          enterprise_id:this.form.enterprise_id,\r\n          status:status,\r\n          remark:this.form.remark\r\n        }).then(res => {\r\n          this.$message({type: \"success\", message: \"操作成功!\",});\r\n          this.dialogVisible = false;\r\n          this.dialogVisibleClose = false;\r\n\r\n          this.getList();\r\n        })\r\n      },\r\n\r\n      opStore1(status) {\r\n        if(status==3&&!this.form.rejection_reason){\r\n          this.$message({type: \"warning\", message: \"请输入驳回原因!\",});\r\n          return\r\n        }\r\n        opData({\r\n          enterprise_id:this.form.enterprise_id,\r\n          fitment_status:status,\r\n          rejection_reason:this.form.rejection_reason,\r\n          status:this.form.status,\r\n          remark:this.form.remark\r\n        }).then(res => {\r\n          this.$message({type: \"success\", message: \"操作成功!\",});\r\n          this.dialogVisible = false;\r\n          this.dialogVisibleClose = false;\r\n           this.dialogVisible1=false\r\n          this.getList();\r\n        })\r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .color-red {\r\n    color: red !important;\r\n    border: 1px solid red !important;\r\n  }\r\n</style>\r\n"]}]}