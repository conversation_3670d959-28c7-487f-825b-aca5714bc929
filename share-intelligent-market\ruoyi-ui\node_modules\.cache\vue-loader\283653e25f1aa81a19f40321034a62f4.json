{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\components\\enterprise-user.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\components\\enterprise-user.vue", "mtime": 1750151094287}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["enterprise-user.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "enterprise-user.vue", "sourceRoot": "src/views/supply/components", "sourcesContent": ["<!-- 添加文章 -->\r\n<template>\r\n  <el-dialog title=\"公司用户\" :visible.sync=\"show\" width=\"80%\" :before-close=\"() => show = false\">\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n        <el-form-item label=\"\" prop=\"name\">\r\n          <el-input clearable v-model=\"queryParams.name\" style=\"width: 240px;\" placeholder=\"请输入姓名\"\r\n            @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"telphone\">\r\n          <el-input clearable v-model=\"queryParams.telphone\" style=\"width: 240px;\" placeholder=\"请输入手机号\"\r\n            @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n\r\n      <el-table v-loading=\"loading\" :data=\"userList\">\r\n        <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\" />\r\n        <el-table-column label=\"手机号\" align=\"center\" width=\"200\" prop=\"telphone\" />\r\n        <el-table-column label=\"真实姓名\" align=\"center\" prop=\"realname\" width=\"200\" />\r\n        <el-table-column label=\"昵称\" align=\"center\" prop=\"nickname\" width=\"200\" />\r\n        <el-table-column label=\"邮箱\" align=\"center\" prop=\"email\" width=\"200\" />\r\n        <el-table-column prop=\"status\" label=\"类型\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag size=\"mini\" v-if=\"scope.row.type=='STAFF'\">员工</el-tag>\r\n            <el-tag size=\"mini\" type='success' v-else>管理员</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag size=\"mini\" type='danger' v-if='scope.row.status == 0'>下线</el-tag>\r\n            <el-tag size=\"mini\" type='success' v-else>上线</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"100\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleOp(scope.row)\">设为管理员</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\" />\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    listUser\r\n  } from '@/api/tool/util';\r\n  import {\r\n    adminData\r\n  } from \"@/api/enterprise/normal\";\r\n  export default {\r\n    props: {\r\n      enterpriseId: Number\r\n    },\r\n    data() {\r\n      return {\r\n        show: false,\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 公告表格数据\r\n        userList: [],\r\n        // 查询参数\r\n        queryParams: {\r\n          enterpriseId: undefined,\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          telphone: undefined,\r\n        },\r\n      };\r\n    },\r\n    methods: {\r\n      open(enterpriseId) {\r\n        this.queryParams.enterpriseId = enterpriseId\r\n        this.show = true\r\n        this.getList();\r\n      },\r\n      /** 查询公告列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listUser(this.queryParams).then(response => {\r\n          this.userList = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n      handleOp(row) {\r\n        adminData({\r\n          id: this.queryParams.enterpriseId,\r\n          adminId: row.id\r\n        }).then(response => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '操作成功!'\r\n          });\r\n          this.getList()\r\n          this.show = false;\r\n        });\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n</style>\r\n"]}]}