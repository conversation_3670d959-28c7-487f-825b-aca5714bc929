{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\dashboard\\LineChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\dashboard\\LineChart.vue", "mtime": 1750151094231}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_echarts", "_interopRequireDefault", "require", "_resize", "_default", "exports", "default", "mixins", "resize", "props", "className", "type", "String", "width", "height", "autoResize", "Boolean", "chartData", "Object", "required", "data", "chart", "watch", "deep", "handler", "val", "setOptions", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "echarts", "init", "$el", "_ref", "arguments", "length", "undefined", "expectedData", "actualData", "setOption", "xAxis", "boundaryGap", "axisTick", "show", "grid", "left", "right", "bottom", "top", "containLabel", "tooltip", "trigger", "axisPointer", "padding", "yAxis", "legend", "series", "name", "itemStyle", "normal", "color", "lineStyle", "smooth", "animationDuration", "animationEasing", "areaStyle"], "sources": ["src/views/dashboard/LineChart.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '350px'\r\n    },\r\n    autoResize: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chartData: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      deep: true,\r\n      handler(val) {\r\n        this.setOptions(val)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n      this.setOptions(this.chartData)\r\n    },\r\n    setOptions({ expectedData, actualData } = {}) {\r\n      this.chart.setOption({\r\n        xAxis: {\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          boundaryGap: false,\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        grid: {\r\n          left: 10,\r\n          right: 10,\r\n          bottom: 20,\r\n          top: 30,\r\n          containLabel: true\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          padding: [5, 10]\r\n        },\r\n        yAxis: {\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['expected', 'actual']\r\n        },\r\n        series: [{\r\n          name: 'expected', itemStyle: {\r\n            normal: {\r\n              color: '#FF005A',\r\n              lineStyle: {\r\n                color: '#FF005A',\r\n                width: 2\r\n              }\r\n            }\r\n          },\r\n          smooth: true,\r\n          type: 'line',\r\n          data: expectedData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'cubicInOut'\r\n        },\r\n        {\r\n          name: 'actual',\r\n          smooth: true,\r\n          type: 'line',\r\n          itemStyle: {\r\n            normal: {\r\n              color: '#3888fa',\r\n              lineStyle: {\r\n                color: '#3888fa',\r\n                width: 2\r\n              },\r\n              areaStyle: {\r\n                color: '#f3f8ff'\r\n              }\r\n            }\r\n          },\r\n          data: actualData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'quadraticOut'\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;AAKA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;AADAA,OAAA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGA;EACAC,MAAA,GAAAC,eAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAO,KAAA;MACAF,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAQ,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAS,UAAA;MACAJ,IAAA,EAAAK,OAAA;MACAV,OAAA;IACA;IACAW,SAAA;MACAN,IAAA,EAAAO,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAL,SAAA;MACAM,IAAA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAC,UAAA,CAAAD,GAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAV,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAW,OAAA;IACA,KAAAX,KAAA;EACA;EACAY,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAT,KAAA,GAAAa,gBAAA,CAAAC,IAAA,MAAAC,GAAA;MACA,KAAAV,UAAA,MAAAT,SAAA;IACA;IACAS,UAAA,WAAAA,WAAA;MAAA,IAAAW,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;QAAAG,YAAA,GAAAJ,IAAA,CAAAI,YAAA;QAAAC,UAAA,GAAAL,IAAA,CAAAK,UAAA;MACA,KAAArB,KAAA,CAAAsB,SAAA;QACAC,KAAA;UACAxB,IAAA;UACAyB,WAAA;UACAC,QAAA;YACAC,IAAA;UACA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,GAAA;UACAC,YAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACA7C,IAAA;UACA;UACA8C,OAAA;QACA;QACAC,KAAA;UACAZ,QAAA;YACAC,IAAA;UACA;QACA;QACAY,MAAA;UACAvC,IAAA;QACA;QACAwC,MAAA;UACAC,IAAA;UAAAC,SAAA;YACAC,MAAA;cACAC,KAAA;cACAC,SAAA;gBACAD,KAAA;gBACAnD,KAAA;cACA;YACA;UACA;UACAqD,MAAA;UACAvD,IAAA;UACAS,IAAA,EAAAqB,YAAA;UACA0B,iBAAA;UACAC,eAAA;QACA,GACA;UACAP,IAAA;UACAK,MAAA;UACAvD,IAAA;UACAmD,SAAA;YACAC,MAAA;cACAC,KAAA;cACAC,SAAA;gBACAD,KAAA;gBACAnD,KAAA;cACA;cACAwD,SAAA;gBACAL,KAAA;cACA;YACA;UACA;UACA5C,IAAA,EAAAsB,UAAA;UACAyB,iBAAA;UACAC,eAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}