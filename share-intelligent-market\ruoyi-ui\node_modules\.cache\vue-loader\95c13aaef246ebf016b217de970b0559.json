{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\credit.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\credit.vue", "mtime": 1750151094287}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0LGFkZCxlZGl0LG9wLGRlbCB9IGZyb20gIkAvYXBpL3N1cHBseS9jcmVkaXQiOw0KaW1wb3J0IHsgYWRkRGF0YSwgZWRpdERhdGEgfSBmcm9tICJAL2FwaS9zZXJ2aWNlL2luZm9yIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgICBuYW1lOiAiSW5mb3IiLA0KICAgIGRhdGEoKSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgICAgICBzaG93OiBmYWxzZSwNCiAgICAgICAgICAgIHRpdGxlOiAiIiwNCiAgICAgICAgICAgIGZvcm06IHsNCiAgICAgICAgICAgICAgICBpZDonJywNCiAgICAgICAgICAgICAgICByYW5rOiAiIiwNCiAgICAgICAgICAgICAgICByZW1hcms6ICcnLA0KICAgICAgICAgICAgICAgIHN0YXR1czogMSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBydWxlczogew0KICAgICAgICAgICAgICAgIHJhbms6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5L+h55So562J57qnIiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgICAgICAgdG90YWw6IDAsDQogICAgICAgICAgICAvLyDlhazlkYrooajmoLzmlbDmja4NCiAgICAgICAgICAgIGluZm9yTGlzdDogW10sDQogICAgICAgICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgICAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgICAgICAgICAgcGFnZTogMSwNCiAgICAgICAgICAgICAgICBzaXplOiAxMCwNCiAgICAgICAgICAgICAgICByYW5rOiAnJywNCiAgICAgICAgICAgICAgICBzdGF0dXM6ICcnLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgICAvKiog5p+l6K+i5YWs5ZGK5YiX6KGoICovDQogICAgICAgIGdldExpc3QoKSB7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgICAgbGlzdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuaW5mb3JMaXN0ID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS5jb3VudA0KICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2UgPSAxOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICAgICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7DQogICAgICAgICAgICAgICAgcGFnZTogMSwNCiAgICAgICAgICAgICAgICBzaXplOiAxMCwNCiAgICAgICAgICAgICAgICByYW5rOiAnJywNCiAgICAgICAgICAgICAgICBzdGF0dXM6ICcnLA0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOeKtuaAgeaUueWPmA0KICAgICAgICBjaGFuZ2VPUChyb3cpew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgIGxldCBvYmogPXsNCiAgICAgICAgICAgICAgICBvcGlkOnJvdy5pZCwNCiAgICAgICAgICAgICAgICBzdGF0dXM6cm93LnN0YXR1cyA9PSAxID8gMCA6IDENCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIG9wKG9iaikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aTjeS9nOaIkOWKnycpOw0KICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgICAgICAgIHRoaXMuYWRkKCk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgICAgICAgDQogICAgICAgIH0sDQogICAgICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlRGVsZXRlKHJvdyxpbmRleCkgew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5bqP5Y+35Li6IicgKyBpbmRleCArICci55qE5pWw5o2u6aG577yfJykNCiAgICAgICAgICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkZWwocm93LmlkKTsNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgICAgIH0sDQogICAgICAgIHJlc2V0KCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICAgIGlkOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgcmFuazogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIHJlbWFyazogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIHN0YXR1czogMSwNCiAgICAgICAgICAgIH07DQogICAgICAgIH0sDQogICAgICAgIGFkZCgpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIHRoaXMudGl0bGUgPSAi5re75YqgIjsNCiAgICAgICAgICAgIHRoaXMuc2hvdyA9IHRydWU7DQogICAgICAgIH0sDQogICAgICAgIGVkaXQoZGF0YSkgew0KICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLnvJbovpEiOw0KICAgICAgICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsNCiAgICAgICAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgICAgICAgICBpZDogZGF0YS5pZCwNCiAgICAgICAgICAgICAgICByYW5rOiBkYXRhLnJhbmssDQogICAgICAgICAgICAgICAgcmVtYXJrOiBkYXRhLnJlbWFyaywNCiAgICAgICAgICAgIH07DQogICAgICAgIH0sDQogICAgICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZSgodmFsaWRhdGUpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodmFsaWRhdGUpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgaWYgKCF0aGlzLmZvcm0uaWQpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGFkZCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgZWRpdCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+WujOWWhOS/oeaBr+WGjeaPkOS6pCEiKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["credit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "credit.vue", "sourceRoot": "src/views/supply", "sourcesContent": ["\r\n<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            @submit.native.prevent\r\n        >\r\n            <el-form-item label=\"信用等级\" prop='rank'>\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.rank\"\r\n                    style=\"width: 200px\"\r\n                    placeholder=\"请输入信用等级\"\r\n                    :maxlength=\"60\"\r\n                     @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"inforList\"\r\n        >\r\n            <el-table-column\r\n                label=\"序号\"\r\n                width=\"55\"\r\n                align=\"center\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"信用等级\"\r\n                align=\"center\"\r\n                prop=\"rank\"\r\n                width=\"200\"\r\n            />\r\n            <el-table-column\r\n                label=\"备注\"\r\n                align=\"center\"\r\n                prop=\"remark\"\r\n            />\r\n            <el-table-column\r\n                label=\"状态\"\r\n                align=\"center\"\r\n                prop=\"create_by\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <!-- 开启 -->\r\n                 <!-- <el-switch v-model=\"form.delivery\"></el-switch> -->\r\n                    <el-tag\r\n                        type=\"success\"\r\n                        v-if=\"scope.row.status == 1\"\r\n                        >启用</el-tag>\r\n                        <el-tag\r\n                        type=\"danger\"\r\n                        v-else\r\n                        >禁用</el-tag\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                fixed=\"right\"\r\n                width=\"180\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        v-if=\"scope.row.status == 0\"\r\n                        style=\"color:#85ce61\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"changeOP(scope.row)\"\r\n                        >启用</el-button\r\n                    >\r\n                    <el-button\r\n                        v-if=\"scope.row.status == 1\"\r\n                        style=\"color:#ebb563\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"changeOP(scope.row)\"\r\n                        >禁用</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"edit(scope.row)\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        style=\"color:red\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"handleDelete(scope.row, scope.$index+1)\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n        <!-- 添加弹窗 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"show\"\r\n            width=\"30%\"\r\n            :before-close=\"() => (show = false)\"\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" :rules=\"rules\">\r\n                <el-form-item label=\"信用等级\" prop=\"rank\">\r\n                    <el-input\r\n                        clearable\r\n                        v-model=\"form.rank\"\r\n                        :maxlength=\"60\"\r\n                        placeholder=\"请输入信用等级\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\">\r\n                    <el-input\r\n                        clearable\r\n                        v-model=\"form.remark\"\r\n                        :maxlength=\"60\"\r\n                        placeholder=\"请输入备注\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"show = false\">取 消</el-button>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    :loading=\"loading\"\r\n                    @click=\"handleSubmit\"\r\n                    >确 定</el-button\r\n                >\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { list,add,edit,op,del } from \"@/api/supply/credit\";\r\nimport { addData, editData } from \"@/api/service/infor\";\r\nexport default {\r\n    name: \"Infor\",\r\n    data() {\r\n        return {\r\n            showSearch: true,\r\n            loading: false,\r\n            show: false,\r\n            title: \"\",\r\n            form: {\r\n                id:'',\r\n                rank: \"\",\r\n                remark: '',\r\n                status: 1,\r\n            },\r\n            rules: {\r\n                rank: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请填写信用等级\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n            // 总条数\r\n            total: 0,\r\n            // 公告表格数据\r\n            inforList: [],\r\n            // 查询参数\r\n            queryParams: {\r\n                page: 1,\r\n                size: 10,\r\n                rank: '',\r\n                status: '',\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        /** 查询公告列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            list(this.queryParams).then((response) => {\r\n                this.inforList = response.data\r\n                this.total = response.count\r\n                this.loading = false;\r\n            });\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.page = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.queryParams = {\r\n                page: 1,\r\n                size: 10,\r\n                rank: '',\r\n                status: '',\r\n            }\r\n            this.getList();\r\n        },\r\n        // 状态改变\r\n        changeOP(row){\r\n            this.loading = true;\r\n            let obj ={\r\n                opid:row.id,\r\n                status:row.status == 1 ? 0 : 1\r\n            }\r\n            op(obj).then((response) => {\r\n                this.loading = false;\r\n                this.$message.success('操作成功');\r\n                this.handleQuery();\r\n            });\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.add();\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            \r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row,index) {\r\n            this.$modal\r\n                .confirm('是否确认删除序号为\"' + index + '\"的数据项？')\r\n                .then(function () {\r\n                    return del(row.id);\r\n                })\r\n                .then(() => {\r\n                    this.handleQuery();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        reset() {\r\n            this.form = {\r\n                id: undefined,\r\n                rank: undefined,\r\n                remark: undefined,\r\n                status: 1,\r\n            };\r\n        },\r\n        add() {\r\n            this.reset();\r\n            this.title = \"添加\";\r\n            this.show = true;\r\n        },\r\n        edit(data) {\r\n            this.title = \"编辑\";\r\n            this.show = true;\r\n            this.form = {\r\n                id: data.id,\r\n                rank: data.rank,\r\n                remark: data.remark,\r\n            };\r\n        },\r\n        handleSubmit() {\r\n            this.$refs.form.validate((validate) => {\r\n                if (validate) {\r\n                    this.loading = true;\r\n                    if (!this.form.id) {\r\n                        add(this.form).then((response) => {\r\n                            this.$message({\r\n                                type: \"success\",\r\n                                message: \"操作成功!\",\r\n                            });\r\n                            this.loading = false;\r\n                            this.show = false;\r\n                            this.handleQuery();\r\n                        });\r\n                    } else {\r\n                        edit(this.form).then((response) => {\r\n                            this.$message({\r\n                                type: \"success\",\r\n                                message: \"操作成功!\",\r\n                            });\r\n                            this.loading = false;\r\n                            this.show = false;\r\n                            this.handleQuery();\r\n                        });\r\n                    }\r\n                } else {\r\n                    this.$modal.msgError(\"请完善信息再提交!\");\r\n                }\r\n            });\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}