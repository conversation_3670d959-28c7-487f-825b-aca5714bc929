{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\register.vue?vue&type=style&index=0&id=77453986&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\register.vue", "mtime": 1750151094276}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750495809569}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucmVnaXN0ZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgaGVpZ2h0OiAxMDAlOw0KICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoIi4uL2Fzc2V0cy9pbWFnZXMvbG9naW4tYmFja2dyb3VuZC5qcGciKTsNCiAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjsNCn0NCi50aXRsZSB7DQogIG1hcmdpbjogMHB4IGF1dG8gMzBweCBhdXRvOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjNzA3MDcwOw0KfQ0KDQoucmVnaXN0ZXItZm9ybSB7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgd2lkdGg6IDQwMHB4Ow0KICBwYWRkaW5nOiAyNXB4IDI1cHggNXB4IDI1cHg7DQogIC5lbC1pbnB1dCB7DQogICAgaGVpZ2h0OiAzOHB4Ow0KICAgIGlucHV0IHsNCiAgICAgIGhlaWdodDogMzhweDsNCiAgICB9DQogIH0NCiAgLmlucHV0LWljb24gew0KICAgIGhlaWdodDogMzlweDsNCiAgICB3aWR0aDogMTRweDsNCiAgICBtYXJnaW4tbGVmdDogMnB4Ow0KICB9DQp9DQoucmVnaXN0ZXItdGlwIHsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjYmZiZmJmOw0KfQ0KLnJlZ2lzdGVyLWNvZGUgew0KICB3aWR0aDogMzMlOw0KICBoZWlnaHQ6IDM4cHg7DQogIGZsb2F0OiByaWdodDsNCiAgaW1nIHsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsNCiAgfQ0KfQ0KLmVsLXJlZ2lzdGVyLWZvb3RlciB7DQogIGhlaWdodDogNDBweDsNCiAgbGluZS1oZWlnaHQ6IDQwcHg7DQogIHBvc2l0aW9uOiBmaXhlZDsNCiAgYm90dG9tOiAwOw0KICB3aWR0aDogMTAwJTsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogI2ZmZjsNCiAgZm9udC1mYW1pbHk6IEFyaWFsOw0KICBmb250LXNpemU6IDEycHg7DQogIGxldHRlci1zcGFjaW5nOiAxcHg7DQp9DQoucmVnaXN0ZXItY29kZS1pbWcgew0KICBoZWlnaHQ6IDM4cHg7DQp9DQo="}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"register\">\r\n    <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\r\n      <h3 class=\"title\">檬豆云采购平台</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input v-model=\"registerForm.username\" type=\"text\" auto-complete=\"off\" placeholder=\"账号\">\r\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"registerForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"confirmPassword\">\r\n        <el-input\r\n          v-model=\"registerForm.confirmPassword\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"确认密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaOnOff\">\r\n        <el-input\r\n          v-model=\"registerForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n        <div class=\"register-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleRegister\"\r\n        >\r\n          <span v-if=\"!loading\">注 册</span>\r\n          <span v-else>注 册 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right;\">\r\n          <router-link class=\"link-type\" :to=\"'/login'\">使用已有账户登录</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-register-footer\">\r\n      <span>Copyright © 2018-2022 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, register } from \"@/api/login\";\r\n\r\nexport default {\r\n  name: \"Register\",\r\n  data() {\r\n    const equalToPassword = (rule, value, callback) => {\r\n      if (this.registerForm.password !== value) {\r\n        callback(new Error(\"两次输入的密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      codeUrl: \"\",\r\n      registerForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        confirmPassword: \"\",\r\n        code: \"\",\r\n        uuid: \"\"\r\n      },\r\n      registerRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\r\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\r\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\r\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\r\n      },\r\n      loading: false,\r\n      captchaOnOff: false\r\n    };\r\n  },\r\n  created() {\r\n    //this.getCode();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;\r\n        if (this.captchaOnOff) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.registerForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    handleRegister() {\r\n      this.$refs.registerForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          register(this.registerForm).then(res => {\r\n            const username = this.registerForm.username;\r\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\r\n              dangerouslyUseHTMLString: true,\r\n              type: 'success'\r\n            }).then(() => {\r\n              this.$router.push(\"/login\");\r\n            }).catch(() => {});\r\n          }).catch(() => {\r\n            this.loading = false;\r\n            if (this.captchaOnOff) {\r\n              this.getCode();\r\n            }\r\n          })\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.register {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.register-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.register-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.register-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-register-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.register-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"]}]}