{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\errorCode.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\errorCode.js", "mtime": 1750151094208}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICAnNDAxJzogJ+iupOivgeWksei0pe+8jOaXoOazleiuv+mXruezu+e7n+i1hOa6kCcsCiAgJzQwMyc6ICflvZPliY3mk43kvZzmsqHmnInmnYPpmZAnLAogICc0MDQnOiAn6K6/6Zeu6LWE5rqQ5LiN5a2Y5ZyoJywKICAnZGVmYXVsdCc6ICfns7vnu5/mnKrnn6XplJnor6/vvIzor7flj43ppojnu5nnrqHnkIblkZgnCn07"}, {"version": 3, "names": [], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/utils/errorCode.js"], "sourcesContent": ["export default {\r\n  '401': '认证失败，无法访问系统资源',\r\n  '403': '当前操作没有权限',\r\n  '404': '访问资源不存在',\r\n  'default': '系统未知错误，请反馈给管理员'\r\n}\r\n"], "mappings": ";;;;;;iCAAe;EACb,KAAK,EAAE,eAAe;EACtB,KAAK,EAAE,UAAU;EACjB,KAAK,EAAE,SAAS;EAChB,SAAS,EAAE;AACb,CAAC", "ignoreList": []}]}