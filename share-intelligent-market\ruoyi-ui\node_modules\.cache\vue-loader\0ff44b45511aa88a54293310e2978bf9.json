{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\member\\level.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\member\\level.vue", "mtime": 1750151094243}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["level.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "level.vue", "sourceRoot": "src/views/member", "sourcesContent": ["// 会员等级\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"名称\" prop=\"name\">\r\n        <el-input clearable v-model=\"queryParams.name\" style=\"width: 300px\" placeholder=\"请输入名称\" :maxlength=\"60\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"手机号码\" prop=\"title\">\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.title\"\r\n                    style=\"width: 300px\"\r\n                    placeholder=\"请输入企业名称\"\r\n                    :maxlength=\"60\"\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item> -->\r\n      <!-- <el-form-item label=\"用户类型\" prop=\"title\">\r\n                <el-select v-model=\"value\" placeholder=\"请选择用户类型\">\r\n                    <el-option\r\n                        v-for=\"item in options\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.label\"\r\n                        :value=\"item.value\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item> -->\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" clearable placeholder=\"请选择状态\">\r\n          <el-option v-for=\"item in optionsStatus\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col>\r\n\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\">删除\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.$index + 1 }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"名称\" align=\"center\" width=\"400\" prop=\"name\" :show-overflow-tooltip=\"true\" />\r\n\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" width=\"160\" />\r\n      <el-table-column label=\"折扣%\" align=\"center\" prop=\"discount\" width=\"160\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"create_by\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- 开启 -->\r\n          <!-- <el-switch v-model=\"form.delivery\"></el-switch> -->\r\n          <el-tag type=\"success\" v-if=\"scope.row.status == 1\">启用</el-tag>\r\n          <el-tag type=\"danger\" v-else>禁用</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button style=\"color: #85ce61\" size=\"mini\" type=\"text\" @click=\"setStatus(scope.row,1)\">启用</el-button>\r\n          <el-button style=\"color: #ebb563\" size=\"mini\" type=\"text\" @click=\"setStatus(scope.row,0)\">禁用</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n    <!-- 添加弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"70%\" :before-close=\"() => (show = false)\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" :rules=\"rules\">\r\n        <el-form-item label=\"名称\" prop=\"name\">\r\n          <el-input clearable v-model=\"form.name\" placeholder=\"请输入名称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"折扣\" prop=\"discount\">\r\n          <el-input clearable type=\"number\" v-model=\"form.discount\" placeholder=\"请输折扣\">\r\n            <template slot=\"append\">%</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input clearable v-model=\"form.remark\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-switch v-model=\"form.status\" active-value=\"1\" inactive-value=\"0\">\r\n          </el-switch>\r\n        </el-form-item>\r\n\r\n\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"show = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    listData,\r\n    setStatus,\r\n    delData,\r\n    addData,\r\n    editData,\r\n  } from \"@/api/member/level.js\";\r\n  export default {\r\n    name: \"Infor\",\r\n    data() {\r\n      return {\r\n        optionsStatus: [{\r\n            value: 1,\r\n            label: \"启用\",\r\n          },\r\n          {\r\n            value: 0,\r\n            label: \"禁用\",\r\n          },\r\n        ],\r\n        normsList: [],\r\n        loading: false,\r\n        show: false,\r\n        title: \"\",\r\n        form: {},\r\n        rules: {\r\n\r\n          name: [{\r\n            required: true,\r\n            message: \"请填写名称\",\r\n            trigger: \"blur\",\r\n          }],\r\n          discount: [{\r\n            required: true,\r\n            message: \"请填写折扣\",\r\n            trigger: \"blur\",\r\n          }],\r\n\r\n          remark: [{\r\n            required: true,\r\n            message: \"请填写备注\",\r\n            trigger: \"blur\",\r\n          }],\r\n\r\n        },\r\n\r\n        // 遮罩层\r\n        loading: true,\r\n        // 选中数组\r\n        ids: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表格数据\r\n        list: [],\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n        },\r\n        form: {\r\n          status: 1\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.getList();\r\n    },\r\n    methods: {\r\n      // 修改状态\r\n      setStatus(row, type) {\r\n        setStatus({\r\n          opid: row.id,\r\n          status: type\r\n        }).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message({\r\n              message: response.msg,\r\n              type: \"success\",\r\n            });\r\n            this.getList();\r\n          }\r\n        });\r\n      },\r\n      uploadNorm(fileList) {\r\n        let name = undefined;\r\n        let url = undefined;\r\n        if (fileList.length) {\r\n          name = fileList[0].name;\r\n          url = fileList[0].url;\r\n        }\r\n        this.form.normfile = name;\r\n        this.form.normurl = url;\r\n        console.log(this.form);\r\n      },\r\n      /** 查询公告列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listData(this.queryParams).then((response) => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n        }\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n      // 多选框选中数据\r\n      handleSelectionChange(selection) {\r\n        this.ids = selection.map((item) => item.id);\r\n        this.single = selection.length != 1;\r\n        this.multiple = !selection.length;\r\n      },\r\n      /** 新增按钮操作 */\r\n      handleAdd() {\r\n        this.add();\r\n      },\r\n      /** 修改按钮操作 */\r\n      handleUpdate(row) {\r\n        const inforId = row.id || this.ids;\r\n\r\n        this.form = JSON.parse(JSON.stringify(row));\r\n        this.title = \"编辑\";\r\n        this.show = true;\r\n        // getData(inforId).then((response) => {\r\n        //     this.edit(response.data);\r\n        // });\r\n      },\r\n      /** 删除按钮操作 */\r\n      handleDelete(row) {\r\n        const inforIds = row.id || this.ids.join(\",\");\r\n        this.$modal\r\n          .confirm('是否确认删除编号为\"' + inforIds + '\"的数据项？')\r\n          .then(function() {\r\n            return delData(inforIds);\r\n          })\r\n          .then(() => {\r\n            this.getList();\r\n            this.$modal.msgSuccess(\"删除成功\");\r\n          })\r\n          .catch(() => {});\r\n      },\r\n      handleCopy(row) {\r\n        const clipboardObj = navigator.clipboard;\r\n        this.$message({\r\n          message: \"链接已复制\",\r\n          type: \"success\",\r\n        });\r\n        clipboardObj.writeText(\r\n          \"https://sc.cnudj.com/infor?id=\" + row.id\r\n        );\r\n      },\r\n      reset() {\r\n        this.form = {\r\n          id: undefined,\r\n          title: undefined,\r\n          content: undefined,\r\n        };\r\n        this.resetForm(\"form\");\r\n      },\r\n      add() {\r\n        this.reset();\r\n        this.title = \"添加\";\r\n        this.show = true;\r\n      },\r\n      edit(data) {\r\n        this.title = \"编辑\";\r\n        this.show = true;\r\n        this.form = data;\r\n      },\r\n      handleSubmit() {\r\n        this.$refs.form.validate((validate) => {\r\n          if (validate) {\r\n            this.loading = true;\r\n            if (!this.form.id) {\r\n              console.log(this.form);\r\n              addData(this.form).then((response) => {\r\n                this.$message({\r\n                  type: \"success\",\r\n                  message: \"操作成功!\",\r\n                });\r\n                this.loading = false;\r\n                this.show = false;\r\n                this.getList()\r\n                this.$emit(\"refresh\");\r\n              });\r\n            } else {\r\n              editData(this.form).then((response) => {\r\n                this.$message({\r\n                  type: \"success\",\r\n                  message: \"操作成功!\",\r\n                });\r\n                this.loading = false;\r\n                this.show = false;\r\n                this.getList()\r\n\r\n                this.$emit(\"refresh\");\r\n              });\r\n            }\r\n          } else {\r\n            this.$modal.msgError(\"请完善信息再提交!\");\r\n          }\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n"]}]}