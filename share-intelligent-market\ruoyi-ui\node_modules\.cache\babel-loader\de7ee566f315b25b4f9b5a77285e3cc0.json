{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\system\\dept.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\system\\dept.js", "mtime": 1750151093980}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDept", "query", "request", "url", "method", "params", "listDept<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deptId", "getDept", "treeselect", "roleDeptTreeselect", "roleId", "addDept", "data", "updateDept", "delDept"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/system/dept.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询部门列表\r\nexport function listDept(query) {\r\n  return request({\r\n    url: '/system/dept/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询部门列表（排除节点）\r\nexport function listDeptExcludeChild(deptId) {\r\n  return request({\r\n    url: '/system/dept/list/exclude/' + deptId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询部门详细\r\nexport function getDept(deptId) {\r\n  return request({\r\n    url: '/system/dept/' + deptId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询部门下拉树结构\r\nexport function treeselect() {\r\n  return request({\r\n    url: '/system/dept/treeselect',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据角色ID查询部门树结构\r\nexport function roleDeptTreeselect(roleId) {\r\n  return request({\r\n    url: '/system/dept/roleDeptTreeselect/' + roleId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增部门\r\nexport function addDept(data) {\r\n  return request({\r\n    url: '/system/dept',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改部门\r\nexport function updateDept(data) {\r\n  return request({\r\n    url: '/system/dept',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除部门\r\nexport function delDept(deptId) {\r\n  return request({\r\n    url: '/system/dept/' + deptId,\r\n    method: 'delete'\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,oBAAoBA,CAACC,MAAM,EAAE;EAC3C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGI,MAAM;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACD,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGQ,MAAM;IAChDP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACR,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}