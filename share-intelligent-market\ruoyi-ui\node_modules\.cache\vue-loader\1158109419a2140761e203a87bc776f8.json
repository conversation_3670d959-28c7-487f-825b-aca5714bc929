{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\achievement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\achievement\\index.vue", "mtime": 1750151094248}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3RBY2hpZXZlbWVudCwNCiAgICBnZXRBY2hpZXZlbWVudCwNCiAgICBkZWxBY2hpZXZlbWVudCwNCiAgICBhZGRBY2hpZXZlbWVudCwNCiAgICB1cGRhdGVBY2hpZXZlbWVudCwNCn0gZnJvbSAiQC9hcGkvdXVjL2FjaGlldmVtZW50IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJBY2hpZXZlbWVudCIsDQogICAgZGljdHM6IFsNCiAgICAgICAgInV1Y19jb29wZXJhdGlvbl90eXBlIiwNCiAgICAgICAgInV1Y19hcHBsaWNhdGlvbl9hcmVhcyIsDQogICAgICAgICJ1dWNfb25saW5lIiwNCiAgICAgICAgInV1Y19zdGVwX3R5cGUiLA0KICAgIF0sDQogICAgZGF0YSgpIHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgICAgICAgaWRzOiBbXSwNCiAgICAgICAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgICAgICAgdG90YWw6IDAsDQogICAgICAgICAgICAvLyDmiJDmnpznrqHnkIbooajmoLzmlbDmja4NCiAgICAgICAgICAgIGFjaGlldmVtZW50TGlzdDogW10sDQogICAgICAgICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgICAgICAgIHRpdGxlOiAiIiwNCiAgICAgICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgICAgICAgb3BlbjogZmFsc2UsDQogICAgICAgICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgICAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgICAgICAgICAgdGl0bGU6IG51bGwsDQogICAgICAgICAgICAgICAgYXBwbGljYXRpb246IG51bGwsDQogICAgICAgICAgICAgICAgc3RlcDogbnVsbCwNCiAgICAgICAgICAgICAgICBjb29wZXJhdGlvbjogbnVsbCwNCiAgICAgICAgICAgICAgICBsaW5rTmFtZTogbnVsbCwNCiAgICAgICAgICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICAgICAgICBmb3JtOiB7fSwNCiAgICAgICAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgICAgICAgcnVsZXM6IHsNCiAgICAgICAgICAgICAgICB0aXRsZTogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmoIfpopjkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgZGV0YWlsOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaPj+i/sOS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICBwaWN0dXJlczpbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuWbvueJh+S4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIC8vIGFwcGxpY2F0aW9uOiBbDQogICAgICAgICAgICAgICAgLy8gICAgIHsNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgbWVzc2FnZTogIuW6lOeUqOmihuWfn+S4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgLy8gICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIiwNCiAgICAgICAgICAgICAgICAvLyAgICAgfSwNCiAgICAgICAgICAgICAgICAvLyBdLA0KICAgICAgICAgICAgICAgIC8vIHN0ZXA6IFsNCiAgICAgICAgICAgICAgICAvLyAgICAgew0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgLy8gICAgICAgICBtZXNzYWdlOiAi5Lqn5ZOB6Zi25q615LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiLA0KICAgICAgICAgICAgICAgIC8vICAgICB9LA0KICAgICAgICAgICAgICAgIC8vIF0sDQogICAgICAgICAgICAgICAgLy8gY29vcGVyYXRpb246IFsNCiAgICAgICAgICAgICAgICAvLyAgICAgew0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgLy8gICAgICAgICBtZXNzYWdlOiAi5ZCI5L2c5pa55byP5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiLA0KICAgICAgICAgICAgICAgIC8vICAgICB9LA0KICAgICAgICAgICAgICAgIC8vIF0sDQogICAgICAgICAgICAgICAgLy8gY3JlYXRlVGltZTogWw0KICAgICAgICAgICAgICAgIC8vICAgICB7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIG1lc3NhZ2U6ICLliJvlu7rml7bpl7TkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgIC8vICAgICB9LA0KICAgICAgICAgICAgICAgIC8vIF0sDQogICAgICAgICAgICB9LA0KICAgICAgICB9Ow0KICAgIH0sDQogICAgY3JlYXRlZCgpIHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZGljdC50eXBlLnV1Y19hcHBsaWNhdGlvbl9hcmVhcyk7DQogICAgfSwNCiAgICB3YXRjaDogew0KICAgICAgICBmb3JtOiB7DQogICAgICAgICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlRmllbGQoWyJwaWN0dXJlcyJdLGFzeW5jICh2YWxpZCk9PnsNCiAgICAgICAgICAgICAgICAgIGlmKHRoaXMuZm9ybS5waWN0dXJlcyl7DQogICAgICAgICAgICAgICAgICAgICAgaWYodmFsaWQpew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLmNsZWFyVmFsaWRhdGUoJ3BpY3R1cmVzJyk7IA0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGVlcDogdHJ1ZSwNCiAgICAgICAgfSwNCiAgICB9LA0KICAgIG1ldGhvZHM6IHsNCiAgICAgICAgLyoqIOafpeivouaIkOaenOeuoeeQhuWIl+ihqCAqLw0KICAgICAgICBnZXRMaXN0KCkgew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgIGxpc3RBY2hpZXZlbWVudCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuYWNoaWV2ZW1lbnRMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgICAgIGNhbmNlbCgpIHsNCiAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICB9LA0KICAgICAgICAvLyDooajljZXph43nva4NCiAgICAgICAgcmVzZXQoKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgICAgICAgICAgaWQ6IG51bGwsDQogICAgICAgICAgICAgICAgdGl0bGU6IG51bGwsDQogICAgICAgICAgICAgICAgZGV0YWlsOiBudWxsLA0KICAgICAgICAgICAgICAgIGFwcGxpY2F0aW9uOiBudWxsLA0KICAgICAgICAgICAgICAgIHN0ZXA6IG51bGwsDQogICAgICAgICAgICAgICAgY29vcGVyYXRpb246IG51bGwsDQogICAgICAgICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgICAgICAgIGNvbXBuYXk6IG51bGwsDQogICAgICAgICAgICAgICAgbGlua05hbWU6IG51bGwsDQogICAgICAgICAgICAgICAgbGlua1RlbDogbnVsbCwNCiAgICAgICAgICAgICAgICBzdGF0dXM6ICIwIiwNCiAgICAgICAgICAgICAgICBwaWN0dXJlczogbnVsbCwNCiAgICAgICAgICAgICAgICBhdHRhY2htZW50czogbnVsbCwNCiAgICAgICAgICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICAgICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICAgICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgICAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICAgICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsNCiAgICAgICAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgICAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVBZGQoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDmiJDmnpznrqHnkIYiOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgICAgICAgZ2V0QWNoaWV2ZW1lbnQoaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICAgICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55oiQ5p6c566h55CGIjsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICAgICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmZvcm0ubGlua1RlbCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IHJlZyA9IC9eMVszNDU3ODldXGR7OX0kLzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghcmVnLnRlc3QodGhpcy5mb3JtLmxpbmtUZWwpKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+i+k+WFpeato+ehruaJi+acuuWPtyIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUFjaGlldmVtZW50KHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgYWRkQWNoaWV2ZW1lbnQodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICAgICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICAgICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAgICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmiJDmnpznrqHnkIbnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykNCiAgICAgICAgICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkZWxBY2hpZXZlbWVudChpZHMpOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgICAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICAgICAgICAgInV1Yy9hY2hpZXZlbWVudC9leHBvcnQiLA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIGBhY2hpZXZlbWVudF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgICAgICAgKTsNCiAgICAgICAgfSwNCiAgICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuWA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/ningmengdou/achievement", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"标题\" prop=\"title\">\r\n                <el-input\r\n                    v-model=\"queryParams.title\"\r\n                    placeholder=\"请输入标题\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"应用领域\" prop=\"application\">\r\n                <el-select\r\n                    v-model=\"queryParams.application\"\r\n                    placeholder=\"请选择应用领域\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_application_areas\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"产品阶段\" prop=\"step\">\r\n                <el-select\r\n                    v-model=\"queryParams.step\"\r\n                    placeholder=\"请选择产品阶段\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_step_type\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"合作方式\" prop=\"cooperation\">\r\n                <el-select\r\n                    v-model=\"queryParams.cooperation\"\r\n                    placeholder=\"请选择合作方式\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_cooperation_type\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"linkName\">\r\n                <el-input\r\n                    v-model=\"queryParams.linkName\"\r\n                    placeholder=\"请输入联系人\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n                <el-select\r\n                    v-model=\"queryParams.status\"\r\n                    placeholder=\"请选择状态\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_online\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:achievement:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:achievement:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:achievement:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['uuc:achievement:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"achievementList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"编码\" align=\"center\" prop=\"id\" />\r\n            <el-table-column label=\"标题\" align=\"center\" prop=\"title\" />\r\n            <el-table-column label=\"应用领域\" align=\"center\" prop=\"application\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_application_areas\"\r\n                        :value=\"scope.row.application\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"产品阶段\" align=\"center\" prop=\"step\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_step_type\"\r\n                        :value=\"scope.row.step\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"合作方式\" align=\"center\" prop=\"cooperation\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_cooperation_type\"\r\n                        :value=\"scope.row.cooperation\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n            <el-table-column label=\"公司名称\" align=\"center\" prop=\"compnay\" />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"linkName\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"linkTel\" />\r\n            <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_online\"\r\n                        :value=\"scope.row.status\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"图片\"\r\n                align=\"center\"\r\n                prop=\"pictures\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <image-preview\r\n                        :src=\"scope.row.pictures\"\r\n                        :width=\"50\"\r\n                        :height=\"50\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:achievement:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:achievement:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改成果管理对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n                <el-form-item label=\"标题\" prop=\"title\">\r\n                    <el-input\r\n                        v-model=\"form.title\"\r\n                        maxlength=\"255\"\r\n                        placeholder=\"请输入标题\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"成果描述\" prop=\"detail\">\r\n                    <el-input\r\n                        v-model=\"form.detail\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"255\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"应用领域\" prop=\"application\">\r\n                    <el-select\r\n                        v-model=\"form.application\"\r\n                        placeholder=\"请选择应用领域\"\r\n                    >\r\n                        <el-option\r\n                            v-for=\"dict in dict.type.uuc_application_areas\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.label\"\r\n                            :value=\"dict.value\"\r\n                        ></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"产品阶段\" prop=\"step\">\r\n                    <el-select v-model=\"form.step\" placeholder=\"请选择产品阶段\">\r\n                        <el-option\r\n                            v-for=\"dict in dict.type.uuc_step_type\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.label\"\r\n                            :value=\"dict.value\"\r\n                        ></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合作方式\" prop=\"cooperation\">\r\n                    <el-select\r\n                        v-model=\"form.cooperation\"\r\n                        placeholder=\"请选择合作方式\"\r\n                    >\r\n                        <el-option\r\n                            v-for=\"dict in dict.type.uuc_cooperation_type\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.label\"\r\n                            :value=\"dict.value\"\r\n                        ></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"255\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称\" prop=\"compnay\">\r\n                    <el-input\r\n                        v-model=\"form.compnay\"\r\n                        maxlength=\"100\"\r\n                        placeholder=\"请输入公司名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" prop=\"linkName\">\r\n                    <el-input\r\n                        v-model=\"form.linkName\"\r\n                        maxlength=\"100\"\r\n                        placeholder=\"请输入联系人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系电话\" prop=\"linkTel\">\r\n                    <el-input\r\n                        v-model=\"form.linkTel\"\r\n                        maxlength=\"20\"\r\n                        type=\"number\"\r\n                        min=\"0\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\">\r\n                    <el-radio-group v-model=\"form.status\">\r\n                        <el-radio\r\n                            v-for=\"dict in dict.type.uuc_online\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.value\"\r\n                            >{{ dict.label }}</el-radio\r\n                        >\r\n                    </el-radio-group>\r\n                </el-form-item>\r\n                <el-form-item label=\"图片\" prop=\"pictures\">\r\n                    <image-upload v-model=\"form.pictures\" :limit=\"1\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"附件\">\r\n                    <file-upload :limit=\"2\" v-model=\"form.attachments\" />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listAchievement,\r\n    getAchievement,\r\n    delAchievement,\r\n    addAchievement,\r\n    updateAchievement,\r\n} from \"@/api/uuc/achievement\";\r\n\r\nexport default {\r\n    name: \"Achievement\",\r\n    dicts: [\r\n        \"uuc_cooperation_type\",\r\n        \"uuc_application_areas\",\r\n        \"uuc_online\",\r\n        \"uuc_step_type\",\r\n    ],\r\n    data() {\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 成果管理表格数据\r\n            achievementList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                title: null,\r\n                application: null,\r\n                step: null,\r\n                cooperation: null,\r\n                linkName: null,\r\n                status: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            // 表单校验\r\n            rules: {\r\n                title: [\r\n                    {\r\n                        required: true,\r\n                        message: \"标题不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                detail: [\r\n                    {\r\n                        required: true,\r\n                        message: \"描述不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                pictures:[\r\n                    {\r\n                        required: true,\r\n                        message: \"图片不能为空\",\r\n                        trigger: \"change\",\r\n                    },\r\n                ],\r\n                // application: [\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"应用领域不能为空\",\r\n                //         trigger: \"change\",\r\n                //     },\r\n                // ],\r\n                // step: [\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"产品阶段不能为空\",\r\n                //         trigger: \"change\",\r\n                //     },\r\n                // ],\r\n                // cooperation: [\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"合作方式不能为空\",\r\n                //         trigger: \"change\",\r\n                //     },\r\n                // ],\r\n                // createTime: [\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"创建时间不能为空\",\r\n                //         trigger: \"blur\",\r\n                //     },\r\n                // ],\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n        console.log(this.dict.type.uuc_application_areas);\r\n    },\r\n    watch: {\r\n        form: {\r\n            handler(newVal, oldVal) {\r\n                this.$refs[\"form\"].validateField([\"pictures\"],async (valid)=>{\r\n                  if(this.form.pictures){\r\n                      if(valid){\r\n                        this.$refs[\"form\"].clearValidate('pictures'); \r\n                      }\r\n                    }\r\n                })\r\n            },\r\n            deep: true,\r\n        },\r\n    },\r\n    methods: {\r\n        /** 查询成果管理列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listAchievement(this.queryParams).then((response) => {\r\n                this.achievementList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                title: null,\r\n                detail: null,\r\n                application: null,\r\n                step: null,\r\n                cooperation: null,\r\n                remark: null,\r\n                compnay: null,\r\n                linkName: null,\r\n                linkTel: null,\r\n                status: \"0\",\r\n                pictures: null,\r\n                attachments: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加成果管理\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getAchievement(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改成果管理\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.linkTel) {\r\n                        let reg = /^1[345789]\\d{9}$/;\r\n                        if (!reg.test(this.form.linkTel)) {\r\n                            this.$modal.msgError(\"请输入正确手机号\");\r\n                            return;\r\n                        }\r\n                    }\r\n                    if (this.form.id != null) {\r\n                        updateAchievement(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addAchievement(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除成果管理编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delAchievement(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/achievement/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `achievement_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}