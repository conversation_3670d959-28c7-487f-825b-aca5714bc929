{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\register.vue", "mtime": 1750151094276}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"register\">\r\n    <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\r\n      <h3 class=\"title\">檬豆云采购平台</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input v-model=\"registerForm.username\" type=\"text\" auto-complete=\"off\" placeholder=\"账号\">\r\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"registerForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"confirmPassword\">\r\n        <el-input\r\n          v-model=\"registerForm.confirmPassword\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"确认密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaOnOff\">\r\n        <el-input\r\n          v-model=\"registerForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n        <div class=\"register-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleRegister\"\r\n        >\r\n          <span v-if=\"!loading\">注 册</span>\r\n          <span v-else>注 册 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right;\">\r\n          <router-link class=\"link-type\" :to=\"'/login'\">使用已有账户登录</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-register-footer\">\r\n      <span>Copyright © 2018-2022 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, register } from \"@/api/login\";\r\n\r\nexport default {\r\n  name: \"Register\",\r\n  data() {\r\n    const equalToPassword = (rule, value, callback) => {\r\n      if (this.registerForm.password !== value) {\r\n        callback(new Error(\"两次输入的密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      codeUrl: \"\",\r\n      registerForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        confirmPassword: \"\",\r\n        code: \"\",\r\n        uuid: \"\"\r\n      },\r\n      registerRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\r\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\r\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\r\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\r\n      },\r\n      loading: false,\r\n      captchaOnOff: false\r\n    };\r\n  },\r\n  created() {\r\n    //this.getCode();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;\r\n        if (this.captchaOnOff) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.registerForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    handleRegister() {\r\n      this.$refs.registerForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          register(this.registerForm).then(res => {\r\n            const username = this.registerForm.username;\r\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\r\n              dangerouslyUseHTMLString: true,\r\n              type: 'success'\r\n            }).then(() => {\r\n              this.$router.push(\"/login\");\r\n            }).catch(() => {});\r\n          }).catch(() => {\r\n            this.loading = false;\r\n            if (this.captchaOnOff) {\r\n              this.getCode();\r\n            }\r\n          })\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.register {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.register-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.register-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.register-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-register-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.register-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"]}]}