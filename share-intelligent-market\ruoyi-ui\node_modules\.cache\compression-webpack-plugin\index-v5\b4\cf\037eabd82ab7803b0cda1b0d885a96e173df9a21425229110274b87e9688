
bd2c26173b1e0d44cba77018f2349dc1ec5e0609	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fui\\u002Foxide-dark\\u002Fskin.css\",\"contentHash\":\"be264dba08d15fdd440f11e93911a63c\"}","integrity":"sha512-np1vbh4Pj4TDBapU5h0aqI2W/WtVk8zJOGaAZOT6dB2C9NypY5gqCp+ZNGEoH6Gzi9d/TuKBmcQQQP/G/QiBjA==","time":1750496064275,"size":43622}