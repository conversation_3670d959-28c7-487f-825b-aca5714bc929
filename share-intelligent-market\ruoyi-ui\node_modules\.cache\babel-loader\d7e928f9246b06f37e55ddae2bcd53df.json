{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\error\\401.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\error\\401.vue", "mtime": 1750151094234}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwp2YXIgXyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9hc3NldHMvNDAxX2ltYWdlcy80MDEuZ2lmIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogJ1BhZ2U0MDEnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBlcnJHaWY6IF8uZGVmYXVsdCArICc/JyArICtuZXcgRGF0ZSgpCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgYmFjazogZnVuY3Rpb24gYmFjaygpIHsKICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5Lm5vR29CYWNrKSB7CiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgICAgcGF0aDogJy8nCiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_", "_interopRequireDefault", "require", "name", "data", "err<PERSON><PERSON>", "Date", "methods", "back", "$route", "query", "noGoBack", "$router", "push", "path", "go"], "sources": ["src/views/error/401.vue"], "sourcesContent": ["<template>\r\n  <div class=\"errPage-container\">\r\n    <el-button icon=\"arrow-left\" class=\"pan-back-btn\" @click=\"back\">\r\n      返回\r\n    </el-button>\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <h1 class=\"text-jumbo text-ginormous\">\r\n          401错误!\r\n        </h1>\r\n        <h2>您没有访问权限！</h2>\r\n        <h6>对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面</h6>\r\n        <ul class=\"list-unstyled\">\r\n          <li class=\"link-type\">\r\n            <router-link to=\"/\">\r\n              回首页\r\n            </router-link>\r\n          </li>\r\n        </ul>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport errGif from '@/assets/401_images/401.gif'\r\n\r\nexport default {\r\n  name: 'Page401',\r\n  data() {\r\n    return {\r\n      errGif: errGif + '?' + +new Date()\r\n    }\r\n  },\r\n  methods: {\r\n    back() {\r\n      if (this.$route.query.noGoBack) {\r\n        this.$router.push({ path: '/' })\r\n      } else {\r\n        this.$router.go(-1)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .errPage-container {\r\n    width: 800px;\r\n    max-width: 100%;\r\n    margin: 100px auto;\r\n    .pan-back-btn {\r\n      background: #008489;\r\n      color: #fff;\r\n      border: none!important;\r\n    }\r\n    .pan-gif {\r\n      margin: 0 auto;\r\n      display: block;\r\n    }\r\n    .pan-img {\r\n      display: block;\r\n      margin: 0 auto;\r\n      width: 100%;\r\n    }\r\n    .text-jumbo {\r\n      font-size: 60px;\r\n      font-weight: 700;\r\n      color: #484848;\r\n    }\r\n    .list-unstyled {\r\n      font-size: 14px;\r\n      li {\r\n        padding-bottom: 5px;\r\n      }\r\n      a {\r\n        color: #008489;\r\n        text-decoration: none;\r\n        &:hover {\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;AA4BA,IAAAA,CAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAA,SAAA,cAAAC,IAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;UAAAC,IAAA;QAAA;MACA;QACA,KAAAF,OAAA,CAAAG,EAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}