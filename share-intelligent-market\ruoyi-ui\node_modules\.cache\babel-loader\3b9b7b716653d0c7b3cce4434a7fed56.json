{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\generator\\js.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\generator\\js.js", "mtime": 1750151094215}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_util", "require", "_index", "_config", "units", "KB", "MB", "GB", "confGlobal", "inheritAttrs", "file", "dialog", "makeUpJs", "conf", "type", "JSON", "parse", "stringify", "dataList", "ruleList", "optionsList", "propsList", "methodList", "mixinMethod", "uploadVarList", "fields", "for<PERSON>ach", "el", "buildAttributes", "script", "buildexport", "join", "buildData", "buildRules", "options", "length", "buildOptions", "dataType", "model", "concat", "vModel", "titleCase", "buildOptionMethod", "props", "buildProps", "action", "tag", "push", "buildBeforeUpload", "buildSubmitUpload", "children", "el2", "list", "minxins", "formBtns", "submitForm", "formRef", "resetForm", "onOpen", "onClose", "close", "handleConfirm", "methods", "Object", "keys", "key", "undefined", "defaultValue", "multiple", "rules", "trigger", "required", "isArray", "message", "placeholder", "label", "regList", "item", "pattern", "eval", "str", "valueKey", "value", "labelKey", "<PERSON><PERSON><PERSON>", "unitNum", "sizeUnit", "rightSizeCode", "acceptCode", "returnList", "fileSize", "accept", "methodName", "data", "selectOptions", "uploadVar", "exportDefault", "formModel", "formRules"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/utils/generator/js.js"], "sourcesContent": ["import { isArray } from 'util'\r\nimport { exportDefault, titleCase } from '@/utils/index'\r\nimport { trigger } from './config'\r\n\r\nconst units = {\r\n  KB: '1024',\r\n  MB: '1024 / 1024',\r\n  GB: '1024 / 1024 / 1024'\r\n}\r\nlet confGlobal\r\nconst inheritAttrs = {\r\n  file: '',\r\n  dialog: 'inheritAttrs: false,'\r\n}\r\n\r\n\r\nexport function makeUpJs(conf, type) {\r\n  confGlobal = conf = JSON.parse(JSON.stringify(conf))\r\n  const dataList = []\r\n  const ruleList = []\r\n  const optionsList = []\r\n  const propsList = []\r\n  const methodList = mixinMethod(type)\r\n  const uploadVarList = []\r\n\r\n  conf.fields.forEach(el => {\r\n    buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\r\n  })\r\n\r\n  const script = buildexport(\r\n    conf,\r\n    type,\r\n    dataList.join('\\n'),\r\n    ruleList.join('\\n'),\r\n    optionsList.join('\\n'),\r\n    uploadVarList.join('\\n'),\r\n    propsList.join('\\n'),\r\n    methodList.join('\\n')\r\n  )\r\n  confGlobal = null\r\n  return script\r\n}\r\n\r\nfunction buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList) {\r\n  buildData(el, dataList)\r\n  buildRules(el, ruleList)\r\n\r\n  if (el.options && el.options.length) {\r\n    buildOptions(el, optionsList)\r\n    if (el.dataType === 'dynamic') {\r\n      const model = `${el.vModel}Options`\r\n      const options = titleCase(model)\r\n      buildOptionMethod(`get${options}`, model, methodList)\r\n    }\r\n  }\r\n\r\n  if (el.props && el.props.props) {\r\n    buildProps(el, propsList)\r\n  }\r\n\r\n  if (el.action && el.tag === 'el-upload') {\r\n    uploadVarList.push(\r\n      `${el.vModel}Action: '${el.action}',\r\n      ${el.vModel}fileList: [],`\r\n    )\r\n    methodList.push(buildBeforeUpload(el))\r\n    if (!el['auto-upload']) {\r\n      methodList.push(buildSubmitUpload(el))\r\n    }\r\n  }\r\n\r\n  if (el.children) {\r\n    el.children.forEach(el2 => {\r\n      buildAttributes(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\r\n    })\r\n  }\r\n}\r\n\r\nfunction mixinMethod(type) {\r\n  const list = []; const\r\n    minxins = {\r\n      file: confGlobal.formBtns ? {\r\n        submitForm: `submitForm() {\r\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\r\n          if(!valid) return\r\n          // TODO 提交表单\r\n        })\r\n      },`,\r\n        resetForm: `resetForm() {\r\n        this.$refs['${confGlobal.formRef}'].resetFields()\r\n      },`\r\n      } : null,\r\n      dialog: {\r\n        onOpen: 'onOpen() {},',\r\n        onClose: `onClose() {\r\n        this.$refs['${confGlobal.formRef}'].resetFields()\r\n      },`,\r\n        close: `close() {\r\n        this.$emit('update:visible', false)\r\n      },`,\r\n        handleConfirm: `handleConfirm() {\r\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\r\n          if(!valid) return\r\n          this.close()\r\n        })\r\n      },`\r\n      }\r\n    }\r\n\r\n  const methods = minxins[type]\r\n  if (methods) {\r\n    Object.keys(methods).forEach(key => {\r\n      list.push(methods[key])\r\n    })\r\n  }\r\n\r\n  return list\r\n}\r\n\r\nfunction buildData(conf, dataList) {\r\n  if (conf.vModel === undefined) return\r\n  let defaultValue\r\n  if (typeof (conf.defaultValue) === 'string' && !conf.multiple) {\r\n    defaultValue = `'${conf.defaultValue}'`\r\n  } else {\r\n    defaultValue = `${JSON.stringify(conf.defaultValue)}`\r\n  }\r\n  dataList.push(`${conf.vModel}: ${defaultValue},`)\r\n}\r\n\r\nfunction buildRules(conf, ruleList) {\r\n  if (conf.vModel === undefined) return\r\n  const rules = []\r\n  if (trigger[conf.tag]) {\r\n    if (conf.required) {\r\n      const type = isArray(conf.defaultValue) ? 'type: \\'array\\',' : ''\r\n      let message = isArray(conf.defaultValue) ? `请至少选择一个${conf.vModel}` : conf.placeholder\r\n      if (message === undefined) message = `${conf.label}不能为空`\r\n      rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)\r\n    }\r\n    if (conf.regList && isArray(conf.regList)) {\r\n      conf.regList.forEach(item => {\r\n        if (item.pattern) {\r\n          rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)\r\n        }\r\n      })\r\n    }\r\n    ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)\r\n  }\r\n}\r\n\r\nfunction buildOptions(conf, optionsList) {\r\n  if (conf.vModel === undefined) return\r\n  if (conf.dataType === 'dynamic') { conf.options = [] }\r\n  const str = `${conf.vModel}Options: ${JSON.stringify(conf.options)},`\r\n  optionsList.push(str)\r\n}\r\n\r\nfunction buildProps(conf, propsList) {\r\n  if (conf.dataType === 'dynamic') {\r\n    conf.valueKey !== 'value' && (conf.props.props.value = conf.valueKey)\r\n    conf.labelKey !== 'label' && (conf.props.props.label = conf.labelKey)\r\n    conf.childrenKey !== 'children' && (conf.props.props.children = conf.childrenKey)\r\n  }\r\n  const str = `${conf.vModel}Props: ${JSON.stringify(conf.props.props)},`\r\n  propsList.push(str)\r\n}\r\n\r\nfunction buildBeforeUpload(conf) {\r\n  const unitNum = units[conf.sizeUnit]; let rightSizeCode = ''; let acceptCode = ''; const\r\n    returnList = []\r\n  if (conf.fileSize) {\r\n    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${conf.fileSize}\r\n    if(!isRightSize){\r\n      this.$message.error('文件大小超过 ${conf.fileSize}${conf.sizeUnit}')\r\n    }`\r\n    returnList.push('isRightSize')\r\n  }\r\n  if (conf.accept) {\r\n    acceptCode = `let isAccept = new RegExp('${conf.accept}').test(file.type)\r\n    if(!isAccept){\r\n      this.$message.error('应该选择${conf.accept}类型的文件')\r\n    }`\r\n    returnList.push('isAccept')\r\n  }\r\n  const str = `${conf.vModel}BeforeUpload(file) {\r\n    ${rightSizeCode}\r\n    ${acceptCode}\r\n    return ${returnList.join('&&')}\r\n  },`\r\n  return returnList.length ? str : ''\r\n}\r\n\r\nfunction buildSubmitUpload(conf) {\r\n  const str = `submitUpload() {\r\n    this.$refs['${conf.vModel}'].submit()\r\n  },`\r\n  return str\r\n}\r\n\r\nfunction buildOptionMethod(methodName, model, methodList) {\r\n  const str = `${methodName}() {\r\n    // TODO 发起请求获取数据\r\n    this.${model}\r\n  },`\r\n  methodList.push(str)\r\n}\r\n\r\nfunction buildexport(conf, type, data, rules, selectOptions, uploadVar, props, methods) {\r\n  const str = `${exportDefault}{\r\n  ${inheritAttrs[type]}\r\n  components: {},\r\n  props: [],\r\n  data () {\r\n    return {\r\n      ${conf.formModel}: {\r\n        ${data}\r\n      },\r\n      ${conf.formRules}: {\r\n        ${rules}\r\n      },\r\n      ${uploadVar}\r\n      ${selectOptions}\r\n      ${props}\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created () {},\r\n  mounted () {},\r\n  methods: {\r\n    ${methods}\r\n  }\r\n}`\r\n  return str\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAEA,IAAMG,KAAK,GAAG;EACZC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,aAAa;EACjBC,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,UAAU;AACd,IAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE;AACV,CAAC;AAGM,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACnCN,UAAU,GAAGK,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC,CAAC;EACpD,IAAMK,QAAQ,GAAG,EAAE;EACnB,IAAMC,QAAQ,GAAG,EAAE;EACnB,IAAMC,WAAW,GAAG,EAAE;EACtB,IAAMC,SAAS,GAAG,EAAE;EACpB,IAAMC,UAAU,GAAGC,WAAW,CAACT,IAAI,CAAC;EACpC,IAAMU,aAAa,GAAG,EAAE;EAExBX,IAAI,CAACY,MAAM,CAACC,OAAO,CAAC,UAAAC,EAAE,EAAI;IACxBC,eAAe,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;EAC5F,CAAC,CAAC;EAEF,IAAMK,MAAM,GAAGC,WAAW,CACxBjB,IAAI,EACJC,IAAI,EACJI,QAAQ,CAACa,IAAI,CAAC,IAAI,CAAC,EACnBZ,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAC,EACnBX,WAAW,CAACW,IAAI,CAAC,IAAI,CAAC,EACtBP,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,EACxBV,SAAS,CAACU,IAAI,CAAC,IAAI,CAAC,EACpBT,UAAU,CAACS,IAAI,CAAC,IAAI,CACtB,CAAC;EACDvB,UAAU,GAAG,IAAI;EACjB,OAAOqB,MAAM;AACf;AAEA,SAASD,eAAeA,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,EAAE;EAClGQ,SAAS,CAACL,EAAE,EAAET,QAAQ,CAAC;EACvBe,UAAU,CAACN,EAAE,EAAER,QAAQ,CAAC;EAExB,IAAIQ,EAAE,CAACO,OAAO,IAAIP,EAAE,CAACO,OAAO,CAACC,MAAM,EAAE;IACnCC,YAAY,CAACT,EAAE,EAAEP,WAAW,CAAC;IAC7B,IAAIO,EAAE,CAACU,QAAQ,KAAK,SAAS,EAAE;MAC7B,IAAMC,KAAK,MAAAC,MAAA,CAAMZ,EAAE,CAACa,MAAM,YAAS;MACnC,IAAMN,OAAO,GAAG,IAAAO,gBAAS,EAACH,KAAK,CAAC;MAChCI,iBAAiB,OAAAH,MAAA,CAAOL,OAAO,GAAII,KAAK,EAAEhB,UAAU,CAAC;IACvD;EACF;EAEA,IAAIK,EAAE,CAACgB,KAAK,IAAIhB,EAAE,CAACgB,KAAK,CAACA,KAAK,EAAE;IAC9BC,UAAU,CAACjB,EAAE,EAAEN,SAAS,CAAC;EAC3B;EAEA,IAAIM,EAAE,CAACkB,MAAM,IAAIlB,EAAE,CAACmB,GAAG,KAAK,WAAW,EAAE;IACvCtB,aAAa,CAACuB,IAAI,IAAAR,MAAA,CACbZ,EAAE,CAACa,MAAM,eAAAD,MAAA,CAAYZ,EAAE,CAACkB,MAAM,gBAAAN,MAAA,CAC/BZ,EAAE,CAACa,MAAM,kBACb,CAAC;IACDlB,UAAU,CAACyB,IAAI,CAACC,iBAAiB,CAACrB,EAAE,CAAC,CAAC;IACtC,IAAI,CAACA,EAAE,CAAC,aAAa,CAAC,EAAE;MACtBL,UAAU,CAACyB,IAAI,CAACE,iBAAiB,CAACtB,EAAE,CAAC,CAAC;IACxC;EACF;EAEA,IAAIA,EAAE,CAACuB,QAAQ,EAAE;IACfvB,EAAE,CAACuB,QAAQ,CAACxB,OAAO,CAAC,UAAAyB,GAAG,EAAI;MACzBvB,eAAe,CAACuB,GAAG,EAAEjC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;IAC7F,CAAC,CAAC;EACJ;AACF;AAEA,SAASD,WAAWA,CAACT,IAAI,EAAE;EACzB,IAAMsC,IAAI,GAAG,EAAE;EAAE,IACfC,OAAO,GAAG;IACR3C,IAAI,EAAEF,UAAU,CAAC8C,QAAQ,GAAG;MAC1BC,UAAU,yCAAAhB,MAAA,CACI/B,UAAU,CAACgD,OAAO,0HAI/B;MACDC,SAAS,wCAAAlB,MAAA,CACK/B,UAAU,CAACgD,OAAO;IAElC,CAAC,GAAG,IAAI;IACR7C,MAAM,EAAE;MACN+C,MAAM,EAAE,cAAc;MACtBC,OAAO,sCAAApB,MAAA,CACO/B,UAAU,CAACgD,OAAO,+BAC/B;MACDI,KAAK,oEAEJ;MACDC,aAAa,4CAAAtB,MAAA,CACC/B,UAAU,CAACgD,OAAO;IAKlC;EACF,CAAC;EAEH,IAAMM,OAAO,GAAGT,OAAO,CAACvC,IAAI,CAAC;EAC7B,IAAIgD,OAAO,EAAE;IACXC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACpC,OAAO,CAAC,UAAAuC,GAAG,EAAI;MAClCb,IAAI,CAACL,IAAI,CAACe,OAAO,CAACG,GAAG,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ;EAEA,OAAOb,IAAI;AACb;AAEA,SAASpB,SAASA,CAACnB,IAAI,EAAEK,QAAQ,EAAE;EACjC,IAAIL,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAIC,YAAY;EAChB,IAAI,OAAQtD,IAAI,CAACsD,YAAa,KAAK,QAAQ,IAAI,CAACtD,IAAI,CAACuD,QAAQ,EAAE;IAC7DD,YAAY,OAAA5B,MAAA,CAAO1B,IAAI,CAACsD,YAAY,MAAG;EACzC,CAAC,MAAM;IACLA,YAAY,MAAA5B,MAAA,CAAMxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACsD,YAAY,CAAC,CAAE;EACvD;EACAjD,QAAQ,CAAC6B,IAAI,IAAAR,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,QAAAD,MAAA,CAAK4B,YAAY,MAAG,CAAC;AACnD;AAEA,SAASlC,UAAUA,CAACpB,IAAI,EAAEM,QAAQ,EAAE;EAClC,IAAIN,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAMG,KAAK,GAAG,EAAE;EAChB,IAAIC,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,EAAE;IACrB,IAAIjC,IAAI,CAAC0D,QAAQ,EAAE;MACjB,IAAMzD,IAAI,GAAG,IAAA0D,aAAO,EAAC3D,IAAI,CAACsD,YAAY,CAAC,GAAG,kBAAkB,GAAG,EAAE;MACjE,IAAIM,OAAO,GAAG,IAAAD,aAAO,EAAC3D,IAAI,CAACsD,YAAY,CAAC,gDAAA5B,MAAA,CAAa1B,IAAI,CAAC2B,MAAM,IAAK3B,IAAI,CAAC6D,WAAW;MACrF,IAAID,OAAO,KAAKP,SAAS,EAAEO,OAAO,MAAAlC,MAAA,CAAM1B,IAAI,CAAC8D,KAAK,6BAAM;MACxDN,KAAK,CAACtB,IAAI,sBAAAR,MAAA,CAAsBzB,IAAI,iBAAAyB,MAAA,CAAckC,OAAO,mBAAAlC,MAAA,CAAgB+B,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,QAAK,CAAC;IAClG;IACA,IAAIjC,IAAI,CAAC+D,OAAO,IAAI,IAAAJ,aAAO,EAAC3D,IAAI,CAAC+D,OAAO,CAAC,EAAE;MACzC/D,IAAI,CAAC+D,OAAO,CAAClD,OAAO,CAAC,UAAAmD,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACC,OAAO,EAAE;UAChBT,KAAK,CAACtB,IAAI,eAAAR,MAAA,CAAewC,IAAI,CAACF,IAAI,CAACC,OAAO,CAAC,kBAAAvC,MAAA,CAAesC,IAAI,CAACJ,OAAO,mBAAAlC,MAAA,CAAgB+B,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,QAAK,CAAC;QAC/G;MACF,CAAC,CAAC;IACJ;IACA3B,QAAQ,CAAC4B,IAAI,IAAAR,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,SAAAD,MAAA,CAAM8B,KAAK,CAACtC,IAAI,CAAC,GAAG,CAAC,OAAI,CAAC;EACxD;AACF;AAEA,SAASK,YAAYA,CAACvB,IAAI,EAAEO,WAAW,EAAE;EACvC,IAAIP,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAIrD,IAAI,CAACwB,QAAQ,KAAK,SAAS,EAAE;IAAExB,IAAI,CAACqB,OAAO,GAAG,EAAE;EAAC;EACrD,IAAM8C,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,eAAAD,MAAA,CAAYxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACqB,OAAO,CAAC,MAAG;EACrEd,WAAW,CAAC2B,IAAI,CAACiC,GAAG,CAAC;AACvB;AAEA,SAASpC,UAAUA,CAAC/B,IAAI,EAAEQ,SAAS,EAAE;EACnC,IAAIR,IAAI,CAACwB,QAAQ,KAAK,SAAS,EAAE;IAC/BxB,IAAI,CAACoE,QAAQ,KAAK,OAAO,KAAKpE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACuC,KAAK,GAAGrE,IAAI,CAACoE,QAAQ,CAAC;IACrEpE,IAAI,CAACsE,QAAQ,KAAK,OAAO,KAAKtE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACgC,KAAK,GAAG9D,IAAI,CAACsE,QAAQ,CAAC;IACrEtE,IAAI,CAACuE,WAAW,KAAK,UAAU,KAAKvE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACO,QAAQ,GAAGrC,IAAI,CAACuE,WAAW,CAAC;EACnF;EACA,IAAMJ,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,aAAAD,MAAA,CAAUxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAAC,MAAG;EACvEtB,SAAS,CAAC0B,IAAI,CAACiC,GAAG,CAAC;AACrB;AAEA,SAAShC,iBAAiBA,CAACnC,IAAI,EAAE;EAC/B,IAAMwE,OAAO,GAAGjF,KAAK,CAACS,IAAI,CAACyE,QAAQ,CAAC;EAAE,IAAIC,aAAa,GAAG,EAAE;EAAE,IAAIC,UAAU,GAAG,EAAE;EAAE,IACjFC,UAAU,GAAG,EAAE;EACjB,IAAI5E,IAAI,CAAC6E,QAAQ,EAAE;IACjBH,aAAa,oCAAAhD,MAAA,CAAoC8C,OAAO,SAAA9C,MAAA,CAAM1B,IAAI,CAAC6E,QAAQ,+FAAAnD,MAAA,CAE3C1B,IAAI,CAAC6E,QAAQ,EAAAnD,MAAA,CAAG1B,IAAI,CAACyE,QAAQ,cAC3D;IACFG,UAAU,CAAC1C,IAAI,CAAC,aAAa,CAAC;EAChC;EACA,IAAIlC,IAAI,CAAC8E,MAAM,EAAE;IACfH,UAAU,iCAAAjD,MAAA,CAAiC1B,IAAI,CAAC8E,MAAM,iGAAApD,MAAA,CAEzB1B,IAAI,CAAC8E,MAAM,4CACtC;IACFF,UAAU,CAAC1C,IAAI,CAAC,UAAU,CAAC;EAC7B;EACA,IAAMiC,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,gCAAAD,MAAA,CACtBgD,aAAa,YAAAhD,MAAA,CACbiD,UAAU,mBAAAjD,MAAA,CACHkD,UAAU,CAAC1D,IAAI,CAAC,IAAI,CAAC,WAC7B;EACH,OAAO0D,UAAU,CAACtD,MAAM,GAAG6C,GAAG,GAAG,EAAE;AACrC;AAEA,SAAS/B,iBAAiBA,CAACpC,IAAI,EAAE;EAC/B,IAAMmE,GAAG,wCAAAzC,MAAA,CACO1B,IAAI,CAAC2B,MAAM,sBACxB;EACH,OAAOwC,GAAG;AACZ;AAEA,SAAStC,iBAAiBA,CAACkD,UAAU,EAAEtD,KAAK,EAAEhB,UAAU,EAAE;EACxD,IAAM0D,GAAG,MAAAzC,MAAA,CAAMqD,UAAU,mFAAArD,MAAA,CAEhBD,KAAK,WACX;EACHhB,UAAU,CAACyB,IAAI,CAACiC,GAAG,CAAC;AACtB;AAEA,SAASlD,WAAWA,CAACjB,IAAI,EAAEC,IAAI,EAAE+E,IAAI,EAAExB,KAAK,EAAEyB,aAAa,EAAEC,SAAS,EAAEpD,KAAK,EAAEmB,OAAO,EAAE;EACtF,IAAMkB,GAAG,MAAAzC,MAAA,CAAMyD,oBAAa,WAAAzD,MAAA,CAC1B9B,YAAY,CAACK,IAAI,CAAC,0EAAAyB,MAAA,CAKd1B,IAAI,CAACoF,SAAS,mBAAA1D,MAAA,CACZsD,IAAI,wBAAAtD,MAAA,CAEN1B,IAAI,CAACqF,SAAS,mBAAA3D,MAAA,CACZ8B,KAAK,wBAAA9B,MAAA,CAEPwD,SAAS,cAAAxD,MAAA,CACTuD,aAAa,cAAAvD,MAAA,CACbI,KAAK,0GAAAJ,MAAA,CAQPuB,OAAO,aAEX;EACA,OAAOkB,GAAG;AACZ", "ignoreList": []}]}