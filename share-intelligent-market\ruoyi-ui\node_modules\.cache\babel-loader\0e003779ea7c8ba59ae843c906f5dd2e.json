{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\feedback\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\feedback\\list.vue", "mtime": 1750151094235}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnZhciBfZGVmaW5lUHJvcGVydHkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2RlZmluZVByb3BlcnR5LmpzIikpOwp2YXIgX2xpc3QgPSByZXF1aXJlKCJAL2FwaS9mZWVkYmFjay9saXN0LmpzIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiZmVlZGJhY2siLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoewogICAgICB0aXRsZTogJycsCiAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgdmFsdWU6ICIwIiwKICAgICAgICBsYWJlbDogIuacquWkhOeQhiIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAiMSIsCiAgICAgICAgbGFiZWw6ICLlt7LlpITnkIYiCiAgICAgIH1dLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgc2hvdzogZmFsc2UsCiAgICAgIGZvcm06IHt9CiAgICB9LCAibG9hZGluZyIsIHRydWUpLCAiaWRzIiwgW10pLCAic2luZ2xlIiwgdHJ1ZSksICJtdWx0aXBsZSIsIHRydWUpLCAic2hvd1NlYXJjaCIsIHRydWUpLCAidG90YWwiLCAwKSwgImxpc3QiLCBbXSksICJxdWVyeVBhcmFtcyIsIHsKICAgICAgcGFnZU51bTogMSwKICAgICAgcGFnZVNpemU6IDEwCiAgICB9KTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi9oYW5kbGVVcGRhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdmFyIGluZm9ySWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIC8vIHRoaXMuZm9ybSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93KSk7CiAgICAgIC8vIHRoaXMuZm9ybS5zdGF0dXMgPSBOdW1iZXIodGhpcy5mb3JtLnN0YXR1cykKCiAgICAgIC8vIGRlbGV0ZSB0aGlzLmZvcm0uc29ydHMKICAgICAgLy8gdGhpcy5mb3JtID0gT2JqZWN0LmFzc2lnbih7fSx0aGlzLmZvcm0pCiAgICAgIC8vIHRoaXMudGl0bGUgPSAi57yW6L6RIjsKICAgICAgLy8gdGhpcy5zaG93ID0gdHJ1ZTsKCiAgICAgICgwLCBfbGlzdC5nZXREYXRhKShpbmZvcklkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLmVkaXQocmVzcG9uc2UuZGF0YSk7CiAgICAgIH0pOwogICAgfSwKICAgIGVkaXQ6IGZ1bmN0aW9uIGVkaXQoZGF0YSkgewogICAgICB0aGlzLnRpdGxlID0gIuivpuaDhSI7CiAgICAgIHRoaXMuc2hvdyA9IHRydWU7CiAgICAgIHRoaXMuZm9ybSA9IGRhdGE7CiAgICB9LAogICAgLy8g5L+u5pS554q25oCBCiAgICBzZXRTdGF0dXM6IGZ1bmN0aW9uIHNldFN0YXR1cyhyb3cpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgICgwLCBfbGlzdC5zZXRTdGF0dXMpKHsKICAgICAgICBvcGlkOiByb3cuaWQsCiAgICAgICAgc3RhdHVzOiAxCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5tc2csCiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIgogICAgICAgICAgfSk7CiAgICAgICAgICBfdGhpczIuZ2V0TGlzdCgpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivouWFrOWRiuWIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAoMCwgX2xpc3QubGlzdERhdGEpKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMzLmxpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzMy50b3RhbCA9IHJlc3BvbnNlLmNvdW50OwogICAgICAgIF90aGlzMy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGlzcmVhZDogIiIKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uaWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHZhciBpbmZvcklkcyA9IHJvdy5pZCB8fCB0aGlzLmlkcy5qb2luKCIsIik7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOe8luWPt+S4uiInICsgaW5mb3JJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX2xpc3QuZGVsRGF0YSkoaW5mb3JJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNC4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_list", "require", "name", "data", "_defineProperty2", "default", "title", "options", "value", "label", "loading", "show", "form", "pageNum", "pageSize", "created", "getList", "methods", "handleUpdate", "row", "_this", "inforId", "id", "ids", "getData", "then", "response", "edit", "setStatus", "_this2", "opid", "status", "code", "$message", "message", "msg", "type", "_this3", "listData", "queryParams", "list", "total", "count", "handleQuery", "reset<PERSON><PERSON>y", "isread", "resetForm", "handleSelectionChange", "selection", "map", "item", "single", "length", "multiple", "handleDelete", "_this4", "inforIds", "join", "$modal", "confirm", "delData", "msgSuccess", "catch"], "sources": ["src/views/feedback/list.vue"], "sourcesContent": ["// 消息管理\r\n<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n        >\r\n            <el-form-item label=\"状态\" prop=\"linkphone\">\r\n                <el-select\r\n                    v-model=\"queryParams.status\"\r\n                    clearable\r\n                    placeholder=\"请选择\"\r\n                >\r\n                    <el-option\r\n                        v-for=\"item in options\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.label\"\r\n                        :value=\"item.value\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"手机号\" prop=\"linkphone\">\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.linkphone\"\r\n                    placeholder=\"请输入手机号\"\r\n                    :maxlength=\"60\"\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.linkman\"\r\n                    placeholder=\"请输入联系人\"\r\n                    :maxlength=\"60\"\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    >删除\r\n                </el-button>\r\n            </el-col>\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"list\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                    <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"意见反馈\"\r\n                align=\"center\"\r\n                prop=\"content\"\r\n                :show-overflow-tooltip=\"true\"\r\n            />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"linkman\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"linkphone\" />\r\n            <el-table-column\r\n                label=\"时间\"\r\n                align=\"center\"\r\n                prop=\"create_time\"\r\n                sortable\r\n            />\r\n            <el-table-column\r\n                label=\"状态\"\r\n                align=\"center\"\r\n                prop=\"create_by\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <!-- 开启 -->\r\n                    <!-- <el-switch v-model=\"form.delivery\"></el-switch> -->\r\n                    <el-tag type=\"success\" v-if=\"scope.row.status == 1\"\r\n                        >已处理</el-tag\r\n                    >\r\n                    <el-tag type=\"danger\" v-else>未处理</el-tag>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                fixed=\"right\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        v-if=\"scope.row.status != 1\"\r\n                        style=\"color: #85ce61\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"setStatus(scope.row, 1)\"\r\n                        >处理</el-button\r\n                    >\r\n                    <!-- <el-button\r\n                        style=\"color: #85ce61\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"setStatus(scope.row, 1)\"\r\n                        >启用</el-button\r\n                    >\r\n                    <el-button\r\n                        style=\"color: #ebb563\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"setStatus(scope.row, 0)\"\r\n                        >禁用</el-button\r\n                    > -->\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        >查看详情</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加弹窗 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"show\"\r\n            width=\"70%\"\r\n            :before-close=\"() => (show = false)\"\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" >\r\n                <el-form-item label=\"意见反馈\" prop=\"content\">\r\n                    <el-input\r\n                        type=\"textarea\"\r\n                        :rows=\"5\"\r\n                         :disabled=\"true\"\r\n                        clearable\r\n                        v-model=\"form.content\"\r\n                        placeholder=\"请输入标题\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\" prop=\"status\">\r\n                    <el-tag type=\"success\" v-if=\"form.status == 1\"\r\n                        >已处理</el-tag\r\n                    >\r\n                    <el-tag type=\"danger\" v-else>未处理</el-tag>\r\n                    <!-- <el-switch\r\n                        v-model=\"form.status\"\r\n                        :active-value=\"1\"\r\n                        :inactive-value=\"0\"\r\n                    >\r\n                    </el-switch> -->\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\">\r\n                    <el-input\r\n                         :disabled=\"true\"\r\n\r\n                        clearable\r\n                        v-model=\"form.linkman\"\r\n                        placeholder=\"请输入联系人\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"联系电话\">\r\n                    <el-input\r\n                         :disabled=\"true\"\r\n\r\n                        clearable\r\n                        v-model=\"form.linkphone\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"show = false\">取 消</el-button>\r\n              \r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { listData, getData, setStatus,delData } from \"@/api/feedback/list.js\";\r\nexport default {\r\n    name: \"feedback\",\r\n    data() {\r\n        return {\r\n            title:'',\r\n            options: [\r\n                {\r\n                    value: \"0\",\r\n                    label: \"未处理\",\r\n                },\r\n                {\r\n                    value: \"1\",\r\n                    label: \"已处理\",\r\n                },\r\n            ],\r\n            loading: false,\r\n            show: false,\r\n            form: {},\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 表格数据\r\n            list: [],\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            const inforId = row.id || this.ids;\r\n            // this.form = JSON.parse(JSON.stringify(row));\r\n            // this.form.status = Number(this.form.status)\r\n\r\n            // delete this.form.sorts\r\n            // this.form = Object.assign({},this.form)\r\n            // this.title = \"编辑\";\r\n            // this.show = true;\r\n\r\n            getData(inforId).then((response) => {\r\n                this.edit(response.data);\r\n            });\r\n        },\r\n        edit(data) {\r\n            this.title = \"详情\";\r\n            this.show = true;\r\n            this.form = data;\r\n        },\r\n        // 修改状态\r\n        setStatus(row) {\r\n            setStatus({\r\n                opid: row.id,\r\n                status: 1,\r\n            }).then((response) => {\r\n                if (response.code == 200) {\r\n                    this.$message({\r\n                        message: response.msg,\r\n                        type: \"success\",\r\n                    });\r\n                    this.getList();\r\n                }\r\n            });\r\n        },\r\n        /** 查询公告列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listData(this.queryParams).then((response) => {\r\n                this.list = response.data;\r\n                this.total = response.count;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.queryParams = {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                isread: \"\",\r\n            };\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length != 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const inforIds = row.id || this.ids.join(\",\");\r\n            this.$modal\r\n                .confirm('是否确认删除编号为\"' + inforIds + '\"的数据项？')\r\n                .then(function () {\r\n                    return delData(inforIds);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {\r\n\r\n                });\r\n        },\r\n    },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;AAyOA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;MACAC,KAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,OAAA;MACAC,IAAA;MACAC,IAAA;IAAA,cAEA,cAEA,eAEA,mBAEA,qBAEA,gBAEA,YAEA,oBAEA;MACAC,OAAA;MACAC,QAAA;IACA;EAEA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,OAAA,GAAAF,GAAA,CAAAG,EAAA,SAAAC,GAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA,IAAAC,aAAA,EAAAH,OAAA,EAAAI,IAAA,WAAAC,QAAA;QACAN,KAAA,CAAAO,IAAA,CAAAD,QAAA,CAAAvB,IAAA;MACA;IACA;IACAwB,IAAA,WAAAA,KAAAxB,IAAA;MACA,KAAAG,KAAA;MACA,KAAAK,IAAA;MACA,KAAAC,IAAA,GAAAT,IAAA;IACA;IACA;IACAyB,SAAA,WAAAA,UAAAT,GAAA;MAAA,IAAAU,MAAA;MACA,IAAAD,eAAA;QACAE,IAAA,EAAAX,GAAA,CAAAG,EAAA;QACAS,MAAA;MACA,GAAAN,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAM,IAAA;UACAH,MAAA,CAAAI,QAAA;YACAC,OAAA,EAAAR,QAAA,CAAAS,GAAA;YACAC,IAAA;UACA;UACAP,MAAA,CAAAb,OAAA;QACA;MACA;IACA;IACA,aACAA,OAAA,WAAAA,QAAA;MAAA,IAAAqB,MAAA;MACA,KAAA3B,OAAA;MACA,IAAA4B,cAAA,OAAAC,WAAA,EAAAd,IAAA,WAAAC,QAAA;QACAW,MAAA,CAAAG,IAAA,GAAAd,QAAA,CAAAvB,IAAA;QACAkC,MAAA,CAAAI,KAAA,GAAAf,QAAA,CAAAgB,KAAA;QACAL,MAAA,CAAA3B,OAAA;MACA;IACA;IACA,aACAiC,WAAA,WAAAA,YAAA;MACA,KAAAJ,WAAA,CAAA1B,OAAA;MACA,KAAAG,OAAA;IACA;IACA,aACA4B,UAAA,WAAAA,WAAA;MACA,KAAAL,WAAA;QACA1B,OAAA;QACAC,QAAA;QACA+B,MAAA;MACA;MACA,KAAAC,SAAA;MACA,KAAAH,WAAA;IACA;IACA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzB,GAAA,GAAAyB,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA5B,EAAA;MAAA;MACA,KAAA6B,MAAA,GAAAH,SAAA,CAAAI,MAAA;MACA,KAAAC,QAAA,IAAAL,SAAA,CAAAI,MAAA;IACA;IACA,aACAE,YAAA,WAAAA,aAAAnC,GAAA;MAAA,IAAAoC,MAAA;MACA,IAAAC,QAAA,GAAArC,GAAA,CAAAG,EAAA,SAAAC,GAAA,CAAAkC,IAAA;MACA,KAAAC,MAAA,CACAC,OAAA,gBAAAH,QAAA,aACA/B,IAAA;QACA,WAAAmC,aAAA,EAAAJ,QAAA;MACA,GACA/B,IAAA;QACA8B,MAAA,CAAAvC,OAAA;QACAuC,MAAA,CAAAG,MAAA,CAAAG,UAAA;MACA,GACAC,KAAA,cAEA;IACA;EACA;AACA", "ignoreList": []}]}