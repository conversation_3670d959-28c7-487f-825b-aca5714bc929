{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\monitor\\job\\log.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\monitor\\job\\log.vue", "mtime": 1750151094245}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_job", "require", "_jobLog", "name", "dicts", "data", "loading", "ids", "multiple", "showSearch", "total", "jobLogList", "open", "date<PERSON><PERSON><PERSON>", "form", "queryParams", "pageNum", "pageSize", "job<PERSON>ame", "undefined", "jobGroup", "status", "created", "_this", "jobId", "$route", "query", "get<PERSON>ob", "then", "response", "getList", "methods", "_this2", "listJobLog", "addDateRange", "rows", "handleClose", "obj", "path", "$tab", "closeOpenPage", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "jobLogId", "length", "handleView", "row", "handleDelete", "_this3", "jobLogIds", "$modal", "confirm", "delJobLog", "msgSuccess", "catch", "handleClean", "_this4", "cleanJobLog", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/monitor/job/log.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"任务名称\" prop=\"jobName\">\r\n        <el-input\r\n          v-model=\"queryParams.jobName\"\r\n          placeholder=\"请输入任务名称\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"任务组名\" prop=\"jobGroup\">\r\n        <el-select\r\n          v-model=\"queryParams.jobGroup\"\r\n          placeholder=\"请选择任务组名\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_job_group\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"执行状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"请选择执行状态\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_common_status\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"执行时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['monitor:job:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          @click=\"handleClean\"\r\n          v-hasPermi=\"['monitor:job:remove']\"\r\n        >清空</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['monitor:job:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          @click=\"handleClose\"\r\n        >关闭</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"jobLogList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"日志编号\" width=\"80\" align=\"center\" prop=\"jobLogId\" />\r\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"jobName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"任务组名\" align=\"center\" prop=\"jobGroup\" :show-overflow-tooltip=\"true\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_job_group\" :value=\"scope.row.jobGroup\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"调用目标字符串\" align=\"center\" prop=\"invokeTarget\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"日志信息\" align=\"center\" prop=\"jobMessage\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"执行状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_common_status\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"执行时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['monitor:job:query']\"\r\n          >详细</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 调度日志详细 -->\r\n    <el-dialog title=\"调度日志详细\" :visible.sync=\"open\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"日志序号：\">{{ form.jobLogId }}</el-form-item>\r\n            <el-form-item label=\"任务名称：\">{{ form.jobName }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"任务分组：\">{{ form.jobGroup }}</el-form-item>\r\n            <el-form-item label=\"执行时间：\">{{ form.createTime }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"调用方法：\">{{ form.invokeTarget }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"日志信息：\">{{ form.jobMessage }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"执行状态：\">\r\n              <div v-if=\"form.status == 0\">正常</div>\r\n              <div v-else-if=\"form.status == 1\">失败</div>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"异常信息：\" v-if=\"form.status == 1\">{{ form.exceptionInfo }}</el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getJob} from \"@/api/monitor/job\";\r\nimport { listJobLog, delJobLog, cleanJobLog } from \"@/api/monitor/jobLog\";\r\n\r\nexport default {\r\n  name: \"JobLog\",\r\n  dicts: ['sys_common_status', 'sys_job_group'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 调度日志表格数据\r\n      jobLogList: [],\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        jobName: undefined,\r\n        jobGroup: undefined,\r\n        status: undefined\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    const jobId = this.$route.query.jobId;\r\n    if (jobId !== undefined && jobId != 0) {\r\n      getJob(jobId).then(response => {\r\n        this.queryParams.jobName = response.data.jobName;\r\n        this.queryParams.jobGroup = response.data.jobGroup;\r\n        this.getList();\r\n      });\r\n    } else {\r\n      this.getList();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询调度日志列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listJobLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.jobLogList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    // 返回按钮\r\n    handleClose() {\r\n      const obj = { path: \"/monitor/job\" };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.jobLogId);\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = row;\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const jobLogIds = this.ids;\r\n      this.$modal.confirm('是否确认删除调度日志编号为\"' + jobLogIds + '\"的数据项？').then(function() {\r\n        return delJobLog(jobLogIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$modal.confirm('是否确认清空所有调度日志数据项？').then(function() {\r\n        return cleanJobLog();\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"清空成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('schedule/job/log/export', {\r\n        ...this.queryParams\r\n      }, `log_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>"], "mappings": ";;;;;;;;;;;;AAsLA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,IAAA;MACA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,KAAA;IACA,IAAAA,KAAA,KAAAL,SAAA,IAAAK,KAAA;MACA,IAAAG,WAAA,EAAAH,KAAA,EAAAI,IAAA,WAAAC,QAAA;QACAN,KAAA,CAAAR,WAAA,CAAAG,OAAA,GAAAW,QAAA,CAAAxB,IAAA,CAAAa,OAAA;QACAK,KAAA,CAAAR,WAAA,CAAAK,QAAA,GAAAS,QAAA,CAAAxB,IAAA,CAAAe,QAAA;QACAG,KAAA,CAAAO,OAAA;MACA;IACA;MACA,KAAAA,OAAA;IACA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,kBAAA,OAAAC,YAAA,MAAAnB,WAAA,OAAAF,SAAA,GAAAe,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAArB,UAAA,GAAAkB,QAAA,CAAAM,IAAA;QACAH,MAAA,CAAAtB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAsB,MAAA,CAAA1B,OAAA;MACA,CACA;IACA;IACA;IACA8B,WAAA,WAAAA,YAAA;MACA,IAAAC,GAAA;QAAAC,IAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAH,GAAA;IACA;IACA,aACAI,WAAA,WAAAA,YAAA;MACA,KAAA1B,WAAA,CAAAC,OAAA;MACA,KAAAc,OAAA;IACA;IACA,aACAY,UAAA,WAAAA,WAAA;MACA,KAAA7B,SAAA;MACA,KAAA8B,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtC,GAAA,GAAAsC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA;MACA,KAAAxC,QAAA,IAAAqC,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAvC,IAAA;MACA,KAAAE,IAAA,GAAAqC,GAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,SAAA,QAAA/C,GAAA;MACA,KAAAgD,MAAA,CAAAC,OAAA,oBAAAF,SAAA,aAAA1B,IAAA;QACA,WAAA6B,iBAAA,EAAAH,SAAA;MACA,GAAA1B,IAAA;QACAyB,MAAA,CAAAvB,OAAA;QACAuB,MAAA,CAAAE,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAN,MAAA,CAAAC,OAAA,qBAAA5B,IAAA;QACA,WAAAkC,mBAAA;MACA,GAAAlC,IAAA;QACAiC,MAAA,CAAA/B,OAAA;QACA+B,MAAA,CAAAN,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAI,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,gCAAAC,cAAA,CAAAC,OAAA,MACA,KAAAnD,WAAA,UAAAoD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}