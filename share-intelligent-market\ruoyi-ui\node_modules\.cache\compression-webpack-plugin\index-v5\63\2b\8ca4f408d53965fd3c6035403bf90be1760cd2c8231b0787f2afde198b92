
96f2bdfffbbd6ceddecb62032249bce9d0eb0485	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"html\\u002Fie.html\",\"contentHash\":\"0cf74916d2262ceb1f5791e17e8f9255\"}","integrity":"sha512-qIy+yXhYJhjRIQAJwwKlpu/VHGeUYXtZSxG4wH/Q4ZoPrYUIhPu8yVl1S3HaLQXvFuwfOi4uyKVdJC29TuVJVw==","time":1750496064276,"size":62075}