package com.ruoyi.auth.controller;

import com.ruoyi.auth.service.SSOClientService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * SSO回调控制器
 * 专门处理SSO登录回调
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/sso")
public class SSOCallbackController {

    private static final Logger log = LoggerFactory.getLogger(SSOCallbackController.class);

    @Autowired
    private SSOClientService ssoClientService;

    /**
     * SSO登录回调处理
     * SSO服务重定向到此接口进行登录处理
     *
     * @param code 授权码
     * @param state 状态参数（原始重定向URL）
     * @param error 错误信息
     * @param response HTTP响应
     */
    @GetMapping("/callback")
    public void ssoCallback(@RequestParam(required = false) String code,
                           @RequestParam(value = "state", required = false) String state,
                           @RequestParam(required = false) String error,
                           HttpServletResponse response) throws IOException {

        if (StringUtils.isNotEmpty(error)) {
            log.error("SSO登录失败: {}", error);
            response.sendRedirect("/login?error=" + error);
            return;
        }

        if (StringUtils.isEmpty(code)) {
            log.error("SSO回调缺少授权码");
            response.sendRedirect("/login?error=missing_code");
            return;
        }

        log.info("SSO登录回调，授权码: {}, 状态: {}", code, state);

        try {
            // 使用授权码换取访问令牌
            Map<String, Object> tokenInfo = ssoClientService.exchangeToken(code);

            if (tokenInfo != null && tokenInfo.get("access_token") != null) {
                String accessToken = (String) tokenInfo.get("access_token");

                // 获取用户信息并创建本地会话，返回JWT token
                Map<String, Object> localTokenInfo = ssoClientService.createLocalSessionWithToken(accessToken);

                if (localTokenInfo != null && localTokenInfo.get("access_token") != null) {
                    // 登录成功，将token信息存储到临时缓存中
                    String jwtToken = (String) localTokenInfo.get("access_token");
                    String redirectUrl = StringUtils.isNotEmpty(state) ? state : "/";

                    // 生成临时token key，用于前端获取真正的JWT token
                    String tempTokenKey = ssoClientService.storeTempToken(localTokenInfo);

                    // 重定向到前端SSO回调页面，携带临时key
                    String callbackUrl = "/sso/callback?key=" + tempTokenKey + "&redirect=" + redirectUrl;
                    log.info("SSO登录成功，跳转到前端回调页面: {}", callbackUrl);
                    response.sendRedirect(callbackUrl);
                } else {
                    log.error("创建本地会话失败");
                    response.sendRedirect("/login?error=session_create_failed");
                }
            } else {
                log.error("获取访问令牌失败");
                response.sendRedirect("/login?error=token_exchange_failed");
            }
        } catch (Exception e) {
            log.error("SSO回调处理异常", e);
            response.sendRedirect("/login?error=callback_error");
        }
    }

    /**
     * 前端通过临时key获取JWT token
     */
    @GetMapping("/token")
    @ResponseBody
    public AjaxResult getTokenByKey(@RequestParam("key") String tempKey) {
        try {
            Map<String, Object> tokenInfo = ssoClientService.getTempToken(tempKey);
            if (tokenInfo != null) {
                log.info("前端获取JWT token成功，key: {}", tempKey);
                return AjaxResult.success("获取token成功", tokenInfo);
            } else {
                log.warn("临时key无效或已过期: {}", tempKey);
                return AjaxResult.error("临时key无效或已过期");
            }
        } catch (Exception e) {
            log.error("获取JWT token异常", e);
            return AjaxResult.error("获取token失败");
        }
    }
}
