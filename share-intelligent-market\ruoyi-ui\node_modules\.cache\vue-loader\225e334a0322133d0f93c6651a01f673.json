{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\index.vue", "mtime": 1750151094283}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgc2VhcmNoRGF0YQp9IGZyb20gJ0AvYXBpL2VudGVycHJpc2UvYXBwbHknOwppbXBvcnQgewogIGxpc3RFbnVtCn0gZnJvbSAnQC9hcGkvdG9vbC91dGlsJzsKaW1wb3J0IHsgbGlzdERhdGEsIG9wRGF0YSx1cFN0b3JlIH0gZnJvbSAnQC9hcGkvc3RvcmUvbGlzdCcKZXhwb3J0IGRlZmF1bHQgewogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDokKXkuJrnirbmgIEKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOS+m+W6lOWVhuWIl+ihqAogICAgICBlbnRlcnByaXNlT3B0aW9uczogW10sCiAgICAgIC8vIOmAieS4reeahOS+m+W6lOWVhgogICAgICBlbnRlcnByaXNlOiB7fSwKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIGVudGVycHJpc2VfaWQ6dW5kZWZpbmVkLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgLy8g5YiX6KGo5pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlm77niYfpooTop4jlnLDlnYAKICAgICAgc3JjTGlzdDogWwogICAgICBdLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nVmlzaWJsZUNsb3NlOmZhbHNlLAogICAgICBzdGF0dXM6ICcnLAogICAgICBkaWFsb2dWaXNpYmxlMTpmYWxzZQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogICAgdGhpcy5nZXRFbnVtcygpOwogIH0sCiAgZmlsdGVyczp7CiAgICBmaWx0ZXJTdGF0dXModmFsKXsKICAgICAgaWYodmFsPT0xKXsKICAgICAgICByZXR1cm4gJ+W+heWuoeaguCcKICAgICAgfWVsc2UgaWYodmFsPT0yKXsKICAgICAgICByZXR1cm4gJ+W3suWuoeaguCcKICAgICAgfWVsc2UgaWYodmFsPT0zKXsKICAgICAgICByZXR1cm4gJ+W3sumps+WbnicKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLyog5p+l6K+i5LyB5Lia5L+h5oGvICovCiAgICByZW1vdGVFbnRlcnByaXNlKGUpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgc2VhcmNoRGF0YShlKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgdGhpcy5lbnRlcnByaXNlT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KQogICAgfSwKICAgIC8qIOWIh+aNouS8geS4muS/oeaBryAqLwogICAgY2hhbmdlRW50ZXJwcmlzZShlKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW50ZXJwcmlzZV9pZCA9IHRoaXMuZW50ZXJwcmlzZS5pZDsKICAgIH0sCiAgICBnZXRFbnVtcygpIHsKICAgICAgbGlzdEVudW0oKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5zdGF0dXNPcHRpb25zID0gcmVzLmRhdGEuc3RvcmVTdGF0dXM7CiAgICAgIH0pCiAgICB9LAogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBlbnRlcnByaXNlX2lkOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQKICAgICAgfQogICAgfSwKICAgIC8qKiDmn6Xor6LnlKjmiLfliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgbGlzdERhdGEodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMubG9hZGluZz1mYWxzZQogICAgICAgIHRoaXMubGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMudG90YWwgPSByZXMuY291bnQ7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOihqOWNleaQnOe0oiAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOaQnOe0oumHjee9rgogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5yZXNldEZvcm0oJ3F1ZXJ5Rm9ybScpOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICBoYW5kbGVQcmV2aWV3KHVybCkgewogICAgICB0aGlzLnNyY0xpc3QgPSBbdXJsXTsKICAgIH0sCiAgICAvKiog5LiL57q/ICovCiAgICBoYW5kbGVMb3dlcihyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpui/m+ihjOW6l+mTuuS4i+e6v++8n+azqO+8muW6l+mTuuWvueW6lOWVhuWTgeWQjOatpeS4i+e6vyIsICLmj5DnpLoiLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgICAgIGNlbnRlcjogdHJ1ZQogICAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy5mb3JtLmVudGVycHJpc2VfaWQgPSByb3cuZW50ZXJwcmlzZV9pZAogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlQ2xvc2U9dHJ1ZQogICAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlVXBwZXIocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKQogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbov5vooYzlupfpk7rkuIrnur/vvJ8iLCAi5o+Q56S6IiwgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsCiAgICAgICAgICBjZW50ZXI6IHRydWUKICAgICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuZm9ybS5lbnRlcnByaXNlX2lkID0gcm93LmVudGVycHJpc2VfaWQKICAgICAgICAgIHRoaXMub3BTdG9yZSgnT1BFTicpCiAgICAgICAgfSkKICAgIH0sCiAgICAvKiog5a6h5qC4ICovCiAgICBoYW5kbGVPcChyb3cpIHsKICAgICAgdGhpcy5mb3JtID0gcm93CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLyoqIOWuoeaguOijheS/riAqLwogICAgaGFuZGxlT3AxKHJvdykgewogICAgICB0aGlzLmZvcm0gPSByb3cKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlMSA9IHRydWU7CiAgICB9LAogICAgLy8g5by556qX56Gu6K6kCiAgICBvcFN0b3JlKHN0YXR1cykgewogICAgICBvcERhdGEoewogICAgICAgIGVudGVycHJpc2VfaWQ6dGhpcy5mb3JtLmVudGVycHJpc2VfaWQsCiAgICAgICAgc3RhdHVzOnN0YXR1cywKICAgICAgICByZW1hcms6dGhpcy5mb3JtLnJlbWFyawogICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7dHlwZTogInN1Y2Nlc3MiLCBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfISIsfSk7CiAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlQ2xvc2UgPSBmYWxzZTsKCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0pCiAgICB9LAoKICAgIG9wU3RvcmUxKHN0YXR1cykgewogICAgICBpZihzdGF0dXM9PTMmJiF0aGlzLmZvcm0ucmVqZWN0aW9uX3JlYXNvbil7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7dHlwZTogIndhcm5pbmciLCBtZXNzYWdlOiAi6K+36L6T5YWl6amz5Zue5Y6f5ZugISIsfSk7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgb3BEYXRhKHsKICAgICAgICBlbnRlcnByaXNlX2lkOnRoaXMuZm9ybS5lbnRlcnByaXNlX2lkLAogICAgICAgIGZpdG1lbnRfc3RhdHVzOnN0YXR1cywKICAgICAgICByZWplY3Rpb25fcmVhc29uOnRoaXMuZm9ybS5yZWplY3Rpb25fcmVhc29uLAogICAgICAgIHN0YXR1czp0aGlzLmZvcm0uc3RhdHVzLAogICAgICAgIHJlbWFyazp0aGlzLmZvcm0ucmVtYXJrCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHt0eXBlOiAic3VjY2VzcyIsIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8hIix9KTsKICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGVDbG9zZSA9IGZhbHNlOwogICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxPWZhbHNlCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0pCiAgICB9LAogIH0sCn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\" prop='enterprise_id'>\r\n            <el-select clearable style=\"width: 300px;\" v-model=\"enterprise\" size='small'\r\n              filterable remote reserve-keyword placeholder=\"请输入企业名称\"\r\n              :remote-method=\"remoteEnterprise\" @change='changeEnterprise' value-key='id' :loading=\"loading\">\r\n              <el-option v-for=\"item in enterpriseOptions\" :key=\"item.id\" :label=\"item.name\" :value=\"item\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='name'>\r\n            <el-input clearable v-model=\"queryParams.name\" placeholder=\"输入店铺名称\" :maxlength='50' size='small'\r\n              style=\"width: 300px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select clearable v-model=\"queryParams.status\" placeholder=\"状态\" size='small'>\r\n              <el-option v-for=\"item in statusOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"企业ID\" align=\"center\" prop=\"enterprise_id\" />\r\n          <el-table-column label=\"店铺名称\" align=\"center\" prop=\"name\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"店铺Logo\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.logo\"\r\n                :preview-src-list=\"[scope.row.logo]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"banner\" align=\"center\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 160px; height: 100px\" v-if='scope.row.banner'\r\n                :src=\"scope.row.banner\" :preview-src-list=\"[scope.row.banner]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"店铺状态\" align=\"center\" >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"店铺装修状态\" align=\"center\" width=\"120\"  prop=\"fitment_status\">\r\n           <template slot-scope=\"scope\">\r\n               <el-tag size=\"mini\" type='warning'>{{scope.row.fitment_status|filterStatus}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"开户行\" align=\"center\" prop=\"openbank\" width=\"140\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"开户地址\" align=\"center\" prop=\"bank\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"户头\" align=\"center\" prop=\"account\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"对公账户\" align=\"center\" prop=\"cardno\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"授权认证\" align=\"center\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 160px; height: 100px\" v-if='scope.row.certfile'\r\n                :src=\"scope.row.certfile\" :preview-src-list=\"[scope.row.certfile]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注信息\" align=\"center\" prop=\"remark\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"申请者\" align=\"center\" prop=\"create_by\" width=\"100\" />\r\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n          <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejection_reason\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n\r\n          <el-table-column label=\"操作\" align=\"center\" fixed='right' width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.status == 'OPEN'\" type=\"text\"  icon=\"el-icon-download\" size=\"mini\" @click=\"handleLower(scope.row)\">下线\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.status == 'CLOSE'\" type=\"text\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleUpper(scope.row)\">上线\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.status == 'WAIT'\" type=\"text\" icon=\"el-icon-user\" size=\"mini\" @click=\"handleOp(scope.row)\">审核\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.fitment_status == '1'\" type=\"text\" icon=\"el-icon-user\" size=\"mini\" @click=\"handleOp1(scope.row)\">装修审核\r\n              </el-button>\r\n\r\n\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog title=\"操作店铺\" :visible.sync=\"dialogVisible\" width=\"30%\" center>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"审核备注\" v-model=\"form.remark\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore('OPEN')\">确 定</el-button>\r\n        <el-button type=\"danger\" @click=\"opStore('DENY')\">驳回</el-button>\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog title=\"审核店铺装修\" :visible.sync=\"dialogVisible1\" width=\"80%\" center>\r\n      <div style=\"color:#333;font-weight:bold\">装修内容：</div>\r\n      <div v-html=\"form.content\"></div>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"驳回原因\" v-model=\"form.rejection_reason\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore1('2')\">通过</el-button>\r\n        <el-button type=\"danger\" @click=\"opStore1('3')\">驳回</el-button>\r\n        <el-button @click=\"dialogVisible1 = false\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog title=\"请输入下架原因\" :visible.sync=\"dialogVisibleClose\" width=\"30%\" center>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"下架原因\" v-model=\"form.remark\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore('CLOSE')\">确 定</el-button>\r\n        <el-button @click=\"dialogVisibleClose = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    searchData\r\n  } from '@/api/enterprise/apply';\r\n  import {\r\n    listEnum\r\n  } from '@/api/tool/util';\r\n  import { listData, opData,upStore } from '@/api/store/list'\r\n  export default {\r\n    data() {\r\n      return {\r\n        // 营业状态\r\n        statusOptions: [],\r\n        // 供应商列表\r\n        enterpriseOptions: [],\r\n        // 选中的供应商\r\n        enterprise: {},\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          enterprise_id:undefined,\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          status: undefined,\r\n        },\r\n        // 列表数据\r\n        list: [],\r\n        // 图片预览地址\r\n        srcList: [\r\n        ],\r\n        dialogVisible: false,\r\n        dialogVisibleClose:false,\r\n        status: '',\r\n        dialogVisible1:false\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n      this.getEnums();\r\n    },\r\n    filters:{\r\n      filterStatus(val){\r\n        if(val==1){\r\n          return '待审核'\r\n        }else if(val==2){\r\n          return '已审核'\r\n        }else if(val==3){\r\n          return '已驳回'\r\n        }\r\n      }\r\n    },\r\n    methods: {\r\n      /* 查询企业信息 */\r\n      remoteEnterprise(e) {\r\n        this.loading = true;\r\n        searchData(e).then(res => {\r\n          this.loading = false;\r\n          this.enterpriseOptions = res.data;\r\n        })\r\n      },\r\n      /* 切换企业信息 */\r\n      changeEnterprise(e) {\r\n        this.queryParams.enterprise_id = this.enterprise.id;\r\n      },\r\n      getEnums() {\r\n        listEnum().then(res => {\r\n          this.statusOptions = res.data.storeStatus;\r\n        })\r\n      },\r\n      reset() {\r\n        this.form = {\r\n          enterprise_id: undefined,\r\n          status: undefined,\r\n          remark: undefined\r\n        }\r\n      },\r\n      /** 查询用户列表 */\r\n      getList() {\r\n        this.loading = true\r\n        listData(this.queryParams).then(res => {\r\n          this.loading=false\r\n          this.list = res.data;\r\n          this.total = res.count;\r\n        })\r\n      },\r\n      /** 表单搜索 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      // 搜索重置\r\n      resetQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.resetForm('queryForm');\r\n        this.getList();\r\n      },\r\n      handlePreview(url) {\r\n        this.srcList = [url];\r\n      },\r\n      /** 下线 */\r\n      handleLower(row) {\r\n        this.reset()\r\n        this.$confirm(\"是否进行店铺下线？注：店铺对应商品同步下线\", \"提示\", {\r\n            confirmButtonText: \"确定\",\r\n            cancelButtonText: \"取消\",\r\n            type: \"warning\",\r\n            center: true\r\n          })\r\n          .then(() => {\r\n            this.form.enterprise_id = row.enterprise_id\r\n            this.dialogVisibleClose=true\r\n          })\r\n      },\r\n      handleUpper(row) {\r\n        this.reset()\r\n        this.$confirm(\"是否进行店铺上线？\", \"提示\", {\r\n            confirmButtonText: \"确定\",\r\n            cancelButtonText: \"取消\",\r\n            type: \"warning\",\r\n            center: true\r\n          })\r\n          .then(() => {\r\n            this.form.enterprise_id = row.enterprise_id\r\n            this.opStore('OPEN')\r\n          })\r\n      },\r\n      /** 审核 */\r\n      handleOp(row) {\r\n        this.form = row\r\n        this.dialogVisible = true;\r\n      },\r\n      /** 审核装修 */\r\n      handleOp1(row) {\r\n        this.form = row\r\n        this.dialogVisible1 = true;\r\n      },\r\n      // 弹窗确认\r\n      opStore(status) {\r\n        opData({\r\n          enterprise_id:this.form.enterprise_id,\r\n          status:status,\r\n          remark:this.form.remark\r\n        }).then(res => {\r\n          this.$message({type: \"success\", message: \"操作成功!\",});\r\n          this.dialogVisible = false;\r\n          this.dialogVisibleClose = false;\r\n\r\n          this.getList();\r\n        })\r\n      },\r\n\r\n      opStore1(status) {\r\n        if(status==3&&!this.form.rejection_reason){\r\n          this.$message({type: \"warning\", message: \"请输入驳回原因!\",});\r\n          return\r\n        }\r\n        opData({\r\n          enterprise_id:this.form.enterprise_id,\r\n          fitment_status:status,\r\n          rejection_reason:this.form.rejection_reason,\r\n          status:this.form.status,\r\n          remark:this.form.remark\r\n        }).then(res => {\r\n          this.$message({type: \"success\", message: \"操作成功!\",});\r\n          this.dialogVisible = false;\r\n          this.dialogVisibleClose = false;\r\n           this.dialogVisible1=false\r\n          this.getList();\r\n        })\r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .color-red {\r\n    color: red !important;\r\n    border: 1px solid red !important;\r\n  }\r\n</style>\r\n"]}]}