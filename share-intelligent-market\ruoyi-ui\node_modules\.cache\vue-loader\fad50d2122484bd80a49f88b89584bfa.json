{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=style&index=0&id=82a94682&scoped=true&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1750151094141}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750495809569}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovLyAuZWwtdXBsb2FkLS1waWN0dXJlLWNhcmQg5o6n5Yi25Yqg5Y+36YOo5YiGDQo6OnYtZGVlcC5oaWRlIC5lbC11cGxvYWQtLXBpY3R1cmUtY2FyZCB7DQogICAgZGlzcGxheTogbm9uZTsNCn0NCi8vIOWOu+aOieWKqOeUu+aViOaenA0KOjp2LWRlZXAgLmVsLWxpc3QtZW50ZXItYWN0aXZlLA0KOjp2LWRlZXAgLmVsLWxpc3QtbGVhdmUtYWN0aXZlIHsNCiAgICB0cmFuc2l0aW9uOiBhbGwgMHM7DQp9DQoNCjo6di1kZWVwIC5lbC1saXN0LWVudGVyLCAuZWwtbGlzdC1sZWF2ZS1hY3RpdmUgew0KICAgIG9wYWNpdHk6IDA7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOw0KfQ0KDQoNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImageUpload", "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      name=\"file\"\r\n      :on-remove=\"handleRemove\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\" v-if='showList || !fileList.length'></i>\r\n      <!-- <img v-if='!showList && fileList.length' :src='fileList[0].url'></img> -->\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 是否显示wen文件列表\r\n    showList: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5,\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"],\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/shop/data/upload/image\", // 上传的图片服务器地址\r\n      headers: {\r\n        authorization: getToken(),\r\n      },\r\n      fileList: []\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',');\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              item = { name: item, url: item };\r\n            }\r\n            return item;\r\n          });\r\n        } else {\r\n          this.fileList = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n  },\r\n  methods: {\r\n    // 删除图片\r\n    handleRemove(file, fileList) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name);\r\n      if(findex > -1) {\r\n        this.fileList.splice(findex, 1);\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n      }\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res) {\r\n      this.uploadList.push({ name: res.url, url: res.url });\r\n      if (this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n        this.$modal.closeLoading();\r\n      }\r\n    },\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false;\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\";\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true;\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\r\n          return false;\r\n        });\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1;\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join(\"/\")}图片格式文件!`);\r\n        return false;\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候...\");\r\n      this.number++;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        strs += list[i].url + separator;\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n    display: none;\r\n}\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n    transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n    opacity: 0;\r\n    transform: translateY(0);\r\n}\r\n\r\n\r\n</style>\r\n"]}]}