{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\validate.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\utils\\validate.js", "mtime": 1750151094220}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isExternal", "path", "test", "validUsername", "str", "valid_map", "indexOf", "trim", "validURL", "url", "reg", "validLowerCase", "validUpperCase", "validAlphabets", "validEmail", "email", "isString", "String", "isArray", "arg", "Array", "Object", "prototype", "toString", "call"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/utils/validate.js"], "sourcesContent": ["/**\r\n * @param {string} path\r\n * @returns {Boolean}\r\n */\r\nexport function isExternal(path) {\r\n  return /^(https?:|mailto:|tel:)/.test(path)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validUsername(str) {\r\n  const valid_map = ['admin', 'editor']\r\n  return valid_map.indexOf(str.trim()) >= 0\r\n}\r\n\r\n/**\r\n * @param {string} url\r\n * @returns {Boolean}\r\n */\r\nexport function validURL(url) {\r\n  const reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/\r\n  return reg.test(url)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validLowerCase(str) {\r\n  const reg = /^[a-z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validUpperCase(str) {\r\n  const reg = /^[A-Z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validAlphabets(str) {\r\n  const reg = /^[A-Za-z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} email\r\n * @returns {Boolean}\r\n */\r\nexport function validEmail(email) {\r\n  const reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\r\n  return reg.test(email)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function isString(str) {\r\n  if (typeof str === 'string' || str instanceof String) {\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\n/**\r\n * @param {Array} arg\r\n * @returns {Boolean}\r\n */\r\nexport function isArray(arg) {\r\n  if (typeof Array.isArray === 'undefined') {\r\n    return Object.prototype.toString.call(arg) === '[object Array]'\r\n  }\r\n  return Array.isArray(arg)\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACO,SAASA,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,yBAAyB,CAACC,IAAI,CAACD,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACO,SAASE,aAAaA,CAACC,GAAG,EAAE;EACjC,IAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EACrC,OAAOA,SAAS,CAACC,OAAO,CAACF,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC5B,IAAMC,GAAG,GAAG,4TAA4T;EACxU,OAAOA,GAAG,CAACR,IAAI,CAACO,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASE,cAAcA,CAACP,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASQ,cAAcA,CAACR,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASS,cAAcA,CAACT,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,aAAa;EACzB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASU,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAML,GAAG,GAAG,yJAAyJ;EACrK,OAAOA,GAAG,CAACR,IAAI,CAACa,KAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAACZ,GAAG,EAAE;EAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYa,MAAM,EAAE;IACpD,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACO,SAASC,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAI,OAAOC,KAAK,CAACF,OAAO,KAAK,WAAW,EAAE;IACxC,OAAOG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,KAAK,gBAAgB;EACjE;EACA,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B", "ignoreList": []}]}