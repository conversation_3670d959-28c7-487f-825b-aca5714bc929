{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\store\\index.vue", "mtime": 1750151094283}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_apply", "require", "_util", "_list", "data", "statusOptions", "enterpriseOptions", "enterprise", "loading", "showSearch", "total", "form", "queryParams", "enterprise_id", "undefined", "pageNum", "pageSize", "name", "status", "list", "srcList", "dialogVisible", "dialogVisibleClose", "dialogVisible1", "created", "getList", "getEnums", "filters", "filterStatus", "val", "methods", "remoteEnterprise", "e", "_this", "searchData", "then", "res", "changeEnterprise", "id", "_this2", "listEnum", "storeStatus", "reset", "remark", "_this3", "listData", "count", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handlePreview", "url", "handleLower", "row", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type", "center", "handleUpper", "_this5", "opStore", "handleOp", "handleOp1", "_this6", "opData", "$message", "message", "opStore1", "_this7", "rejection_reason", "fitment_status"], "sources": ["src/views/store/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\" prop='enterprise_id'>\r\n            <el-select clearable style=\"width: 300px;\" v-model=\"enterprise\" size='small'\r\n              filterable remote reserve-keyword placeholder=\"请输入企业名称\"\r\n              :remote-method=\"remoteEnterprise\" @change='changeEnterprise' value-key='id' :loading=\"loading\">\r\n              <el-option v-for=\"item in enterpriseOptions\" :key=\"item.id\" :label=\"item.name\" :value=\"item\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='name'>\r\n            <el-input clearable v-model=\"queryParams.name\" placeholder=\"输入店铺名称\" :maxlength='50' size='small'\r\n              style=\"width: 300px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select clearable v-model=\"queryParams.status\" placeholder=\"状态\" size='small'>\r\n              <el-option v-for=\"item in statusOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"企业ID\" align=\"center\" prop=\"enterprise_id\" />\r\n          <el-table-column label=\"店铺名称\" align=\"center\" prop=\"name\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"店铺Logo\" align=\"center\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 100px; height: 100px\" :src=\"scope.row.logo\"\r\n                :preview-src-list=\"[scope.row.logo]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"banner\" align=\"center\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 160px; height: 100px\" v-if='scope.row.banner'\r\n                :src=\"scope.row.banner\" :preview-src-list=\"[scope.row.banner]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"店铺状态\" align=\"center\" >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"店铺装修状态\" align=\"center\" width=\"120\"  prop=\"fitment_status\">\r\n           <template slot-scope=\"scope\">\r\n               <el-tag size=\"mini\" type='warning'>{{scope.row.fitment_status|filterStatus}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"开户行\" align=\"center\" prop=\"openbank\" width=\"140\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"开户地址\" align=\"center\" prop=\"bank\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"户头\" align=\"center\" prop=\"account\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"对公账户\" align=\"center\" prop=\"cardno\" width=\"200\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"授权认证\" align=\"center\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-image style=\"width: 160px; height: 100px\" v-if='scope.row.certfile'\r\n                :src=\"scope.row.certfile\" :preview-src-list=\"[scope.row.certfile]\">\r\n              </el-image>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注信息\" align=\"center\" prop=\"remark\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"申请者\" align=\"center\" prop=\"create_by\" width=\"100\" />\r\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n          <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejection_reason\" width=\"280\" :show-overflow-tooltip=\"true\"/>\r\n\r\n          <el-table-column label=\"操作\" align=\"center\" fixed='right' width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.status == 'OPEN'\" type=\"text\"  icon=\"el-icon-download\" size=\"mini\" @click=\"handleLower(scope.row)\">下线\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.status == 'CLOSE'\" type=\"text\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleUpper(scope.row)\">上线\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.status == 'WAIT'\" type=\"text\" icon=\"el-icon-user\" size=\"mini\" @click=\"handleOp(scope.row)\">审核\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.fitment_status == '1'\" type=\"text\" icon=\"el-icon-user\" size=\"mini\" @click=\"handleOp1(scope.row)\">装修审核\r\n              </el-button>\r\n\r\n\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog title=\"操作店铺\" :visible.sync=\"dialogVisible\" width=\"30%\" center>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"审核备注\" v-model=\"form.remark\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore('OPEN')\">确 定</el-button>\r\n        <el-button type=\"danger\" @click=\"opStore('DENY')\">驳回</el-button>\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog title=\"审核店铺装修\" :visible.sync=\"dialogVisible1\" width=\"80%\" center>\r\n      <div style=\"color:#333;font-weight:bold\">装修内容：</div>\r\n      <div v-html=\"form.content\"></div>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"驳回原因\" v-model=\"form.rejection_reason\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore1('2')\">通过</el-button>\r\n        <el-button type=\"danger\" @click=\"opStore1('3')\">驳回</el-button>\r\n        <el-button @click=\"dialogVisible1 = false\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog title=\"请输入下架原因\" :visible.sync=\"dialogVisibleClose\" width=\"30%\" center>\r\n      <el-input type=\"textarea\" :rows=\"3\" placeholder=\"下架原因\" v-model=\"form.remark\"></el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opStore('CLOSE')\">确 定</el-button>\r\n        <el-button @click=\"dialogVisibleClose = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    searchData\r\n  } from '@/api/enterprise/apply';\r\n  import {\r\n    listEnum\r\n  } from '@/api/tool/util';\r\n  import { listData, opData,upStore } from '@/api/store/list'\r\n  export default {\r\n    data() {\r\n      return {\r\n        // 营业状态\r\n        statusOptions: [],\r\n        // 供应商列表\r\n        enterpriseOptions: [],\r\n        // 选中的供应商\r\n        enterprise: {},\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          enterprise_id:undefined,\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          status: undefined,\r\n        },\r\n        // 列表数据\r\n        list: [],\r\n        // 图片预览地址\r\n        srcList: [\r\n        ],\r\n        dialogVisible: false,\r\n        dialogVisibleClose:false,\r\n        status: '',\r\n        dialogVisible1:false\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n      this.getEnums();\r\n    },\r\n    filters:{\r\n      filterStatus(val){\r\n        if(val==1){\r\n          return '待审核'\r\n        }else if(val==2){\r\n          return '已审核'\r\n        }else if(val==3){\r\n          return '已驳回'\r\n        }\r\n      }\r\n    },\r\n    methods: {\r\n      /* 查询企业信息 */\r\n      remoteEnterprise(e) {\r\n        this.loading = true;\r\n        searchData(e).then(res => {\r\n          this.loading = false;\r\n          this.enterpriseOptions = res.data;\r\n        })\r\n      },\r\n      /* 切换企业信息 */\r\n      changeEnterprise(e) {\r\n        this.queryParams.enterprise_id = this.enterprise.id;\r\n      },\r\n      getEnums() {\r\n        listEnum().then(res => {\r\n          this.statusOptions = res.data.storeStatus;\r\n        })\r\n      },\r\n      reset() {\r\n        this.form = {\r\n          enterprise_id: undefined,\r\n          status: undefined,\r\n          remark: undefined\r\n        }\r\n      },\r\n      /** 查询用户列表 */\r\n      getList() {\r\n        this.loading = true\r\n        listData(this.queryParams).then(res => {\r\n          this.loading=false\r\n          this.list = res.data;\r\n          this.total = res.count;\r\n        })\r\n      },\r\n      /** 表单搜索 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      // 搜索重置\r\n      resetQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.resetForm('queryForm');\r\n        this.getList();\r\n      },\r\n      handlePreview(url) {\r\n        this.srcList = [url];\r\n      },\r\n      /** 下线 */\r\n      handleLower(row) {\r\n        this.reset()\r\n        this.$confirm(\"是否进行店铺下线？注：店铺对应商品同步下线\", \"提示\", {\r\n            confirmButtonText: \"确定\",\r\n            cancelButtonText: \"取消\",\r\n            type: \"warning\",\r\n            center: true\r\n          })\r\n          .then(() => {\r\n            this.form.enterprise_id = row.enterprise_id\r\n            this.dialogVisibleClose=true\r\n          })\r\n      },\r\n      handleUpper(row) {\r\n        this.reset()\r\n        this.$confirm(\"是否进行店铺上线？\", \"提示\", {\r\n            confirmButtonText: \"确定\",\r\n            cancelButtonText: \"取消\",\r\n            type: \"warning\",\r\n            center: true\r\n          })\r\n          .then(() => {\r\n            this.form.enterprise_id = row.enterprise_id\r\n            this.opStore('OPEN')\r\n          })\r\n      },\r\n      /** 审核 */\r\n      handleOp(row) {\r\n        this.form = row\r\n        this.dialogVisible = true;\r\n      },\r\n      /** 审核装修 */\r\n      handleOp1(row) {\r\n        this.form = row\r\n        this.dialogVisible1 = true;\r\n      },\r\n      // 弹窗确认\r\n      opStore(status) {\r\n        opData({\r\n          enterprise_id:this.form.enterprise_id,\r\n          status:status,\r\n          remark:this.form.remark\r\n        }).then(res => {\r\n          this.$message({type: \"success\", message: \"操作成功!\",});\r\n          this.dialogVisible = false;\r\n          this.dialogVisibleClose = false;\r\n\r\n          this.getList();\r\n        })\r\n      },\r\n\r\n      opStore1(status) {\r\n        if(status==3&&!this.form.rejection_reason){\r\n          this.$message({type: \"warning\", message: \"请输入驳回原因!\",});\r\n          return\r\n        }\r\n        opData({\r\n          enterprise_id:this.form.enterprise_id,\r\n          fitment_status:status,\r\n          rejection_reason:this.form.rejection_reason,\r\n          status:this.form.status,\r\n          remark:this.form.remark\r\n        }).then(res => {\r\n          this.$message({type: \"success\", message: \"操作成功!\",});\r\n          this.dialogVisible = false;\r\n          this.dialogVisibleClose = false;\r\n           this.dialogVisible1=false\r\n          this.getList();\r\n        })\r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .color-red {\r\n    color: red !important;\r\n    border: 1px solid red !important;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;AA0HA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,KAAA,GAAAD,OAAA;AAGA,IAAAE,KAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,iBAAA;MACA;MACAC,UAAA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,OAAA;QACAC,QAAA;QACAC,IAAA,EAAAH,SAAA;QACAI,MAAA,EAAAJ;MACA;MACA;MACAK,IAAA;MACA;MACAC,OAAA,IACA;MACAC,aAAA;MACAC,kBAAA;MACAJ,MAAA;MACAK,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAA,GAAA;QACA;MACA,WAAAA,GAAA;QACA;MACA,WAAAA,GAAA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACA,YACAC,gBAAA,WAAAA,iBAAAC,CAAA;MAAA,IAAAC,KAAA;MACA,KAAAzB,OAAA;MACA,IAAA0B,iBAAA,EAAAF,CAAA,EAAAG,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAzB,OAAA;QACAyB,KAAA,CAAA3B,iBAAA,GAAA8B,GAAA,CAAAhC,IAAA;MACA;IACA;IACA,YACAiC,gBAAA,WAAAA,iBAAAL,CAAA;MACA,KAAApB,WAAA,CAAAC,aAAA,QAAAN,UAAA,CAAA+B,EAAA;IACA;IACAZ,QAAA,WAAAA,SAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,cAAA,IAAAL,IAAA,WAAAC,GAAA;QACAG,MAAA,CAAAlC,aAAA,GAAA+B,GAAA,CAAAhC,IAAA,CAAAqC,WAAA;MACA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAA/B,IAAA;QACAE,aAAA,EAAAC,SAAA;QACAI,MAAA,EAAAJ,SAAA;QACA6B,MAAA,EAAA7B;MACA;IACA;IACA,aACAW,OAAA,WAAAA,QAAA;MAAA,IAAAmB,MAAA;MACA,KAAApC,OAAA;MACA,IAAAqC,cAAA,OAAAjC,WAAA,EAAAuB,IAAA,WAAAC,GAAA;QACAQ,MAAA,CAAApC,OAAA;QACAoC,MAAA,CAAAzB,IAAA,GAAAiB,GAAA,CAAAhC,IAAA;QACAwC,MAAA,CAAAlC,KAAA,GAAA0B,GAAA,CAAAU,KAAA;MACA;IACA;IACA,WACAC,WAAA,WAAAA,YAAA;MACA,KAAAnC,WAAA,CAAAG,OAAA;MACA,KAAAU,OAAA;IACA;IACA;IACAuB,UAAA,WAAAA,WAAA;MACA,KAAApC,WAAA,CAAAG,OAAA;MACA,KAAAkC,SAAA;MACA,KAAAxB,OAAA;IACA;IACAyB,aAAA,WAAAA,cAAAC,GAAA;MACA,KAAA/B,OAAA,IAAA+B,GAAA;IACA;IACA,SACAC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAZ,KAAA;MACA,KAAAa,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACAxB,IAAA;QACAmB,MAAA,CAAA3C,IAAA,CAAAE,aAAA,GAAAwC,GAAA,CAAAxC,aAAA;QACAyC,MAAA,CAAAhC,kBAAA;MACA;IACA;IACAsC,WAAA,WAAAA,YAAAP,GAAA;MAAA,IAAAQ,MAAA;MACA,KAAAnB,KAAA;MACA,KAAAa,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACAxB,IAAA;QACA0B,MAAA,CAAAlD,IAAA,CAAAE,aAAA,GAAAwC,GAAA,CAAAxC,aAAA;QACAgD,MAAA,CAAAC,OAAA;MACA;IACA;IACA,SACAC,QAAA,WAAAA,SAAAV,GAAA;MACA,KAAA1C,IAAA,GAAA0C,GAAA;MACA,KAAAhC,aAAA;IACA;IACA,WACA2C,SAAA,WAAAA,UAAAX,GAAA;MACA,KAAA1C,IAAA,GAAA0C,GAAA;MACA,KAAA9B,cAAA;IACA;IACA;IACAuC,OAAA,WAAAA,QAAA5C,MAAA;MAAA,IAAA+C,MAAA;MACA,IAAAC,YAAA;QACArD,aAAA,OAAAF,IAAA,CAAAE,aAAA;QACAK,MAAA,EAAAA,MAAA;QACAyB,MAAA,OAAAhC,IAAA,CAAAgC;MACA,GAAAR,IAAA,WAAAC,GAAA;QACA6B,MAAA,CAAAE,QAAA;UAAAT,IAAA;UAAAU,OAAA;QAAA;QACAH,MAAA,CAAA5C,aAAA;QACA4C,MAAA,CAAA3C,kBAAA;QAEA2C,MAAA,CAAAxC,OAAA;MACA;IACA;IAEA4C,QAAA,WAAAA,SAAAnD,MAAA;MAAA,IAAAoD,MAAA;MACA,IAAApD,MAAA,eAAAP,IAAA,CAAA4D,gBAAA;QACA,KAAAJ,QAAA;UAAAT,IAAA;UAAAU,OAAA;QAAA;QACA;MACA;MACA,IAAAF,YAAA;QACArD,aAAA,OAAAF,IAAA,CAAAE,aAAA;QACA2D,cAAA,EAAAtD,MAAA;QACAqD,gBAAA,OAAA5D,IAAA,CAAA4D,gBAAA;QACArD,MAAA,OAAAP,IAAA,CAAAO,MAAA;QACAyB,MAAA,OAAAhC,IAAA,CAAAgC;MACA,GAAAR,IAAA,WAAAC,GAAA;QACAkC,MAAA,CAAAH,QAAA;UAAAT,IAAA;UAAAU,OAAA;QAAA;QACAE,MAAA,CAAAjD,aAAA;QACAiD,MAAA,CAAAhD,kBAAA;QACAgD,MAAA,CAAA/C,cAAA;QACA+C,MAAA,CAAA7C,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}