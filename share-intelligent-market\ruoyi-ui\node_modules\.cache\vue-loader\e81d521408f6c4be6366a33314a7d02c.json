{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\achievement_joint\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\achievement_joint\\index.vue", "mtime": 1750151094249}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3RBY2hpZXZlbWVudF9qb2ludCwNCiAgICBnZXRBY2hpZXZlbWVudF9qb2ludCwNCiAgICBkZWxBY2hpZXZlbWVudF9qb2ludCwNCiAgICBhZGRBY2hpZXZlbWVudF9qb2ludCwNCiAgICB1cGRhdGVBY2hpZXZlbWVudF9qb2ludCwNCn0gZnJvbSAiQC9hcGkvdXVjL2FjaGlldmVtZW50X2pvaW50IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJBY2hpZXZlbWVudF9qb2ludCIsDQogICAgZGF0YSgpIHsNCiAgICAgICAgbGV0IGNoZWNrUGhvbmUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICAgICAgICBsZXQgcmVnID0gL14xWzM0NTc4OV1cZHs5fSQvOw0KICAgICAgICAgICAgaWYgKCFyZWcudGVzdCh2YWx1ZSkpIHsNCiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuivt+i+k+WFpTEx5L2N5omL5py65Y+3IikpOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgICAgICAgfQ0KICAgICAgICB9Ow0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgLy8g6YGu572p5bGCDQogICAgICAgICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICAgICAgICBpZHM6IFtdLA0KICAgICAgICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICAgICAgICBzaW5nbGU6IHRydWUsDQogICAgICAgICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgICAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICAgICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgICAgICAgLy8g5oC75p2h5pWwDQogICAgICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgICAgIC8vIOaIkOaenOWQiOS9nOihqOagvOaVsOaNrg0KICAgICAgICAgICAgYWNoaWV2ZW1lbnRfam9pbnRMaXN0OiBbXSwNCiAgICAgICAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgICAgICAgICBhY2hpZXZlbWVudElkOiBudWxsLA0KICAgICAgICAgICAgICAgIGFjaGlldmVtZW50TmFtZTogbnVsbCwNCiAgICAgICAgICAgICAgICBsaW5rTWFuOiBudWxsLA0KICAgICAgICAgICAgICAgIGxpbmtUZWw6IG51bGwsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICAgICAgICBmb3JtOiB7fSwNCiAgICAgICAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgICAgICAgcnVsZXM6IHsNCiAgICAgICAgICAgICAgICBhY2hpZXZlbWVudElkOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuaIkOaenElE5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIGFjaGlldmVtZW50TmFtZTogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmiJDmnpzlkI3np7DkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgbGlua01hbjogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLogZTns7vkurrkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgbGlua1RlbDogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLogZTns7vnlLXor53kuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHZhbGlkYXRvcjogY2hlY2tQaG9uZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7ciLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICB9LA0KICAgICAgICB9Ow0KICAgIH0sDQogICAgY3JlYXRlZCgpIHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBtZXRob2RzOiB7DQogICAgICAgIC8qKiDmn6Xor6LmiJDmnpzlkIjkvZzliJfooaggKi8NCiAgICAgICAgZ2V0TGlzdCgpIHsNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgICBsaXN0QWNoaWV2ZW1lbnRfam9pbnQodGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmFjaGlldmVtZW50X2pvaW50TGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOWPlua2iOaMiemSrg0KICAgICAgICBjYW5jZWwoKSB7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g6KGo5Y2V6YeN572uDQogICAgICAgIHJlc2V0KCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICAgIGlkOiBudWxsLA0KICAgICAgICAgICAgICAgIGFjaGlldmVtZW50SWQ6IG51bGwsDQogICAgICAgICAgICAgICAgYWNoaWV2ZW1lbnROYW1lOiBudWxsLA0KICAgICAgICAgICAgICAgIGxpbmtNYW46IG51bGwsDQogICAgICAgICAgICAgICAgbGlua1RlbDogbnVsbCwNCiAgICAgICAgICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgICAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgICAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICAgICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgICAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5pZCk7DQogICAgICAgICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICAgICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlQWRkKCkgew0KICAgICAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5oiQ5p6c5ZCI5L2cIjsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgICAgICAgIGdldEFjaGlldmVtZW50X2pvaW50KGlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueaIkOaenOWQiOS9nCI7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgICAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUFjaGlldmVtZW50X2pvaW50KHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgYWRkQWNoaWV2ZW1lbnRfam9pbnQodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICAgICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICAgICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAgICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmiJDmnpzlkIjkvZznvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykNCiAgICAgICAgICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBkZWxBY2hpZXZlbWVudF9qb2ludChpZHMpOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgICAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICAgICAgICAgInV1Yy9hY2hpZXZlbWVudF9qb2ludC9leHBvcnQiLA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIGBhY2hpZXZlbWVudF9qb2ludF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgICAgICAgKTsNCiAgICAgICAgfSwNCiAgICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwOA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/ningmengdou/achievement_joint", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"成果ID\" prop=\"achievementId\">\r\n                <el-input\r\n                    v-model=\"queryParams.achievementId\"\r\n                    placeholder=\"请输入成果ID\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"成果名称\" prop=\"achievementName\">\r\n                <el-input\r\n                    v-model=\"queryParams.achievementName\"\r\n                    placeholder=\"请输入成果名称\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"linkMan\">\r\n                <el-input\r\n                    v-model=\"queryParams.linkMan\"\r\n                    placeholder=\"请输入联系人\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\" prop=\"linkTel\">\r\n                <el-input\r\n                    v-model=\"queryParams.linkTel\"\r\n                    placeholder=\"请输入联系电话\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:achievement_joint:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:achievement_joint:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:achievement_joint:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['uuc:achievement_joint:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"achievement_jointList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n            <el-table-column\r\n                label=\"成果ID\"\r\n                align=\"center\"\r\n                prop=\"achievementId\"\r\n            />\r\n            <el-table-column\r\n                label=\"成果名称\"\r\n                align=\"center\"\r\n                prop=\"achievementName\"\r\n            />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"linkMan\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"linkTel\" />\r\n            <!-- <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" /> -->\r\n                 <el-table-column\r\n                label=\"备注\"\r\n                align=\"center\"\r\n                width=\"160px\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <div style=\"width: 140px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;\">\r\n                        {{ scope.row.remark }}\r\n                    </div>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:achievement_joint:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:achievement_joint:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改成果合作对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n                <el-form-item label=\"成果ID\" prop=\"achievementId\">\r\n                    <el-input\r\n                        v-model=\"form.achievementId\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入成果ID\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"成果名称\" prop=\"achievementName\">\r\n                    <el-input\r\n                        v-model=\"form.achievementName\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入成果名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" prop=\"linkMan\">\r\n                    <el-input\r\n                        v-model=\"form.linkMan\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入联系人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系电话\" prop=\"linkTel\">\r\n                    <el-input\r\n                        v-model=\"form.linkTel\"\r\n                        maxlength=\"20\"\r\n                        type=\"number\"\r\n                        min=\"0\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"500\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listAchievement_joint,\r\n    getAchievement_joint,\r\n    delAchievement_joint,\r\n    addAchievement_joint,\r\n    updateAchievement_joint,\r\n} from \"@/api/uuc/achievement_joint\";\r\n\r\nexport default {\r\n    name: \"Achievement_joint\",\r\n    data() {\r\n        let checkPhone = (rule, value, callback) => {\r\n            let reg = /^1[345789]\\d{9}$/;\r\n            if (!reg.test(value)) {\r\n                callback(new Error(\"请输入11位手机号\"));\r\n            } else {\r\n                callback();\r\n            }\r\n        };\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 成果合作表格数据\r\n            achievement_jointList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                achievementId: null,\r\n                achievementName: null,\r\n                linkMan: null,\r\n                linkTel: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            // 表单校验\r\n            rules: {\r\n                achievementId: [\r\n                    {\r\n                        required: true,\r\n                        message: \"成果ID不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                achievementName: [\r\n                    {\r\n                        required: true,\r\n                        message: \"成果名称不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                linkMan: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                linkTel: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系电话不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    {\r\n                        type: \"number\",\r\n                        validator: checkPhone,\r\n                        message: \"请输入正确的手机号\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        /** 查询成果合作列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listAchievement_joint(this.queryParams).then((response) => {\r\n                this.achievement_jointList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                achievementId: null,\r\n                achievementName: null,\r\n                linkMan: null,\r\n                linkTel: null,\r\n                remark: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加成果合作\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getAchievement_joint(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改成果合作\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.id != null) {\r\n                        updateAchievement_joint(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addAchievement_joint(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除成果合作编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delAchievement_joint(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/achievement_joint/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `achievement_joint_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}