{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\demand\\index.vue", "mtime": 1750151094254}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3REZW1hbmQsDQogICAgZ2V0RGVtYW5kLA0KICAgIGRlbERlbWFuZCwNCiAgICBhZGREZW1hbmQsDQogICAgdXBkYXRlRGVtYW5kLA0KfSBmcm9tICJAL2FwaS91dWMvZGVtYW5kIjsNCmltcG9ydCB7DQogICAgYWRkRGVtYW5kX2ZvbGxvdywNCn0gZnJvbSAiQC9hcGkvdXVjL2RlbWFuZF9mb2xsb3ciOw0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJEZW1hbmQiLA0KICAgIGRpY3RzOiBbInV1Y19vbmxpbmUiXSwNCiAgICBkYXRhKCkgew0KICAgICAgICBsZXQgY2hlY2tQaG9uZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgICAgICAgIGxldCByZWcgPSAvXjFbMzQ1Nzg5XVxkezl9JC87DQogICAgICAgICAgICBpZiAoIXJlZy50ZXN0KHZhbHVlKSkgew0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6K+36L6T5YWlMTHkvY3miYvmnLrlj7ciKSk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgICAgICB9DQogICAgICAgIH07DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAvLyDpga7nvanlsYINCiAgICAgICAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAgICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgICAgICAgIGlkczogW10sDQogICAgICAgICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgICAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAgICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgICAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAgICAgICAvLyDmgLvmnaHmlbANCiAgICAgICAgICAgIHRvdGFsOiAwLA0KICAgICAgICAgICAgLy8g6ZyA5rGC566h55CG6KGo5qC85pWw5o2uDQogICAgICAgICAgICBkZW1hbmRMaXN0OiBbXSwNCiAgICAgICAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgICAgIG9wZW4xOiBmYWxzZSwNCiAgICAgICAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgICAgICAgICB0aXRsZTogbnVsbCwNCiAgICAgICAgICAgICAgICBidHlwZU5hbWU6IG51bGwsDQogICAgICAgICAgICAgICAgbGlua21hbjogbnVsbCwNCiAgICAgICAgICAgICAgICByZWNvbW1lbmRTdGF0dXM6IG51bGwsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICAgICAgICBmb3JtOiB7fSwNCiAgICAgICAgICAgIGZvcm0xOiB7fSwNCiAgICAgICAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgICAgICAgcnVsZXM6IHsNCiAgICAgICAgICAgICAgICB0aXRsZTogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmoIfpopjkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgbGlua21hbjogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLogZTns7vkurrkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgcGhvbmU6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6IGU57O755S16K+d5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICAgICAgICAgICAgICB2YWxpZGF0b3I6IGNoZWNrUGhvbmUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+3IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHJ1bGVzMTogew0KICAgICAgICAgICAgICAgIGRlbWFuZElkOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIumcgOaxgmlk5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIGRlbWFuZFRpdGxlOiBbDQogICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIumcgOaxguagh+mimOS4jeiDveS4uuepuiIsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICBwcmluY2lwYWw6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6LSf6LSj5Lq65LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgICAvLyDot5/ov5sNCiAgICAgICAgaGFuZGxlRm9sbG93KGl0ZW0pIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIHRoaXMuZm9ybTEgPSB7DQogICAgICAgICAgICAgICAgZGVtYW5kSWQ6IGl0ZW0uaWQsDQogICAgICAgICAgICAgICAgZGVtYW5kVGl0bGU6IGl0ZW0udGl0bGUsDQogICAgICAgICAgICAgICAgcHJpbmNpcGFsOiBudWxsLA0KICAgICAgICAgICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICB0aGlzLm9wZW4xID0gdHJ1ZTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOafpeivoumcgOaxgueuoeeQhuWIl+ihqCAqLw0KICAgICAgICBnZXRMaXN0KCkgew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgIGxpc3REZW1hbmQodGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmRlbWFuZExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlj5bmtojmjInpkq4NCiAgICAgICAgY2FuY2VsKCkgew0KICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgIH0sDQogICAgICAgIGNhbmNlbDEoKXsNCiAgICAgICAgICAgIHRoaXMub3BlbjEgPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g6KGo5Y2V6YeN572uDQogICAgICAgIHJlc2V0KCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICAgIGlkOiBudWxsLA0KICAgICAgICAgICAgICAgIHRpdGxlOiBudWxsLA0KICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBudWxsLA0KICAgICAgICAgICAgICAgIHBob25lOiBudWxsLA0KICAgICAgICAgICAgICAgIGJ0eXBlTmFtZTogbnVsbCwNCiAgICAgICAgICAgICAgICBsaW5rbWFuOiBudWxsLA0KICAgICAgICAgICAgICAgIHByb0Nvc3Q6IG51bGwsDQogICAgICAgICAgICAgICAgcmVjb21tZW5kU3RhdHVzOiAiMCIsDQogICAgICAgICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICAgICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgICAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICB0aGlzLmZvcm0xID0gew0KICAgICAgICAgICAgICAgIGRlbWFuZElkOiBudWxsLA0KICAgICAgICAgICAgICAgIGRlbWFuZFRpdGxlOiBudWxsLA0KICAgICAgICAgICAgICAgIHByaW5jaXBhbDogbnVsbCwNCiAgICAgICAgICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtMSIpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgICAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICAgICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpOw0KICAgICAgICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOmcgOaxgueuoeeQhiI7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICAgICAgY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICAgICAgICBnZXREZW1hbmQoaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICAgICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56ZyA5rGC566h55CGIjsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICAgICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmZvcm0ucGhvbmUpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCByZWcgPSAvXjFbMzQ1Nzg5XVxkezl9JC87DQogICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXJlZy50ZXN0KHRoaXMuZm9ybS5waG9uZSkpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36L6T5YWl5q2j56Gu5omL5py65Y+3Iik7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRGVtYW5kKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgYWRkRGVtYW5kKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIHN1Ym1pdEZvcm0xKCkgew0KICAgICAgICAgICAgdGhpcy4kcmVmc1siZm9ybTEiXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAgICAgICAgICAgYWRkRGVtYW5kX2ZvbGxvdyh0aGlzLmZvcm0xKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6Lef6L+b5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW4xID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6ZyA5rGC566h55CG57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpDQogICAgICAgICAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGVsRGVtYW5kKGlkcyk7DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgICAgICAgdGhpcy5kb3dubG9hZCgNCiAgICAgICAgICAgICAgICAidXVjL2RlbWFuZC9leHBvcnQiLA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIGBkZW1hbmRfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGANCiAgICAgICAgICAgICk7DQogICAgICAgIH0sDQogICAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/ningmengdou/demand", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"标题\" prop=\"title\">\r\n                <el-input\r\n                    v-model=\"queryParams.title\"\r\n                    placeholder=\"请输入标题\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"公司名称\" prop=\"btypeName\">\r\n                <el-input\r\n                    v-model=\"queryParams.btypeName\"\r\n                    placeholder=\"请输入公司名称\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                <el-input\r\n                    v-model=\"queryParams.linkman\"\r\n                    placeholder=\"请输入联系人\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"recommendStatus\">\r\n                <el-select\r\n                    v-model=\"queryParams.recommendStatus\"\r\n                    placeholder=\"请选择状态\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_online\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:demand:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:demand:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:demand:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['uuc:demand:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"demandList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n            <el-table-column label=\"标题\" align=\"center\" prop=\"title\" />\r\n            <el-table-column label=\"描述\" align=\"center\" prop=\"description\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"phone\" />\r\n            <el-table-column label=\"公司名称\" align=\"center\" prop=\"btypeName\" />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"linkman\" />\r\n            <el-table-column label=\"预算费用\" align=\"center\" prop=\"proCost\" />\r\n            <el-table-column label=\"状态\" align=\"center\" prop=\"recommendStatus\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_online\"\r\n                        :value=\"scope.row.recommendStatus\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:demand:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:demand:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleFollow(scope.row)\"\r\n                        v-hasPermi=\"['uuc:demand:edit']\"\r\n                        >跟进</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改需求管理对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n                <el-form-item label=\"标题\" prop=\"title\">\r\n                    <el-input\r\n                        v-model=\"form.title\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入标题\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"描述\" prop=\"description\">\r\n                    <el-input\r\n                        v-model=\"form.description\"\r\n                        maxlength=\"200\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                    <el-input\r\n                        v-model=\"form.phone\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称\" prop=\"btypeName\">\r\n                    <el-input\r\n                        v-model=\"form.btypeName\"\r\n                        maxlength=\"50\"\r\n                        placeholder=\"请输入公司名称\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                    <el-input\r\n                        v-model=\"form.linkman\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入联系人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"预算费用\" prop=\"proCost\">\r\n                    <el-input\r\n                        v-model=\"form.proCost\"\r\n                        type=\"number\"\r\n                        min=\"0\"\r\n                        placeholder=\"请输入预算费用\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\">\r\n                    <el-radio-group v-model=\"form.recommendStatus\">\r\n                        <el-radio\r\n                            v-for=\"dict in dict.type.uuc_online\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.value\"\r\n                            >{{ dict.label }}</el-radio\r\n                        >\r\n                    </el-radio-group>\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        maxlength=\"200\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <!-- 跟进 -->\r\n        <el-dialog\r\n            title=\"需求跟进\"\r\n            :visible.sync=\"open1\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form\r\n                ref=\"form1\"\r\n                :model=\"form1\"\r\n                :rules=\"rules1\"\r\n                label-width=\"80px\"\r\n            >\r\n                <el-form-item label=\"需求id\" prop=\"demandId\">\r\n                    <el-input\r\n                        disabled\r\n                        v-model=\"form1.demandId\"\r\n                        maxlength=\"20\"\r\n                        type=\"number\"\r\n                        placeholder=\"请输入需求id\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n                    <el-input\r\n                        disabled\r\n                        v-model=\"form1.demandTitle\"\r\n                        maxlength=\"200\"\r\n                        placeholder=\"请输入需求标题\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"负责人\" prop=\"principal\">\r\n                    <el-input\r\n                        v-model=\"form1.principal\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入负责人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form1.remark\"\r\n                        maxlength=\"500\"\r\n                        type=\"textarea\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm1\">确 定</el-button>\r\n                <el-button @click=\"cancel1\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listDemand,\r\n    getDemand,\r\n    delDemand,\r\n    addDemand,\r\n    updateDemand,\r\n} from \"@/api/uuc/demand\";\r\nimport {\r\n    addDemand_follow,\r\n} from \"@/api/uuc/demand_follow\";\r\nexport default {\r\n    name: \"Demand\",\r\n    dicts: [\"uuc_online\"],\r\n    data() {\r\n        let checkPhone = (rule, value, callback) => {\r\n            let reg = /^1[345789]\\d{9}$/;\r\n            if (!reg.test(value)) {\r\n                callback(new Error(\"请输入11位手机号\"));\r\n            } else {\r\n                callback();\r\n            }\r\n        };\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 需求管理表格数据\r\n            demandList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            open1: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                title: null,\r\n                btypeName: null,\r\n                linkman: null,\r\n                recommendStatus: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            form1: {},\r\n            // 表单校验\r\n            rules: {\r\n                title: [\r\n                    {\r\n                        required: true,\r\n                        message: \"标题不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                linkman: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                phone: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系电话不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    {\r\n                        type: \"number\",\r\n                        validator: checkPhone,\r\n                        message: \"请输入正确的手机号\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n            rules1: {\r\n                demandId: [\r\n                    {\r\n                        required: true,\r\n                        message: \"需求id不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                demandTitle: [\r\n                    {\r\n                        required: true,\r\n                        message: \"需求标题不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                principal: [\r\n                    {\r\n                        required: true,\r\n                        message: \"负责人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        // 跟进\r\n        handleFollow(item) {\r\n            this.reset();\r\n            this.form1 = {\r\n                demandId: item.id,\r\n                demandTitle: item.title,\r\n                principal: null,\r\n                remark: null,\r\n            };\r\n            this.open1 = true;\r\n        },\r\n        /** 查询需求管理列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listDemand(this.queryParams).then((response) => {\r\n                this.demandList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        cancel1(){\r\n            this.open1 = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                title: null,\r\n                description: null,\r\n                phone: null,\r\n                btypeName: null,\r\n                linkman: null,\r\n                proCost: null,\r\n                recommendStatus: \"0\",\r\n                remark: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.form1 = {\r\n                demandId: null,\r\n                demandTitle: null,\r\n                principal: null,\r\n                remark: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n            this.resetForm(\"form1\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加需求管理\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getDemand(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改需求管理\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.phone) {\r\n                        let reg = /^1[345789]\\d{9}$/;\r\n                        if (!reg.test(this.form.phone)) {\r\n                            this.$modal.msgError(\"请输入正确手机号\");\r\n                            return;\r\n                        }\r\n                    }\r\n                    if (this.form.id != null) {\r\n                        updateDemand(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addDemand(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        submitForm1() {\r\n            this.$refs[\"form1\"].validate((valid) => {\r\n                if (valid) {\r\n                    addDemand_follow(this.form1).then((response) => {\r\n                        this.$modal.msgSuccess(\"跟进成功\");\r\n                        this.open1 = false;\r\n                        this.getList();\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除需求管理编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delDemand(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/demand/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `demand_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}