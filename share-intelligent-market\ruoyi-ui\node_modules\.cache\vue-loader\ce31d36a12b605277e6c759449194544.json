{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\InnerLink\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\InnerLink\\index.vue", "mtime": 1750151094167}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHt9Ow0KICB9LA0KICByZW5kZXIoKSB7DQogICAgY29uc3QgeyAkcm91dGU6IHsgbWV0YTogeyBsaW5rIH0gfSwgfSA9IHRoaXM7DQogICAgaWYgKHsgbGluayB9LmxpbmsgPT09ICIiKSB7DQogICAgICByZXR1cm4gIjQwNCI7DQogICAgfQ0KICAgIGxldCB1cmwgPSB7IGxpbmsgfS5saW5rOw0KICAgIGNvbnN0IGhlaWdodCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSA5NC41ICsgInB4IjsNCiAgICBjb25zdCBzdHlsZSA9IHsgaGVpZ2h0OiBoZWlnaHQgfTsNCg0KICAgIHJldHVybiAoDQogICAgICA8ZGl2IHN0eWxlPXtzdHlsZX0+DQogICAgICAgIDxpZnJhbWUNCiAgICAgICAgICBzcmM9e3VybH0NCiAgICAgICAgICBmcmFtZWJvcmRlcj0ibm8iDQogICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlOyBoZWlnaHQ6IDEwMCUiDQogICAgICAgICAgc2Nyb2xsaW5nPSJhdXRvIg0KICAgICAgICA+PC9pZnJhbWU+DQogICAgICA8L2Rpdj4NCiAgICApOw0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/InnerLink", "sourcesContent": ["<script>\r\nexport default {\r\n  data() {\r\n    return {};\r\n  },\r\n  render() {\r\n    const { $route: { meta: { link } }, } = this;\r\n    if ({ link }.link === \"\") {\r\n      return \"404\";\r\n    }\r\n    let url = { link }.link;\r\n    const height = document.documentElement.clientHeight - 94.5 + \"px\";\r\n    const style = { height: height };\r\n\r\n    return (\r\n      <div style={style}>\r\n        <iframe\r\n          src={url}\r\n          frameborder=\"no\"\r\n          style=\"width: 100%; height: 100%\"\r\n          scrolling=\"auto\"\r\n        ></iframe>\r\n      </div>\r\n    );\r\n  },\r\n};\r\n</script>\r\n"]}]}