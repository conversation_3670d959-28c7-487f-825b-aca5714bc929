{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\components\\orderDetails.vue?vue&type=style&index=0&id=497ffffc&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\components\\orderDetails.vue", "mtime": 1750151094267}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750495809569}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouaXRlbS1mb3JtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KfQ0KDQoud2lyZSB7DQogIGJhY2tncm91bmQ6ICNjY2M7DQogIGhlaWdodDogMXB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoucmVkIHsNCiAgY29sb3I6IHJlZDsNCn0NCg=="}, {"version": 3, "sources": ["orderDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsrBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "orderDetails.vue", "sourceRoot": "src/views/order/components", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-dialog\r\n      width=\"80%\"\r\n      title=\"订单详情\"\r\n      :visible.sync=\"dialogVisible\"\r\n      append-to-body\r\n      center\r\n    >\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        title=\"基本信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"订单类型\">{{\r\n          form.order_type_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单状态\">\r\n          <el-tag size=\"mini\" type=\"primary\">{{ form.status_str }}</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"订单号\">{{\r\n          form.order_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"下单时间\">{{\r\n          form.create_time\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"付款方式\">{{\r\n          form.payment_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采状态\">{{\r\n          form.central_status_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采付款状态\">{{\r\n          form.central_pay_status_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"操作员\">{{\r\n          form.operator\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单总价\">{{\r\n          form.total_price\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采定金\">{{\r\n          form.deposit\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"物流单号\">{{\r\n          form.logistics_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"合同编号\">{{\r\n          form.contract_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"地址\">{{\r\n          form.address\r\n        }}</el-descriptions-item>\r\n        <!-- <el-descriptions-item label=\"订单条款\">{{\r\n                    form.terms\r\n                }}</el-descriptions-item> -->\r\n        <el-descriptions-item label=\"订单备注\">{{\r\n          form.remark\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        style=\"margin-top: 20px\"\r\n        title=\"需方信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"需方名称\">{{\r\n          form.demand_name\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方代理\">{{\r\n          form.demand_proxy\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方电话\">{{\r\n          form.demand_phone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方地址\">{{\r\n          form.demand_location\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        style=\"margin-top: 20px\"\r\n        title=\"供方信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"供方名称\">{{\r\n          form.supply_name\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方代理\">{{\r\n          form.supply_proxy\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方电话\">{{\r\n          form.supply_phone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方地址\">{{\r\n          form.supply_location\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-tabs v-model=\"activetab\" type=\"card\" style=\"margin-top: 20px\">\r\n        <el-tab-pane label=\"订单明细\" name=\"items\">\r\n          <el-table :data=\"form.items\">\r\n            <el-table-column label=\"物料分类\" align=\"center\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.classify_name }}-{{ scope.row.classify2_name }}-{{\r\n                  scope.row.classify3_name\r\n                }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料编号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_name\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"规格\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"specs\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"单位\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"unit\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物品数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"已发数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"shiped_quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"品牌\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"brand\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"包装\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pack\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"税率\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"tax_rate\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"商品总价\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"total_price\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"运费\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"freight\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"明细总价\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"total_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"期望交货日期\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"delivery_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"交货日期\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"delivered_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              fixed=\"right\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.shiped_quantity < scope.row.quantity\"\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleDeliver(scope.row)\"\r\n                  >确认发货</el-button\r\n                >\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"付款申请单\" name=\"pay\">\r\n          <el-table :data=\"pays\">\r\n            <el-table-column\r\n              label=\"申请单号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"request_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"申请时间\"\r\n              align=\"center\"\r\n              width=\"160\"\r\n              prop=\"create_time\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"申请金额\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"total_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付方式\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"paymentStr\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"币种\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"currency\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付比例\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_ratio\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付金额\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付状态\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_statusStr\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付时间\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"pay_time\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付凭证\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"pay_proof\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-image\r\n                  v-if=\"scope.row.pay_proof\"\r\n                  style=\"width: 100px; height: 100px\"\r\n                  :src=\"scope.row.pay_proof\"\r\n                  :preview-src-list=\"[scope.row.pay_proof]\"\r\n                >\r\n                </el-image>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              align=\"center\"\r\n              width=\"80\"\r\n              fixed=\"right\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"\r\n                    scope.row.pay_status == 'WAIT' &&\r\n                    (scope.row.payment == 'OFFLINE' ||\r\n                      scope.row.payment == 'PERIOD')\r\n                  \"\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handlePay(scope.row)\"\r\n                  >确认</el-button\r\n                >\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"发货记录\" name=\"deliver\">\r\n          <el-table :data=\"delivers\">\r\n            <el-table-column\r\n              label=\"物流单号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"logistics_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"车牌号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"carnno\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"司机名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"linker\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"司机电话\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"linkphone\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              align=\"center\"\r\n              property=\"proof\"\r\n              label=\"发货凭证\"\r\n              width=\"100px\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"scope.row.proof\">\r\n                  <el-image\r\n                    style=\"width: 60px; height: 60px\"\r\n                    :src=\"scope.row.proof\"\r\n                    :preview-src-list=\"[scope.row.proof]\"\r\n                  >\r\n                  </el-image>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              align=\"center\"\r\n              property=\"proof\"\r\n              label=\"签收凭证\"\r\n              width=\"100px\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"scope.row.sproof\">\r\n                  <el-image\r\n                    style=\"width: 60px; height: 60px\"\r\n                    :src=\"scope.row.sproof\"\r\n                    :preview-src-list=\"[scope.row.sproof]\"\r\n                  >\r\n                  </el-image>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_name\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"单位\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"unit\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"规格\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"specs\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"品牌\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"brand\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"包装\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pack\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"期望交货日期\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"delivery_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"交货日期\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"delivered_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"发货数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"shipped_qty\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_qty\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收人\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_by\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收时间\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_time\"\r\n            >\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n    <el-dialog\r\n      width=\"40%\"\r\n      title=\"发货\"\r\n      :visible.sync=\"deliverOpen\"\r\n      append-to-body\r\n      center\r\n    >\r\n      <el-form\r\n        ref=\"dform\"\r\n        :rules=\"rules\"\r\n        :model=\"dform\"\r\n        label-width=\"110px\"\r\n        label-position=\"left\"\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"发货数量\" prop=\"shipped_qty\">\r\n              <el-input\r\n                type=\"number\"\r\n                v-model=\"dform.shipped_qty\"\r\n                placeholder=\"请输入发货数量\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物流号\" prop=\"logistics_no\">\r\n              <el-input\r\n                clearable\r\n                v-model=\"dform.logistics_no\"\r\n                placeholder=\"请输入物流号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"司机名称\" prop=\"linker\">\r\n              <el-input\r\n                v-model=\"dform.linker\"\r\n                placeholder=\"请输入司机名称\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"司机电话\" prop=\"linkphone\">\r\n              <el-input\r\n                clearable\r\n                v-model=\"dform.linkphone\"\r\n                placeholder=\"请输入物流号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"车牌号\" prop=\"carno\">\r\n              <el-input\r\n                v-model=\"dform.carno\"\r\n                placeholder=\"请输入车牌号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"发货凭证\" prop=\"proof\">\r\n              <ImageUpload\r\n                v-model=\"dform.proof\"\r\n                @input=\"getLogo\"\r\n                :limit=\"1\"\r\n                :isShowTip=\"false\"\r\n              ></ImageUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"deliverOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"opDeliver\" :loading=\"btnload\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getData,\r\n  payData,\r\n  deliverData,\r\n  confirmPay,\r\n  confirmDeliver,\r\n} from \"@/api/order/list\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      btnload: false,\r\n      form: {},\r\n      dform: {\r\n        logistics_no: \"\",\r\n        linker: \"\",\r\n        proof: \"\",\r\n        linkphone: \"\",\r\n      },\r\n      pays: [],\r\n      activetab: \"items\",\r\n      delivers: [],\r\n      dialogVisible: false,\r\n      deliverOpen: false,\r\n      rules: {\r\n        shipped_qty: [\r\n          {\r\n            required: true,\r\n            message: \"发货数量不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        logistics_no: [\r\n          {\r\n            required: true,\r\n            message: \"物料号不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {},\r\n  methods: {\r\n    getLogo(e) {\r\n      this.dform.proof = e;\r\n      if (this.dform.proof) {\r\n        this.$refs.dform.clearValidate(\"proof\");\r\n      }\r\n    },\r\n    open(orderId) {\r\n      this.dialogVisible = true;\r\n      getData(orderId).then((res) => {\r\n        this.form = res.data;\r\n      });\r\n      payData({\r\n        order_id: orderId,\r\n      }).then((res) => {\r\n        this.pays = res.data;\r\n      });\r\n      deliverData({\r\n        order_id: orderId,\r\n      }).then((res) => {\r\n        this.delivers = res.data;\r\n      });\r\n    },\r\n    handlePay(row) {\r\n      this.$confirm(\"是否确认该付款申请单?\", \"提示\", {\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        let data = {\r\n          id: row.id,\r\n        };\r\n        confirmPay(data).then((res) => {\r\n          this.$message({\r\n            type: \"success\",\r\n            message: \"操作成功!\",\r\n          });\r\n          row.pay_status = \"CONFIRM\";\r\n          row.pay_statusStr = \"已确认\";\r\n        });\r\n      });\r\n    },\r\n    handleDeliver(row) {\r\n      this.dform.order_item_id = row.id;\r\n      this.dform.order_id = row.order_id;\r\n      this.dform.left_qty = row.quantity - row.shiped_quantity;\r\n      this.dform.shipped_qty = row.quantity - row.shiped_quantity;\r\n      this.dform.logistics_no = \"\";\r\n      this.dform.linker = \"\";\r\n      this.dform.linkphone = \"\";\r\n      this.deliverOpen = true;\r\n    },\r\n    opDeliver() {\r\n      this.$refs.dform.validate((validate) => {\r\n        if (validate) {\r\n          if (this.dform.shipped_qty > this.dform.left_qty) {\r\n            this.$message({\r\n              type: \"error\",\r\n              message: \"发货数量不得多余总数量!\",\r\n            });\r\n            return;\r\n          }\r\n          this.btnload = true;\r\n          confirmDeliver(this.dform).then(() => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n            this.btnload = false;\r\n            this.deliverOpen = false;\r\n            this.open(this.dform.order_id);\r\n          });\r\n        } else {\r\n          this.$modal.msgError(\"请完善信息再提交!\");\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.item-form {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.wire {\r\n  background: #ccc;\r\n  height: 1px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.red {\r\n  color: red;\r\n}\r\n</style>\r\n"]}]}