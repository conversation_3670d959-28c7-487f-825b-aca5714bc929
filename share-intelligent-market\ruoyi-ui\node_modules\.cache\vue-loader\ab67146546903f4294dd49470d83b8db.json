{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\components\\orders.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\components\\orders.vue", "mtime": 1750151094225}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RyZXNvdXJjZXMgfSBmcm9tICdAL2FwaS9jZW50cmFsL2xpc3QnOwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlvLnnqpcKICAgICAgY29sbGVjdGlvbkRpYWxvZzogZmFsc2UsCiAgICAgIHRpdGxlOiAn6aKE57qm5YiX6KGoJwogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5YiX6KGoICovCiAgICBnZXRMaXN0KGlkKSB7CiAgICAgIGxpc3RyZXNvdXJjZXMoaWQpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmNvbGxlY3Rpb25EaWFsb2cgPSB0cnVlOwogICAgICAgIHRoaXMubGlzdCA9IHJlcy5kYXRhOwogICAgICB9KQogICAgfSwKICAgIC8qIOeCueWHu+ehruiupCAqLwogICAgc3VibWl0KCkgewogICAgICB0aGlzLmNvbGxlY3Rpb25EaWFsb2cgPSBmYWxzZTsKICAgIH0KICB9LAp9Owo="}, {"version": 3, "sources": ["orders.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "orders.vue", "sourceRoot": "src/views/central/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :title=\"title\" :visible.sync=\"collectionDialog\" width=\"90%\" center>\r\n      <el-table :data=\"list\">\r\n        <el-table-column type='index' label=\"序号\" align=\"center\"  width=\"50px\" />\r\n        <el-table-column label=\"公司名称\" align=\"center\" prop=\"demand_name\" />\r\n        <el-table-column label=\"联系人\" align=\"center\" prop=\"demand_proxy\" />\r\n        <el-table-column label=\"联系方式\" align=\"center\" prop=\"demand_phone\" />\r\n        <el-table-column label=\"联系地址\" align=\"center\" prop=\"demand_location\" />\r\n        <el-table-column label=\"集采数量\" align=\"center\" prop=\"total_number\" />\r\n        <el-table-column label=\"订金金额\" align=\"center\" prop=\"total_price\" />\r\n        <el-table-column label=\"订单号\" align=\"center\" prop=\"order_no\" />\r\n      </el-table>\r\n      <el-row>\r\n        <el-col :span=\"24\">\r\n          <div class=\"text-center mt-30\">\r\n            <el-button type=\"primary\" @click=\"submit\">关闭</el-button>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import { listresources } from '@/api/central/list';\r\n  export default {\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: false,\r\n        // 表格数据\r\n        list: [],\r\n        // 弹窗\r\n        collectionDialog: false,\r\n        title: '预约列表'\r\n      };\r\n    },\r\n    created() {\r\n    },\r\n    methods: {\r\n      /** 查询列表 */\r\n      getList(id) {\r\n        listresources(id).then(res => {\r\n          this.collectionDialog = true;\r\n          this.list = res.data;\r\n        })\r\n      },\r\n      /* 点击确认 */\r\n      submit() {\r\n        this.collectionDialog = false;\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style scoped>\r\n</style>\r\n"]}]}