{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\service\\banner.vue?vue&type=style&index=0&id=ffcb5540&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\service\\banner.vue", "mtime": 1750151094277}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5iYW5uZXItYm94IC5lbC11cGxvYWQtLXBpY3R1cmUtY2FyZCB7CiAgd2lkdGg6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["banner.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA;AACA;AACA", "file": "banner.vue", "sourceRoot": "src/views/service", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div>\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"addBanner\"\r\n          >添加轮播图</el-button>\r\n    </div>\r\n    <el-row>\r\n      <el-col :span='8' v-for=\"(item,index) in bannerList\" :key=\"index\">\r\n        <div class=\"banner-box mt-20\">\r\n          <ImageUpload @input=\"uploadSuccess($event,item)\" sizeTxt='1920X412' style=\"width: 100%\" :value='item.picture'\r\n            :limit='1'></ImageUpload>\r\n          <div class=\"banner-bottom\">\r\n            <div class=\"left\">排序</div>\r\n            <div class=\"right\">\r\n              <el-input style=\"width:200px;\" class='mr-10' type=\"number\" v-model=\"item.sorts\" placeholder=\"请输入排序\">\r\n              </el-input>\r\n              <el-button size=\"small\" type=\"danger\" @click='handleRemove(item, index)'>删除</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"banner-bottom\">\r\n            <div class=\"left\">链接</div>\r\n            <div class=\"right\">\r\n              <el-input clearable style=\"width:200px;\" class='mr-10' v-model=\"item.link\" placeholder=\"请输入链接\"></el-input>\r\n              <el-button size=\"small\" type=\"primary\" @click='handleSubmit(item, index)'>确认</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\n  import {\r\n    listData,\r\n    addData,\r\n    editData,\r\n    delData\r\n  } from '@/api/service/banner';\r\n  export default {\r\n    data() {\r\n      return {\r\n        bannerList: [],\r\n        form: {}\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n    },\r\n    methods: {\r\n      getList() {\r\n        listData().then(response => {\r\n          this.bannerList = response.data\r\n        });\r\n      },\r\n      addBanner() {\r\n        this.bannerList.push({\r\n          \"module\": \"infor\",\r\n          \"picture\": \"\",\r\n          \"link\": \"\",\r\n          \"sorts\": \"0\"\r\n        })\r\n      },\r\n      uploadSuccess(event,item){\r\n        item.picture = event\r\n      },\r\n      /* 点击确认 */\r\n      handleSubmit(item) {\r\n        if (!item.picture) {\r\n          this.$message({\r\n            type: 'error',\r\n            message: '请上传图片!'\r\n          });\r\n          return false;\r\n        }\r\n        if (!item.sorts) {\r\n          this.$message({\r\n            type: 'error',\r\n            message: '请填写排序!'\r\n          });\r\n          return false;\r\n        }\r\n        if (!item.id) {\r\n          addData(item).then(response => {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '操作成功!'\r\n            });\r\n            this.getList()\r\n          });\r\n        } else {\r\n          editData(item).then(response => {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '操作成功!'\r\n            });\r\n            this.getList()\r\n          });\r\n        }\r\n      },\r\n      /* 点击删除 */\r\n      handleRemove(item, index) {\r\n        this.$confirm('确定删除该条数据吗?', '提示', {\r\n          type: 'warning'\r\n        }).then(confirm => {\r\n          if (item.id) {\r\n            this.bannerList.splice(index, 1)\r\n            delData(item.id).then(response => {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '操作成功!'\r\n              });\r\n            });\r\n          } else {\r\n            this.bannerList.splice(index, 1)\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .banner-box .el-upload--picture-card {\r\n    width: 100%;\r\n  }\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n  .banner-box {\r\n    margin-right: 40px;\r\n    padding: 20px;\r\n    // height: 200px;\r\n    border: 1px solid #ccc;\r\n\r\n    .banner-bottom {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      margin-top: 10px;\r\n\r\n      .right {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        div {\r\n          margin-left: 10px;\r\n        }\r\n\r\n        .el-input {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  ::v-deep .el-upload-list--picture-card .el-upload-list__item {\r\n    width: 100% !important;\r\n    display: block !important;\r\n    margin: 0 !important;\r\n  }\r\n</style>\r\n"]}]}