package com.ruoyi.sso.service.impl;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.Member;
import com.ruoyi.shop.api.domain.MarketUser;
import com.ruoyi.sso.domain.SSOUser;
import com.ruoyi.sso.service.SSOUserSyncService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.RemoteMemberService;
import com.ruoyi.shop.api.RemoteMarketUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SSO用户同步服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SSOUserSyncServiceImpl implements SSOUserSyncService {

    private static final Logger log = LoggerFactory.getLogger(SSOUserSyncServiceImpl.class);

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteMemberService remoteMemberService;

    @Autowired
    private RemoteMarketUserService remoteMarketUserService;

    @Override
    public boolean syncToMainSystem(SSOUser ssoUser) {
        try {
            log.info("开始同步SSO用户到主系统Member表: {}", ssoUser.getUsername());

            // 1. 检查主系统Member表中是否已存在该用户（通过手机号查询）
            R<Member> memberResult = remoteMemberService.getMemberInfo(ssoUser.getPhone(), "inner");

            if (memberResult != null && memberResult.getData() != null) {
                // 用户已存在，更新用户信息
                Member existingMember = memberResult.getData();
                existingMember.setNickname(ssoUser.getNickname());
                // 注意：Member类可能没有setEmail方法，跳过email设置
                // existingMember.setEmail(ssoUser.getEmail());
                existingMember.setAvatar(ssoUser.getAvatar());

                // 注意：这里暂时跳过更新，因为没有找到对应的更新接口
                // 可以考虑后续添加更新接口或者直接返回成功
                log.info("主系统Member信息已存在，跳过更新: {}", ssoUser.getUsername());
                return true;
            } else {
                // 用户不存在，这是正常情况
                // 主系统用户应该通过注册流程创建，SSO登录时不创建主系统用户
                log.info("主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: {}", ssoUser.getUsername());
                return true;
            }

        } catch (Exception e) {
            log.error("同步用户到主系统异常: {}", ssoUser.getUsername(), e);
            return false;
        }
    }

    @Override
    public boolean syncToMarketSystem(SSOUser ssoUser) {
        try {
            log.info("开始同步SSO用户到市场系统tb_user表: {}", ssoUser.getUsername());

            // 1. 检查市场系统tb_user表中是否已存在该用户（通过手机号查询）
            R<MarketUser> marketUserResult = remoteMarketUserService.getUserInfoByTelphone(ssoUser.getPhone(), "inner");

            if (marketUserResult != null && marketUserResult.getData() != null) {
                // 用户已存在，更新用户信息
                MarketUser existingUser = marketUserResult.getData();
                // 注意：这里暂时跳过更新，因为没有找到对应的更新接口
                // 可以考虑后续添加更新接口或者直接返回成功
                log.info("市场系统tb_user信息已存在，跳过更新: {}", ssoUser.getUsername());
                return true;
            } else {
                // 用户不存在，这是正常情况
                // 市场系统用户应该通过注册流程创建，SSO登录时不创建市场系统用户
                log.info("市场系统tb_user不存在，这是正常情况（用户可能是从其他系统注册的）: {}", ssoUser.getUsername());
                return true;
            }

        } catch (Exception e) {
            log.error("同步用户到市场系统异常: {}", ssoUser.getUsername(), e);
            return false;
        }
    }

    @Override
    public boolean syncToAllSystems(SSOUser ssoUser) {
        boolean mainSystemResult = syncToMainSystem(ssoUser);
        boolean marketSystemResult = syncToMarketSystem(ssoUser);
        
        return mainSystemResult && marketSystemResult;
    }

    /**
     * 将SSO用户转换为主系统Member对象
     */
    private Member createMemberFromSSO(SSOUser ssoUser) {
        Member member = new Member();
        member.setMemberPhone(ssoUser.getPhone());
        member.setMemberRealName(ssoUser.getNickname());
        member.setNickname(ssoUser.getNickname());
        // 注意：Member类可能没有setEmail方法，跳过email设置
        // member.setEmail(ssoUser.getEmail());
        member.setAvatar(ssoUser.getAvatar());
        member.setMemberStatus("0"); // 正常状态
        member.setRemark("SSO自动创建Member");
        return member;
    }

    /**
     * 将SSO用户转换为市场系统MarketUser对象
     */
    private MarketUser createMarketUserFromSSO(SSOUser ssoUser) {
        MarketUser marketUser = new MarketUser();
        marketUser.setTelphone(ssoUser.getPhone());
        marketUser.setType("ADMIN"); // 默认管理员类型
        marketUser.setStatus(1); // 正常状态
        marketUser.setRemark("SSO自动创建用户");
        return marketUser;
    }

    /**
     * 为新Member分配默认角色
     */
    private void assignDefaultRoleToMember(String phone) {
        try {
            // TODO: 调用角色分配服务，为Member分配默认角色
            log.info("为Member {} 分配默认角色", phone);
        } catch (Exception e) {
            log.error("为Member分配默认角色失败: {}", phone, e);
        }
    }

    /**
     * 为新用户分配默认角色
     */
    private void assignDefaultRoleToUser(String username) {
        try {
            // TODO: 调用角色分配服务，为用户分配默认角色
            // 例如：普通用户角色
            log.info("为用户 {} 分配默认角色", username);
        } catch (Exception e) {
            log.error("为用户分配默认角色失败: {}", username, e);
        }
    }
}
