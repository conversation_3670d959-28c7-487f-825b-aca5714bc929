11:48:43.526 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:48:44.795 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c9c2d341-feaa-4c20-9397-ede64a20039a_config-0
11:48:44.892 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 51 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:44.948 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:44.964 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:45.246 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 279 ms to scan 248 urls, producing 0 keys and 0 values 
11:48:45.256 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:45.271 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:45.286 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:45.497 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 208 ms to scan 248 urls, producing 0 keys and 0 values 
11:48:45.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:45.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/2010546406
11:48:45.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/1242874959
11:48:45.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:45.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:45.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:47.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477727494_127.0.0.1_64187
11:48:47.800 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] Notify connected event to listeners.
11:48:47.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:47.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9c2d341-feaa-4c20-9397-ede64a20039a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/572137576
11:48:47.971 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
