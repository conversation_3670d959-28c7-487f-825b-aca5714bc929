{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\mixin\\ResizeHandler.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\mixin\\ResizeHandler.js", "mtime": 1750151094185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_document", "document", "body", "WIDTH", "_default", "exports", "default", "watch", "$route", "route", "device", "sidebar", "opened", "store", "dispatch", "withoutAnimation", "beforeMount", "window", "addEventListener", "$_resizeHandler", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "mounted", "isMobile", "$_isMobile", "methods", "rect", "getBoundingClientRect", "width", "hidden"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/layout/mixin/ResizeHandler.js"], "sourcesContent": ["import store from '@/store'\r\n\r\nconst { body } = document\r\nconst WIDTH = 992 // refer to Bootstrap's responsive design\r\n\r\nexport default {\r\n  watch: {\r\n    $route(route) {\r\n      if (this.device === 'mobile' && this.sidebar.opened) {\r\n        store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.$_resizeHandler)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.$_resizeHandler)\r\n  },\r\n  mounted() {\r\n    const isMobile = this.$_isMobile()\r\n    if (isMobile) {\r\n      store.dispatch('app/toggleDevice', 'mobile')\r\n      store.dispatch('app/closeSideBar', { withoutAnimation: true })\r\n    }\r\n  },\r\n  methods: {\r\n    // use $_ for mixins properties\r\n    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential\r\n    $_isMobile() {\r\n      const rect = body.getBoundingClientRect()\r\n      return rect.width - 1 < WIDTH\r\n    },\r\n    $_resizeHandler() {\r\n      if (!document.hidden) {\r\n        const isMobile = this.$_isMobile()\r\n        store.dispatch('app/toggleDevice', isMobile ? 'mobile' : 'desktop')\r\n\r\n        if (isMobile) {\r\n          store.dispatch('app/closeSideBar', { withoutAnimation: true })\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAiBC,QAAQ;EAAjBC,IAAI,GAAAF,SAAA,CAAJE,IAAI;AACZ,IAAMC,KAAK,GAAG,GAAG,EAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEH;EACbC,KAAK,EAAE;IACLC,MAAM,WAANA,MAAMA,CAACC,KAAK,EAAE;MACZ,IAAI,IAAI,CAACC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,EAAE;QACnDC,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;UAAEC,gBAAgB,EAAE;QAAM,CAAC,CAAC;MACjE;IACF;EACF,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAC;EACzD,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACdH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAAC;EAC5D,CAAC;EACDG,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAClC,IAAID,QAAQ,EAAE;MACZV,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC;MAC5CD,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;QAAEC,gBAAgB,EAAE;MAAK,CAAC,CAAC;IAChE;EACF,CAAC;EACDU,OAAO,EAAE;IACP;IACA;IACAD,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAME,IAAI,GAAGxB,IAAI,CAACyB,qBAAqB,CAAC,CAAC;MACzC,OAAOD,IAAI,CAACE,KAAK,GAAG,CAAC,GAAGzB,KAAK;IAC/B,CAAC;IACDgB,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAClB,QAAQ,CAAC4B,MAAM,EAAE;QACpB,IAAMN,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;QAClCX,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAES,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;QAEnE,IAAIA,QAAQ,EAAE;UACZV,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;YAAEC,gBAAgB,EAAE;UAAK,CAAC,CAAC;QAChE;MACF;IACF;EACF;AACF,CAAC", "ignoreList": []}]}