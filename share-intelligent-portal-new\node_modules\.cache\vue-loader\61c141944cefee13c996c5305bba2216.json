{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue?vue&type=style&index=0&id=08fc19dd&scoped=true&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\sso-callback.vue", "mtime": 1750413626109}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5zc28tY2FsbGJhY2stY29udGFpbmVyIHsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMHZoOwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwp9CgoubG9hZGluZy1jb250ZW50IHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgY29sb3I6IHdoaXRlOwp9CgoubG9hZGluZy1zcGlubmVyIHsKICB3aWR0aDogNTBweDsKICBoZWlnaHQ6IDUwcHg7CiAgYm9yZGVyOiA0cHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOwogIGJvcmRlci10b3A6IDRweCBzb2xpZCB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA1MCU7CiAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTsKICBtYXJnaW46IDAgYXV0byAyMHB4Owp9CgpAa2V5ZnJhbWVzIHNwaW4gewogIDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH0KICAxMDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfQp9CgoubG9hZGluZy10ZXh0IHsKICBmb250LXNpemU6IDE4cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKfQo="}, {"version": 3, "sources": ["sso-callback.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "sso-callback.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"sso-callback-container\">\n    <div class=\"loading-content\">\n      <div class=\"loading-spinner\"></div>\n      <div class=\"loading-text\">{{ loadingText }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { handleSSOCallback } from \"@/api/login\";\n\nexport default {\n  name: \"SSOCallback\",\n  data() {\n    return {\n      loadingText: \"正在处理SSO登录...\"\n    };\n  },\n  mounted() {\n    this.handleCallback();\n  },\n  methods: {\n    handleCallback() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const token = urlParams.get('token');\n      const redirect = urlParams.get('redirect');\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n\n      // 如果有token参数，说明是从后端SSO回调重定向过来的\n      if (token) {\n        this.loadingText = \"正在设置登录状态...\";\n\n        try {\n          // 设置token到store\n          this.$store.commit(\"SET_TOKEN\", token);\n\n          this.loadingText = \"登录成功，正在跳转...\";\n          this.$message.success(\"SSO登录成功\");\n\n          // 跳转到目标页面或首页\n          const redirectUrl = redirect || '/';\n          setTimeout(() => {\n            this.$router.push(redirectUrl);\n          }, 1000);\n\n        } catch (error) {\n          console.error(\"设置登录状态失败:\", error);\n          this.loadingText = \"登录状态设置失败\";\n          this.$message.error(\"登录状态设置失败\");\n          setTimeout(() => {\n            this.$router.push('/login');\n          }, 2000);\n        }\n        return;\n      }\n\n      // 如果没有token但有code，说明是旧的API回调方式\n      if (code) {\n        this.loadingText = \"正在验证授权码...\";\n\n        handleSSOCallback(code, state)\n          .then(response => {\n            if (response.code === 200) {\n              this.loadingText = \"登录成功，正在跳转...\";\n              this.$message.success(\"SSO登录成功\");\n\n              // 设置token\n              if (response.data && response.data.access_token) {\n                this.$store.commit(\"SET_TOKEN\", response.data.access_token);\n              }\n\n              // 跳转到目标页面或首页\n              const redirectUrl = state || '/';\n              setTimeout(() => {\n                this.$router.push(redirectUrl);\n              }, 1000);\n            } else {\n              this.loadingText = \"SSO登录失败\";\n              this.$message.error(response.msg || \"SSO登录失败\");\n              setTimeout(() => {\n                this.$router.push('/login');\n              }, 2000);\n            }\n          })\n          .catch(error => {\n            console.error(\"SSO回调处理失败:\", error);\n            this.loadingText = \"SSO登录异常\";\n            this.$message.error(\"SSO登录服务异常\");\n            setTimeout(() => {\n              this.$router.push('/login');\n            }, 2000);\n          });\n        return;\n      }\n\n      // 既没有token也没有code\n      this.loadingText = \"SSO登录失败：缺少必要参数\";\n      this.$message.error(\"SSO登录失败：缺少必要参数\");\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.sso-callback-container {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 18px;\n  font-weight: 500;\n}\n</style>\n"]}]}