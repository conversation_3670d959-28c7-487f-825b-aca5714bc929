09:11:32.197 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:11:34.304 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 463d8456-5cca-436d-af27-ac1e70116b7d_config-0
09:11:34.506 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 104 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:34.630 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:34.657 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:35.054 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 391 ms to scan 293 urls, producing 0 keys and 0 values 
09:11:35.072 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:35.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:35.121 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:35.452 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 327 ms to scan 293 urls, producing 0 keys and 0 values 
09:11:35.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:35.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1183701566
09:11:35.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428664849
09:11:35.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:35.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:35.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:39.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381899031_127.0.0.1_50119
09:11:39.431 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] Notify connected event to listeners.
09:11:39.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:39.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [463d8456-5cca-436d-af27-ac1e70116b7d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/552266488
09:11:39.696 [main] INFO  c.r.s.RuoYiShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:11:45.851 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,53] - Automatically configure Seata
09:11:45.864 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is registry
09:11:46.102 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
09:11:46.134 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is file.conf
09:11:46.136 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
09:11:46.441 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,189] - Initializing Global Transaction Clients ... 
09:11:46.983 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
09:11:46.983 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,197] - Transaction Manager Client is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
09:11:47.022 [main] INFO  i.s.r.d.AsyncWorker - [<init>,71] - Async Commit Buffer Limit: 10000
09:11:47.023 [main] INFO  i.s.r.d.x.ResourceManagerXA - [init,40] - ResourceManagerXA init ...
09:11:47.047 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
09:11:47.047 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,202] - Resource Manager is initialized. applicationId[ruoyi-shop] txServiceGroup[ruoyi-shop-seata-service-group]
09:11:47.047 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,206] - Global Transaction Clients are initialized. 
09:11:49.386 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9707"]
09:11:49.388 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:11:49.388 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:11:49.828 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:11:51.336 [main] INFO  i.s.s.a.d.SeataAutoDataSourceProxyCreator - [getAdvicesAndAdvisorsForBean,47] - Auto proxy of [dataSource]
09:11:52.106 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
09:12:01.030 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:01.697 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5ad87b0f-f00d-470c-9d11-eddd5524e3f7
09:12:01.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] RpcClient init label, labels = {module=naming, source=sdk}
09:12:01.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:01.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:01.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:01.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:12:01.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381921709_127.0.0.1_50582
09:12:01.817 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Notify connected event to listeners.
09:12:01.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:01.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$345/552266488
09:12:05.023 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9707"]
09:12:05.068 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-shop 192.168.0.68:9707 register finished
09:12:05.368 [main] INFO  c.r.s.RuoYiShopApplication - [logStarted,61] - Started RuoYiShopApplication in 34.85 seconds (JVM running for 37.2)
09:12:05.446 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop-dev.yaml, group=DEFAULT_GROUP
09:12:05.446 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop, group=DEFAULT_GROUP
09:12:05.446 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-shop.yaml, group=DEFAULT_GROUP
09:12:05.610 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:12:05.615 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad87b0f-f00d-470c-9d11-eddd5524e3f7] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:12:06.142 [RMI TCP Connection(16)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:39:46.410 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:39:46.411 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:39:46.761 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:39:46.761 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6b94f170[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:39:46.762 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750381921709_127.0.0.1_50582
12:39:46.768 [nacos-grpc-client-executor-2508] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750381921709_127.0.0.1_50582]Ignore complete event,isRunning:false,isAbandon=false
12:39:46.849 [SpringContextShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7ad38b2e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2509]
12:39:47.251 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
12:39:47.265 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
