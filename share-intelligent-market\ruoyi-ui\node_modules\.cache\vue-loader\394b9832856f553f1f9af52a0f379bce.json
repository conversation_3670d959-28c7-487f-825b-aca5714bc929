{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\CodeTypeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\CodeTypeDialog.vue", "mtime": 1750151094307}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIGluaGVyaXRBdHRyczogZmFsc2UsDQogIHByb3BzOiBbJ3Nob3dGaWxlTmFtZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBmb3JtRGF0YTogew0KICAgICAgICBmaWxlTmFtZTogdW5kZWZpbmVkLA0KICAgICAgICB0eXBlOiAnZmlsZScNCiAgICAgIH0sDQogICAgICBydWxlczogew0KICAgICAgICBmaWxlTmFtZTogW3sNCiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5paH5Lu25ZCNJywNCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicNCiAgICAgICAgfV0sDQogICAgICAgIHR5cGU6IFt7DQogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgbWVzc2FnZTogJ+eUn+aIkOexu+Wei+S4jeiDveS4uuepuicsDQogICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZScNCiAgICAgICAgfV0NCiAgICAgIH0sDQogICAgICB0eXBlT3B0aW9uczogW3sNCiAgICAgICAgbGFiZWw6ICfpobXpnaInLA0KICAgICAgICB2YWx1ZTogJ2ZpbGUnDQogICAgICB9LCB7DQogICAgICAgIGxhYmVsOiAn5by556qXJywNCiAgICAgICAgdmFsdWU6ICdkaWFsb2cnDQogICAgICB9XQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgfSwNCiAgd2F0Y2g6IHt9LA0KICBtb3VudGVkKCkge30sDQogIG1ldGhvZHM6IHsNCiAgICBvbk9wZW4oKSB7DQogICAgICBpZiAodGhpcy5zaG93RmlsZU5hbWUpIHsNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5maWxlTmFtZSA9IGAkeytuZXcgRGF0ZSgpfS52dWVgDQogICAgICB9DQogICAgfSwNCiAgICBvbkNsb3NlKCkgew0KICAgIH0sDQogICAgY2xvc2UoZSkgew0KICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCBmYWxzZSkNCiAgICB9LA0KICAgIGhhbmRsZUNvbmZpcm0oKSB7DQogICAgICB0aGlzLiRyZWZzLmVsRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICghdmFsaWQpIHJldHVybg0KICAgICAgICB0aGlzLiRlbWl0KCdjb25maXJtJywgeyAuLi50aGlzLmZvcm1EYXRhIH0pDQogICAgICAgIHRoaXMuY2xvc2UoKQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["CodeTypeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CodeTypeDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      v-bind=\"$attrs\"\r\n      width=\"500px\"\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n      v-on=\"$listeners\"\r\n      @open=\"onOpen\"\r\n      @close=\"onClose\"\r\n    >\r\n      <el-row :gutter=\"15\">\r\n        <el-form\r\n          ref=\"elForm\"\r\n          :model=\"formData\"\r\n          :rules=\"rules\"\r\n          size=\"medium\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"生成类型\" prop=\"type\">\r\n              <el-radio-group v-model=\"formData.type\">\r\n                <el-radio-button\r\n                  v-for=\"(item, index) in typeOptions\"\r\n                  :key=\"index\"\r\n                  :label=\"item.value\"\r\n                  :disabled=\"item.disabled\"\r\n                >\r\n                  {{ item.label }}\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"showFileName\" label=\"文件名\" prop=\"fileName\">\r\n              <el-input v-model=\"formData.fileName\" placeholder=\"请输入文件名\" clearable />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"close\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">\r\n          确定\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  inheritAttrs: false,\r\n  props: ['showFileName'],\r\n  data() {\r\n    return {\r\n      formData: {\r\n        fileName: undefined,\r\n        type: 'file'\r\n      },\r\n      rules: {\r\n        fileName: [{\r\n          required: true,\r\n          message: '请输入文件名',\r\n          trigger: 'blur'\r\n        }],\r\n        type: [{\r\n          required: true,\r\n          message: '生成类型不能为空',\r\n          trigger: 'change'\r\n        }]\r\n      },\r\n      typeOptions: [{\r\n        label: '页面',\r\n        value: 'file'\r\n      }, {\r\n        label: '弹窗',\r\n        value: 'dialog'\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  watch: {},\r\n  mounted() {},\r\n  methods: {\r\n    onOpen() {\r\n      if (this.showFileName) {\r\n        this.formData.fileName = `${+new Date()}.vue`\r\n      }\r\n    },\r\n    onClose() {\r\n    },\r\n    close(e) {\r\n      this.$emit('update:visible', false)\r\n    },\r\n    handleConfirm() {\r\n      this.$refs.elForm.validate(valid => {\r\n        if (!valid) return\r\n        this.$emit('confirm', { ...this.formData })\r\n        this.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}