
78908d4c3dc63385959dfdd1ed2a97da876cff81	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Fdocument\\u002Fcontent.css\",\"contentHash\":\"a5f6c11e9e6453ec64cb591e9c0f0c34\"}","integrity":"sha512-ZQgY1TCoZnIcnBldum7uOn+vS3cM1FTMq1Hv5HOa8wjT4341lpibrwvBVXLhBcGydEJwNgbeJZkyGeJY+7gsuA==","time":1750496064272,"size":2395}