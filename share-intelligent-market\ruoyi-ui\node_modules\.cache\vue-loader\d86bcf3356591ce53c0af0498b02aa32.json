{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\feedback\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\feedback\\list.vue", "mtime": 1750151094235}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGF0YSwgZ2V0RGF0YSwgc2V0U3RhdHVzLGRlbERhdGEgfSBmcm9tICJAL2FwaS9mZWVkYmFjay9saXN0LmpzIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgICBuYW1lOiAiZmVlZGJhY2siLA0KICAgIGRhdGEoKSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgICB0aXRsZTonJywNCiAgICAgICAgICAgIG9wdGlvbnM6IFsNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIHZhbHVlOiAiMCIsDQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5pyq5aSE55CGIiwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICIxIiwNCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLlt7LlpITnkIYiLA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICBdLA0KICAgICAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgICAgICBzaG93OiBmYWxzZSwNCiAgICAgICAgICAgIGZvcm06IHt9LA0KICAgICAgICAgICAgLy8g6YGu572p5bGCDQogICAgICAgICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICAgICAgICBpZHM6IFtdLA0KICAgICAgICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICAgICAgICBzaW5nbGU6IHRydWUsDQogICAgICAgICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgICAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICAgICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgICAgICAgLy8g5oC75p2h5pWwDQogICAgICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgICAgIC8vIOihqOagvOaVsOaNrg0KICAgICAgICAgICAgbGlzdDogW10sDQogICAgICAgICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgICAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgICAgICB9LA0KICAgICAgICB9Ow0KICAgIH0sDQogICAgY3JlYXRlZCgpIHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBtZXRob2RzOiB7DQogICAgICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgICAgICAgY29uc3QgaW5mb3JJZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgICAgICAgIC8vIHRoaXMuZm9ybSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93KSk7DQogICAgICAgICAgICAvLyB0aGlzLmZvcm0uc3RhdHVzID0gTnVtYmVyKHRoaXMuZm9ybS5zdGF0dXMpDQoNCiAgICAgICAgICAgIC8vIGRlbGV0ZSB0aGlzLmZvcm0uc29ydHMNCiAgICAgICAgICAgIC8vIHRoaXMuZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sdGhpcy5mb3JtKQ0KICAgICAgICAgICAgLy8gdGhpcy50aXRsZSA9ICLnvJbovpEiOw0KICAgICAgICAgICAgLy8gdGhpcy5zaG93ID0gdHJ1ZTsNCg0KICAgICAgICAgICAgZ2V0RGF0YShpbmZvcklkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICBlZGl0KGRhdGEpIHsNCiAgICAgICAgICAgIHRoaXMudGl0bGUgPSAi6K+m5oOFIjsNCiAgICAgICAgICAgIHRoaXMuc2hvdyA9IHRydWU7DQogICAgICAgICAgICB0aGlzLmZvcm0gPSBkYXRhOw0KICAgICAgICB9LA0KICAgICAgICAvLyDkv67mlLnnirbmgIENCiAgICAgICAgc2V0U3RhdHVzKHJvdykgew0KICAgICAgICAgICAgc2V0U3RhdHVzKHsNCiAgICAgICAgICAgICAgICBvcGlkOiByb3cuaWQsDQogICAgICAgICAgICAgICAgc3RhdHVzOiAxLA0KICAgICAgICAgICAgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5tc2csDQogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOafpeivouWFrOWRiuWIl+ihqCAqLw0KICAgICAgICBnZXRMaXN0KCkgew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgIGxpc3REYXRhKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuY291bnQ7DQogICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgICAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgICAgICAgICBpc3JlYWQ6ICIiLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgICAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5pZCk7DQogICAgICAgICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMTsNCiAgICAgICAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgICAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICAgICAgICBjb25zdCBpbmZvcklkcyA9IHJvdy5pZCB8fCB0aGlzLmlkcy5qb2luKCIsIik7DQogICAgICAgICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAgICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTnvJblj7fkuLoiJyArIGluZm9ySWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAgICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRlbERhdGEoaW5mb3JJZHMpOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4gew0KDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgfSwNCn07DQo="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/feedback", "sourcesContent": ["// 消息管理\r\n<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n        >\r\n            <el-form-item label=\"状态\" prop=\"linkphone\">\r\n                <el-select\r\n                    v-model=\"queryParams.status\"\r\n                    clearable\r\n                    placeholder=\"请选择\"\r\n                >\r\n                    <el-option\r\n                        v-for=\"item in options\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.label\"\r\n                        :value=\"item.value\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"手机号\" prop=\"linkphone\">\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.linkphone\"\r\n                    placeholder=\"请输入手机号\"\r\n                    :maxlength=\"60\"\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.linkman\"\r\n                    placeholder=\"请输入联系人\"\r\n                    :maxlength=\"60\"\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    >删除\r\n                </el-button>\r\n            </el-col>\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"list\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                    <span>{{ scope.$index + 1 }}</span>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"意见反馈\"\r\n                align=\"center\"\r\n                prop=\"content\"\r\n                :show-overflow-tooltip=\"true\"\r\n            />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"linkman\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"linkphone\" />\r\n            <el-table-column\r\n                label=\"时间\"\r\n                align=\"center\"\r\n                prop=\"create_time\"\r\n                sortable\r\n            />\r\n            <el-table-column\r\n                label=\"状态\"\r\n                align=\"center\"\r\n                prop=\"create_by\"\r\n                width=\"100\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <!-- 开启 -->\r\n                    <!-- <el-switch v-model=\"form.delivery\"></el-switch> -->\r\n                    <el-tag type=\"success\" v-if=\"scope.row.status == 1\"\r\n                        >已处理</el-tag\r\n                    >\r\n                    <el-tag type=\"danger\" v-else>未处理</el-tag>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                fixed=\"right\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        v-if=\"scope.row.status != 1\"\r\n                        style=\"color: #85ce61\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"setStatus(scope.row, 1)\"\r\n                        >处理</el-button\r\n                    >\r\n                    <!-- <el-button\r\n                        style=\"color: #85ce61\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"setStatus(scope.row, 1)\"\r\n                        >启用</el-button\r\n                    >\r\n                    <el-button\r\n                        style=\"color: #ebb563\"\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        @click=\"setStatus(scope.row, 0)\"\r\n                        >禁用</el-button\r\n                    > -->\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        >查看详情</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加弹窗 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"show\"\r\n            width=\"70%\"\r\n            :before-close=\"() => (show = false)\"\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" >\r\n                <el-form-item label=\"意见反馈\" prop=\"content\">\r\n                    <el-input\r\n                        type=\"textarea\"\r\n                        :rows=\"5\"\r\n                         :disabled=\"true\"\r\n                        clearable\r\n                        v-model=\"form.content\"\r\n                        placeholder=\"请输入标题\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\" prop=\"status\">\r\n                    <el-tag type=\"success\" v-if=\"form.status == 1\"\r\n                        >已处理</el-tag\r\n                    >\r\n                    <el-tag type=\"danger\" v-else>未处理</el-tag>\r\n                    <!-- <el-switch\r\n                        v-model=\"form.status\"\r\n                        :active-value=\"1\"\r\n                        :inactive-value=\"0\"\r\n                    >\r\n                    </el-switch> -->\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\">\r\n                    <el-input\r\n                         :disabled=\"true\"\r\n\r\n                        clearable\r\n                        v-model=\"form.linkman\"\r\n                        placeholder=\"请输入联系人\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"联系电话\">\r\n                    <el-input\r\n                         :disabled=\"true\"\r\n\r\n                        clearable\r\n                        v-model=\"form.linkphone\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    ></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"show = false\">取 消</el-button>\r\n              \r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { listData, getData, setStatus,delData } from \"@/api/feedback/list.js\";\r\nexport default {\r\n    name: \"feedback\",\r\n    data() {\r\n        return {\r\n            title:'',\r\n            options: [\r\n                {\r\n                    value: \"0\",\r\n                    label: \"未处理\",\r\n                },\r\n                {\r\n                    value: \"1\",\r\n                    label: \"已处理\",\r\n                },\r\n            ],\r\n            loading: false,\r\n            show: false,\r\n            form: {},\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 表格数据\r\n            list: [],\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            const inforId = row.id || this.ids;\r\n            // this.form = JSON.parse(JSON.stringify(row));\r\n            // this.form.status = Number(this.form.status)\r\n\r\n            // delete this.form.sorts\r\n            // this.form = Object.assign({},this.form)\r\n            // this.title = \"编辑\";\r\n            // this.show = true;\r\n\r\n            getData(inforId).then((response) => {\r\n                this.edit(response.data);\r\n            });\r\n        },\r\n        edit(data) {\r\n            this.title = \"详情\";\r\n            this.show = true;\r\n            this.form = data;\r\n        },\r\n        // 修改状态\r\n        setStatus(row) {\r\n            setStatus({\r\n                opid: row.id,\r\n                status: 1,\r\n            }).then((response) => {\r\n                if (response.code == 200) {\r\n                    this.$message({\r\n                        message: response.msg,\r\n                        type: \"success\",\r\n                    });\r\n                    this.getList();\r\n                }\r\n            });\r\n        },\r\n        /** 查询公告列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listData(this.queryParams).then((response) => {\r\n                this.list = response.data;\r\n                this.total = response.count;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.queryParams = {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                isread: \"\",\r\n            };\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length != 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const inforIds = row.id || this.ids.join(\",\");\r\n            this.$modal\r\n                .confirm('是否确认删除编号为\"' + inforIds + '\"的数据项？')\r\n                .then(function () {\r\n                    return delData(inforIds);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {\r\n\r\n                });\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}