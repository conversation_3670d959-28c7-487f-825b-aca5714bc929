{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\RightPanel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\RightPanel\\index.vue", "mtime": 1750151094146}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_utils", "require", "name", "props", "clickNotClose", "default", "type", "Boolean", "buttonTop", "Number", "computed", "show", "get", "$store", "state", "settings", "showSettings", "set", "val", "dispatch", "key", "value", "theme", "watch", "addEventClick", "addClass", "document", "body", "removeClass", "mounted", "insertToBody", "<PERSON><PERSON><PERSON><PERSON>", "elx", "$refs", "rightPanel", "remove", "methods", "window", "addEventListener", "closeSidebar", "evt", "parent", "target", "closest", "removeEventListener", "querySelector", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/RightPanel/index.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"rightPanel\" :class=\"{show:show}\" class=\"rightPanel-container\">\r\n    <div class=\"rightPanel-background\" />\r\n    <div class=\"rightPanel\">\r\n      <div class=\"rightPanel-items\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { addClass, removeClass } from '@/utils'\r\n\r\nexport default {\r\n  name: 'RightPanel',\r\n  props: {\r\n    clickNotClose: {\r\n      default: false,\r\n      type: Boolean\r\n    },\r\n    buttonTop: {\r\n      default: 250,\r\n      type: Number\r\n    }\r\n  },\r\n  computed: {\r\n    show: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    },\r\n  },\r\n  watch: {\r\n    show(value) {\r\n      if (value && !this.clickNotClose) {\r\n        this.addEventClick()\r\n      }\r\n      if (value) {\r\n        addClass(document.body, 'showRightPanel')\r\n      } else {\r\n        removeClass(document.body, 'showRightPanel')\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.insertToBody()\r\n    this.addEventClick()\r\n  },\r\n  beforeDestroy() {\r\n    const elx = this.$refs.rightPanel\r\n    elx.remove()\r\n  },\r\n  methods: {\r\n    addEventClick() {\r\n      window.addEventListener('click', this.closeSidebar)\r\n    },\r\n    closeSidebar(evt) {\r\n      const parent = evt.target.closest('.rightPanel')\r\n      if (!parent) {\r\n        this.show = false\r\n        window.removeEventListener('click', this.closeSidebar)\r\n      }\r\n    },\r\n    insertToBody() {\r\n      const elx = this.$refs.rightPanel\r\n      const body = document.querySelector('body')\r\n      body.insertBefore(elx, body.firstChild)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.showRightPanel {\r\n  overflow: hidden;\r\n  position: relative;\r\n  width: calc(100% - 15px);\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\" scoped>\r\n.rightPanel-background {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  opacity: 0;\r\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\r\n  background: rgba(0, 0, 0, .2);\r\n  z-index: -1;\r\n}\r\n\r\n.rightPanel {\r\n  width: 100%;\r\n  max-width: 260px;\r\n  height: 100vh;\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\r\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n  transform: translate(100%);\r\n  background: #fff;\r\n  z-index: 40000;\r\n}\r\n\r\n.show {\r\n  transition: all .3s cubic-bezier(.7, .3, .1, 1);\r\n\r\n  .rightPanel-background {\r\n    z-index: 20000;\r\n    opacity: 1;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .rightPanel {\r\n    transform: translate(0);\r\n  }\r\n}\r\n\r\n.handle-button {\r\n  width: 48px;\r\n  height: 48px;\r\n  position: absolute;\r\n  left: -48px;\r\n  text-align: center;\r\n  font-size: 24px;\r\n  border-radius: 6px 0 0 6px !important;\r\n  z-index: 0;\r\n  pointer-events: auto;\r\n  cursor: pointer;\r\n  color: #fff;\r\n  line-height: 48px;\r\n  i {\r\n    font-size: 24px;\r\n    line-height: 48px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAYA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,aAAA;MACAC,OAAA;MACAC,IAAA,EAAAC;IACA;IACAC,SAAA;MACAH,OAAA;MACAC,IAAA,EAAAG;IACA;EACA;EACAC,QAAA;IACAC,IAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA;MACA;MACAC,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAL,MAAA,CAAAM,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAI,KAAA,WAAAA,MAAA;MACA,YAAAT,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAO,KAAA;IACA;EACA;EACAC,KAAA;IACAZ,IAAA,WAAAA,KAAAU,KAAA;MACA,IAAAA,KAAA,UAAAjB,aAAA;QACA,KAAAoB,aAAA;MACA;MACA,IAAAH,KAAA;QACA,IAAAI,eAAA,EAAAC,QAAA,CAAAC,IAAA;MACA;QACA,IAAAC,kBAAA,EAAAF,QAAA,CAAAC,IAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAN,aAAA;EACA;EACAO,aAAA,WAAAA,cAAA;IACA,IAAAC,GAAA,QAAAC,KAAA,CAAAC,UAAA;IACAF,GAAA,CAAAG,MAAA;EACA;EACAC,OAAA;IACAZ,aAAA,WAAAA,cAAA;MACAa,MAAA,CAAAC,gBAAA,eAAAC,YAAA;IACA;IACAA,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAC,MAAA,GAAAD,GAAA,CAAAE,MAAA,CAAAC,OAAA;MACA,KAAAF,MAAA;QACA,KAAA9B,IAAA;QACA0B,MAAA,CAAAO,mBAAA,eAAAL,YAAA;MACA;IACA;IACAT,YAAA,WAAAA,aAAA;MACA,IAAAE,GAAA,QAAAC,KAAA,CAAAC,UAAA;MACA,IAAAP,IAAA,GAAAD,QAAA,CAAAmB,aAAA;MACAlB,IAAA,CAAAmB,YAAA,CAAAd,GAAA,EAAAL,IAAA,CAAAoB,UAAA;IACA;EACA;AACA", "ignoreList": []}]}