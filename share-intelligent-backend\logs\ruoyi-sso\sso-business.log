10:29:56.358 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:30:09.003 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 18.054 seconds (JVM running for 19.252)
10:32:07.158 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
10:32:09.295 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=738f3381b62143a99159d5ba3105bc26, code=3794, verifyKey=sso:captcha:738f3381b62143a99159d5ba3105bc26
10:32:09.297 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3794, captchaEnabled=true, uuid=738f3381b62143a99159d5ba3105bc26}}
10:32:16.976 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
10:32:18.427 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
10:32:18.518 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
10:32:20.172 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
10:32:20.173 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
10:32:20.203 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 3f090c124f514e1dad525e004e772047 for client: backend
10:32:20.204 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
10:32:20.204 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
10:32:20.320 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=3f090c124f514e1dad525e004e772047, clientId=backend
10:32:20.329 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
10:32:20.330 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
10:32:20.360 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=31e8923af5...
10:32:20.379 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
10:49:16.719 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
10:49:16.740 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=d7785c4fcd3741bf8bad8c8a8e1a3d82, code=4663, verifyKey=sso:captcha:d7785c4fcd3741bf8bad8c8a8e1a3d82
10:49:16.740 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=4663, captchaEnabled=true, uuid=d7785c4fcd3741bf8bad8c8a8e1a3d82}}
10:49:36.146 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
10:49:36.182 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
10:49:36.265 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
10:49:36.278 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
10:49:36.278 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
10:49:36.318 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 1f2c1dc8ba41413b9dd79c2180d9caab for client: backend
10:49:36.319 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
10:49:36.319 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
10:49:36.335 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=1f2c1dc8ba41413b9dd79c2180d9caab, clientId=backend
10:49:36.339 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
10:49:36.339 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
10:49:36.342 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=dd6944d40a...
10:49:36.346 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
10:49:47.092 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=54d23da4e79746db8c86bf53eaf4feae, code=3464, verifyKey=sso:captcha:54d23da4e79746db8c86bf53eaf4feae
10:49:47.092 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3464, captchaEnabled=true, uuid=54d23da4e79746db8c86bf53eaf4feae}}
10:49:50.573 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
10:49:50.576 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
10:49:50.654 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
10:49:50.670 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
10:49:50.670 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
10:49:50.671 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: e12c60d44a864942aa3d00199cdb9c97 for client: backend
10:49:50.672 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
10:49:50.672 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
10:49:50.696 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=e12c60d44a864942aa3d00199cdb9c97, clientId=backend
10:49:50.700 [http-nio-9100-exec-8] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
10:49:50.700 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
10:49:50.703 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=067bea0865...
10:49:50.705 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
10:53:13.286 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
10:53:13.309 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=3e2395645da046efa82eeec4204e6cf2, code=3965, verifyKey=sso:captcha:3e2395645da046efa82eeec4204e6cf2
10:53:13.309 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3965, captchaEnabled=true, uuid=3e2395645da046efa82eeec4204e6cf2}}
10:53:23.316 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
10:53:23.319 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
10:53:23.399 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
10:53:23.408 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
10:53:23.408 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
10:53:23.408 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 1d8298be10f24fc9b20b9bdf42fcb852 for client: backend
10:53:23.409 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
10:53:23.409 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
10:53:23.422 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=1d8298be10f24fc9b20b9bdf42fcb852, clientId=backend
10:53:23.424 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
10:53:23.424 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
10:53:23.427 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=9d16ef8e8c...
10:53:23.430 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
10:59:23.640 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:59:31.758 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 12.293 seconds (JVM running for 13.629)
11:00:17.367 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
11:00:19.473 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=f7301b987c074829ac8227af94822545, code=8479, verifyKey=sso:captcha:f7301b987c074829ac8227af94822545
11:00:19.476 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=8479, captchaEnabled=true, uuid=f7301b987c074829ac8227af94822545}}
11:00:31.344 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=7018b66fbab443eaa525ca89b19900ba, code=2983, verifyKey=sso:captcha:7018b66fbab443eaa525ca89b19900ba
11:00:31.344 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=2983, captchaEnabled=true, uuid=7018b66fbab443eaa525ca89b19900ba}}
11:00:34.582 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
11:00:35.997 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
11:00:36.088 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
11:00:36.414 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
11:00:36.414 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
11:00:36.439 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: e36ee7858dca462797036c378d172edf for client: backend
11:00:36.439 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
11:00:36.439 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
11:00:36.542 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=e36ee7858dca462797036c378d172edf, clientId=backend
11:00:36.586 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
11:00:36.586 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
11:00:36.606 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=630a1ec164...
11:00:36.623 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
