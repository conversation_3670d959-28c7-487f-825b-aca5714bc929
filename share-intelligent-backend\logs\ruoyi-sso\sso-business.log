16:14:19.463 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:14:27.877 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 13.263 seconds (JVM running for 14.336)
16:15:03.183 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:15:03.183 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:15:03.440 [http-nio-9100-exec-2] ERROR c.r.s.s.i.SSOUserServiceImpl - [selectSSOUserByUsername,32] - 根据用户名查询SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.selectSSOUserByUsername(SSOUserServiceImpl.java:30)
	at com.ruoyi.sso.controller.SSOUserManagementController.checkUserExists(SSOUserManagementController.java:75)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 61 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 69 common frames omitted
Caused by: java.sql.SQLException: url not set
	at com.alibaba.druid.pool.DruidDataSource.resolveDriver(DruidDataSource.java:1273)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:898)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1462)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 81 common frames omitted
16:15:03.443 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: false, 耗时: 260ms ===
16:15:05.735 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - 开始创建SSO用户: 18454831889
16:16:03.570 [http-nio-9100-exec-1] ERROR c.r.s.s.i.SSOUserServiceImpl - [selectSSOUserByUsername,32] - 根据用户名查询SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.selectSSOUserByUsername(SSOUserServiceImpl.java:30)
	at com.ruoyi.sso.service.impl.SSOUserManagementServiceImpl.createSSOUserForRegistration(SSOUserManagementServiceImpl.java:121)
	at com.ruoyi.sso.controller.SSOUserManagementController.createSSOUser(SSOUserManagementController.java:47)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 62 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 70 common frames omitted
Caused by: java.sql.SQLException: null
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1802)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1493)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1473)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 82 common frames omitted
Caused by: java.lang.InterruptedException: null
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.reportInterruptAfterWait(AbstractQueuedSynchronizer.java:2014)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2088)
	at com.alibaba.druid.pool.DruidDataSource.pollLast(DruidDataSource.java:2370)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1783)
	... 89 common frames omitted
16:16:03.570 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,123] - 检查用户存在性耗时: 57834ms, 用户: 18454831889, 结果: 不存在
16:16:13.347 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:16:22.588 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 13.773 seconds (JVM running for 14.886)
16:16:47.169 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:16:47.169 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:16:47.302 [http-nio-9100-exec-1] ERROR c.r.s.s.i.SSOUserServiceImpl - [selectSSOUserByUsername,32] - 根据用户名查询SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.selectSSOUserByUsername(SSOUserServiceImpl.java:30)
	at com.ruoyi.sso.controller.SSOUserManagementController.checkUserExists(SSOUserManagementController.java:75)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 61 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: url not set
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 69 common frames omitted
Caused by: java.sql.SQLException: url not set
	at com.alibaba.druid.pool.DruidDataSource.resolveDriver(DruidDataSource.java:1273)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:898)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1462)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 81 common frames omitted
16:16:47.303 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: false, 耗时: 134ms ===
16:17:01.410 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:17:01.410 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:18:01.414 [http-nio-9100-exec-2] ERROR c.r.s.s.i.SSOUserServiceImpl - [selectSSOUserByUsername,32] - 根据用户名查询SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.selectSSOUserByUsername(SSOUserServiceImpl.java:30)
	at com.ruoyi.sso.service.impl.SSOUserManagementServiceImpl.createSSOUserForRegistration(SSOUserManagementServiceImpl.java:123)
	at com.ruoyi.sso.controller.SSOUserManagementController.createSSOUser(SSOUserManagementController.java:47)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 62 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1852)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1493)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1473)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 70 common frames omitted
16:18:01.415 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 60005ms, 用户: 18454831889, 结果: 不存在
16:18:41.560 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:18:41.560 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:19:01.418 [http-nio-9100-exec-2] ERROR c.r.s.s.i.SSOUserServiceImpl - [insertSSOUser,82] - 插入SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.insertSSOUser(SSOUserServiceImpl.java:67)
	at com.ruoyi.sso.service.impl.SSOUserManagementServiceImpl.createSSOUserForRegistration(SSOUserManagementServiceImpl.java:143)
	at com.ruoyi.sso.controller.SSOUserManagementController.createSSOUser(SSOUserManagementController.java:47)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 62 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1852)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1493)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1473)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 70 common frames omitted
16:19:01.418 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,145] - 插入SSO用户耗时: 60003ms, 用户: 18454831889
16:19:01.419 [http-nio-9100-exec-2] WARN  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,152] - 为注册创建SSO用户失败: 18454831889
16:19:41.567 [http-nio-9100-exec-3] ERROR c.r.s.s.i.SSOUserServiceImpl - [selectSSOUserByUsername,32] - 根据用户名查询SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.selectSSOUserByUsername(SSOUserServiceImpl.java:30)
	at com.ruoyi.sso.service.impl.SSOUserManagementServiceImpl.createSSOUserForRegistration(SSOUserManagementServiceImpl.java:123)
	at com.ruoyi.sso.controller.SSOUserManagementController.createSSOUser(SSOUserManagementController.java:47)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 62 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1852)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1493)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1473)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 70 common frames omitted
16:19:41.567 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 60007ms, 用户: 18454831889, 结果: 不存在
16:20:21.801 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:20:21.802 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:20:41.570 [http-nio-9100-exec-3] ERROR c.r.s.s.i.SSOUserServiceImpl - [insertSSOUser,82] - 插入SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.insertSSOUser(SSOUserServiceImpl.java:67)
	at com.ruoyi.sso.service.impl.SSOUserManagementServiceImpl.createSSOUserForRegistration(SSOUserManagementServiceImpl.java:143)
	at com.ruoyi.sso.controller.SSOUserManagementController.createSSOUser(SSOUserManagementController.java:47)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 62 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1852)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1493)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1473)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 70 common frames omitted
16:20:41.570 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,145] - 插入SSO用户耗时: 60002ms, 用户: 18454831889
16:20:41.570 [http-nio-9100-exec-3] WARN  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,152] - 为注册创建SSO用户失败: 18454831889
16:21:21.805 [http-nio-9100-exec-4] ERROR c.r.s.s.i.SSOUserServiceImpl - [selectSSOUserByUsername,32] - 根据用户名查询SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.selectSSOUserByUsername(SSOUserServiceImpl.java:30)
	at com.ruoyi.sso.service.impl.SSOUserManagementServiceImpl.createSSOUserForRegistration(SSOUserManagementServiceImpl.java:123)
	at com.ruoyi.sso.controller.SSOUserManagementController.createSSOUser(SSOUserManagementController.java:47)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 62 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1852)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1493)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1473)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 70 common frames omitted
16:21:21.805 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 60003ms, 用户: 18454831889, 结果: 不存在
16:22:21.809 [http-nio-9100-exec-4] ERROR c.r.s.s.i.SSOUserServiceImpl - [insertSSOUser,82] - 插入SSO用户异常: 18454831889
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy127.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy128.selectByUsername(Unknown Source)
	at com.ruoyi.sso.service.impl.SSOUserServiceImpl.insertSSOUser(SSOUserServiceImpl.java:67)
	at com.ruoyi.sso.service.impl.SSOUserManagementServiceImpl.createSSOUserForRegistration(SSOUserManagementServiceImpl.java:143)
	at com.ruoyi.sso.controller.SSOUserManagementController.createSSOUser(SSOUserManagementController.java:47)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.NullPointerException
### The error may exist in file [E:\company\nmd\nmdnew\share-intelligent\share-intelligent-backend\ruoyi-sso\target\classes\mapper\SSOUserMapper.xml]
### The error may involve com.ruoyi.sso.mapper.SSOUserMapper.selectByUsername
### The error occurred while executing a query
### Cause: java.lang.NullPointerException
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 62 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1852)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1493)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1473)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1458)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy155.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 70 common frames omitted
16:22:21.809 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,145] - 插入SSO用户耗时: 60004ms, 用户: 18454831889
16:22:21.809 [http-nio-9100-exec-4] WARN  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,152] - 为注册创建SSO用户失败: 18454831889
16:25:10.514 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:25:19.319 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 13.239 seconds (JVM running for 14.286)
16:25:29.269 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:25:37.360 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 11.932 seconds (JVM running for 13.036)
16:25:58.675 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:25:58.675 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:26:00.011 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: false, 耗时: 1336ms ===
16:26:00.143 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:26:00.144 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:26:00.146 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 2ms, 用户: 18454831889, 结果: 不存在
16:26:00.260 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserServiceImpl - [insertSSOUser,76] - SSO用户创建成功: 18454831889
16:26:00.260 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,145] - 插入SSO用户耗时: 114ms, 用户: 18454831889
16:26:00.260 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,149] - 为注册创建SSO用户成功: 18454831889, 总耗时: 117ms
16:27:44.565 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 19323037514 ===
16:27:44.565 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 19323037514
16:27:44.590 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 19323037514, 存在: false, 耗时: 25ms ===
16:27:44.665 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 19323037514 ===
16:27:44.665 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 19323037514, 姓名: 19323037514, 密码长度: 60
16:27:44.667 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 1ms, 用户: 19323037514, 结果: 不存在
16:27:44.673 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserServiceImpl - [insertSSOUser,76] - SSO用户创建成功: 19323037514
16:27:44.673 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,145] - 插入SSO用户耗时: 6ms, 用户: 19323037514
16:27:44.673 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,149] - 为注册创建SSO用户成功: 19323037514, 总耗时: 8ms
16:34:14.144 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:34:22.672 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 12.288 seconds (JVM running for 13.568)
16:36:03.680 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:36:03.680 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:36:05.128 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 1448ms, 用户: 18454831889, 结果: 存在
16:36:05.128 [http-nio-9100-exec-1] WARN  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,129] - SSO用户已存在，跳过创建: 18454831889
16:36:05.364 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:36:05.364 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:36:05.369 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: true, 耗时: 5ms ===
16:42:53.968 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 19323037514 ===
16:42:53.968 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 19323037514, 姓名: 19323037514, 密码长度: 60
16:42:53.997 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 29ms, 用户: 19323037514, 结果: 存在
16:42:53.997 [http-nio-9100-exec-4] WARN  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,129] - SSO用户已存在，跳过创建: 19323037514
16:44:25.671 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:44:34.020 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 12.774 seconds (JVM running for 13.936)
16:44:47.426 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 19323037514 ===
16:44:47.427 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 19323037514
16:44:48.686 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 19323037514, 存在: true, 耗时: 1260ms ===
16:46:41.058 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:46:41.058 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:46:41.086 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 28ms, 用户: 18454831889, 结果: 存在
16:46:41.086 [http-nio-9100-exec-3] WARN  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,129] - SSO用户已存在，跳过创建: 18454831889
16:46:41.110 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:46:41.110 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:46:41.112 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: true, 耗时: 2ms ===
16:48:07.338 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:48:07.339 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:48:07.341 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 2ms, 用户: 18454831889, 结果: 存在
16:48:07.341 [http-nio-9100-exec-7] WARN  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,129] - SSO用户已存在，跳过创建: 18454831889
16:48:07.357 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:48:07.358 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:48:07.359 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: true, 耗时: 2ms ===
16:48:53.545 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
16:49:02.049 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 12.363 seconds (JVM running for 13.48)
16:50:30.391 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:50:30.391 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:50:31.893 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: true, 耗时: 1502ms ===
16:51:55.392 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:51:55.394 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:51:55.424 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 29ms, 用户: 18454831889, 结果: 存在
16:51:55.424 [http-nio-9100-exec-3] WARN  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,129] - SSO用户已存在，跳过创建: 18454831889
16:51:55.480 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:51:55.481 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:51:55.484 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: true, 耗时: 4ms ===
16:52:29.089 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
16:52:31.202 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=efa113ecea674824ae1cd477b1278e05, code=4619, verifyKey=sso:captcha:efa113ecea674824ae1cd477b1278e05
16:52:31.202 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=4619, captchaEnabled=true, uuid=efa113ecea674824ae1cd477b1278e05}}
16:52:39.882 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
16:52:39.928 [http-nio-9100-exec-8] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
16:52:40.017 [http-nio-9100-exec-8] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
16:52:40.017 [http-nio-9100-exec-8] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
16:52:40.061 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=06f541968fce41338f504650fdba2797, code=1808, verifyKey=sso:captcha:06f541968fce41338f504650fdba2797
16:52:40.061 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=1808, captchaEnabled=true, uuid=06f541968fce41338f504650fdba2797}}
16:53:34.776 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
16:53:34.779 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
16:53:34.852 [http-nio-9100-exec-3] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
16:53:34.852 [http-nio-9100-exec-3] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
16:53:34.859 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=e8933eece566427baf9129a248c92110, code=5634, verifyKey=sso:captcha:e8933eece566427baf9129a248c92110
16:53:34.860 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=5634, captchaEnabled=true, uuid=e8933eece566427baf9129a248c92110}}
16:53:45.179 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
16:53:45.184 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
16:53:45.254 [http-nio-9100-exec-4] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
16:53:45.255 [http-nio-9100-exec-4] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
16:53:45.260 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=9b8b3f09369f4f91b24d69c2229299d2, code=6502, verifyKey=sso:captcha:9b8b3f09369f4f91b24d69c2229299d2
16:53:45.260 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=6502, captchaEnabled=true, uuid=9b8b3f09369f4f91b24d69c2229299d2}}
16:54:36.121 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,116] - === 开始创建SSO用户: 18454831889 ===
16:54:36.123 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,117] - 参数 - 手机号: 18454831889, 姓名: 18454831889, 密码长度: 60
16:54:36.124 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,125] - 检查用户存在性耗时: 1ms, 用户: 18454831889, 结果: 不存在
16:54:36.262 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserServiceImpl - [insertSSOUser,76] - SSO用户创建成功: 18454831889
16:54:36.262 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,145] - 插入SSO用户耗时: 138ms, 用户: 18454831889
16:54:36.263 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserManagementServiceImpl - [createSSOUserForRegistration,149] - 为注册创建SSO用户成功: 18454831889, 总耗时: 142ms
16:54:36.280 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,65] - === 开始检查SSO用户是否存在: 18454831889 ===
16:54:36.280 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,74] - 开始查询SSO用户: 18454831889
16:54:36.282 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOUserManagementController - [checkUserExists,79] - === SSO用户查询完成: 18454831889, 存在: true, 耗时: 2ms ===
16:55:10.856 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
16:55:10.874 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=0315e9acae154a3585fdfd72c0086f17, code=4906, verifyKey=sso:captcha:0315e9acae154a3585fdfd72c0086f17
16:55:10.876 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=4906, captchaEnabled=true, uuid=0315e9acae154a3585fdfd72c0086f17}}
16:55:20.708 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
16:55:20.712 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
16:55:20.791 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
16:55:21.271 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
16:55:21.271 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
16:55:21.294 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 31461b38f84a4e29b93b0e640fab53fc for client: backend
16:55:21.294 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
16:55:21.294 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
16:55:21.357 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=31461b38f84a4e29b93b0e640fab53fc, clientId=backend
16:55:21.357 [http-nio-9100-exec-3] WARN  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,289] - 客户端密钥验证失败: clientId=backend
16:55:21.358 [http-nio-9100-exec-3] WARN  c.r.s.c.SSOAuthController - [getToken,248] - 令牌换取失败: authCode=31461b38f84a4e29b93b0e640fab53fc, clientId=backend
17:00:20.146 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:00:28.180 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 12.345 seconds (JVM running for 13.67)
17:01:30.996 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=8146144b0d7244abbb1dcd4597a4e72a, code=9576, verifyKey=sso:captcha:8146144b0d7244abbb1dcd4597a4e72a
17:01:30.999 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=9576, captchaEnabled=true, uuid=8146144b0d7244abbb1dcd4597a4e72a}}
17:01:31.118 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=321af02ab5ab42a1bd0c498994f4f0cb, code=7540, verifyKey=sso:captcha:321af02ab5ab42a1bd0c498994f4f0cb
17:01:31.118 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=7540, captchaEnabled=true, uuid=321af02ab5ab42a1bd0c498994f4f0cb}}
17:01:31.536 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=c00e45e49488472a8104842b8a37735f, code=9263, verifyKey=sso:captcha:c00e45e49488472a8104842b8a37735f
17:01:31.536 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=9263, captchaEnabled=true, uuid=c00e45e49488472a8104842b8a37735f}}
17:01:36.395 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:01:37.887 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
17:01:37.976 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
17:01:38.313 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
17:01:38.313 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
17:01:38.342 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 01597f10cea441feb74b8e1fb3e830b4 for client: backend
17:01:38.343 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
17:01:38.343 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
17:01:44.690 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=01597f10cea441feb74b8e1fb3e830b4, clientId=backend
17:01:49.369 [http-nio-9100-exec-5] WARN  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,289] - 客户端密钥验证失败: clientId=backend
17:01:53.832 [http-nio-9100-exec-5] WARN  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,289] - 客户端密钥验证失败: clientId=backend
17:02:13.165 [http-nio-9100-exec-5] WARN  c.r.s.c.SSOAuthController - [getToken,248] - 令牌换取失败: authCode=01597f10cea441feb74b8e1fb3e830b4, clientId=backend
17:03:33.814 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
17:03:33.832 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=b6a14c0224e3475fbaaa852d093edaa7, code=7153, verifyKey=sso:captcha:b6a14c0224e3475fbaaa852d093edaa7
17:03:33.832 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=7153, captchaEnabled=true, uuid=b6a14c0224e3475fbaaa852d093edaa7}}
17:03:44.948 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:03:44.984 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
17:03:45.067 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
17:03:45.081 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
17:03:45.081 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
17:03:45.082 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 74aba07b01274ae6b12f670ce2f78dc3 for client: backend
17:03:45.082 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
17:03:45.084 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
17:03:47.125 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=74aba07b01274ae6b12f670ce2f78dc3, clientId=backend
17:04:27.024 [http-nio-9100-exec-1] WARN  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,289] - 客户端密钥验证失败: clientId=backend
17:04:27.712 [http-nio-9100-exec-1] WARN  c.r.s.c.SSOAuthController - [getToken,248] - 令牌换取失败: authCode=74aba07b01274ae6b12f670ce2f78dc3, clientId=backend
17:05:49.841 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=bd213059bfea422384b66268a68111d6, code=2267, verifyKey=sso:captcha:bd213059bfea422384b66268a68111d6
17:05:49.841 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=2267, captchaEnabled=true, uuid=bd213059bfea422384b66268a68111d6}}
17:05:53.621 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:05:53.625 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
17:05:53.702 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
17:05:53.719 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
17:05:53.719 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
17:05:53.719 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 2ab613a7298d44fd86982f3ad009120a for client: backend
17:05:53.719 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
17:05:53.719 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
17:05:53.741 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=2ab613a7298d44fd86982f3ad009120a, clientId=backend
17:05:53.742 [http-nio-9100-exec-6] WARN  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,289] - 客户端密钥验证失败: clientId=backend
17:05:53.742 [http-nio-9100-exec-6] WARN  c.r.s.c.SSOAuthController - [getToken,248] - 令牌换取失败: authCode=2ab613a7298d44fd86982f3ad009120a, clientId=backend
17:06:46.484 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=477e183d7a0a4cbca39eb78bfb0b1818, code=6359, verifyKey=sso:captcha:477e183d7a0a4cbca39eb78bfb0b1818
17:06:46.484 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=6359, captchaEnabled=true, uuid=477e183d7a0a4cbca39eb78bfb0b1818}}
17:06:50.795 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:06:50.799 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
17:06:50.879 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
17:06:50.892 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
17:06:50.892 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
17:06:50.894 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: b8163a602e594f21849b71c02434c224 for client: backend
17:06:50.894 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
17:06:50.894 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
17:07:03.074 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=b8163a602e594f21849b71c02434c224, clientId=backend
17:08:52.305 [http-nio-9100-exec-1] WARN  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,289] - 客户端密钥验证失败: clientId=backend
17:08:55.514 [http-nio-9100-exec-1] WARN  c.r.s.c.SSOAuthController - [getToken,248] - 令牌换取失败: authCode=b8163a602e594f21849b71c02434c224, clientId=backend
17:09:01.871 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:09:09.699 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 11.476 seconds (JVM running for 12.653)
17:11:20.444 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:11:28.484 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 12.306 seconds (JVM running for 13.447)
17:11:43.706 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
17:11:45.619 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=0317d8d97efa4fa8b6fbdef16ea6b5fc, code=1710, verifyKey=sso:captcha:0317d8d97efa4fa8b6fbdef16ea6b5fc
17:11:45.622 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=1710, captchaEnabled=true, uuid=0317d8d97efa4fa8b6fbdef16ea6b5fc}}
17:11:56.166 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:11:57.563 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
17:11:57.653 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
17:11:57.971 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
17:11:57.975 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
17:11:57.997 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 432bdf645e894d53a85f6a832bd6d430 for client: backend
17:11:57.997 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
17:11:58.000 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
17:12:08.761 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=432bdf645e894d53a85f6a832bd6d430, clientId=backend
17:12:11.572 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
17:12:12.053 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
17:12:13.487 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=1a2209794c...
17:12:13.505 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
17:15:54.058 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:17:37.073 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:18:59.990 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:19:11.665 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 16.498 seconds (JVM running for 17.969)
17:51:55.614 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:52:04.048 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 12.913 seconds (JVM running for 14.107)
17:52:17.836 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=727451bdd8cd45b2b23ca9113df7bca1, code=4791, verifyKey=sso:captcha:727451bdd8cd45b2b23ca9113df7bca1
17:52:17.839 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=4791, captchaEnabled=true, uuid=727451bdd8cd45b2b23ca9113df7bca1}}
17:52:17.935 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=c1f37c1f07084a9bb847fb399e25f965, code=8962, verifyKey=sso:captcha:c1f37c1f07084a9bb847fb399e25f965
17:52:17.936 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=8962, captchaEnabled=true, uuid=c1f37c1f07084a9bb847fb399e25f965}}
17:52:21.563 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:52:22.987 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
17:52:23.074 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
17:52:24.697 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
17:52:24.697 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
17:52:24.723 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 7e8182b8b01e4e2da6f3d39ce5f846c0 for client: backend
17:52:24.727 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
17:52:24.727 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
17:52:27.448 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=7e8182b8b01e4e2da6f3d39ce5f846c0, clientId=backend
17:53:11.673 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
17:53:14.691 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
17:55:24.558 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:55:24.595 [http-nio-9100-exec-7] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 验证码已过期
17:55:24.601 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=7b878497557d4bfdabe126209fc8c5ed, code=8551, verifyKey=sso:captcha:7b878497557d4bfdabe126209fc8c5ed
17:55:24.601 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=8551, captchaEnabled=true, uuid=7b878497557d4bfdabe126209fc8c5ed}}
17:55:25.823 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=c4fabdf9faac49d3ace97490c92834ee, code=1354, verifyKey=sso:captcha:c4fabdf9faac49d3ace97490c92834ee
17:55:25.824 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=1354, captchaEnabled=true, uuid=c4fabdf9faac49d3ace97490c92834ee}}
17:55:28.553 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:55:28.590 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
17:55:28.668 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
17:55:28.681 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
17:55:28.682 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
17:55:28.684 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: fda14ddbfd054a9cb67ba3d68d3fd708 for client: backend
17:55:28.684 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
17:55:28.684 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
17:55:42.582 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=fda14ddbfd054a9cb67ba3d68d3fd708, clientId=backend
17:55:42.586 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
17:55:42.586 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
17:55:48.462 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=1f3da2a668...
17:55:48.476 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
17:56:13.861 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
17:56:13.888 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=c574e834b8714816bccff76d4cc884bf, code=9471, verifyKey=sso:captcha:c574e834b8714816bccff76d4cc884bf
17:56:13.888 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=9471, captchaEnabled=true, uuid=c574e834b8714816bccff76d4cc884bf}}
17:56:22.569 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=f65400f0cffa4b958efc617b8354ba29, code=8224, verifyKey=sso:captcha:f65400f0cffa4b958efc617b8354ba29
17:56:22.570 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=8224, captchaEnabled=true, uuid=f65400f0cffa4b958efc617b8354ba29}}
17:56:31.527 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
17:56:31.531 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
17:56:31.606 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
17:56:31.615 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
17:56:31.616 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
17:56:31.617 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: c0a6eec467124e78a2eb3cba632c7fa0 for client: backend
17:56:31.617 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
17:56:31.617 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
17:56:36.125 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=c0a6eec467124e78a2eb3cba632c7fa0, clientId=backend
17:56:36.127 [http-nio-9100-exec-8] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
17:56:36.127 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
17:56:36.130 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=a48c880264...
17:56:36.133 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
18:02:20.228 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
18:02:30.437 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 15.479 seconds (JVM running for 16.653)
18:07:00.578 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
18:07:02.654 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=0ecc86b615854d39ad9b99c1021e0d77, code=6467, verifyKey=sso:captcha:0ecc86b615854d39ad9b99c1021e0d77
18:07:02.657 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=6467, captchaEnabled=true, uuid=0ecc86b615854d39ad9b99c1021e0d77}}
18:07:14.223 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
18:07:15.996 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
18:07:16.091 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
18:07:16.418 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
18:07:16.418 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
18:07:16.446 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 4a4a4733d4e143efbc8f9a2c15c4d2e6 for client: backend
18:07:16.447 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
18:07:16.447 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
18:07:16.555 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=4a4a4733d4e143efbc8f9a2c15c4d2e6, clientId=backend
18:07:16.561 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
18:07:16.561 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
18:07:16.582 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=33be2d4366...
18:07:16.631 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
