10:29:56.358 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:30:09.003 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 18.054 seconds (JVM running for 19.252)
10:32:07.158 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
10:32:09.295 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=738f3381b62143a99159d5ba3105bc26, code=3794, verifyKey=sso:captcha:738f3381b62143a99159d5ba3105bc26
10:32:09.297 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3794, captchaEnabled=true, uuid=738f3381b62143a99159d5ba3105bc26}}
10:32:16.976 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
10:32:18.427 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
10:32:18.518 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
10:32:20.172 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
10:32:20.173 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
10:32:20.203 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 3f090c124f514e1dad525e004e772047 for client: backend
10:32:20.204 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
10:32:20.204 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
10:32:20.320 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=3f090c124f514e1dad525e004e772047, clientId=backend
10:32:20.329 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
10:32:20.330 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
10:32:20.360 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=31e8923af5...
10:32:20.379 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
10:49:16.719 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
10:49:16.740 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=d7785c4fcd3741bf8bad8c8a8e1a3d82, code=4663, verifyKey=sso:captcha:d7785c4fcd3741bf8bad8c8a8e1a3d82
10:49:16.740 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=4663, captchaEnabled=true, uuid=d7785c4fcd3741bf8bad8c8a8e1a3d82}}
10:49:36.146 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
10:49:36.182 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
10:49:36.265 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
10:49:36.278 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
10:49:36.278 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
10:49:36.318 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 1f2c1dc8ba41413b9dd79c2180d9caab for client: backend
10:49:36.319 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
10:49:36.319 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
10:49:36.335 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=1f2c1dc8ba41413b9dd79c2180d9caab, clientId=backend
10:49:36.339 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
10:49:36.339 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
10:49:36.342 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=dd6944d40a...
10:49:36.346 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
10:49:47.092 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=54d23da4e79746db8c86bf53eaf4feae, code=3464, verifyKey=sso:captcha:54d23da4e79746db8c86bf53eaf4feae
10:49:47.092 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3464, captchaEnabled=true, uuid=54d23da4e79746db8c86bf53eaf4feae}}
10:49:50.573 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
10:49:50.576 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
10:49:50.654 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
10:49:50.670 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
10:49:50.670 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
10:49:50.671 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: e12c60d44a864942aa3d00199cdb9c97 for client: backend
10:49:50.672 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
10:49:50.672 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
10:49:50.696 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=e12c60d44a864942aa3d00199cdb9c97, clientId=backend
10:49:50.700 [http-nio-9100-exec-8] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
10:49:50.700 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
10:49:50.703 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=067bea0865...
10:49:50.705 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
10:53:13.286 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
10:53:13.309 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=3e2395645da046efa82eeec4204e6cf2, code=3965, verifyKey=sso:captcha:3e2395645da046efa82eeec4204e6cf2
10:53:13.309 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3965, captchaEnabled=true, uuid=3e2395645da046efa82eeec4204e6cf2}}
10:53:23.316 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
10:53:23.319 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
10:53:23.399 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
10:53:23.408 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
10:53:23.408 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
10:53:23.408 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 1d8298be10f24fc9b20b9bdf42fcb852 for client: backend
10:53:23.409 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
10:53:23.409 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
10:53:23.422 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=1d8298be10f24fc9b20b9bdf42fcb852, clientId=backend
10:53:23.424 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
10:53:23.424 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
10:53:23.427 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=9d16ef8e8c...
10:53:23.430 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
10:59:23.640 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:59:31.758 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 12.293 seconds (JVM running for 13.629)
11:00:17.367 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
11:00:19.473 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=f7301b987c074829ac8227af94822545, code=8479, verifyKey=sso:captcha:f7301b987c074829ac8227af94822545
11:00:19.476 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=8479, captchaEnabled=true, uuid=f7301b987c074829ac8227af94822545}}
11:00:31.344 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=7018b66fbab443eaa525ca89b19900ba, code=2983, verifyKey=sso:captcha:7018b66fbab443eaa525ca89b19900ba
11:00:31.344 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=2983, captchaEnabled=true, uuid=7018b66fbab443eaa525ca89b19900ba}}
11:00:34.582 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
11:00:35.997 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
11:00:36.088 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
11:00:36.414 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
11:00:36.414 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
11:00:36.439 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: e36ee7858dca462797036c378d172edf for client: backend
11:00:36.439 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
11:00:36.439 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
11:00:36.542 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=e36ee7858dca462797036c378d172edf, clientId=backend
11:00:36.586 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
11:00:36.586 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
11:00:36.606 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=630a1ec164...
11:00:36.623 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
11:07:07.431 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:07:15.936 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 13.57 seconds (JVM running for 14.76)
11:09:08.685 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login?redirect=%2Fuser%2Fprofile
11:09:08.685 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login?redirect=%2Fuser%2Fprofile
11:09:10.748 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=381d3ad976994be9b961ac36b049aa93, code=2360, verifyKey=sso:captcha:381d3ad976994be9b961ac36b049aa93
11:09:10.751 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=2360, captchaEnabled=true, uuid=381d3ad976994be9b961ac36b049aa93}}
11:09:18.717 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
11:09:20.180 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
11:09:20.271 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
11:09:20.594 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
11:09:20.594 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
11:09:20.617 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 204190eb98ca46c7a49a06d083361120 for client: backend
11:09:20.618 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
11:09:20.618 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
11:09:20.715 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=204190eb98ca46c7a49a06d083361120, clientId=backend
11:09:20.719 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
11:09:20.721 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
11:09:20.739 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=cd7ca6a3da...
11:09:20.756 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
11:09:45.289 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login?redirect=%2Fuser%2Fprofile
11:09:45.316 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=df442719125c4bac8965f7a6684bedf7, code=3686, verifyKey=sso:captcha:df442719125c4bac8965f7a6684bedf7
11:09:45.316 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3686, captchaEnabled=true, uuid=df442719125c4bac8965f7a6684bedf7}}
11:09:54.766 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
11:09:54.771 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
11:09:54.850 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
11:09:54.867 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
11:09:54.867 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
11:09:54.869 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 54727a8fc8904b249222c6f3393ae41a for client: backend
11:09:54.869 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
11:09:54.869 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
11:09:54.898 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=54727a8fc8904b249222c6f3393ae41a, clientId=backend
11:09:54.902 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
11:09:54.902 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
11:09:54.905 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=586de8db63...
11:09:54.908 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
11:14:35.914 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:14:47.974 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 16.737 seconds (JVM running for 17.777)
11:18:40.138 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
11:18:42.167 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=0a0863565bab424b879f3c7fb94dcbd6, code=2295, verifyKey=sso:captcha:0a0863565bab424b879f3c7fb94dcbd6
11:18:42.169 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=2295, captchaEnabled=true, uuid=0a0863565bab424b879f3c7fb94dcbd6}}
11:18:48.033 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
11:18:49.514 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
11:18:49.605 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
11:18:50.104 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
11:18:50.104 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
11:18:50.127 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: 7f27c34fba2a4b25a015d3a1dfd3b522 for client: backend
11:18:50.127 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
11:18:50.127 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
11:18:50.193 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=7f27c34fba2a4b25a015d3a1dfd3b522, clientId=backend
11:18:50.199 [http-nio-9100-exec-4] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
11:18:50.199 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
11:18:50.217 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=0fb6de8d76...
11:18:50.233 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
11:24:22.688 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
11:24:22.707 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=c4134fc2e4a7490ba79cf3fa4e683f23, code=4923, verifyKey=sso:captcha:c4134fc2e4a7490ba79cf3fa4e683f23
11:24:22.707 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=4923, captchaEnabled=true, uuid=c4134fc2e4a7490ba79cf3fa4e683f23}}
11:24:31.333 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
11:24:31.366 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
11:24:31.450 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
11:24:31.463 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
11:24:31.463 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
11:24:31.466 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: d222d1fb17a4458394e81092f79513d2 for client: backend
11:24:31.467 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
11:24:31.467 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
11:24:31.479 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=d222d1fb17a4458394e81092f79513d2, clientId=backend
11:24:31.482 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
11:24:31.484 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
11:24:31.486 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=bb9a548c3f...
11:24:31.490 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
11:51:11.933 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://************:3000/login
11:51:11.992 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=fcb688a05daa41209f60ee4d75113f45, code=1862, verifyKey=sso:captcha:fcb688a05daa41209f60ee4d75113f45
11:51:11.994 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=1862, captchaEnabled=true, uuid=fcb688a05daa41209f60ee4d75113f45}}
11:51:36.665 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
11:51:36.669 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
11:51:36.750 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
11:51:36.761 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
11:51:36.761 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
11:51:36.763 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: ff95a18eb1c04d1e9897bd54ddb7c923 for client: backend
11:51:36.763 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
11:51:36.763 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
11:51:36.779 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=ff95a18eb1c04d1e9897bd54ddb7c923, clientId=backend
11:51:36.781 [http-nio-9100-exec-1] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
11:51:36.781 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
11:51:36.785 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=3aed07a934...
11:51:36.788 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
11:51:45.929 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://************:3000/login/
11:51:45.987 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=6acf40bcd6694ff4a2a0e6c4091aefa3, code=2761, verifyKey=sso:captcha:6acf40bcd6694ff4a2a0e6c4091aefa3
11:51:45.989 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=2761, captchaEnabled=true, uuid=6acf40bcd6694ff4a2a0e6c4091aefa3}}
11:52:02.240 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
11:52:02.243 [http-nio-9100-exec-6] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
11:52:02.319 [http-nio-9100-exec-6] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
11:52:02.319 [http-nio-9100-exec-6] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
11:52:02.326 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=722f23edfe764e52ab43ff0830aa85a8, code=1770, verifyKey=sso:captcha:722f23edfe764e52ab43ff0830aa85a8
11:52:02.326 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=1770, captchaEnabled=true, uuid=722f23edfe764e52ab43ff0830aa85a8}}
11:52:07.567 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://************:3000/login/
11:52:07.590 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=5800007815cf41c18050b7f6049e7edf, code=1592, verifyKey=sso:captcha:5800007815cf41c18050b7f6049e7edf
11:52:07.590 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=1592, captchaEnabled=true, uuid=5800007815cf41c18050b7f6049e7edf}}
13:07:30.933 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://************:3000/login
13:07:30.959 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=1d8bb9df49b5454d820c84b8ad657f81, code=1839, verifyKey=sso:captcha:1d8bb9df49b5454d820c84b8ad657f81
13:07:30.959 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=1839, captchaEnabled=true, uuid=1d8bb9df49b5454d820c84b8ad657f81}}
13:11:06.478 [main] INFO  c.r.s.RuoyiSSOApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
13:11:14.562 [main] INFO  c.r.s.RuoyiSSOApplication - [logStarted,61] - Started RuoyiSSOApplication in 11.882 seconds (JVM running for 13.187)
13:14:41.108 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://************:3000/login/
13:14:43.171 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=77f3cdf24dda481fa60cec09007fd268, code=3141, verifyKey=sso:captcha:77f3cdf24dda481fa60cec09007fd268
13:14:43.174 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3141, captchaEnabled=true, uuid=77f3cdf24dda481fa60cec09007fd268}}
13:16:22.181 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=market, redirectUri=http://localhost:9700/sso/callback, state=http://************:3000/login/
13:16:22.231 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=dc920758ca5d43ecba9868b2a94dd251, code=9291, verifyKey=sso:captcha:dc920758ca5d43ecba9868b2a94dd251
13:16:22.231 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=9291, captchaEnabled=true, uuid=dc920758ca5d43ecba9868b2a94dd251}}
13:16:32.039 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=market, redirectUri=http://localhost:9700/sso/callback, state=http://************:3000/login/
13:16:32.065 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=e3bd4591491c49b29e3c8906176ed131, code=3998, verifyKey=sso:captcha:e3bd4591491c49b29e3c8906176ed131
13:16:32.065 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3998, captchaEnabled=true, uuid=e3bd4591491c49b29e3c8906176ed131}}
13:16:55.508 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=market
13:16:56.978 [http-nio-9100-exec-10] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: market
13:16:57.061 [http-nio-9100-exec-10] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
13:16:57.061 [http-nio-9100-exec-10] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
13:16:57.068 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=199b644671ac4803827ccbbf1c0d0733, code=6164, verifyKey=sso:captcha:199b644671ac4803827ccbbf1c0d0733
13:16:57.069 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=6164, captchaEnabled=true, uuid=199b644671ac4803827ccbbf1c0d0733}}
13:17:26.903 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=market, redirectUri=http://localhost:9700/sso/callback, state=http://************:3000/login/
13:17:26.932 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=df45c581924248cb97918fad1a28a8d6, code=7645, verifyKey=sso:captcha:df45c581924248cb97918fad1a28a8d6
13:17:26.932 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=7645, captchaEnabled=true, uuid=df45c581924248cb97918fad1a28a8d6}}
13:17:37.405 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=4d473a83db8f4259b10eda01bbf3f537, code=9984, verifyKey=sso:captcha:4d473a83db8f4259b10eda01bbf3f537
13:17:37.405 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=9984, captchaEnabled=true, uuid=4d473a83db8f4259b10eda01bbf3f537}}
13:17:39.809 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=market
13:17:39.815 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: market
13:17:39.892 [http-nio-9100-exec-5] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
13:17:39.892 [http-nio-9100-exec-5] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
13:17:39.899 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=66c5ccd1423f4c98955e8396bcac47e8, code=2556, verifyKey=sso:captcha:66c5ccd1423f4c98955e8396bcac47e8
13:17:39.899 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=2556, captchaEnabled=true, uuid=66c5ccd1423f4c98955e8396bcac47e8}}
13:17:57.205 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=market
13:17:57.209 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: market
13:17:57.283 [http-nio-9100-exec-7] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
13:17:57.284 [http-nio-9100-exec-7] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
13:17:57.290 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=62663ca24555492abf41fbdb740e429a, code=7807, verifyKey=sso:captcha:62663ca24555492abf41fbdb740e429a
13:17:57.290 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=7807, captchaEnabled=true, uuid=62663ca24555492abf41fbdb740e429a}}
13:18:44.911 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=market
13:18:44.915 [http-nio-9100-exec-9] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: market
13:18:44.991 [http-nio-9100-exec-9] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
13:18:44.991 [http-nio-9100-exec-9] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
13:18:44.998 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=6d4f351f6feb4501aef96a6d8aae469a, code=3845, verifyKey=sso:captcha:6d4f351f6feb4501aef96a6d8aae469a
13:18:44.998 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=3845, captchaEnabled=true, uuid=6d4f351f6feb4501aef96a6d8aae469a}}
13:19:56.891 [http-nio-9100-exec-3] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=market
13:21:11.809 [http-nio-9100-exec-3] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: market
13:21:33.429 [http-nio-9100-exec-3] WARN  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,578] - SSO用户 18454831889 密码验证失败
13:21:33.429 [http-nio-9100-exec-3] WARN  c.r.s.c.SSOAuthController - [authenticate,217] - 用户 18454831889 登录失败: 用户名或密码错误
13:21:33.435 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=1fc3356b46a9465b85a1ae68a36ddf80, code=9175, verifyKey=sso:captcha:1fc3356b46a9465b85a1ae68a36ddf80
13:21:33.436 [http-nio-9100-exec-4] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=9175, captchaEnabled=true, uuid=1fc3356b46a9465b85a1ae68a36ddf80}}
13:22:05.653 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=backend, redirectUri=http://localhost:9200/sso/callback, state=http://localhost:81/login
13:22:05.670 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=e7c64c73211e4c03bbc7c030ac6704ba, code=5298, verifyKey=sso:captcha:e7c64c73211e4c03bbc7c030ac6704ba
13:22:05.671 [http-nio-9100-exec-6] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=5298, captchaEnabled=true, uuid=e7c64c73211e4c03bbc7c030ac6704ba}}
13:22:14.790 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=backend
13:22:14.794 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: backend
13:22:31.723 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,34] - 开始同步SSO用户到主系统Member表: 18454831889
13:22:32.074 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMainSystem,54] - 主系统Member不存在，这是正常情况（用户可能是从其他系统注册的）: 18454831889
13:22:32.075 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: backend
13:22:32.099 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: e0876532387044a5892c42d0a1130c36 for client: backend
13:22:32.099 [http-nio-9100-exec-7] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: backend
13:22:32.099 [http-nio-9100-exec-7] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
13:22:32.123 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=e0876532387044a5892c42d0a1130c36, clientId=backend
13:22:32.128 [http-nio-9100-exec-8] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=backend
13:22:32.128 [http-nio-9100-exec-8] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=backend
13:22:32.130 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getUserInfo,296] - 获取用户信息请求: accessToken=1ee5e4b496...
13:22:32.145 [http-nio-9100-exec-10] INFO  c.r.s.c.SSOAuthController - [getUserInfo,301] - 用户信息获取成功: userId=7
13:22:43.331 [http-nio-9100-exec-9] INFO  c.r.s.c.SSOAuthController - [loginPage,170] - SSO登录请求: clientId=market, redirectUri=http://localhost:9700/sso/callback, state=http://************:3000/login/
13:22:43.365 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,77] - 生成验证码成功: uuid=52912f771b5941ea8341e156e2c4b661, code=9009, verifyKey=sso:captcha:52912f771b5941ea8341e156e2c4b661
13:22:43.365 [http-nio-9100-exec-1] INFO  c.r.s.c.SSOAuthController - [getCaptcha,80] - 返回的AjaxResult: {msg=验证码生成成功, code=200, data={captchaCode=9009, captchaEnabled=true, uuid=52912f771b5941ea8341e156e2c4b661}}
13:22:52.625 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [authenticate,208] - 用户登录认证: username=18454831889, clientId=market
13:22:52.629 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,574] - SSO用户 18454831889 访问系统: market
13:36:42.409 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMarketSystem,67] - 开始检查市场系统用户: 18454831889
13:36:42.410 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOUserSyncServiceImpl - [syncToMarketSystem,75] - 市场系统用户将在首次访问时自动创建: 18454831889
13:36:42.411 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticateUser,600] - SSO用户 18454831889 验证成功，访问系统: market
13:36:42.415 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [generateAuthCode,131] - 生成授权码: cec69b84de5e4fffbadc0e2de5168ab9 for client: market
13:36:42.415 [http-nio-9100-exec-2] INFO  c.r.s.s.i.SSOAuthServiceImpl - [authenticate,267] - 用户 18454831889 登录成功，客户端: market
13:36:42.415 [http-nio-9100-exec-2] INFO  c.r.s.c.SSOAuthController - [authenticate,214] - 用户 18454831889 登录成功
13:36:42.518 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,240] - 授权码换取令牌: authCode=cec69b84de5e4fffbadc0e2de5168ab9, clientId=market
13:36:42.521 [http-nio-9100-exec-5] INFO  c.r.s.s.i.SSOAuthServiceImpl - [exchangeToken,342] - 授权码换取令牌成功: clientId=market
13:36:42.521 [http-nio-9100-exec-5] INFO  c.r.s.c.SSOAuthController - [getToken,245] - 令牌换取成功: clientId=market
