{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\gen\\basicInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\gen\\basicInfoForm.vue", "mtime": 1750151094311}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgaW5mbzogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgdGFibGVOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeihqOWQjeensCIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHRhYmxlQ29tbWVudDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXooajmj4/ov7AiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBjbGFzc05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5a6e5L2T57G75ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgZnVuY3Rpb25BdXRob3I6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5L2c6ICFIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0NCn07DQo="}, {"version": 3, "sources": ["basicInfoForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "basicInfoForm.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\r\n  <el-form ref=\"basicInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"表名称\" prop=\"tableName\">\r\n          <el-input placeholder=\"请输入仓库名称\" v-model=\"info.tableName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"表描述\" prop=\"tableComment\">\r\n          <el-input placeholder=\"请输入\" v-model=\"info.tableComment\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"实体类名称\" prop=\"className\">\r\n          <el-input placeholder=\"请输入\" v-model=\"info.className\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"作者\" prop=\"functionAuthor\">\r\n          <el-input placeholder=\"请输入\" v-model=\"info.functionAuthor\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"24\">\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input type=\"textarea\" :rows=\"3\" v-model=\"info.remark\"></el-input>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      rules: {\r\n        tableName: [\r\n          { required: true, message: \"请输入表名称\", trigger: \"blur\" }\r\n        ],\r\n        tableComment: [\r\n          { required: true, message: \"请输入表描述\", trigger: \"blur\" }\r\n        ],\r\n        className: [\r\n          { required: true, message: \"请输入实体类名称\", trigger: \"blur\" }\r\n        ],\r\n        functionAuthor: [\r\n          { required: true, message: \"请输入作者\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n};\r\n</script>\r\n"]}]}