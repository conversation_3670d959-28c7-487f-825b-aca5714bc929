{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\store.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\store.js", "mtime": 1750151094001}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRTdG9yZSA9IGFkZFN0b3JlOwpleHBvcnRzLmRlbFN0b3JlID0gZGVsU3RvcmU7CmV4cG9ydHMuZ2V0U3RvcmUgPSBnZXRTdG9yZTsKZXhwb3J0cy5saXN0U3RvcmUgPSBsaXN0U3RvcmU7CmV4cG9ydHMudXBkYXRlU3RvcmUgPSB1cGRhdGVTdG9yZTsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouW6lOeUqOeuoeeQhuWIl+ihqApmdW5jdGlvbiBsaXN0U3RvcmUocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy91dWMvc3RvcmUvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LlupTnlKjnrqHnkIbor6bnu4YKZnVuY3Rpb24gZ2V0U3RvcmUoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy91dWMvc3RvcmUvJyArIGlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7lupTnlKjnrqHnkIYKZnVuY3Rpb24gYWRkU3RvcmUoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9zdG9yZScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55bqU55So566h55CGCmZ1bmN0aW9uIHVwZGF0ZVN0b3JlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy91dWMvc3RvcmUnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5bqU55So566h55CGCmZ1bmN0aW9uIGRlbFN0b3JlKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvdXVjL3N0b3JlLycgKyBpZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listStore", "query", "request", "url", "method", "params", "getStore", "id", "addStore", "data", "updateStore", "delStore"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/uuc/store.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询应用管理列表\r\nexport function listStore(query) {\r\n  return request({\r\n    url: '/uuc/store/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询应用管理详细\r\nexport function getStore(id) {\r\n  return request({\r\n    url: '/uuc/store/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增应用管理\r\nexport function addStore(data) {\r\n  return request({\r\n    url: '/uuc/store',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改应用管理\r\nexport function updateStore(data) {\r\n  return request({\r\n    url: '/uuc/store',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除应用管理\r\nexport function delStore(id) {\r\n  return request({\r\n    url: '/uuc/store/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,QAAQA,CAACC,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa,GAAGI,EAAE;IACvBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,QAAQA,CAACJ,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa,GAAGI,EAAE;IACvBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}