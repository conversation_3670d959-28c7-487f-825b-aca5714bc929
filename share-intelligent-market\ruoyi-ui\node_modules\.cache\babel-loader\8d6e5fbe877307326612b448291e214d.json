{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\role\\selectUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\role\\selectUser.vue", "mtime": 1750151094300}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm51bWJlci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwp2YXIgX3JvbGUgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vcm9sZSIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJ10sCiAgcHJvcHM6IHsKICAgIC8vIOinkuiJsue8luWPtwogICAgcm9sZUlkOiB7CiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10KICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOmAieS4reaVsOe7hOWAvAogICAgICB1c2VySWRzOiBbXSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDmnKrmjojmnYPnlKjmiLfmlbDmja4KICAgICAgdXNlckxpc3Q6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICByb2xlSWQ6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHBob25lbnVtYmVyOiB1bmRlZmluZWQKICAgICAgfQogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOaYvuekuuW8ueahhgogICAgc2hvdzogZnVuY3Rpb24gc2hvdygpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5yb2xlSWQgPSB0aGlzLnJvbGVJZDsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIHRoaXMudmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgY2xpY2tSb3c6IGZ1bmN0aW9uIGNsaWNrUm93KHJvdykgewogICAgICB0aGlzLiRyZWZzLnRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihyb3cpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMudXNlcklkcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS51c2VySWQ7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOafpeivouihqOaVsOaNrgogICAgZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgKDAsIF9yb2xlLnVuYWxsb2NhdGVkVXNlckxpc3QpKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzLnVzZXJMaXN0ID0gcmVzLnJvd3M7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXMudG90YWw7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog6YCJ5oup5o6I5p2D55So5oi35pON5L2cICovaGFuZGxlU2VsZWN0VXNlcjogZnVuY3Rpb24gaGFuZGxlU2VsZWN0VXNlcigpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciByb2xlSWQgPSB0aGlzLnF1ZXJ5UGFyYW1zLnJvbGVJZDsKICAgICAgdmFyIHVzZXJJZHMgPSB0aGlzLnVzZXJJZHMuam9pbigiLCIpOwogICAgICBpZiAodXNlcklkcyA9PSAiIikgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHliIbphY3nmoTnlKjmiLciKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgKDAsIF9yb2xlLmF1dGhVc2VyU2VsZWN0QWxsKSh7CiAgICAgICAgcm9sZUlkOiByb2xlSWQsCiAgICAgICAgdXNlcklkczogdXNlcklkcwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczIuJG1vZGFsLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgIF90aGlzMi52aXNpYmxlID0gZmFsc2U7CiAgICAgICAgICBfdGhpczIuJGVtaXQoIm9rIik7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_role", "require", "dicts", "props", "roleId", "type", "Number", "String", "data", "visible", "userIds", "total", "userList", "queryParams", "pageNum", "pageSize", "undefined", "userName", "phonenumber", "methods", "show", "getList", "clickRow", "row", "$refs", "table", "toggleRowSelection", "handleSelectionChange", "selection", "map", "item", "userId", "_this", "unallocatedUserList", "then", "res", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectUser", "_this2", "join", "$modal", "msgError", "authUserSelectAll", "msgSuccess", "msg", "code", "$emit"], "sources": ["src/views/system/role/selectUser.vue"], "sourcesContent": ["<template>\r\n  <!-- 授权用户 -->\r\n  <el-dialog title=\"选择用户\" :visible.sync=\"visible\" width=\"800px\" top=\"5vh\" append-to-body>\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\r\n      <el-form-item label=\"用户名称\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n        <el-input\r\n          v-model=\"queryParams.phonenumber\"\r\n          placeholder=\"请输入手机号码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row>\r\n      <el-table @row-click=\"clickRow\" ref=\"table\" :data=\"userList\" @selection-change=\"handleSelectionChange\" height=\"260px\">\r\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n        <el-table-column label=\"用户名称\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"用户昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"手机\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-row>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"handleSelectUser\">确 定</el-button>\r\n      <el-button @click=\"visible = false\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { unallocatedUserList, authUserSelectAll } from \"@/api/system/role\";\r\nexport default {\r\n  dicts: ['sys_normal_disable'],\r\n  props: {\r\n    // 角色编号\r\n    roleId: {\r\n      type: [Number, String]\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      visible: false,\r\n      // 选中数组值\r\n      userIds: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 未授权用户数据\r\n      userList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        roleId: undefined,\r\n        userName: undefined,\r\n        phonenumber: undefined\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 显示弹框\r\n    show() {\r\n      this.queryParams.roleId = this.roleId;\r\n      this.getList();\r\n      this.visible = true;\r\n    },\r\n    clickRow(row) {\r\n      this.$refs.table.toggleRowSelection(row);\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.userIds = selection.map(item => item.userId);\r\n    },\r\n    // 查询表数据\r\n    getList() {\r\n      unallocatedUserList(this.queryParams).then(res => {\r\n        this.userList = res.rows;\r\n        this.total = res.total;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 选择授权用户操作 */\r\n    handleSelectUser() {\r\n      const roleId = this.queryParams.roleId;\r\n      const userIds = this.userIds.join(\",\");\r\n      if (userIds == \"\") {\r\n        this.$modal.msgError(\"请选择要分配的用户\");\r\n        return;\r\n      }\r\n      authUserSelectAll({ roleId: roleId, userIds: userIds }).then(res => {\r\n        this.$modal.msgSuccess(res.msg);\r\n        if (res.code === 200) {\r\n          this.visible = false;\r\n          this.$emit(\"ok\");\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA2DA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,KAAA;EACAC,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAX,MAAA,EAAAY,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,WAAA,EAAAF;MACA;IACA;EACA;EACAG,OAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAP,WAAA,CAAAT,MAAA,QAAAA,MAAA;MACA,KAAAiB,OAAA;MACA,KAAAZ,OAAA;IACA;IACAa,QAAA,WAAAA,SAAAC,GAAA;MACA,KAAAC,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAH,GAAA;IACA;IACA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAlB,OAAA,GAAAkB,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;IACA;IACA;IACAV,OAAA,WAAAA,QAAA;MAAA,IAAAW,KAAA;MACA,IAAAC,yBAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAApB,QAAA,GAAAuB,GAAA,CAAAC,IAAA;QACAJ,KAAA,CAAArB,KAAA,GAAAwB,GAAA,CAAAxB,KAAA;MACA;IACA;IACA,aACA0B,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAO,OAAA;IACA;IACA,aACAiB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,eACAG,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAArC,MAAA,QAAAS,WAAA,CAAAT,MAAA;MACA,IAAAM,OAAA,QAAAA,OAAA,CAAAgC,IAAA;MACA,IAAAhC,OAAA;QACA,KAAAiC,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAC,uBAAA;QAAAzC,MAAA,EAAAA,MAAA;QAAAM,OAAA,EAAAA;MAAA,GAAAwB,IAAA,WAAAC,GAAA;QACAM,MAAA,CAAAE,MAAA,CAAAG,UAAA,CAAAX,GAAA,CAAAY,GAAA;QACA,IAAAZ,GAAA,CAAAa,IAAA;UACAP,MAAA,CAAAhC,OAAA;UACAgC,MAAA,CAAAQ,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}