{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1750151094141}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "props", "value", "String", "Object", "Array", "showList", "type", "Boolean", "default", "limit", "Number", "fileSize", "fileType", "isShowTip", "data", "number", "uploadList", "dialogImageUrl", "dialogVisible", "hideUpload", "uploadImgUrl", "process", "env", "VUE_APP_BASE_API", "headers", "authorization", "getToken", "fileList", "watch", "handler", "val", "list", "isArray", "split", "map", "item", "name", "url", "deep", "immediate", "computed", "showTip", "methods", "handleRemove", "file", "findex", "f", "indexOf", "splice", "$emit", "listToString", "handleUploadSuccess", "res", "push", "length", "concat", "$modal", "closeLoading", "handleBeforeUpload", "isImg", "fileExtension", "lastIndexOf", "slice", "some", "msgError", "join", "isLt", "size", "loading", "handleExceed", "handleUploadError", "handlePictureCardPreview", "separator", "strs", "i", "substr"], "sources": ["src/components/ImageUpload/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      name=\"file\"\r\n      :on-remove=\"handleRemove\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\" v-if='showList || !fileList.length'></i>\r\n      <!-- <img v-if='!showList && fileList.length' :src='fileList[0].url'></img> -->\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 是否显示wen文件列表\r\n    showList: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5,\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5,\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"],\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/shop/data/upload/image\", // 上传的图片服务器地址\r\n      headers: {\r\n        authorization: getToken(),\r\n      },\r\n      fileList: []\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',');\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              item = { name: item, url: item };\r\n            }\r\n            return item;\r\n          });\r\n        } else {\r\n          this.fileList = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize);\r\n    },\r\n  },\r\n  methods: {\r\n    // 删除图片\r\n    handleRemove(file, fileList) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name);\r\n      if(findex > -1) {\r\n        this.fileList.splice(findex, 1);\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n      }\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res) {\r\n      this.uploadList.push({ name: res.url, url: res.url });\r\n      if (this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList);\r\n        this.uploadList = [];\r\n        this.number = 0;\r\n        this.$emit(\"input\", this.listToString(this.fileList));\r\n        this.$modal.closeLoading();\r\n      }\r\n    },\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false;\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\";\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true;\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\r\n          return false;\r\n        });\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1;\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join(\"/\")}图片格式文件!`);\r\n        return false;\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);\r\n          return false;\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候...\");\r\n      this.number++;\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\");\r\n      this.$modal.closeLoading();\r\n    },\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\";\r\n      separator = separator || \",\";\r\n      for (let i in list) {\r\n        strs += list[i].url + separator;\r\n      }\r\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n    display: none;\r\n}\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n    transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n    opacity: 0;\r\n    transform: translateY(0);\r\n}\r\n\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AA6CA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,KAAA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,QAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAI,QAAA;MACAN,IAAA,EAAAF,KAAA;MACAI,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAK,SAAA;MACAP,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,UAAA;MACAC,cAAA;MACAC,aAAA;MACAC,UAAA;MACAC,YAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,OAAA;QACAC,aAAA,MAAAC,cAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA3B,KAAA;MACA4B,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA;UACA;UACA,IAAAC,IAAA,GAAA3B,KAAA,CAAA4B,OAAA,CAAAF,GAAA,IAAAA,GAAA,QAAA7B,KAAA,CAAAgC,KAAA;UACA;UACA,KAAAN,QAAA,GAAAI,IAAA,CAAAG,GAAA,WAAAC,IAAA;YACA,WAAAA,IAAA;cACAA,IAAA;gBAAAC,IAAA,EAAAD,IAAA;gBAAAE,GAAA,EAAAF;cAAA;YACA;YACA,OAAAA,IAAA;UACA;QACA;UACA,KAAAR,QAAA;UACA;QACA;MACA;MACAW,IAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAA5B,SAAA,UAAAD,QAAA,SAAAD,QAAA;IACA;EACA;EACA+B,OAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA,EAAAjB,QAAA;MACA,IAAAkB,MAAA,QAAAlB,QAAA,CAAAO,GAAA,WAAAY,CAAA;QAAA,OAAAA,CAAA,CAAAV,IAAA;MAAA,GAAAW,OAAA,CAAAH,IAAA,CAAAR,IAAA;MACA,IAAAS,MAAA;QACA,KAAAlB,QAAA,CAAAqB,MAAA,CAAAH,MAAA;QACA,KAAAI,KAAA,eAAAC,YAAA,MAAAvB,QAAA;MACA;IACA;IACA;IACAwB,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAApC,UAAA,CAAAqC,IAAA;QAAAjB,IAAA,EAAAgB,GAAA,CAAAf,GAAA;QAAAA,GAAA,EAAAe,GAAA,CAAAf;MAAA;MACA,SAAArB,UAAA,CAAAsC,MAAA,UAAAvC,MAAA;QACA,KAAAY,QAAA,QAAAA,QAAA,CAAA4B,MAAA,MAAAvC,UAAA;QACA,KAAAA,UAAA;QACA,KAAAD,MAAA;QACA,KAAAkC,KAAA,eAAAC,YAAA,MAAAvB,QAAA;QACA,KAAA6B,MAAA,CAAAC,YAAA;MACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAd,IAAA;MACA,IAAAe,KAAA;MACA,SAAA/C,QAAA,CAAA0C,MAAA;QACA,IAAAM,aAAA;QACA,IAAAhB,IAAA,CAAAR,IAAA,CAAAyB,WAAA;UACAD,aAAA,GAAAhB,IAAA,CAAAR,IAAA,CAAA0B,KAAA,CAAAlB,IAAA,CAAAR,IAAA,CAAAyB,WAAA;QACA;QACAF,KAAA,QAAA/C,QAAA,CAAAmD,IAAA,WAAAzD,IAAA;UACA,IAAAsC,IAAA,CAAAtC,IAAA,CAAAyC,OAAA,CAAAzC,IAAA;UACA,IAAAsD,aAAA,IAAAA,aAAA,CAAAb,OAAA,CAAAzC,IAAA;UACA;QACA;MACA;QACAqD,KAAA,GAAAf,IAAA,CAAAtC,IAAA,CAAAyC,OAAA;MACA;MAEA,KAAAY,KAAA;QACA,KAAAH,MAAA,CAAAQ,QAAA,kEAAAT,MAAA,MAAA3C,QAAA,CAAAqD,IAAA;QACA;MACA;MACA,SAAAtD,QAAA;QACA,IAAAuD,IAAA,GAAAtB,IAAA,CAAAuB,IAAA,sBAAAxD,QAAA;QACA,KAAAuD,IAAA;UACA,KAAAV,MAAA,CAAAQ,QAAA,6EAAAT,MAAA,MAAA5C,QAAA;UACA;QACA;MACA;MACA,KAAA6C,MAAA,CAAAY,OAAA;MACA,KAAArD,MAAA;IACA;IACA;IACAsD,YAAA,WAAAA,aAAA;MACA,KAAAb,MAAA,CAAAQ,QAAA,iEAAAT,MAAA,MAAA9C,KAAA;IACA;IACA;IACA6D,iBAAA,WAAAA,kBAAA;MACA,KAAAd,MAAA,CAAAQ,QAAA;MACA,KAAAR,MAAA,CAAAC,YAAA;IACA;IACA;IACAc,wBAAA,WAAAA,yBAAA3B,IAAA;MACA,KAAA3B,cAAA,GAAA2B,IAAA,CAAAP,GAAA;MACA,KAAAnB,aAAA;IACA;IACA;IACAgC,YAAA,WAAAA,aAAAnB,IAAA,EAAAyC,SAAA;MACA,IAAAC,IAAA;MACAD,SAAA,GAAAA,SAAA;MACA,SAAAE,CAAA,IAAA3C,IAAA;QACA0C,IAAA,IAAA1C,IAAA,CAAA2C,CAAA,EAAArC,GAAA,GAAAmC,SAAA;MACA;MACA,OAAAC,IAAA,SAAAA,IAAA,CAAAE,MAAA,IAAAF,IAAA,CAAAnB,MAAA;IACA;EACA;AACA", "ignoreList": []}]}