{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\partner\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\partner\\index.vue", "mtime": 1750151094258}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIGxpc3RQYXJ0bmVyLA0KICAgIGdldFBhcnRuZXIsDQogICAgZGVsUGFydG5lciwNCiAgICBhZGRQYXJ0bmVyLA0KICAgIHVwZGF0ZVBhcnRuZXIsDQp9IGZyb20gIkAvYXBpL3V1Yy9wYXJ0bmVyIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIG5hbWU6ICJQYXJ0bmVyIiwNCiAgICBkaWN0czogWyJ1dWNfY29sbGFib3JhdGl2ZV9hcmVhcyJdLA0KICAgIGRhdGEoKSB7DQogICAgICAgIGxldCBjaGVja1Bob25lID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgICAgICAgbGV0IHJlZyA9IC9eMVszNDU3ODldXGR7OX0kLzsNCiAgICAgICAgICAgIGlmICghcmVnLnRlc3QodmFsdWUpKSB7DQogICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLor7fovpPlhaUxMeS9jeaJi+acuuWPtyIpKTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgfTsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgICAgICAgaWRzOiBbXSwNCiAgICAgICAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgICAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgICAgICAgdG90YWw6IDAsDQogICAgICAgICAgICAvLyDlkIjkvZzkvJnkvLTooajmoLzmlbDmja4NCiAgICAgICAgICAgIHBhcnRuZXJMaXN0OiBbXSwNCiAgICAgICAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgICAgICAgICBmaWVsZE5hbWU6IG51bGwsDQogICAgICAgICAgICAgICAgY29udGFjdDogbnVsbCwNCiAgICAgICAgICAgICAgICBwaG9uZTogbnVsbCwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvLyDooajljZXlj4LmlbANCiAgICAgICAgICAgIGZvcm06IHt9LA0KICAgICAgICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICAgICAgICBydWxlczogew0KICAgICAgICAgICAgICAgIC8vIGZpZWxkTmFtZTogWw0KICAgICAgICAgICAgICAgIC8vICAgICB7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIG1lc3NhZ2U6ICLpoobln5/lkI3np7DkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsDQogICAgICAgICAgICAgICAgLy8gICAgIH0sDQogICAgICAgICAgICAgICAgLy8gXSwNCiAgICAgICAgICAgICAgICAvLyB0aG91Z2h0czogWw0KICAgICAgICAgICAgICAgIC8vICAgICB7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIG1lc3NhZ2U6ICLlkIjkvZzmgJ3ot6/kuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgIC8vICAgICB9LA0KICAgICAgICAgICAgICAgIC8vIF0sDQogICAgICAgICAgICAgICAgY29udGFjdDogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLogZTns7vkurrkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICAgICAgcGhvbmU6IFsNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6IGU57O755S16K+d5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICAgICAgICAgICAgICB2YWxpZGF0b3I6IGNoZWNrUGhvbmUsDQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+3IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgICAvKiog5p+l6K+i5ZCI5L2c5LyZ5Ly05YiX6KGoICovDQogICAgICAgIGdldExpc3QoKSB7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgICAgbGlzdFBhcnRuZXIodGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLnBhcnRuZXJMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgICAgIGNhbmNlbCgpIHsNCiAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICB9LA0KICAgICAgICAvLyDooajljZXph43nva4NCiAgICAgICAgcmVzZXQoKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgICAgICAgICAgaWQ6IG51bGwsDQogICAgICAgICAgICAgICAgZmllbGRDb2RlOiBudWxsLA0KICAgICAgICAgICAgICAgIGZpZWxkTmFtZTogbnVsbCwNCiAgICAgICAgICAgICAgICB0aG91Z2h0czogbnVsbCwNCiAgICAgICAgICAgICAgICBidHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgICAgICAgYnR5cGVOYW1lOiBudWxsLA0KICAgICAgICAgICAgICAgIGNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgICAgICAgcGhvbmU6IG51bGwsDQogICAgICAgICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICAgICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgICAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB9LA0KICAgICAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICAgICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgICAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICAgICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpOw0KICAgICAgICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOWQiOS9nOS8meS8tCI7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgICAgICAgY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICAgICAgICBnZXRQYXJ0bmVyKGlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWQiOS9nOS8meS8tCI7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgICAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZVBhcnRuZXIodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICBhZGRQYXJ0bmVyKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICAgICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5ZCI5L2c5LyZ5Ly057yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpDQogICAgICAgICAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGVsUGFydG5lcihpZHMpOw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgICAgICB9LA0KICAgICAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgICAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICAgICAgICAgInV1Yy9wYXJ0bmVyL2V4cG9ydCIsDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgYHBhcnRuZXJfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGANCiAgICAgICAgICAgICk7DQogICAgICAgIH0sDQogICAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgOA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/ningmengdou/partner", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"queryForm\"\r\n            size=\"small\"\r\n            :inline=\"true\"\r\n            v-show=\"showSearch\"\r\n            label-width=\"68px\"\r\n        >\r\n            <el-form-item label=\"领域名称\" prop=\"fieldName\">\r\n                <el-select\r\n                    v-model=\"queryParams.fieldName\"\r\n                    placeholder=\"请选择领域名称\"\r\n                    clearable\r\n                >\r\n                    <el-option\r\n                        v-for=\"dict in dict.type.uuc_collaborative_areas\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                    />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人\" prop=\"contact\">\r\n                <el-input\r\n                    v-model=\"queryParams.contact\"\r\n                    placeholder=\"请输入联系人\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                <el-input\r\n                    v-model=\"queryParams.phone\"\r\n                    placeholder=\"请输入联系电话\"\r\n                    clearable\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                >\r\n                <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                >\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"primary\"\r\n                    plain\r\n                    icon=\"el-icon-plus\"\r\n                    size=\"mini\"\r\n                    @click=\"handleAdd\"\r\n                    v-hasPermi=\"['uuc:partner:add']\"\r\n                    >新增</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"success\"\r\n                    plain\r\n                    icon=\"el-icon-edit\"\r\n                    size=\"mini\"\r\n                    :disabled=\"single\"\r\n                    @click=\"handleUpdate\"\r\n                    v-hasPermi=\"['uuc:partner:edit']\"\r\n                    >修改</el-button\r\n                >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"danger\"\r\n                    plain\r\n                    icon=\"el-icon-delete\"\r\n                    size=\"mini\"\r\n                    :disabled=\"multiple\"\r\n                    @click=\"handleDelete\"\r\n                    v-hasPermi=\"['uuc:partner:remove']\"\r\n                    >删除</el-button\r\n                >\r\n            </el-col>\r\n            <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['uuc:partner:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n            <right-toolbar\r\n                :showSearch.sync=\"showSearch\"\r\n                @queryTable=\"getList\"\r\n            ></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"partnerList\"\r\n            @selection-change=\"handleSelectionChange\"\r\n        >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n            <el-table-column label=\"领域名称\" align=\"center\" prop=\"fieldName\">\r\n                <template slot-scope=\"scope\">\r\n                    <dict-tag\r\n                        :options=\"dict.type.uuc_collaborative_areas\"\r\n                        :value=\"scope.row.fieldName\"\r\n                    />\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"合作思路\" align=\"center\" prop=\"thoughts\" />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"contact\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"phone\" />\r\n            <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n            <el-table-column\r\n                label=\"操作\"\r\n                align=\"center\"\r\n                class-name=\"small-padding fixed-width\"\r\n            >\r\n                <template slot-scope=\"scope\">\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleUpdate(scope.row)\"\r\n                        v-hasPermi=\"['uuc:partner:edit']\"\r\n                        >修改</el-button\r\n                    >\r\n                    <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"handleDelete(scope.row)\"\r\n                        v-hasPermi=\"['uuc:partner:remove']\"\r\n                        >删除</el-button\r\n                    >\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改合作伙伴对话框 -->\r\n        <el-dialog\r\n            :title=\"title\"\r\n            :visible.sync=\"open\"\r\n            width=\"500px\"\r\n            append-to-body\r\n        >\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n                <el-form-item label=\"领域名称\" prop=\"fieldName\">\r\n                    <el-select\r\n                        v-model=\"form.fieldName\"\r\n                        placeholder=\"请选择领域名称\"\r\n                    >\r\n                        <el-option\r\n                            v-for=\"dict in dict.type.uuc_collaborative_areas\"\r\n                            :key=\"dict.value\"\r\n                            :label=\"dict.label\"\r\n                            :value=\"dict.value\"\r\n                        ></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合作思路\" prop=\"thoughts\">\r\n                    <el-input\r\n                        v-model=\"form.thoughts\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"255\"\r\n                        placeholder=\"请输入合作思路\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" prop=\"contact\">\r\n                    <el-input\r\n                        v-model=\"form.contact\"\r\n                        maxlength=\"20\"\r\n                        placeholder=\"请输入联系人\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                    <el-input\r\n                        v-model=\"form.phone\"\r\n                        maxlength=\"20\"\r\n                        type=\"number\"\r\n                        placeholder=\"请输入联系电话\"\r\n                    />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                    <el-input\r\n                        v-model=\"form.remark\"\r\n                        type=\"textarea\"\r\n                        maxlength=\"500\"\r\n                        placeholder=\"请输入内容\"\r\n                    />\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    listPartner,\r\n    getPartner,\r\n    delPartner,\r\n    addPartner,\r\n    updatePartner,\r\n} from \"@/api/uuc/partner\";\r\n\r\nexport default {\r\n    name: \"Partner\",\r\n    dicts: [\"uuc_collaborative_areas\"],\r\n    data() {\r\n        let checkPhone = (rule, value, callback) => {\r\n            let reg = /^1[345789]\\d{9}$/;\r\n            if (!reg.test(value)) {\r\n                callback(new Error(\"请输入11位手机号\"));\r\n            } else {\r\n                callback();\r\n            }\r\n        };\r\n        return {\r\n            // 遮罩层\r\n            loading: true,\r\n            // 选中数组\r\n            ids: [],\r\n            // 非单个禁用\r\n            single: true,\r\n            // 非多个禁用\r\n            multiple: true,\r\n            // 显示搜索条件\r\n            showSearch: true,\r\n            // 总条数\r\n            total: 0,\r\n            // 合作伙伴表格数据\r\n            partnerList: [],\r\n            // 弹出层标题\r\n            title: \"\",\r\n            // 是否显示弹出层\r\n            open: false,\r\n            // 查询参数\r\n            queryParams: {\r\n                pageNum: 1,\r\n                pageSize: 10,\r\n                fieldName: null,\r\n                contact: null,\r\n                phone: null,\r\n            },\r\n            // 表单参数\r\n            form: {},\r\n            // 表单校验\r\n            rules: {\r\n                // fieldName: [\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"领域名称不能为空\",\r\n                //         trigger: \"change\",\r\n                //     },\r\n                // ],\r\n                // thoughts: [\r\n                //     {\r\n                //         required: true,\r\n                //         message: \"合作思路不能为空\",\r\n                //         trigger: \"blur\",\r\n                //     },\r\n                // ],\r\n                contact: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系人不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                phone: [\r\n                    {\r\n                        required: true,\r\n                        message: \"联系电话不能为空\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    {\r\n                        type: \"number\",\r\n                        validator: checkPhone,\r\n                        message: \"请输入正确的手机号\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    created() {\r\n        this.getList();\r\n    },\r\n    methods: {\r\n        /** 查询合作伙伴列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listPartner(this.queryParams).then((response) => {\r\n                this.partnerList = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                fieldCode: null,\r\n                fieldName: null,\r\n                thoughts: null,\r\n                btypeCode: null,\r\n                btypeName: null,\r\n                contact: null,\r\n                phone: null,\r\n                remark: null,\r\n                createBy: null,\r\n                createTime: null,\r\n                updateBy: null,\r\n                updateTime: null,\r\n            };\r\n            this.resetForm(\"form\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 多选框选中数据\r\n        handleSelectionChange(selection) {\r\n            this.ids = selection.map((item) => item.id);\r\n            this.single = selection.length !== 1;\r\n            this.multiple = !selection.length;\r\n        },\r\n        /** 新增按钮操作 */\r\n        handleAdd() {\r\n            this.reset();\r\n            this.open = true;\r\n            this.title = \"添加合作伙伴\";\r\n        },\r\n        /** 修改按钮操作 */\r\n        handleUpdate(row) {\r\n            this.reset();\r\n            const id = row.id || this.ids;\r\n            getPartner(id).then((response) => {\r\n                this.form = response.data;\r\n                this.open = true;\r\n                this.title = \"修改合作伙伴\";\r\n            });\r\n        },\r\n        /** 提交按钮 */\r\n        submitForm() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.id != null) {\r\n                        updatePartner(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"修改成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    } else {\r\n                        addPartner(this.form).then((response) => {\r\n                            this.$modal.msgSuccess(\"新增成功\");\r\n                            this.open = false;\r\n                            this.getList();\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        /** 删除按钮操作 */\r\n        handleDelete(row) {\r\n            const ids = row.id || this.ids;\r\n            this.$modal\r\n                .confirm('是否确认删除合作伙伴编号为\"' + ids + '\"的数据项？')\r\n                .then(function () {\r\n                    return delPartner(ids);\r\n                })\r\n                .then(() => {\r\n                    this.getList();\r\n                    this.$modal.msgSuccess(\"删除成功\");\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        /** 导出按钮操作 */\r\n        handleExport() {\r\n            this.download(\r\n                \"uuc/partner/export\",\r\n                {\r\n                    ...this.queryParams,\r\n                },\r\n                `partner_${new Date().getTime()}.xlsx`\r\n            );\r\n        },\r\n    },\r\n};\r\n</script>\r\n"]}]}