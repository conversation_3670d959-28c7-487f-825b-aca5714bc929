
e76a49c6c91c29ce46a3765ae07ba4ad2ebd647e	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Fdocument\\u002Fcontent.min.css\",\"contentHash\":\"a73165222a1f85c0c89a4ad0e1bc95a0\"}","integrity":"sha512-yvhAln0CxVn6YJSYoj7rAZUK69UiEelH0IaSi16fGANrvgvv0TdNUVEEclwWANrs+WlMEExcYdWlEQB12d6fpA==","time":1750496064273,"size":1942}