{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\news.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\uuc\\news.js", "mtime": 1750151093997}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGROZXdzID0gYWRkTmV3czsKZXhwb3J0cy5kZWxOZXdzID0gZGVsTmV3czsKZXhwb3J0cy5nZXROZXdzID0gZ2V0TmV3czsKZXhwb3J0cy5saXN0TmV3cyA9IGxpc3ROZXdzOwpleHBvcnRzLnVwZGF0ZU5ld3MgPSB1cGRhdGVOZXdzOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i5LyB5Lia5Yqo5oCB5YiX6KGoCmZ1bmN0aW9uIGxpc3ROZXdzKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvdXVjL25ld3MvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LkvIHkuJrliqjmgIHor6bnu4YKZnVuY3Rpb24gZ2V0TmV3cyhpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9uZXdzLycgKyBpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5LyB5Lia5Yqo5oCBCmZ1bmN0aW9uIGFkZE5ld3MoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3V1Yy9uZXdzJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnkvIHkuJrliqjmgIEKZnVuY3Rpb24gdXBkYXRlTmV3cyhkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvdXVjL25ld3MnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5LyB5Lia5Yqo5oCBCmZ1bmN0aW9uIGRlbE5ld3MoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy91dWMvbmV3cy8nICsgaWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listNews", "query", "request", "url", "method", "params", "getNews", "id", "addNews", "data", "updateNews", "delNews"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/uuc/news.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询企业动态列表\r\nexport function listNews(query) {\r\n  return request({\r\n    url: '/uuc/news/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询企业动态详细\r\nexport function getNews(id) {\r\n  return request({\r\n    url: '/uuc/news/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增企业动态\r\nexport function addNews(data) {\r\n  return request({\r\n    url: '/uuc/news',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改企业动态\r\nexport function updateNews(data) {\r\n  return request({\r\n    url: '/uuc/news',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除企业动态\r\nexport function delNews(id) {\r\n  return request({\r\n    url: '/uuc/news/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,EAAE,EAAE;EAC1B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGI,EAAE;IACtBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,EAAE,EAAE;EAC1B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGI,EAAE;IACtBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}