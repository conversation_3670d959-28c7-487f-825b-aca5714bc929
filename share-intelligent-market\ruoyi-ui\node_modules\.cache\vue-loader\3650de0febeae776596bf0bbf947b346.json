{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\components\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\components\\list.vue", "mtime": 1750151094270}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZm9ybTogewogICAgICAgIHBob25lOiAiIiwKICAgICAgICBuYW1lOiAiIiwKICAgICAgfSwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGxpc3Q6IFt7CiAgICAgICAgdGFiMTogIkREMjAyMjA1MjctMDAwNSIsCiAgICAgICAgdGFiMjogIumdkuWym+WFrOWPuCIsCiAgICAgICAgdGFiMzogIueOi+S8nyIsCiAgICAgICAgdGFiNDogIjE5MTc4NjIzNjU0IiwKICAgICAgICB0YWI1OiAiMTIwIiwKICAgICAgICB0YWI2OiAiMzUwMCIsCiAgICAgICAgdGFiNzogIjExMSIsCiAgICAgIH1dLAogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgb3BlbigpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgIH0KICB9LAp9Cg=="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/project/inquiry/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog title=\"查看所有报价\" :visible.sync=\"dialogVisible\" append-to-body center>\r\n      <el-form ref=\"form1\" :label-position=\"'left'\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-table v-loading=\"loading\" border :data=\"list\">\r\n          <el-table-column label=\"序号\" align=\"center\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.$index + 1 }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"单号\" align=\"center\" prop=\"tab1\" />\r\n          <el-table-column label=\"报价公司\" align=\"center\" prop=\"tab2\" />\r\n          <el-table-column label=\"联系人\" align=\"center\" prop=\"tab3\" />\r\n          <el-table-column label=\"联系方式\" align=\"center\" prop=\"tab4\" />\r\n          <el-table-column label=\"含税单价\" align=\"center\" prop=\"tab5\" />\r\n          <el-table-column label=\"含税总价\" align=\"center\" prop=\"tab6\" />\r\n          <el-table-column label=\"运费\" align=\"center\" prop=\"tab7\" />\r\n        </el-table>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">确 定</el-button>\r\n        <!-- <el-button @click=\"cancel('open')\">取 消</el-button> -->\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        form: {\r\n          phone: \"\",\r\n          name: \"\",\r\n        },\r\n        dialogVisible: false,\r\n        list: [{\r\n          tab1: \"DD20220527-0005\",\r\n          tab2: \"青岛公司\",\r\n          tab3: \"王伟\",\r\n          tab4: \"19178623654\",\r\n          tab5: \"120\",\r\n          tab6: \"3500\",\r\n          tab7: \"111\",\r\n        }],\r\n      }\r\n    },\r\n    methods: {\r\n      open() {\r\n        this.dialogVisible = true;\r\n      },\r\n      submitForm() {\r\n        this.dialogVisible = false;\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}