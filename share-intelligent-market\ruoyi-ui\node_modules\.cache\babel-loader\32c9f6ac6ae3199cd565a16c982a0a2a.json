{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\gen\\editTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\gen\\editTable.vue", "mtime": 1750151094312}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zb3J0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zcGxpY2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5ldmVyeS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIik7CnZhciBfZ2VuID0gcmVxdWlyZSgiQC9hcGkvdG9vbC9nZW4iKTsKdmFyIF90eXBlID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL2RpY3QvdHlwZSIpOwp2YXIgX21lbnUgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vbWVudSIpOwp2YXIgX2Jhc2ljSW5mb0Zvcm0gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vYmFzaWNJbmZvRm9ybSIpKTsKdmFyIF9nZW5JbmZvRm9ybSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9nZW5JbmZvRm9ybSIpKTsKdmFyIF9zb3J0YWJsZWpzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJzb3J0YWJsZWpzIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIkdlbkVkaXQiLAogIGNvbXBvbmVudHM6IHsKICAgIGJhc2ljSW5mb0Zvcm06IF9iYXNpY0luZm9Gb3JtLmRlZmF1bHQsCiAgICBnZW5JbmZvRm9ybTogX2dlbkluZm9Gb3JtLmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpgInkuK3pgInpobnljaHnmoQgbmFtZQogICAgICBhY3RpdmVOYW1lOiAiY29sdW1uSW5mbyIsCiAgICAgIC8vIOihqOagvOeahOmrmOW6pgogICAgICB0YWJsZUhlaWdodDogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbEhlaWdodCAtIDI0NSArICJweCIsCiAgICAgIC8vIOihqOS/oeaBrwogICAgICB0YWJsZXM6IFtdLAogICAgICAvLyDooajliJfkv6Hmga8KICAgICAgY29sdW1uczogW10sCiAgICAgIC8vIOWtl+WFuOS/oeaBrwogICAgICBkaWN0T3B0aW9uczogW10sCiAgICAgIC8vIOiPnOWNleS/oeaBrwogICAgICBtZW51czogW10sCiAgICAgIC8vIOihqOivpue7huS/oeaBrwogICAgICBpbmZvOiB7fQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdmFyIHRhYmxlSWQgPSB0aGlzLiRyb3V0ZS5wYXJhbXMgJiYgdGhpcy4kcm91dGUucGFyYW1zLnRhYmxlSWQ7CiAgICBpZiAodGFibGVJZCkgewogICAgICAvLyDojrflj5booajor6bnu4bkv6Hmga8KICAgICAgKDAsIF9nZW4uZ2V0R2VuVGFibGUpKHRhYmxlSWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzLmNvbHVtbnMgPSByZXMuZGF0YS5yb3dzOwogICAgICAgIF90aGlzLmluZm8gPSByZXMuZGF0YS5pbmZvOwogICAgICAgIF90aGlzLnRhYmxlcyA9IHJlcy5kYXRhLnRhYmxlczsKICAgICAgfSk7CiAgICAgIC8qKiDmn6Xor6LlrZflhbjkuIvmi4nliJfooaggKi8KICAgICAgKDAsIF90eXBlLm9wdGlvbnNlbGVjdCkoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLmRpY3RPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICAgIC8qKiDmn6Xor6Loj5zljZXkuIvmi4nliJfooaggKi8KICAgICAgKDAsIF9tZW51Lmxpc3RNZW51KSgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMubWVudXMgPSBfdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJtZW51SWQiKTsKICAgICAgfSk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciBiYXNpY0Zvcm0gPSB0aGlzLiRyZWZzLmJhc2ljSW5mby4kcmVmcy5iYXNpY0luZm9Gb3JtOwogICAgICB2YXIgZ2VuRm9ybSA9IHRoaXMuJHJlZnMuZ2VuSW5mby4kcmVmcy5nZW5JbmZvRm9ybTsKICAgICAgUHJvbWlzZS5hbGwoW2Jhc2ljRm9ybSwgZ2VuRm9ybV0ubWFwKHRoaXMuZ2V0Rm9ybVByb21pc2UpKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICB2YXIgdmFsaWRhdGVSZXN1bHQgPSByZXMuZXZlcnkoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiAhIWl0ZW07CiAgICAgICAgfSk7CiAgICAgICAgaWYgKHZhbGlkYXRlUmVzdWx0KSB7CiAgICAgICAgICB2YXIgZ2VuVGFibGUgPSBPYmplY3QuYXNzaWduKHt9LCBiYXNpY0Zvcm0ubW9kZWwsIGdlbkZvcm0ubW9kZWwpOwogICAgICAgICAgZ2VuVGFibGUuY29sdW1ucyA9IF90aGlzMi5jb2x1bW5zOwogICAgICAgICAgZ2VuVGFibGUucGFyYW1zID0gewogICAgICAgICAgICB0cmVlQ29kZTogZ2VuVGFibGUudHJlZUNvZGUsCiAgICAgICAgICAgIHRyZWVOYW1lOiBnZW5UYWJsZS50cmVlTmFtZSwKICAgICAgICAgICAgdHJlZVBhcmVudENvZGU6IGdlblRhYmxlLnRyZWVQYXJlbnRDb2RlLAogICAgICAgICAgICBwYXJlbnRNZW51SWQ6IGdlblRhYmxlLnBhcmVudE1lbnVJZAogICAgICAgICAgfTsKICAgICAgICAgICgwLCBfZ2VuLnVwZGF0ZUdlblRhYmxlKShnZW5UYWJsZSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIF90aGlzMi4kbW9kYWwubXNnU3VjY2VzcyhyZXMubXNnKTsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICBfdGhpczIuY2xvc2UoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzMi4kbW9kYWwubXNnRXJyb3IoIuihqOWNleagoemqjOacqumAmui/h++8jOivt+mHjeaWsOajgOafpeaPkOS6pOWGheWuuSIpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZ2V0Rm9ybVByb21pc2U6IGZ1bmN0aW9uIGdldEZvcm1Qcm9taXNlKGZvcm0pIHsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlKSB7CiAgICAgICAgZm9ybS52YWxpZGF0ZShmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICByZXNvbHZlKHJlcyk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlhbPpl63mjInpkq4gKi9jbG9zZTogZnVuY3Rpb24gY2xvc2UoKSB7CiAgICAgIHZhciBvYmogPSB7CiAgICAgICAgcGF0aDogIi90b29sL2dlbiIsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIHQ6IERhdGUubm93KCksCiAgICAgICAgICBwYWdlTnVtOiB0aGlzLiRyb3V0ZS5xdWVyeS5wYWdlTnVtCiAgICAgICAgfQogICAgICB9OwogICAgICB0aGlzLiR0YWIuY2xvc2VPcGVuUGFnZShvYmopOwogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgdmFyIGVsID0gdGhpcy4kcmVmcy5kcmFnVGFibGUuJGVsLnF1ZXJ5U2VsZWN0b3JBbGwoIi5lbC10YWJsZV9fYm9keS13cmFwcGVyID4gdGFibGUgPiB0Ym9keSIpWzBdOwogICAgdmFyIHNvcnRhYmxlID0gX3NvcnRhYmxlanMuZGVmYXVsdC5jcmVhdGUoZWwsIHsKICAgICAgaGFuZGxlOiAiLmFsbG93RHJhZyIsCiAgICAgIG9uRW5kOiBmdW5jdGlvbiBvbkVuZChldnQpIHsKICAgICAgICB2YXIgdGFyZ2V0Um93ID0gX3RoaXMzLmNvbHVtbnMuc3BsaWNlKGV2dC5vbGRJbmRleCwgMSlbMF07CiAgICAgICAgX3RoaXMzLmNvbHVtbnMuc3BsaWNlKGV2dC5uZXdJbmRleCwgMCwgdGFyZ2V0Um93KTsKICAgICAgICBmb3IgKHZhciBpbmRleCBpbiBfdGhpczMuY29sdW1ucykgewogICAgICAgICAgX3RoaXMzLmNvbHVtbnNbaW5kZXhdLnNvcnQgPSBwYXJzZUludChpbmRleCkgKyAxOwogICAgICAgIH0KICAgICAgfQogICAgfSk7CiAgfQp9Ow=="}, {"version": 3, "names": ["_gen", "require", "_type", "_menu", "_basicInfoForm", "_interopRequireDefault", "_genInfoForm", "_sortablejs", "name", "components", "basicInfoForm", "genInfoForm", "data", "activeName", "tableHeight", "document", "documentElement", "scrollHeight", "tables", "columns", "dictOptions", "menus", "info", "created", "_this", "tableId", "$route", "params", "getGenTable", "then", "res", "rows", "getDictOptionselect", "response", "getMenuTreeselect", "handleTree", "methods", "submitForm", "_this2", "basicForm", "$refs", "basicInfo", "genForm", "genInfo", "Promise", "all", "map", "getFormPromise", "validateResult", "every", "item", "genTable", "Object", "assign", "model", "treeCode", "treeName", "treeParentCode", "parentMenuId", "updateGenTable", "$modal", "msgSuccess", "msg", "code", "close", "msgError", "form", "resolve", "validate", "obj", "path", "query", "t", "Date", "now", "pageNum", "$tab", "closeOpenPage", "mounted", "_this3", "el", "dragTable", "$el", "querySelectorAll", "sortable", "Sortable", "create", "handle", "onEnd", "evt", "targetRow", "splice", "oldIndex", "newIndex", "index", "sort", "parseInt"], "sources": ["src/views/tool/gen/editTable.vue"], "sourcesContent": ["<template>\r\n  <el-card>\r\n    <el-tabs v-model=\"activeName\">\r\n      <el-tab-pane label=\"基本信息\" name=\"basic\">\r\n        <basic-info-form ref=\"basicInfo\" :info=\"info\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"字段信息\" name=\"columnInfo\">\r\n        <el-table ref=\"dragTable\" :data=\"columns\" row-key=\"columnId\" :max-height=\"tableHeight\">\r\n          <el-table-column label=\"序号\" type=\"index\" min-width=\"5%\" class-name=\"allowDrag\" />\r\n          <el-table-column\r\n            label=\"字段列名\"\r\n            prop=\"columnName\"\r\n            min-width=\"10%\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"字段描述\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.columnComment\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"物理类型\"\r\n            prop=\"columnType\"\r\n            min-width=\"10%\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"Java类型\" min-width=\"11%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.javaType\">\r\n                <el-option label=\"Long\" value=\"Long\" />\r\n                <el-option label=\"String\" value=\"String\" />\r\n                <el-option label=\"Integer\" value=\"Integer\" />\r\n                <el-option label=\"Double\" value=\"Double\" />\r\n                <el-option label=\"BigDecimal\" value=\"BigDecimal\" />\r\n                <el-option label=\"Date\" value=\"Date\" />\r\n                <el-option label=\"Boolean\" value=\"Boolean\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"java属性\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.javaField\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"插入\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isInsert\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"编辑\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isEdit\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"列表\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isList\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isQuery\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询方式\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.queryType\">\r\n                <el-option label=\"=\" value=\"EQ\" />\r\n                <el-option label=\"!=\" value=\"NE\" />\r\n                <el-option label=\">\" value=\"GT\" />\r\n                <el-option label=\">=\" value=\"GTE\" />\r\n                <el-option label=\"<\" value=\"LT\" />\r\n                <el-option label=\"<=\" value=\"LTE\" />\r\n                <el-option label=\"LIKE\" value=\"LIKE\" />\r\n                <el-option label=\"BETWEEN\" value=\"BETWEEN\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"必填\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isRequired\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"显示类型\" min-width=\"12%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.htmlType\">\r\n                <el-option label=\"文本框\" value=\"input\" />\r\n                <el-option label=\"文本域\" value=\"textarea\" />\r\n                <el-option label=\"下拉框\" value=\"select\" />\r\n                <el-option label=\"单选框\" value=\"radio\" />\r\n                <el-option label=\"复选框\" value=\"checkbox\" />\r\n                <el-option label=\"日期控件\" value=\"datetime\" />\r\n                <el-option label=\"图片上传\" value=\"imageUpload\" />\r\n                <el-option label=\"文件上传\" value=\"fileUpload\" />\r\n                <el-option label=\"富文本控件\" value=\"editor\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"字典类型\" min-width=\"12%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.dictType\" clearable filterable placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"dict in dictOptions\"\r\n                  :key=\"dict.dictType\"\r\n                  :label=\"dict.dictName\"\r\n                  :value=\"dict.dictType\">\r\n                  <span style=\"float: left\">{{ dict.dictName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ dict.dictType }}</span>\r\n              </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"生成信息\" name=\"genInfo\">\r\n        <gen-info-form ref=\"genInfo\" :info=\"info\" :tables=\"tables\" :menus=\"menus\"/>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <el-form label-width=\"100px\">\r\n      <el-form-item style=\"text-align: center;margin-left:-100px;margin-top:10px;\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\r\n        <el-button @click=\"close()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport { getGenTable, updateGenTable } from \"@/api/tool/gen\";\r\nimport { optionselect as getDictOptionselect } from \"@/api/system/dict/type\";\r\nimport { listMenu as getMenuTreeselect } from \"@/api/system/menu\";\r\nimport basicInfoForm from \"./basicInfoForm\";\r\nimport genInfoForm from \"./genInfoForm\";\r\nimport Sortable from 'sortablejs'\r\n\r\nexport default {\r\n  name: \"GenEdit\",\r\n  components: {\r\n    basicInfoForm,\r\n    genInfoForm\r\n  },\r\n  data() {\r\n    return {\r\n      // 选中选项卡的 name\r\n      activeName: \"columnInfo\",\r\n      // 表格的高度\r\n      tableHeight: document.documentElement.scrollHeight - 245 + \"px\",\r\n      // 表信息\r\n      tables: [],\r\n      // 表列信息\r\n      columns: [],\r\n      // 字典信息\r\n      dictOptions: [],\r\n      // 菜单信息\r\n      menus: [],\r\n      // 表详细信息\r\n      info: {}\r\n    };\r\n  },\r\n  created() {\r\n    const tableId = this.$route.params && this.$route.params.tableId;\r\n    if (tableId) {\r\n      // 获取表详细信息\r\n      getGenTable(tableId).then(res => {\r\n        this.columns = res.data.rows;\r\n        this.info = res.data.info;\r\n        this.tables = res.data.tables;\r\n      });\r\n      /** 查询字典下拉列表 */\r\n      getDictOptionselect().then(response => {\r\n        this.dictOptions = response.data;\r\n      });\r\n      /** 查询菜单下拉列表 */\r\n      getMenuTreeselect().then(response => {\r\n        this.menus = this.handleTree(response.data, \"menuId\");\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;\r\n      const genForm = this.$refs.genInfo.$refs.genInfoForm;\r\n      Promise.all([basicForm, genForm].map(this.getFormPromise)).then(res => {\r\n        const validateResult = res.every(item => !!item);\r\n        if (validateResult) {\r\n          const genTable = Object.assign({}, basicForm.model, genForm.model);\r\n          genTable.columns = this.columns;\r\n          genTable.params = {\r\n            treeCode: genTable.treeCode,\r\n            treeName: genTable.treeName,\r\n            treeParentCode: genTable.treeParentCode,\r\n            parentMenuId: genTable.parentMenuId\r\n          };\r\n          updateGenTable(genTable).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            if (res.code === 200) {\r\n              this.close();\r\n            }\r\n          });\r\n        } else {\r\n          this.$modal.msgError(\"表单校验未通过，请重新检查提交内容\");\r\n        }\r\n      });\r\n    },\r\n    getFormPromise(form) {\r\n      return new Promise(resolve => {\r\n        form.validate(res => {\r\n          resolve(res);\r\n        });\r\n      });\r\n    },\r\n    /** 关闭按钮 */\r\n    close() {\r\n      const obj = { path: \"/tool/gen\", query: { t: Date.now(), pageNum: this.$route.query.pageNum } };\r\n      this.$tab.closeOpenPage(obj);\r\n    }\r\n  },\r\n  mounted() {\r\n    const el = this.$refs.dragTable.$el.querySelectorAll(\".el-table__body-wrapper > table > tbody\")[0];\r\n    const sortable = Sortable.create(el, {\r\n      handle: \".allowDrag\",\r\n      onEnd: evt => {\r\n        const targetRow = this.columns.splice(evt.oldIndex, 1)[0];\r\n        this.columns.splice(evt.newIndex, 0, targetRow);\r\n        for (let index in this.columns) {\r\n          this.columns[index].sort = parseInt(index) + 1;\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAiIA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,WAAA,GAAAF,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,UAAA;MACA;MACAC,WAAA,EAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,OAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,OAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,OAAA;IACA,IAAAA,OAAA;MACA;MACA,IAAAG,gBAAA,EAAAH,OAAA,EAAAI,IAAA,WAAAC,GAAA;QACAN,KAAA,CAAAL,OAAA,GAAAW,GAAA,CAAAlB,IAAA,CAAAmB,IAAA;QACAP,KAAA,CAAAF,IAAA,GAAAQ,GAAA,CAAAlB,IAAA,CAAAU,IAAA;QACAE,KAAA,CAAAN,MAAA,GAAAY,GAAA,CAAAlB,IAAA,CAAAM,MAAA;MACA;MACA;MACA,IAAAc,kBAAA,IAAAH,IAAA,WAAAI,QAAA;QACAT,KAAA,CAAAJ,WAAA,GAAAa,QAAA,CAAArB,IAAA;MACA;MACA;MACA,IAAAsB,cAAA,IAAAL,IAAA,WAAAI,QAAA;QACAT,KAAA,CAAAH,KAAA,GAAAG,KAAA,CAAAW,UAAA,CAAAF,QAAA,CAAArB,IAAA;MACA;IACA;EACA;EACAwB,OAAA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,SAAA,QAAAC,KAAA,CAAAC,SAAA,CAAAD,KAAA,CAAA9B,aAAA;MACA,IAAAgC,OAAA,QAAAF,KAAA,CAAAG,OAAA,CAAAH,KAAA,CAAA7B,WAAA;MACAiC,OAAA,CAAAC,GAAA,EAAAN,SAAA,EAAAG,OAAA,EAAAI,GAAA,MAAAC,cAAA,GAAAlB,IAAA,WAAAC,GAAA;QACA,IAAAkB,cAAA,GAAAlB,GAAA,CAAAmB,KAAA,WAAAC,IAAA;UAAA,SAAAA,IAAA;QAAA;QACA,IAAAF,cAAA;UACA,IAAAG,QAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAd,SAAA,CAAAe,KAAA,EAAAZ,OAAA,CAAAY,KAAA;UACAH,QAAA,CAAAhC,OAAA,GAAAmB,MAAA,CAAAnB,OAAA;UACAgC,QAAA,CAAAxB,MAAA;YACA4B,QAAA,EAAAJ,QAAA,CAAAI,QAAA;YACAC,QAAA,EAAAL,QAAA,CAAAK,QAAA;YACAC,cAAA,EAAAN,QAAA,CAAAM,cAAA;YACAC,YAAA,EAAAP,QAAA,CAAAO;UACA;UACA,IAAAC,mBAAA,EAAAR,QAAA,EAAAtB,IAAA,WAAAC,GAAA;YACAQ,MAAA,CAAAsB,MAAA,CAAAC,UAAA,CAAA/B,GAAA,CAAAgC,GAAA;YACA,IAAAhC,GAAA,CAAAiC,IAAA;cACAzB,MAAA,CAAA0B,KAAA;YACA;UACA;QACA;UACA1B,MAAA,CAAAsB,MAAA,CAAAK,QAAA;QACA;MACA;IACA;IACAlB,cAAA,WAAAA,eAAAmB,IAAA;MACA,WAAAtB,OAAA,WAAAuB,OAAA;QACAD,IAAA,CAAAE,QAAA,WAAAtC,GAAA;UACAqC,OAAA,CAAArC,GAAA;QACA;MACA;IACA;IACA,WACAkC,KAAA,WAAAA,MAAA;MACA,IAAAK,GAAA;QAAAC,IAAA;QAAAC,KAAA;UAAAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;UAAAC,OAAA,OAAAjD,MAAA,CAAA6C,KAAA,CAAAI;QAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAR,GAAA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IAAAC,EAAA,QAAAxC,KAAA,CAAAyC,SAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA,IAAAC,QAAA,GAAAC,mBAAA,CAAAC,MAAA,CAAAN,EAAA;MACAO,MAAA;MACAC,KAAA,WAAAA,MAAAC,GAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAA5D,OAAA,CAAAwE,MAAA,CAAAF,GAAA,CAAAG,QAAA;QACAb,MAAA,CAAA5D,OAAA,CAAAwE,MAAA,CAAAF,GAAA,CAAAI,QAAA,KAAAH,SAAA;QACA,SAAAI,KAAA,IAAAf,MAAA,CAAA5D,OAAA;UACA4D,MAAA,CAAA5D,OAAA,CAAA2E,KAAA,EAAAC,IAAA,GAAAC,QAAA,CAAAF,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}