package com.ruoyi.sso.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SSO测试控制器
 * 用于测试日志配置是否正常
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sso/test")
public class SSOTestController {

    private static final Logger logger = LoggerFactory.getLogger(SSOTestController.class);

    /**
     * 测试日志输出
     */
    @GetMapping("/log")
    public String testLog() {
        logger.debug("这是DEBUG级别日志");
        logger.info("这是INFO级别日志");
        logger.warn("这是WARN级别日志");
        logger.error("这是ERROR级别日志");
        
        return "日志测试完成，请检查控制台和日志文件";
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public String health() {
        logger.info("SSO服务健康检查");
        return "SSO服务运行正常";
    }
}
