{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\Navbar.vue?vue&type=style&index=0&id=d16d6306&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\Navbar.vue", "mtime": 1750151094170}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750495811116}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750495818185}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750495815031}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750495809569}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <div class=\"navbar\">\r\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\r\n\r\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" v-if=\"!topNav\"/>\r\n    <top-nav id=\"topmenu-container\" class=\"topmenu-container\" v-if=\"topNav\"/>\r\n\r\n    <div class=\"right-menu\">\r\n      <template v-if=\"device!=='mobile'\">\r\n\r\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\r\n\r\n        <el-tooltip content=\"布局大小\" effect=\"dark\" placement=\"bottom\">\r\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\" />\r\n        </el-tooltip>\r\n\r\n      </template>\r\n\r\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\r\n        <div class=\"avatar-wrapper\">\r\n          <img :src=\"avatar || 'https://xp-tech.oss-cn-beijing.aliyuncs.com/20220531/1654011275576410.jpg?Expires=4807611275&OSSAccessKeyId=LTAI4G5Udf4KbAUamwr8dKC9&Signature=5LpfMmRJgRxewzmkx66yQwfBT08%3D'\" class=\"user-avatar\">\r\n          <i class=\"el-icon-caret-bottom\" />\r\n        </div>\r\n        <el-dropdown-menu slot=\"dropdown\">\r\n          <router-link to=\"/user/profile\">\r\n            <el-dropdown-item>个人中心</el-dropdown-item>\r\n          </router-link>\r\n          <el-dropdown-item @click.native=\"setting = true\">\r\n            <span>布局设置</span>\r\n          </el-dropdown-item>\r\n          <el-dropdown-item divided @click.native=\"logout\">\r\n            <span>退出登录</span>\r\n          </el-dropdown-item>\r\n        </el-dropdown-menu>\r\n      </el-dropdown>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Breadcrumb from '@/components/Breadcrumb'\r\nimport TopNav from '@/components/TopNav'\r\nimport Hamburger from '@/components/Hamburger'\r\nimport Screenfull from '@/components/Screenfull'\r\nimport SizeSelect from '@/components/SizeSelect'\r\nimport Search from '@/components/HeaderSearch'\r\nimport RuoYiGit from '@/components/RuoYi/Git'\r\nimport RuoYiDoc from '@/components/RuoYi/Doc'\r\n\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    RuoYiGit,\r\n    RuoYiDoc\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebar',\r\n      'avatar',\r\n      'device'\r\n    ]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.$store.dispatch('app/toggleSideBar')\r\n    },\r\n    async logout() {\r\n      this.$confirm('确定注销并退出系统吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$store.dispatch('LogOut').then(() => {\r\n          location.href = '/chainadmin';\r\n        })\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.navbar {\r\n  height: 50px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\r\n\r\n  .hamburger-container {\r\n    line-height: 46px;\r\n    height: 100%;\r\n    float: left;\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n    -webkit-tap-highlight-color:transparent;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .025)\r\n    }\r\n  }\r\n\r\n  .breadcrumb-container {\r\n    float: left;\r\n  }\r\n\r\n  .topmenu-container {\r\n    position: absolute;\r\n    left: 50px;\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .right-menu {\r\n    float: right;\r\n    height: 100%;\r\n    line-height: 50px;\r\n\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background .3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, .025)\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      margin-right: 30px;\r\n\r\n      .avatar-wrapper {\r\n        margin-top: 5px;\r\n        position: relative;\r\n\r\n        .user-avatar {\r\n          cursor: pointer;\r\n          width: 40px;\r\n          height: 40px;\r\n          border-radius: 10px;\r\n        }\r\n\r\n        .el-icon-caret-bottom {\r\n          cursor: pointer;\r\n          position: absolute;\r\n          right: -20px;\r\n          top: 25px;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}