11:48:37.116 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:48:38.660 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0
11:48:38.768 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 55 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:38.821 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:38.842 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:39.091 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 246 ms to scan 207 urls, producing 0 keys and 0 values 
11:48:39.102 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:39.125 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:39.139 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:39.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 250 ms to scan 207 urls, producing 0 keys and 0 values 
11:48:39.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:39.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/605201451
11:48:39.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/72615125
11:48:39.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:39.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:39.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:42.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477721697_127.0.0.1_63950
11:48:42.176 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Notify connected event to listeners.
11:48:42.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:42.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/118566118
11:48:42.346 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:48:45.856 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
11:48:45.856 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:48:45.857 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:48:46.213 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:48:49.620 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:48:49.785 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
11:50:44.512 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:50:45.405 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c0b07348-aeac-4340-993d-1e783607c0fa_config-0
11:50:45.463 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
11:50:45.495 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:50:45.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:50:45.674 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 165 ms to scan 207 urls, producing 0 keys and 0 values 
11:50:45.686 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
11:50:45.701 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
11:50:45.716 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
11:50:45.843 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 126 ms to scan 207 urls, producing 0 keys and 0 values 
11:50:45.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:50:45.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/341887005
11:50:45.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/59465509
11:50:45.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:50:45.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:50:45.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:50:47.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477847163_127.0.0.1_64818
11:50:47.431 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] Notify connected event to listeners.
11:50:47.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:50:47.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0b07348-aeac-4340-993d-1e783607c0fa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1328705686
11:50:47.541 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:50:49.719 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
11:50:49.721 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:50:49.721 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:50:49.986 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:50:52.174 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:50:52.782 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 332e2c9f-ac4e-40c1-a127-e48420f9b7b0
11:50:52.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] RpcClient init label, labels = {module=naming, source=sdk}
11:50:52.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:50:52.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:50:52.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:50:52.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:50:52.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477852793_127.0.0.1_64841
11:50:52.904 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Notify connected event to listeners.
11:50:52.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:50:52.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1328705686
11:50:54.259 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
11:50:54.304 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
11:50:54.534 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 10.709 seconds (JVM running for 11.973)
11:50:54.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
11:50:54.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
11:50:54.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
11:50:54.857 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Receive server push request, request = NotifySubscriberRequest, requestId = 59
11:50:54.868 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [332e2c9f-ac4e-40c1-a127-e48420f9b7b0] Ack server push request, request = NotifySubscriberRequest, requestId = 59
11:50:54.887 [RMI TCP Connection(1)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:51:11.229 [http-nio-9700-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_c9becf9ebb7e326e43d6b118d826a735=1750313378,1750383095,1750477699;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
13:15:18.917 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:15:18.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:15:19.244 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:15:19.244 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5b5c447e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:15:19.244 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750477852793_127.0.0.1_64841
13:15:19.245 [nacos-grpc-client-executor-1026] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750477852793_127.0.0.1_64841]Ignore complete event,isRunning:false,isAbandon=false
13:15:19.255 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@464d102d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1027]
13:15:25.469 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
13:15:26.429 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93f60172-297d-4058-acad-20de76357991_config-0
13:15:26.500 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
13:15:26.533 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
13:15:26.546 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
13:15:26.708 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 160 ms to scan 207 urls, producing 0 keys and 0 values 
13:15:26.718 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
13:15:26.732 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
13:15:26.747 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
13:15:26.906 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 157 ms to scan 207 urls, producing 0 keys and 0 values 
13:15:26.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:15:26.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/197107207
13:15:26.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1878454236
13:15:26.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:15:26.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:15:26.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:15:28.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750482928301_127.0.0.1_58637
13:15:28.570 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] Notify connected event to listeners.
13:15:28.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:15:28.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93f60172-297d-4058-acad-20de76357991_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/404994500
13:15:28.672 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
13:15:31.121 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
13:15:31.122 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:15:31.122 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
13:15:31.398 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:15:34.041 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:15:34.732 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 378d1ecb-6173-4012-8363-bb6bcc0f473b
13:15:34.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] RpcClient init label, labels = {module=naming, source=sdk}
13:15:34.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:15:34.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:15:34.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:15:34.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:15:34.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750482934744_127.0.0.1_58664
13:15:34.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:15:34.848 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Notify connected event to listeners.
13:15:34.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/404994500
13:15:36.112 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
13:15:36.151 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
13:15:36.377 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 11.593 seconds (JVM running for 13.004)
13:15:36.402 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
13:15:36.403 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
13:15:36.404 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
13:15:36.639 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Receive server push request, request = NotifySubscriberRequest, requestId = 67
13:15:36.643 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [378d1ecb-6173-4012-8363-bb6bcc0f473b] Ack server push request, request = NotifySubscriberRequest, requestId = 67
13:15:36.795 [RMI TCP Connection(5)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:16:21.460 [http-nio-9700-exec-2] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_c9becf9ebb7e326e43d6b118d826a735=1750313378,1750383095,1750477699;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
13:16:22.032 [http-nio-9700-exec-2] INFO  c.r.a.c.SSOClientController - [getSSOLoginUrl,189] - 市场系统生成SSO登录URL: http://localhost:9100/sso/login?client_id=market&redirect_uri=http%3A%2F%2Flocalhost%3A9700%2Fsso%2Fcallback&state=http%3A%2F%2F192.168.0.68%3A3000%2Flogin%2F
13:16:32.026 [http-nio-9700-exec-1] INFO  c.r.a.c.SSOClientController - [getSSOLoginUrl,189] - 市场系统生成SSO登录URL: http://localhost:9100/sso/login?client_id=market&redirect_uri=http%3A%2F%2Flocalhost%3A9700%2Fsso%2Fcallback&state=http%3A%2F%2F192.168.0.68%3A3000%2Flogin%2F
