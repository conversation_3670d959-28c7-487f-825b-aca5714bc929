11:48:37.116 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:48:38.660 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0
11:48:38.768 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 55 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:38.821 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:38.842 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:39.091 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 246 ms to scan 207 urls, producing 0 keys and 0 values 
11:48:39.102 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:39.125 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:39.139 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:39.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 250 ms to scan 207 urls, producing 0 keys and 0 values 
11:48:39.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:39.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/605201451
11:48:39.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/72615125
11:48:39.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:39.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:39.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:48:42.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750477721697_127.0.0.1_63950
11:48:42.176 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Notify connected event to listeners.
11:48:42.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:42.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b700dd2-6912-46ab-ac61-bcd0d9a3b297_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/118566118
11:48:42.346 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:48:45.856 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
11:48:45.856 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:48:45.857 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:48:46.213 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:48:49.620 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:48:49.785 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
