10:29:55.660 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:29:55.753 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:29:56.342 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:29:56.342 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:30:00.074 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:30:04.898 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:30:04.904 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:30:04.904 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
10:30:05.279 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:30:07.558 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
10:30:08.746 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
10:30:08.774 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
10:30:08.821 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
10:30:09.408 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:30:11.943 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:30:12.017 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:30:12.017 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:30:12.278 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
10:30:12.566 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 17.829 seconds (JVM running for 19.211)
10:30:12.640 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
10:30:12.642 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
10:30:12.642 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
10:30:13.157 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:32:20.224 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 3f090c124f514e1dad525e004e772047, 状态: http://localhost:81/login
10:32:20.358 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
10:32:20.400 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750473140400
10:32:20.978 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
10:32:20.978 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
10:32:20.978 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
10:32:22.097 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
10:32:22.099 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: 2dc0fb7bd2074179ae62bb7507a52fd9
10:32:22.099 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=2dc0fb7bd2074179ae62bb7507a52fd9&redirect=http://localhost:81/login
10:49:36.331 [http-nio-9200-exec-9] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 1f2c1dc8ba41413b9dd79c2180d9caab, 状态: http://localhost:81/login
10:49:36.340 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
10:49:36.349 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750474176349
10:49:36.358 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
10:49:36.358 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
10:49:36.358 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
10:49:36.367 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
10:49:36.369 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: af5d68fa760242338879316a672d1d5b
10:49:36.370 [http-nio-9200-exec-9] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=af5d68fa760242338879316a672d1d5b&redirect=http://localhost:81/login
10:49:37.202 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: af5d68fa760242338879316a672d1d5b
10:49:37.202 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,109] - 前端获取JWT token成功，key: af5d68fa760242338879316a672d1d5b
10:49:50.683 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: e12c60d44a864942aa3d00199cdb9c97, 状态: http://localhost:81/login
10:49:50.701 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
10:49:50.708 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750474190708
10:49:50.720 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
10:49:50.720 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
10:49:50.720 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
10:49:50.726 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
10:49:50.729 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: 0e12ffdf344f4794a50b6c9535c5146f
10:49:50.730 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=0e12ffdf344f4794a50b6c9535c5146f&redirect=http://localhost:81/login
10:49:51.268 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: 0e12ffdf344f4794a50b6c9535c5146f
10:49:51.268 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,109] - 前端获取JWT token成功，key: 0e12ffdf344f4794a50b6c9535c5146f
10:50:21.083 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: 2dc0fb7bd2074179ae62bb7507a52fd9
10:50:21.083 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,109] - 前端获取JWT token成功，key: 2dc0fb7bd2074179ae62bb7507a52fd9
10:53:23.420 [http-nio-9200-exec-9] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 1d8298be10f24fc9b20b9bdf42fcb852, 状态: http://localhost:81/login
10:53:23.426 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
10:53:23.432 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750474403432
10:53:23.443 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
10:53:23.444 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
10:53:23.444 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
10:53:23.447 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
10:53:23.450 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: c93acb72beb94307bb63e163061d5563
10:53:23.450 [http-nio-9200-exec-9] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=c93acb72beb94307bb63e163061d5563&redirect=http://localhost:81/login
10:53:24.560 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: c93acb72beb94307bb63e163061d5563
10:53:24.560 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,109] - 前端获取JWT token成功，key: c93acb72beb94307bb63e163061d5563
10:59:12.825 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
10:59:12.827 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
10:59:18.597 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:59:18.692 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:59:19.303 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:59:19.304 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:59:22.090 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:59:24.877 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:59:24.881 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:59:24.882 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
10:59:25.159 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:59:27.022 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
10:59:28.090 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
10:59:28.113 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
10:59:28.149 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
10:59:28.731 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:59:30.857 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:59:30.929 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:59:30.929 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:59:31.162 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
10:59:31.425 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.718 seconds (JVM running for 15.141)
10:59:31.473 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
10:59:31.475 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
10:59:31.475 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
10:59:31.540 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:00:36.457 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: e36ee7858dca462797036c378d172edf, 状态: http://localhost:81/login
11:00:36.603 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
11:00:36.645 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750474836645
11:00:37.179 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
11:00:37.180 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
11:00:37.180 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
11:00:38.109 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
11:00:38.110 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: f6d7e5fcacc14c118b099f78f4bcab65
11:00:38.110 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,98] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=f6d7e5fcacc14c118b099f78f4bcab65&redirect=%2Findex
11:00:39.065 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: f6d7e5fcacc14c118b099f78f4bcab65
11:00:39.065 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,123] - 前端获取JWT token成功，key: f6d7e5fcacc14c118b099f78f4bcab65
11:06:59.616 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
11:06:59.619 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
