10:29:55.660 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:29:55.753 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:29:56.342 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:29:56.342 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:30:00.074 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:30:04.898 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:30:04.904 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:30:04.904 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
10:30:05.279 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:30:07.558 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
10:30:08.746 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
10:30:08.774 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
10:30:08.821 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
10:30:09.408 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:30:11.943 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:30:12.017 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:30:12.017 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:30:12.278 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
10:30:12.566 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 17.829 seconds (JVM running for 19.211)
10:30:12.640 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
10:30:12.642 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
10:30:12.642 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
10:30:13.157 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:32:20.224 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 3f090c124f514e1dad525e004e772047, 状态: http://localhost:81/login
10:32:20.358 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
10:32:20.400 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750473140400
10:32:20.978 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
10:32:20.978 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
10:32:20.978 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
10:32:22.097 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
10:32:22.099 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: 2dc0fb7bd2074179ae62bb7507a52fd9
10:32:22.099 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=2dc0fb7bd2074179ae62bb7507a52fd9&redirect=http://localhost:81/login
10:49:36.331 [http-nio-9200-exec-9] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 1f2c1dc8ba41413b9dd79c2180d9caab, 状态: http://localhost:81/login
10:49:36.340 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
10:49:36.349 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750474176349
10:49:36.358 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
10:49:36.358 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
10:49:36.358 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
10:49:36.367 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
10:49:36.369 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: af5d68fa760242338879316a672d1d5b
10:49:36.370 [http-nio-9200-exec-9] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=af5d68fa760242338879316a672d1d5b&redirect=http://localhost:81/login
10:49:37.202 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: af5d68fa760242338879316a672d1d5b
10:49:37.202 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,109] - 前端获取JWT token成功，key: af5d68fa760242338879316a672d1d5b
10:49:50.683 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: e12c60d44a864942aa3d00199cdb9c97, 状态: http://localhost:81/login
10:49:50.701 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
10:49:50.708 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750474190708
10:49:50.720 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
10:49:50.720 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
10:49:50.720 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
10:49:50.726 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
10:49:50.729 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: 0e12ffdf344f4794a50b6c9535c5146f
10:49:50.730 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=0e12ffdf344f4794a50b6c9535c5146f&redirect=http://localhost:81/login
10:49:51.268 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: 0e12ffdf344f4794a50b6c9535c5146f
10:49:51.268 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,109] - 前端获取JWT token成功，key: 0e12ffdf344f4794a50b6c9535c5146f
10:50:21.083 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: 2dc0fb7bd2074179ae62bb7507a52fd9
10:50:21.083 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,109] - 前端获取JWT token成功，key: 2dc0fb7bd2074179ae62bb7507a52fd9
10:53:23.420 [http-nio-9200-exec-9] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 1d8298be10f24fc9b20b9bdf42fcb852, 状态: http://localhost:81/login
10:53:23.426 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
10:53:23.432 [http-nio-9200-exec-9] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750474403432
10:53:23.443 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
10:53:23.444 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
10:53:23.444 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
10:53:23.447 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
10:53:23.450 [http-nio-9200-exec-9] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: c93acb72beb94307bb63e163061d5563
10:53:23.450 [http-nio-9200-exec-9] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,84] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=c93acb72beb94307bb63e163061d5563&redirect=http://localhost:81/login
10:53:24.560 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: c93acb72beb94307bb63e163061d5563
10:53:24.560 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,109] - 前端获取JWT token成功，key: c93acb72beb94307bb63e163061d5563
10:59:12.825 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
10:59:12.827 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
10:59:18.597 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:59:18.692 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:59:19.303 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:59:19.304 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:59:22.090 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
10:59:24.877 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:59:24.881 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:59:24.882 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
10:59:25.159 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:59:27.022 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
10:59:28.090 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
10:59:28.113 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
10:59:28.149 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
10:59:28.731 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:59:30.857 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:59:30.929 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:59:30.929 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:59:31.162 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
10:59:31.425 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.718 seconds (JVM running for 15.141)
10:59:31.473 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
10:59:31.475 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
10:59:31.475 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
10:59:31.540 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:00:36.457 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: e36ee7858dca462797036c378d172edf, 状态: http://localhost:81/login
11:00:36.603 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [exchangeToken,90] - 授权码换取令牌成功
11:00:36.645 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /user/info/18454831889, 开始时间: 1750474836645
11:00:37.179 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createTempUserFromSSO,283] - 基于SSO信息创建临时用户: 18454831889 (ID: 55)
11:00:37.180 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,227] - 创建临时用户信息: 18454831889
11:00:37.180 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalUser,233] - 用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
11:00:38.109 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,146] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
11:00:38.110 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [storeTempToken,407] - 存储临时token成功，key: f6d7e5fcacc14c118b099f78f4bcab65
11:00:38.110 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,98] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=f6d7e5fcacc14c118b099f78f4bcab65&redirect=%2Findex
11:00:39.065 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getTempToken,424] - 获取临时token成功，key: f6d7e5fcacc14c118b099f78f4bcab65
11:00:39.065 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,123] - 前端获取JWT token成功，key: f6d7e5fcacc14c118b099f78f4bcab65
11:06:59.616 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
11:06:59.619 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
11:08:46.347 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:08:46.406 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:08:46.772 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:08:46.773 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:08:49.082 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:08:51.327 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
11:08:51.330 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:08:51.330 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
11:08:51.576 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:08:53.090 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
11:08:54.022 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
11:08:54.047 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
11:08:54.090 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
11:08:54.546 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:08:56.530 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
11:08:56.591 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:08:56.591 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:08:56.805 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
11:08:57.047 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 11.311 seconds (JVM running for 12.757)
11:08:57.094 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
11:08:57.095 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
11:08:57.096 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
11:08:57.779 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:09:20.637 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 204190eb98ca46c7a49a06d083361120, 状态: http://localhost:81/login?redirect=/user/profile
11:09:20.736 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [exchangeToken,100] - 授权码换取令牌成功
11:09:20.775 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750475360775
11:09:21.343 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,219] - 找到本地门户用户: 18454831889
11:09:21.344 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,230] - 使用本地门户用户信息: 18454831889
11:09:21.344 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,242] - 门户用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
11:09:22.296 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,156] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
11:09:22.298 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [storeTempToken,515] - 存储临时token成功，key: 0fb7a4c634914d70ad7fe7d032bbb870
11:09:22.298 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,98] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=0fb7a4c634914d70ad7fe7d032bbb870&redirect=%2Findex
11:09:23.772 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getTempToken,532] - 获取临时token成功，key: 0fb7a4c634914d70ad7fe7d032bbb870
11:09:23.773 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,123] - 前端获取JWT token成功，key: 0fb7a4c634914d70ad7fe7d032bbb870
11:09:54.880 [http-nio-9200-exec-6] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 54727a8fc8904b249222c6f3393ae41a, 状态: http://localhost:81/login?redirect=/user/profile
11:09:54.903 [http-nio-9200-exec-6] INFO  c.r.a.s.SSOClientService - [exchangeToken,100] - 授权码换取令牌成功
11:09:54.910 [http-nio-9200-exec-6] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750475394910
11:09:54.928 [http-nio-9200-exec-6] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,219] - 找到本地门户用户: 18454831889
11:09:54.928 [http-nio-9200-exec-6] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,230] - 使用本地门户用户信息: 18454831889
11:09:54.928 [http-nio-9200-exec-6] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,242] - 门户用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
11:09:54.931 [http-nio-9200-exec-6] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,156] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
11:09:54.933 [http-nio-9200-exec-6] INFO  c.r.a.s.SSOClientService - [storeTempToken,515] - 存储临时token成功，key: 9d339ad2e4964a1db12bdf7763524700
11:09:54.933 [http-nio-9200-exec-6] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,98] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=9d339ad2e4964a1db12bdf7763524700&redirect=%2Findex
11:09:55.731 [http-nio-9200-exec-7] INFO  c.r.a.s.SSOClientService - [getTempToken,532] - 获取临时token成功，key: 9d339ad2e4964a1db12bdf7763524700
11:09:55.731 [http-nio-9200-exec-7] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,123] - 前端获取JWT token成功，key: 9d339ad2e4964a1db12bdf7763524700
11:10:21.347 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/info/18454831889, 开始时间: 1750475421347
11:10:21.457 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750475421457
11:14:28.059 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
11:14:28.063 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
11:14:36.280 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:14:36.381 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:14:36.913 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:14:36.914 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:14:40.884 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:14:44.561 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
11:14:44.565 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:14:44.566 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
11:14:44.913 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:14:47.300 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
11:14:48.511 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
11:14:48.540 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
11:14:48.586 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
11:14:49.478 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:14:52.140 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
11:14:52.240 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:14:52.240 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:14:52.473 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
11:14:52.750 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 17.34 seconds (JVM running for 18.829)
11:14:52.808 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
11:14:52.809 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
11:14:52.810 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
11:14:53.068 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:18:31.590 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750475911590
11:18:35.680 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750475915680
11:18:50.145 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: 7f27c34fba2a4b25a015d3a1dfd3b522, 状态: http://localhost:81/login
11:18:50.214 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [exchangeToken,100] - 授权码换取令牌成功
11:18:50.251 [http-nio-9200-exec-4] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750475930251
11:18:50.388 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,221] - 找到本地门户用户: 18454831889
11:18:50.389 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,232] - 使用本地门户用户信息: 18454831889
11:18:50.389 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,244] - 门户用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
11:18:50.718 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,154] - SSO登录：开始使用ProtalTokenService创建JWT token，用户: 18454831889
11:18:50.851 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,156] - SSO登录：ProtalTokenService创建token完成，结果: {access_token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxODIsImxvZ2luX3R5cGUiOiJsb2dpbl9tZW1iZXIiLCJ1c2VyX2tleSI6IjFlOTU2ZWE4LTc0YWMtNDFkMy04NGJhLTVhOTczMTYwNTU1YSIsInVzZXJuYW1lIjoiMTg0NTQ4MzE4ODkifQ.kGByrksFZr9L3_JMi-SiDHJqD1-z9QmsLSk3alpGFjEy54cUvXhnzh6qwY-a61Pvq81H_RMBIZ9pgR3PfKdriQ, success=true, disabled=false, expires_in=720}
11:18:50.851 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,158] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
11:18:50.852 [http-nio-9200-exec-4] INFO  c.r.a.s.SSOClientService - [storeTempToken,517] - 存储临时token成功，key: 7cec67de495642b49d028b274e2c74c7
11:18:50.852 [http-nio-9200-exec-4] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,98] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=7cec67de495642b49d028b274e2c74c7&redirect=%2Findex
11:18:52.742 [http-nio-9200-exec-5] INFO  c.r.a.s.SSOClientService - [getTempToken,534] - 获取临时token成功，key: 7cec67de495642b49d028b274e2c74c7
11:18:52.742 [http-nio-9200-exec-5] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,123] - 前端获取JWT token成功，key: 7cec67de495642b49d028b274e2c74c7
11:24:31.477 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: d222d1fb17a4458394e81092f79513d2, 状态: http://localhost:81/login
11:24:31.485 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [exchangeToken,100] - 授权码换取令牌成功
11:24:31.493 [http-nio-9200-exec-2] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750476271493
11:24:31.505 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,221] - 找到本地门户用户: 18454831889
11:24:31.505 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,232] - 使用本地门户用户信息: 18454831889
11:24:31.505 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,244] - 门户用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
11:24:31.509 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,154] - SSO登录：开始使用ProtalTokenService创建JWT token，用户: 18454831889
11:24:31.513 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,156] - SSO登录：ProtalTokenService创建token完成，结果: {access_token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxODIsImxvZ2luX3R5cGUiOiJsb2dpbl9tZW1iZXIiLCJ1c2VyX2tleSI6ImQzMTExMzk4LWVhZjctNDE4MC1iOTE4LTM1NTA1MjZmNzNlMSIsInVzZXJuYW1lIjoiMTg0NTQ4MzE4ODkifQ.NeSWwm7fds2aaK8wvGmaZevbATP7N2x4dM9xuv0JTO26Jvon8EeDwDAAwdHQi9rxoS1Fbi4j3qZ5B_NEB3ukyQ, success=true, disabled=false, expires_in=720}
11:24:31.513 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,158] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
11:24:31.516 [http-nio-9200-exec-2] INFO  c.r.a.s.SSOClientService - [storeTempToken,517] - 存储临时token成功，key: daf1a4c0693b4136a72fedb98a555cfc
11:24:31.516 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,98] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=daf1a4c0693b4136a72fedb98a555cfc&redirect=%2Findex
11:24:32.227 [http-nio-9200-exec-1] INFO  c.r.a.s.SSOClientService - [getTempToken,534] - 获取临时token成功，key: daf1a4c0693b4136a72fedb98a555cfc
11:24:32.227 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,123] - 前端获取JWT token成功，key: daf1a4c0693b4136a72fedb98a555cfc
11:51:11.926 [http-nio-9200-exec-7] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,48] - 跳转到SSO登录，重定向地址: http://************:3000/login
11:51:11.927 [http-nio-9200-exec-7] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,60] - SSO登录URL: http://localhost:9100/sso/login?client_id=backend&redirect_uri=http%3A%2F%2Flocalhost%3A9200%2Fsso%2Fcallback&state=http%3A%2F%2F************%3A3000%2Flogin
11:51:36.775 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: ff95a18eb1c04d1e9897bd54ddb7c923, 状态: http://************:3000/login
11:51:36.783 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [exchangeToken,100] - 授权码换取令牌成功
11:51:36.791 [http-nio-9200-exec-10] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750477896791
11:51:36.801 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,221] - 找到本地门户用户: 18454831889
11:51:36.802 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,232] - 使用本地门户用户信息: 18454831889
11:51:36.802 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,244] - 门户用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
11:51:36.804 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,154] - SSO登录：开始使用ProtalTokenService创建JWT token，用户: 18454831889
11:51:36.807 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,156] - SSO登录：ProtalTokenService创建token完成，结果: {access_token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxODIsImxvZ2luX3R5cGUiOiJsb2dpbl9tZW1iZXIiLCJ1c2VyX2tleSI6IjkyNjU3MTYyLTg3OGItNDBiZi04ZjBjLTUxODczMGU1YzczOCIsInVzZXJuYW1lIjoiMTg0NTQ4MzE4ODkifQ.tybkQGjLOnrKk5RLtNEmcafvzMExdPGtc9CB41LSHW9PZMV2ZY8JkSnoLYKHqMn1aP9U4lgtsRQAZ32qyNJOfg, success=true, disabled=false, expires_in=720}
11:51:36.807 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,158] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
11:51:36.810 [http-nio-9200-exec-10] INFO  c.r.a.s.SSOClientService - [storeTempToken,517] - 存储临时token成功，key: b4f1820ae04045e9b1033f170209bdb0
11:51:36.810 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,98] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=b4f1820ae04045e9b1033f170209bdb0&redirect=%2Findex
11:51:37.893 [http-nio-9200-exec-8] INFO  c.r.a.s.SSOClientService - [getTempToken,534] - 获取临时token成功，key: b4f1820ae04045e9b1033f170209bdb0
11:51:37.894 [http-nio-9200-exec-8] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,123] - 前端获取JWT token成功，key: b4f1820ae04045e9b1033f170209bdb0
11:51:45.918 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,48] - 跳转到SSO登录，重定向地址: http://************:3000/login/
11:51:45.918 [http-nio-9200-exec-2] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,60] - SSO登录URL: http://localhost:9100/sso/login?client_id=backend&redirect_uri=http%3A%2F%2Flocalhost%3A9200%2Fsso%2Fcallback&state=http%3A%2F%2F************%3A3000%2Flogin%2F
11:52:07.563 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,48] - 跳转到SSO登录，重定向地址: http://************:3000/login/
11:52:07.563 [http-nio-9200-exec-1] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,60] - SSO登录URL: http://localhost:9100/sso/login?client_id=backend&redirect_uri=http%3A%2F%2Flocalhost%3A9200%2Fsso%2Fcallback&state=http%3A%2F%2F************%3A3000%2Flogin%2F
13:07:30.927 [http-nio-9200-exec-6] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,48] - 跳转到SSO登录，重定向地址: http://************:3000/login
13:07:30.929 [http-nio-9200-exec-6] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,60] - SSO登录URL: http://localhost:9100/sso/login?client_id=backend&redirect_uri=http%3A%2F%2Flocalhost%3A9200%2Fsso%2Fcallback&state=http%3A%2F%2F************%3A3000%2Flogin
13:14:40.554 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,48] - 跳转到SSO登录，重定向地址: http://************:3000/login/
13:14:40.555 [http-nio-9200-exec-10] INFO  c.r.a.c.SSOClientController - [redirectToSSOLogin,60] - SSO登录URL: http://localhost:9100/sso/login?client_id=backend&redirect_uri=http%3A%2F%2Flocalhost%3A9200%2Fsso%2Fcallback&state=http%3A%2F%2F************%3A3000%2Flogin%2F
13:21:54.996 [http-nio-9200-exec-1] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: POST /logininfor, 开始时间: 1750483314996
13:22:32.119 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,61] - SSO登录回调，授权码: e0876532387044a5892c42d0a1130c36, 状态: http://localhost:81/login
13:22:32.129 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [exchangeToken,100] - 授权码换取令牌成功
13:22:32.160 [http-nio-9200-exec-3] INFO  c.r.a.c.FeignPerformanceInterceptor - [apply,27] - Feign请求开始: GET /Member/smsCodeInfo/18454831889, 开始时间: 1750483352160
13:22:32.169 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,221] - 找到本地门户用户: 18454831889
13:22:32.169 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,232] - 使用本地门户用户信息: 18454831889
13:22:32.169 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [getOrCreateLocalMember,244] - 门户用户 18454831889 拥有角色: user 和权限: system:user:list,system:user:view
13:22:32.172 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,154] - SSO登录：开始使用ProtalTokenService创建JWT token，用户: 18454831889
13:22:32.173 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,156] - SSO登录：ProtalTokenService创建token完成，结果: {access_token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxODIsImxvZ2luX3R5cGUiOiJsb2dpbl9tZW1iZXIiLCJ1c2VyX2tleSI6IjE1NTdiYjlhLWVmODAtNGZmNy05N2Y0LTMxMTJmMWQ0OGI0YyIsInVzZXJuYW1lIjoiMTg0NTQ4MzE4ODkifQ.5xfRllyrgzl0Dhe0y71Bg2CFkgpTPHmmcm4iM3VtZ2uiwjKEmV86YOdVhJ9_kbUHPOZ_V3ZDp_LipIxjsdm7yg, success=true, disabled=false, expires_in=720}
13:22:32.173 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [createLocalSessionWithToken,158] - 创建本地会话成功，用户: 18454831889, JWT Token: 已生成
13:22:32.208 [http-nio-9200-exec-3] INFO  c.r.a.s.SSOClientService - [storeTempToken,517] - 存储临时token成功，key: 9c8cc90eea3545f4acddc5e304470a72
13:22:32.210 [http-nio-9200-exec-3] INFO  c.r.a.c.SSOCallbackController - [ssoCallback,98] - SSO登录成功，跳转到前端回调页面: http://localhost:81/sso/callback?key=9c8cc90eea3545f4acddc5e304470a72&redirect=%2Findex
13:22:33.342 [http-nio-9200-exec-5] INFO  c.r.a.s.SSOClientService - [getTempToken,534] - 获取临时token成功，key: 9c8cc90eea3545f4acddc5e304470a72
13:22:33.342 [http-nio-9200-exec-5] INFO  c.r.a.c.SSOCallbackController - [getTokenByKey,123] - 前端获取JWT token成功，key: 9c8cc90eea3545f4acddc5e304470a72
13:54:30.437 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
13:54:30.439 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
13:54:34.191 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
13:54:34.249 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:54:34.658 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:54:34.659 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:54:37.070 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
13:54:39.611 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
13:54:39.614 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:54:39.614 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
13:54:39.904 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:54:41.628 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
13:54:42.663 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
13:54:42.690 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
13:54:42.732 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
13:54:43.288 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:54:45.523 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
13:54:45.615 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:54:45.615 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:54:45.832 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
13:54:46.120 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 12.568 seconds (JVM running for 14.243)
13:54:46.173 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
13:54:46.174 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
13:54:46.175 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
13:54:46.645 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:59:37.632 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
13:59:37.634 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
13:59:41.313 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
13:59:41.376 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:59:41.748 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:59:41.749 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:59:44.045 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
13:59:46.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
13:59:46.506 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:59:46.507 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
13:59:46.786 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:59:48.654 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-portalweb' URL not provided. Will try picking an instance via load-balancing.
13:59:50.058 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
13:59:50.098 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-system' URL not provided. Will try picking an instance via load-balancing.
13:59:50.149 [main] INFO  o.s.c.o.FeignClientFactoryBean - [getTarget,418] - For 'ruoyi-sso' URL not provided. Will try picking an instance via load-balancing.
13:59:50.763 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:59:53.376 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
13:59:53.485 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:59:53.486 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:59:53.697 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-auth ************:9200 register finished
13:59:53.950 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.333 seconds (JVM running for 14.889)
13:59:54.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth.yml, group=DEFAULT_GROUP
13:59:54.001 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth-prod.yml, group=DEFAULT_GROUP
13:59:54.001 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
13:59:54.374 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
