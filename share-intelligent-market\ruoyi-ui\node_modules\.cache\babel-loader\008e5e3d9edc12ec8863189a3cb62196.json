{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\member\\level.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\member\\level.vue", "mtime": 1750151094243}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_level", "require", "name", "data", "_defineProperty2", "default", "optionsStatus", "value", "label", "normsList", "loading", "show", "title", "form", "rules", "required", "message", "trigger", "discount", "remark", "pageNum", "pageSize", "status", "created", "getList", "methods", "setStatus", "row", "type", "_this", "opid", "id", "then", "response", "code", "$message", "msg", "uploadNorm", "fileList", "undefined", "url", "length", "normfile", "<PERSON><PERSON><PERSON>", "console", "log", "_this2", "listData", "queryParams", "list", "total", "count", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "ids", "map", "item", "single", "multiple", "handleAdd", "add", "handleUpdate", "inforId", "JSON", "parse", "stringify", "handleDelete", "_this3", "inforIds", "join", "$modal", "confirm", "delData", "msgSuccess", "catch", "handleCopy", "clipboardObj", "navigator", "clipboard", "writeText", "reset", "content", "edit", "handleSubmit", "_this4", "$refs", "validate", "addData", "$emit", "editData", "msgError"], "sources": ["src/views/member/level.vue"], "sourcesContent": ["// 会员等级\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"名称\" prop=\"name\">\r\n        <el-input clearable v-model=\"queryParams.name\" style=\"width: 300px\" placeholder=\"请输入名称\" :maxlength=\"60\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"手机号码\" prop=\"title\">\r\n                <el-input\r\n                    clearable\r\n                    v-model=\"queryParams.title\"\r\n                    style=\"width: 300px\"\r\n                    placeholder=\"请输入企业名称\"\r\n                    :maxlength=\"60\"\r\n                    @keyup.enter.native=\"handleQuery\"\r\n                />\r\n            </el-form-item> -->\r\n      <!-- <el-form-item label=\"用户类型\" prop=\"title\">\r\n                <el-select v-model=\"value\" placeholder=\"请选择用户类型\">\r\n                    <el-option\r\n                        v-for=\"item in options\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.label\"\r\n                        :value=\"item.value\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item> -->\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" clearable placeholder=\"请选择状态\">\r\n          <el-option v-for=\"item in optionsStatus\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col>\r\n\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\">删除\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.$index + 1 }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"名称\" align=\"center\" width=\"400\" prop=\"name\" :show-overflow-tooltip=\"true\" />\r\n\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" width=\"160\" />\r\n      <el-table-column label=\"折扣%\" align=\"center\" prop=\"discount\" width=\"160\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"create_by\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- 开启 -->\r\n          <!-- <el-switch v-model=\"form.delivery\"></el-switch> -->\r\n          <el-tag type=\"success\" v-if=\"scope.row.status == 1\">启用</el-tag>\r\n          <el-tag type=\"danger\" v-else>禁用</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button style=\"color: #85ce61\" size=\"mini\" type=\"text\" @click=\"setStatus(scope.row,1)\">启用</el-button>\r\n          <el-button style=\"color: #ebb563\" size=\"mini\" type=\"text\" @click=\"setStatus(scope.row,0)\">禁用</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n    <!-- 添加弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"70%\" :before-close=\"() => (show = false)\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" :rules=\"rules\">\r\n        <el-form-item label=\"名称\" prop=\"name\">\r\n          <el-input clearable v-model=\"form.name\" placeholder=\"请输入名称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"折扣\" prop=\"discount\">\r\n          <el-input clearable type=\"number\" v-model=\"form.discount\" placeholder=\"请输折扣\">\r\n            <template slot=\"append\">%</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input clearable v-model=\"form.remark\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-switch v-model=\"form.status\" active-value=\"1\" inactive-value=\"0\">\r\n          </el-switch>\r\n        </el-form-item>\r\n\r\n\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"show = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    listData,\r\n    setStatus,\r\n    delData,\r\n    addData,\r\n    editData,\r\n  } from \"@/api/member/level.js\";\r\n  export default {\r\n    name: \"Infor\",\r\n    data() {\r\n      return {\r\n        optionsStatus: [{\r\n            value: 1,\r\n            label: \"启用\",\r\n          },\r\n          {\r\n            value: 0,\r\n            label: \"禁用\",\r\n          },\r\n        ],\r\n        normsList: [],\r\n        loading: false,\r\n        show: false,\r\n        title: \"\",\r\n        form: {},\r\n        rules: {\r\n\r\n          name: [{\r\n            required: true,\r\n            message: \"请填写名称\",\r\n            trigger: \"blur\",\r\n          }],\r\n          discount: [{\r\n            required: true,\r\n            message: \"请填写折扣\",\r\n            trigger: \"blur\",\r\n          }],\r\n\r\n          remark: [{\r\n            required: true,\r\n            message: \"请填写备注\",\r\n            trigger: \"blur\",\r\n          }],\r\n\r\n        },\r\n\r\n        // 遮罩层\r\n        loading: true,\r\n        // 选中数组\r\n        ids: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表格数据\r\n        list: [],\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n        },\r\n        form: {\r\n          status: 1\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.getList();\r\n    },\r\n    methods: {\r\n      // 修改状态\r\n      setStatus(row, type) {\r\n        setStatus({\r\n          opid: row.id,\r\n          status: type\r\n        }).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message({\r\n              message: response.msg,\r\n              type: \"success\",\r\n            });\r\n            this.getList();\r\n          }\r\n        });\r\n      },\r\n      uploadNorm(fileList) {\r\n        let name = undefined;\r\n        let url = undefined;\r\n        if (fileList.length) {\r\n          name = fileList[0].name;\r\n          url = fileList[0].url;\r\n        }\r\n        this.form.normfile = name;\r\n        this.form.normurl = url;\r\n        console.log(this.form);\r\n      },\r\n      /** 查询公告列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listData(this.queryParams).then((response) => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n        }\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n      // 多选框选中数据\r\n      handleSelectionChange(selection) {\r\n        this.ids = selection.map((item) => item.id);\r\n        this.single = selection.length != 1;\r\n        this.multiple = !selection.length;\r\n      },\r\n      /** 新增按钮操作 */\r\n      handleAdd() {\r\n        this.add();\r\n      },\r\n      /** 修改按钮操作 */\r\n      handleUpdate(row) {\r\n        const inforId = row.id || this.ids;\r\n\r\n        this.form = JSON.parse(JSON.stringify(row));\r\n        this.title = \"编辑\";\r\n        this.show = true;\r\n        // getData(inforId).then((response) => {\r\n        //     this.edit(response.data);\r\n        // });\r\n      },\r\n      /** 删除按钮操作 */\r\n      handleDelete(row) {\r\n        const inforIds = row.id || this.ids.join(\",\");\r\n        this.$modal\r\n          .confirm('是否确认删除编号为\"' + inforIds + '\"的数据项？')\r\n          .then(function() {\r\n            return delData(inforIds);\r\n          })\r\n          .then(() => {\r\n            this.getList();\r\n            this.$modal.msgSuccess(\"删除成功\");\r\n          })\r\n          .catch(() => {});\r\n      },\r\n      handleCopy(row) {\r\n        const clipboardObj = navigator.clipboard;\r\n        this.$message({\r\n          message: \"链接已复制\",\r\n          type: \"success\",\r\n        });\r\n        clipboardObj.writeText(\r\n          \"https://sc.cnudj.com/infor?id=\" + row.id\r\n        );\r\n      },\r\n      reset() {\r\n        this.form = {\r\n          id: undefined,\r\n          title: undefined,\r\n          content: undefined,\r\n        };\r\n        this.resetForm(\"form\");\r\n      },\r\n      add() {\r\n        this.reset();\r\n        this.title = \"添加\";\r\n        this.show = true;\r\n      },\r\n      edit(data) {\r\n        this.title = \"编辑\";\r\n        this.show = true;\r\n        this.form = data;\r\n      },\r\n      handleSubmit() {\r\n        this.$refs.form.validate((validate) => {\r\n          if (validate) {\r\n            this.loading = true;\r\n            if (!this.form.id) {\r\n              console.log(this.form);\r\n              addData(this.form).then((response) => {\r\n                this.$message({\r\n                  type: \"success\",\r\n                  message: \"操作成功!\",\r\n                });\r\n                this.loading = false;\r\n                this.show = false;\r\n                this.getList()\r\n                this.$emit(\"refresh\");\r\n              });\r\n            } else {\r\n              editData(this.form).then((response) => {\r\n                this.$message({\r\n                  type: \"success\",\r\n                  message: \"操作成功!\",\r\n                });\r\n                this.loading = false;\r\n                this.show = false;\r\n                this.getList()\r\n\r\n                this.$emit(\"refresh\");\r\n              });\r\n            }\r\n          } else {\r\n            this.$modal.msgError(\"请完善信息再提交!\");\r\n          }\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAmHA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAOA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,SAAA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,KAAA;QAEAZ,IAAA;UACAa,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;QACAC,QAAA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;QAEAE,MAAA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IAAA,cAGA,cAEA,eAEA,mBAEA,qBAEA,gBAEA,YAEA,oBAEA;MACAG,OAAA;MACAC,QAAA;IACA,YACA;MACAC,MAAA;IACA;EAEA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,GAAA,EAAAC,IAAA;MAAA,IAAAC,KAAA;MACA,IAAAH,gBAAA;QACAI,IAAA,EAAAH,GAAA,CAAAI,EAAA;QACAT,MAAA,EAAAM;MACA,GAAAI,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAL,KAAA,CAAAM,QAAA;YACAnB,OAAA,EAAAiB,QAAA,CAAAG,GAAA;YACAR,IAAA;UACA;UACAC,KAAA,CAAAL,OAAA;QACA;MACA;IACA;IACAa,UAAA,WAAAA,WAAAC,QAAA;MACA,IAAApC,IAAA,GAAAqC,SAAA;MACA,IAAAC,GAAA,GAAAD,SAAA;MACA,IAAAD,QAAA,CAAAG,MAAA;QACAvC,IAAA,GAAAoC,QAAA,IAAApC,IAAA;QACAsC,GAAA,GAAAF,QAAA,IAAAE,GAAA;MACA;MACA,KAAA3B,IAAA,CAAA6B,QAAA,GAAAxC,IAAA;MACA,KAAAW,IAAA,CAAA8B,OAAA,GAAAH,GAAA;MACAI,OAAA,CAAAC,GAAA,MAAAhC,IAAA;IACA;IACA,aACAW,OAAA,WAAAA,QAAA;MAAA,IAAAsB,MAAA;MACA,KAAApC,OAAA;MACA,IAAAqC,eAAA,OAAAC,WAAA,EAAAhB,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAG,IAAA,GAAAhB,QAAA,CAAA9B,IAAA;QACA2C,MAAA,CAAAI,KAAA,GAAAjB,QAAA,CAAAkB,KAAA;QACAL,MAAA,CAAApC,OAAA;MACA;IACA;IACA,aACA0C,WAAA,WAAAA,YAAA;MAEA,KAAAJ,WAAA,CAAA5B,OAAA;MACA,KAAAI,OAAA;IACA;IACA,aACA6B,UAAA,WAAAA,WAAA;MACA,KAAAL,WAAA;QACA5B,OAAA;QACAC,QAAA;MACA;MACA,KAAAiC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAC,GAAA,GAAAD,SAAA,CAAAE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA5B,EAAA;MAAA;MACA,KAAA6B,MAAA,GAAAJ,SAAA,CAAAf,MAAA;MACA,KAAAoB,QAAA,IAAAL,SAAA,CAAAf,MAAA;IACA;IACA,aACAqB,SAAA,WAAAA,UAAA;MACA,KAAAC,GAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAArC,GAAA;MACA,IAAAsC,OAAA,GAAAtC,GAAA,CAAAI,EAAA,SAAA0B,GAAA;MAEA,KAAA5C,IAAA,GAAAqD,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAzC,GAAA;MACA,KAAAf,KAAA;MACA,KAAAD,IAAA;MACA;MACA;MACA;IACA;IACA,aACA0D,YAAA,WAAAA,aAAA1C,GAAA;MAAA,IAAA2C,MAAA;MACA,IAAAC,QAAA,GAAA5C,GAAA,CAAAI,EAAA,SAAA0B,GAAA,CAAAe,IAAA;MACA,KAAAC,MAAA,CACAC,OAAA,gBAAAH,QAAA,aACAvC,IAAA;QACA,WAAA2C,cAAA,EAAAJ,QAAA;MACA,GACAvC,IAAA;QACAsC,MAAA,CAAA9C,OAAA;QACA8C,MAAA,CAAAG,MAAA,CAAAG,UAAA;MACA,GACAC,KAAA;IACA;IACAC,UAAA,WAAAA,WAAAnD,GAAA;MACA,IAAAoD,YAAA,GAAAC,SAAA,CAAAC,SAAA;MACA,KAAA9C,QAAA;QACAnB,OAAA;QACAY,IAAA;MACA;MACAmD,YAAA,CAAAG,SAAA,CACA,mCAAAvD,GAAA,CAAAI,EACA;IACA;IACAoD,KAAA,WAAAA,MAAA;MACA,KAAAtE,IAAA;QACAkB,EAAA,EAAAQ,SAAA;QACA3B,KAAA,EAAA2B,SAAA;QACA6C,OAAA,EAAA7C;MACA;MACA,KAAAe,SAAA;IACA;IACAS,GAAA,WAAAA,IAAA;MACA,KAAAoB,KAAA;MACA,KAAAvE,KAAA;MACA,KAAAD,IAAA;IACA;IACA0E,IAAA,WAAAA,KAAAlF,IAAA;MACA,KAAAS,KAAA;MACA,KAAAD,IAAA;MACA,KAAAE,IAAA,GAAAV,IAAA;IACA;IACAmF,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA3E,IAAA,CAAA4E,QAAA,WAAAA,QAAA;QACA,IAAAA,QAAA;UACAF,MAAA,CAAA7E,OAAA;UACA,KAAA6E,MAAA,CAAA1E,IAAA,CAAAkB,EAAA;YACAa,OAAA,CAAAC,GAAA,CAAA0C,MAAA,CAAA1E,IAAA;YACA,IAAA6E,cAAA,EAAAH,MAAA,CAAA1E,IAAA,EAAAmB,IAAA,WAAAC,QAAA;cACAsD,MAAA,CAAApD,QAAA;gBACAP,IAAA;gBACAZ,OAAA;cACA;cACAuE,MAAA,CAAA7E,OAAA;cACA6E,MAAA,CAAA5E,IAAA;cACA4E,MAAA,CAAA/D,OAAA;cACA+D,MAAA,CAAAI,KAAA;YACA;UACA;YACA,IAAAC,eAAA,EAAAL,MAAA,CAAA1E,IAAA,EAAAmB,IAAA,WAAAC,QAAA;cACAsD,MAAA,CAAApD,QAAA;gBACAP,IAAA;gBACAZ,OAAA;cACA;cACAuE,MAAA,CAAA7E,OAAA;cACA6E,MAAA,CAAA5E,IAAA;cACA4E,MAAA,CAAA/D,OAAA;cAEA+D,MAAA,CAAAI,KAAA;YACA;UACA;QACA;UACAJ,MAAA,CAAAd,MAAA,CAAAoB,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}