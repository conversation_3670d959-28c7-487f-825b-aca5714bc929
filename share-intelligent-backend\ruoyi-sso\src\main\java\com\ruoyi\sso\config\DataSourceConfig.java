package com.ruoyi.sso.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * SSO服务数据源配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
@MapperScan(basePackages = "com.ruoyi.sso.mapper", sqlSessionFactoryRef = "sqlSessionFactory")
public class DataSourceConfig {

    @Value("${spring.datasource.druid.master.url:*************************************************************************************************************************************************}")
    private String url;

    @Value("${spring.datasource.druid.master.username:root}")
    private String username;

    @Value("${spring.datasource.druid.master.password:root}")
    private String password;

    @Value("${spring.datasource.driverClassName:com.mysql.cj.jdbc.Driver}")
    private String driverClassName;

    @Value("${spring.datasource.druid.initialSize:5}")
    private int initialSize;

    @Value("${spring.datasource.druid.minIdle:10}")
    private int minIdle;

    @Value("${spring.datasource.druid.maxActive:20}")
    private int maxActive;

    @Value("${spring.datasource.druid.maxWait:60000}")
    private long maxWait;

    @Value("${spring.datasource.druid.timeBetweenEvictionRunsMillis:60000}")
    private long timeBetweenEvictionRunsMillis;

    @Value("${spring.datasource.druid.minEvictableIdleTimeMillis:300000}")
    private long minEvictableIdleTimeMillis;

    @Value("${spring.datasource.druid.validationQuery:SELECT 1 FROM DUAL}")
    private String validationQuery;

    @Value("${spring.datasource.druid.testWhileIdle:true}")
    private boolean testWhileIdle;

    @Value("${spring.datasource.druid.testOnBorrow:false}")
    private boolean testOnBorrow;

    @Value("${spring.datasource.druid.testOnReturn:false}")
    private boolean testOnReturn;

    /**
     * 配置数据源
     */
    @Bean("dataSource")
    @Primary
    public DataSource druidDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setDriverClassName(driverClassName);

        // 连接池配置
        dataSource.setInitialSize(initialSize);
        dataSource.setMinIdle(minIdle);
        dataSource.setMaxActive(maxActive);
        dataSource.setMaxWait(maxWait);
        dataSource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        dataSource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
        dataSource.setValidationQuery(validationQuery);
        dataSource.setTestWhileIdle(testWhileIdle);
        dataSource.setTestOnBorrow(testOnBorrow);
        dataSource.setTestOnReturn(testOnReturn);

        return dataSource;
    }

    /**
     * 配置SqlSessionFactory
     */
    @Bean("sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        factoryBean.setDataSource(druidDataSource());

        // 设置mapper xml文件路径
        factoryBean.setMapperLocations(
            new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/**/*Mapper.xml")
        );

        // 设置mybatis配置文件
        factoryBean.setConfigLocation(
            new PathMatchingResourcePatternResolver().getResource("classpath:mybatis/mybatis-config.xml")
        );

        // 设置类型别名包
        factoryBean.setTypeAliasesPackage("com.ruoyi.sso.domain");

        return factoryBean.getObject();
    }

    /**
     * 配置事务管理器
     */
    @Bean("transactionManager")
    @Primary
    public DataSourceTransactionManager transactionManager() {
        return new DataSourceTransactionManager(druidDataSource());
    }
}
