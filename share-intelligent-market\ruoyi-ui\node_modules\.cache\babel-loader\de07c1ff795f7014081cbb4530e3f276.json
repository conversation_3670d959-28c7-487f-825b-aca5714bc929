{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\solution\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\ningmengdou\\solution\\index.vue", "mtime": 1750151094265}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_solution", "require", "name", "dicts", "data", "aa", "loading", "ids", "single", "multiple", "showSearch", "total", "solutionList", "title", "open", "queryParams", "pageNum", "pageSize", "typeId", "form", "rules", "required", "message", "trigger", "image", "detail", "watch", "created", "getList", "methods", "_this", "listSolution", "then", "response", "rows", "cancel", "reset", "id", "content", "sorts", "createBy", "createTime", "updateBy", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getSolution", "submitForm", "_this3", "$refs", "validate", "valid", "$message", "type", "num", "Math", "floor", "updateSolution", "$modal", "msgSuccess", "addSolution", "handleDelete", "_this4", "confirm", "delSolution", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/ningmengdou/solution/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"行业名称\" prop=\"typeId\">\r\n        <el-select\r\n          v-model=\"queryParams.typeId\"\r\n          placeholder=\"请选择行业名称\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.uuc_solution_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"标题\" prop=\"title\">\r\n        <el-input\r\n          v-model=\"queryParams.title\"\r\n          placeholder=\"请输入标题\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['uuc:solution:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['uuc:solution:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['uuc:solution:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n                <el-button\r\n                    type=\"warning\"\r\n                    plain\r\n                    icon=\"el-icon-download\"\r\n                    size=\"mini\"\r\n                    @click=\"handleExport\"\r\n                    v-hasPermi=\"['uuc:solution:export']\"\r\n                    >导出</el-button\r\n                >\r\n            </el-col> -->\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"solutionList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"id\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"行业名称\" align=\"center\" prop=\"typeId\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag\r\n            :options=\"dict.type.uuc_solution_type\"\r\n            :value=\"scope.row.typeId\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标题\" align=\"center\" prop=\"title\" />\r\n      <el-table-column label=\"主图\" align=\"center\" prop=\"image\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview :src=\"scope.row.image\" :width=\"50\" :height=\"50\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"描述\" align=\"center\" prop=\"detail\" />\r\n      <!-- <el-table-column label=\"详情页\" align=\"center\" prop=\"content\" /> -->\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sorts\" />\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['uuc:solution:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['uuc:solution:remove']\"\r\n            >删除</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改解决方案对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      v-if=\"open\"\r\n      :visible.sync=\"open\"\r\n      width=\"50%\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"行业名称\" prop=\"typeId\">\r\n          <el-select v-model=\"form.typeId\" placeholder=\"请选择行业名称\">\r\n            <el-option\r\n              v-for=\"dict in dict.type.uuc_solution_type\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"标题\" prop=\"title\">\r\n          <el-input\r\n            v-model=\"form.title\"\r\n            maxlength=\"30\"\r\n            placeholder=\"请输入标题\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"主图\" prop=\"image\">\r\n          <image-upload v-model=\"form.image\" :limit=\"1\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" prop=\"detail\">\r\n          <el-input\r\n            v-model=\"form.detail\"\r\n            type=\"textarea\"\r\n            maxlength=\"255\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"详情页\">\r\n          <editor v-model=\"form.content\" :min-height=\"192\" />\r\n          <!-- 富文本 -->\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"sorts\">\r\n          <el-input\r\n            v-model=\"form.sorts\"\r\n            type=\"number\"\r\n            min=\"1\"\r\n            placeholder=\"请输入排序\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listSolution,\r\n  getSolution,\r\n  delSolution,\r\n  addSolution,\r\n  updateSolution,\r\n} from \"@/api/uuc/solution\";\r\n\r\nexport default {\r\n  name: \"Solution\",\r\n  dicts: [\"uuc_solution_type\"],\r\n  data() {\r\n    return {\r\n      aa: \"<p>爱家家居</p>\",\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 解决方案表格数据\r\n      solutionList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        typeId: null,\r\n        title: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        typeId: [\r\n          {\r\n            required: true,\r\n            message: \"行业名称不能为空\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"标题不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        image: [\r\n          {\r\n            required: true,\r\n            message: \"主图不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        detail: [\r\n          {\r\n            required: true,\r\n            message: \"描述不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  watch: {\r\n    // 下列代码解开注释将会报错,拉下来就有问题\r\n    /* form: {\r\n      handler(newVal, oldVal) {\r\n        this.$refs[\"form\"].validateField([\"image\"], async (valid) => {\r\n          if (this.form.image) {\r\n            if (valid) {\r\n              this.$refs[\"form\"].clearValidate(\"image\");\r\n            }\r\n          }\r\n        });\r\n      },\r\n      deep: true,\r\n    }, */\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询解决方案列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listSolution(this.queryParams).then((response) => {\r\n        this.solutionList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        typeId: null,\r\n        title: null,\r\n        image: null,\r\n        detail: null,\r\n        content: null,\r\n        sorts: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加解决方案\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getSolution(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改解决方案\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (this.form.sorts && this.form.sorts < 0) {\r\n          this.$message({\r\n            message: \"请填写正确的排序\",\r\n            type: \"warning\",\r\n          });\r\n          return;\r\n        }\r\n        let num = Math.floor(this.form.sorts) == this.form.sorts;\r\n        if (this.form.sorts && !num) {\r\n          this.$message({\r\n            message: \"请填写正确的排序\",\r\n            type: \"warning\",\r\n          });\r\n          return;\r\n        }\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateSolution(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addSolution(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除解决方案编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delSolution(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"uuc/solution/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `solution_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AAwNA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAQA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,EAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAL,KAAA;MACA;MACA;MACAM,IAAA;MACA;MACAC,KAAA;QACAF,MAAA,GACA;UACAG,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAV,KAAA,GACA;UACAQ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,KAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAE,MAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAG,KAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXA,CAYA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAxB,OAAA;MACA,IAAAyB,sBAAA,OAAAhB,WAAA,EAAAiB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAlB,YAAA,GAAAqB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAnB,KAAA,GAAAsB,QAAA,CAAAtB,KAAA;QACAmB,KAAA,CAAAxB,OAAA;MACA;IACA;IACA;IACA6B,MAAA,WAAAA,OAAA;MACA,KAAArB,IAAA;MACA,KAAAsB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAjB,IAAA;QACAkB,EAAA;QACAnB,MAAA;QACAL,KAAA;QACAW,KAAA;QACAC,MAAA;QACAa,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA9B,WAAA,CAAAC,OAAA;MACA,KAAAY,OAAA;IACA;IACA,aACAkB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzC,GAAA,GAAAyC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAb,EAAA;MAAA;MACA,KAAA7B,MAAA,GAAAwC,SAAA,CAAAG,MAAA;MACA,KAAA1C,QAAA,IAAAuC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAhB,KAAA;MACA,KAAAtB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAwC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAnB,KAAA;MACA,IAAAC,EAAA,GAAAiB,GAAA,CAAAjB,EAAA,SAAA9B,GAAA;MACA,IAAAiD,qBAAA,EAAAnB,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAApC,IAAA,GAAAc,QAAA,CAAA7B,IAAA;QACAmD,MAAA,CAAAzC,IAAA;QACAyC,MAAA,CAAA1C,KAAA;MACA;IACA;IACA,WACA4C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAH,MAAA,CAAAvC,IAAA,CAAAoB,KAAA,IAAAmB,MAAA,CAAAvC,IAAA,CAAAoB,KAAA;UACAmB,MAAA,CAAAI,QAAA;YACAxC,OAAA;YACAyC,IAAA;UACA;UACA;QACA;QACA,IAAAC,GAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAR,MAAA,CAAAvC,IAAA,CAAAoB,KAAA,KAAAmB,MAAA,CAAAvC,IAAA,CAAAoB,KAAA;QACA,IAAAmB,MAAA,CAAAvC,IAAA,CAAAoB,KAAA,KAAAyB,GAAA;UACAN,MAAA,CAAAI,QAAA;YACAxC,OAAA;YACAyC,IAAA;UACA;UACA;QACA;QACA,IAAAF,KAAA;UACA,IAAAH,MAAA,CAAAvC,IAAA,CAAAkB,EAAA;YACA,IAAA8B,wBAAA,EAAAT,MAAA,CAAAvC,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAyB,MAAA,CAAAU,MAAA,CAAAC,UAAA;cACAX,MAAA,CAAA5C,IAAA;cACA4C,MAAA,CAAA9B,OAAA;YACA;UACA;YACA,IAAA0C,qBAAA,EAAAZ,MAAA,CAAAvC,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAyB,MAAA,CAAAU,MAAA,CAAAC,UAAA;cACAX,MAAA,CAAA5C,IAAA;cACA4C,MAAA,CAAA9B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,IAAAjE,GAAA,GAAA+C,GAAA,CAAAjB,EAAA,SAAA9B,GAAA;MACA,KAAA6D,MAAA,CACAK,OAAA,oBAAAlE,GAAA,aACAyB,IAAA;QACA,WAAA0C,qBAAA,EAAAnE,GAAA;MACA,GACAyB,IAAA;QACAwC,MAAA,CAAA5C,OAAA;QACA4C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,2BAAAC,cAAA,CAAAC,OAAA,MAEA,KAAAhE,WAAA,eAAAiE,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}