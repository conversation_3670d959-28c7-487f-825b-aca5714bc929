{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\index.vue?vue&type=template&id=39cfdb14", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\index.vue", "mtime": 1750151094310}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750495818315}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}