{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\store\\product.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\store\\product.js", "mtime": 1750151093976}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5nZXREYXRhID0gZ2V0RGF0YTsKZXhwb3J0cy5saXN0RGF0YSA9IGxpc3REYXRhOwpleHBvcnRzLm9wQ2VudHJhbCA9IG9wQ2VudHJhbDsKZXhwb3J0cy5vcERhdGEgPSBvcERhdGE7CmV4cG9ydHMub3BHcm91cCA9IG9wR3JvdXA7CmV4cG9ydHMub3BSZWNvbW1lbmQgPSBvcFJlY29tbWVuZDsKZXhwb3J0cy5wcm9kdWN0U3RvY2sgPSBwcm9kdWN0U3RvY2s7CmV4cG9ydHMucHJvZ3Jlc3NEYXRhID0gcHJvZ3Jlc3NEYXRhOwpleHBvcnRzLnNvcnREYXRhID0gc29ydERhdGE7CmV4cG9ydHMuc3RvY2tSZWNvcmQgPSBzdG9ja1JlY29yZDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyIpOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5ZWG5ZOB566h55CGCgovLyDliJfooajmlbDmja4KZnVuY3Rpb24gbGlzdERhdGEocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICJzaG9wL2FkbWluL3Byb2R1Y3QvbGlzdC8iLmNvbmNhdChwYXJhbXMucGFnZU51bSwgIi8iKS5jb25jYXQocGFyYW1zLnBhZ2VTaXplKSwKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHBhcmFtcwogIH0pOwp9CgovLyDor6bmg4XmlbDmja4KZnVuY3Rpb24gZ2V0RGF0YShpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAic2hvcC9hZG1pbi9wcm9kdWN0L2RldGFpbC8iLmNvbmNhdChpZCksCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOS/ruaUueS6p+WTgeWuoeaguOeKtuaAgQpmdW5jdGlvbiBvcERhdGEocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICdzaG9wL2FkbWluL3Byb2R1Y3Qvc3RhdHVzL29wJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQoKLy8g5L+u5pS55Lqn5ZOB5o6S5bqPCmZ1bmN0aW9uIHNvcnREYXRhKHBhcmFtcykgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnc2hvcC9hZG1pbi9wcm9kdWN0L3NvcnQvb3AnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBwYXJhbXM6IHBhcmFtcwogIH0pOwp9CgovLyDkv67mlLnkuqflk4Hov5vluqYKZnVuY3Rpb24gcHJvZ3Jlc3NEYXRhKHBhcmFtcykgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnc2hvcC9hZG1pbi9wcm9kdWN0L3Byb2dyZXNzL29wJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQoKLy8g5L+u5pS55Lqn5ZOB6ZuG6YeH54q25oCBCmZ1bmN0aW9uIG9wQ2VudHJhbChwYXJhbXMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJ3Nob3AvYWRtaW4vcHJvZHVjdC9jZW50cmFsL29wJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQoKLy8g5L+u5pS55Lqn5ZOB5Zui6LSt54q25oCBCmZ1bmN0aW9uIG9wR3JvdXAocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICdzaG9wL2FkbWluL3Byb2R1Y3QvZ3JvdXAvb3AnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBwYXJhbXM6IHBhcmFtcwogIH0pOwp9CgovLyDkv67mlLnkuqflk4HmjqjojZDnirbmgIEKZnVuY3Rpb24gb3BSZWNvbW1lbmQocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICdzaG9wL2FkbWluL3Byb2R1Y3QvcmVjb21tZW5kL29wJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQovLyDlh7rlhaXlupMKZnVuY3Rpb24gcHJvZHVjdFN0b2NrKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJ3Nob3AvYWRtaW4vcHJvZHVjdFN0b2NrL2FkZCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQovLyDlh7rlhaXlupPorrDlvZUKZnVuY3Rpb24gc3RvY2tSZWNvcmQocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICdzaG9wL2FkbWluL3Byb2R1Y3RTdG9jay9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHBhcmFtcwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listData", "params", "request", "url", "concat", "pageNum", "pageSize", "method", "getData", "id", "opData", "sortData", "progressData", "opCentral", "opGroup", "opRecommend", "productStock", "data", "stockRecord"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/store/product.js"], "sourcesContent": ["// 商品管理\r\nimport request from '@/utils/request'\r\n\r\n// 列表数据\r\nexport function listData(params) {\r\n  return request({\r\n    url: `shop/admin/product/list/${params.pageNum}/${params.pageSize}`,\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 详情数据\r\nexport function getData(id) {\r\n  return request({\r\n    url: `shop/admin/product/detail/${id}`,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n\r\n// 修改产品审核状态\r\nexport function opData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/status/op',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n// 修改产品排序\r\nexport function sortData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/sort/op',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n// 修改产品进度\r\nexport function progressData(params) {\r\n  return request({\r\n    url: 'shop/admin/product/progress/op',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n\r\n// 修改产品集采状态\r\nexport function opCentral(params) {\r\n  return request({\r\n    url: 'shop/admin/product/central/op',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n// 修改产品团购状态\r\nexport function opGroup(params) {\r\n  return request({\r\n    url: 'shop/admin/product/group/op',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n// 修改产品推荐状态\r\nexport function opRecommend(params) {\r\n  return request({\r\n    url: 'shop/admin/product/recommend/op',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n// 出入库\r\nexport function productStock(data) {\r\n  return request({\r\n    url: 'shop/admin/productStock/add',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 出入库记录\r\nexport function stockRecord(params) {\r\n  return request({\r\n    url: 'shop/admin/productStock/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;AACA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AADA;;AAGA;AACO,SAASC,QAAQA,CAACC,MAAM,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,6BAAAC,MAAA,CAA6BH,MAAM,CAACI,OAAO,OAAAD,MAAA,CAAIH,MAAM,CAACK,QAAQ,CAAE;IACnEC,MAAM,EAAE,KAAK;IACbN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,OAAOA,CAACC,EAAE,EAAE;EAC1B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,+BAAAC,MAAA,CAA+BK,EAAE,CAAE;IACtCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAGA;AACO,SAASG,MAAMA,CAACT,MAAM,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCI,MAAM,EAAE,MAAM;IACdN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,QAAQA,CAACV,MAAM,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCI,MAAM,EAAE,MAAM;IACdN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,YAAYA,CAACX,MAAM,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCI,MAAM,EAAE,MAAM;IACdN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAGA;AACO,SAASY,SAASA,CAACZ,MAAM,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCI,MAAM,EAAE,MAAM;IACdN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,OAAOA,CAACb,MAAM,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCI,MAAM,EAAE,MAAM;IACdN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,WAAWA,CAACd,MAAM,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCI,MAAM,EAAE,MAAM;IACdN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASe,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCI,MAAM,EAAE,MAAM;IACdU,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASC,WAAWA,CAACjB,MAAM,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCI,MAAM,EAAE,KAAK;IACbN,MAAM,EAANA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}