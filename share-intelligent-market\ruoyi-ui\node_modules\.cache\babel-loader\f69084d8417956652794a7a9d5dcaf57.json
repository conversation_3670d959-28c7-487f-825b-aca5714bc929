{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\menu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\menu\\index.vue", "mtime": 1750151094295}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_menu", "require", "_vueTreeselect", "_interopRequireDefault", "_IconSelect", "name", "dicts", "components", "Treeselect", "IconSelect", "data", "loading", "showSearch", "menuList", "menuOptions", "title", "open", "isExpandAll", "refreshTable", "queryParams", "menuName", "undefined", "visible", "form", "rules", "required", "message", "trigger", "orderNum", "path", "created", "getList", "methods", "selected", "icon", "_this", "listMenu", "then", "response", "handleTree", "normalizer", "node", "children", "length", "id", "menuId", "label", "getTreeselect", "_this2", "menu", "push", "cancel", "reset", "parentId", "menuType", "isFrame", "isCache", "status", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "toggleExpandAll", "_this3", "$nextTick", "handleUpdate", "_this4", "getMenu", "submitForm", "_this5", "$refs", "validate", "valid", "updateMenu", "$modal", "msgSuccess", "addMenu", "handleDelete", "_this6", "confirm", "delMenu", "catch"], "sources": ["src/views/system/menu/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"菜单名称\" prop=\"menuName\">\r\n        <el-input\r\n          v-model=\"queryParams.menuName\"\r\n          placeholder=\"请输入菜单名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"菜单状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:menu:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-sort\"\r\n          size=\"mini\"\r\n          @click=\"toggleExpandAll\"\r\n        >展开/折叠</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-if=\"refreshTable\"\r\n      v-loading=\"loading\"\r\n      :data=\"menuList\"\r\n      row-key=\"menuId\"\r\n      :default-expand-all=\"isExpandAll\"\r\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n    >\r\n      <el-table-column prop=\"menuName\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"160\"></el-table-column>\r\n      <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <svg-icon :icon-class=\"scope.row.icon\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"60\"></el-table-column>\r\n      <el-table-column prop=\"perms\" label=\"权限标识\" :show-overflow-tooltip=\"true\"></el-table-column>\r\n      <el-table-column prop=\"component\" label=\"组件路径\" :show-overflow-tooltip=\"true\"></el-table-column>\r\n      <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button \r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:menu:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd(scope.row)\"\r\n            v-hasPermi=\"['system:menu:add']\"\r\n          >新增</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:menu:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 添加或修改菜单对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"680px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"上级菜单\">\r\n              <treeselect\r\n                v-model=\"form.parentId\"\r\n                :options=\"menuOptions\"\r\n                :normalizer=\"normalizer\"\r\n                :show-count=\"true\"\r\n                placeholder=\"选择上级菜单\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"菜单类型\" prop=\"menuType\">\r\n              <el-radio-group v-model=\"form.menuType\">\r\n                <el-radio label=\"M\">目录</el-radio>\r\n                <el-radio label=\"C\">菜单</el-radio>\r\n                <el-radio label=\"F\">按钮</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item label=\"菜单图标\" prop=\"icon\">\r\n              <el-popover\r\n                placement=\"bottom-start\"\r\n                width=\"460\"\r\n                trigger=\"click\"\r\n                @show=\"$refs['iconSelect'].reset()\"\r\n              >\r\n                <IconSelect ref=\"iconSelect\" @selected=\"selected\" />\r\n                <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\r\n                  <svg-icon\r\n                    v-if=\"form.icon\"\r\n                    slot=\"prefix\"\r\n                    :icon-class=\"form.icon\"\r\n                    class=\"el-input__icon\"\r\n                    style=\"height: 32px;width: 16px;\"\r\n                  />\r\n                  <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\r\n                </el-input>\r\n              </el-popover>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"菜单名称\" prop=\"menuName\">\r\n              <el-input v-model=\"form.menuName\" placeholder=\"请输入菜单名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择是外链则路由地址需要以`http(s)://`开头\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                是否外链\r\n              </span>\r\n              <el-radio-group v-model=\"form.isFrame\">\r\n                <el-radio label=\"0\">是</el-radio>\r\n                <el-radio label=\"1\">否</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item prop=\"path\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                路由地址\r\n              </span>\r\n              <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item prop=\"component\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"访问的组件路径，如：`system/user/index`，默认在`views`目录下\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                组件路径\r\n              </span>\r\n              <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'M'\">\r\n            <el-form-item>\r\n              <el-input v-model=\"form.perms\" placeholder=\"请输入权限标识\" maxlength=\"100\" />\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                权限字符\r\n              </span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item>\r\n              <el-input v-model=\"form.query\" placeholder=\"请输入路由参数\" maxlength=\"255\" />\r\n              <span slot=\"label\">\r\n                <el-tooltip content='访问路由的默认传递参数，如：`{\"id\": 1, \"name\": \"ry\"}`' placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                路由参数\r\n              </span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                是否缓存\r\n              </span>\r\n              <el-radio-group v-model=\"form.isCache\">\r\n                <el-radio label=\"0\">缓存</el-radio>\r\n                <el-radio label=\"1\">不缓存</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                显示状态\r\n              </span>\r\n              <el-radio-group v-model=\"form.visible\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_show_hide\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择停用则路由将不会出现在侧边栏，也不能被访问\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                菜单状态\r\n              </span>\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from \"@/api/system/menu\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport IconSelect from \"@/components/IconSelect\";\r\n\r\nexport default {\r\n  name: \"Menu\",\r\n  dicts: ['sys_show_hide', 'sys_normal_disable'],\r\n  components: { Treeselect, IconSelect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 菜单表格树数据\r\n      menuList: [],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部折叠\r\n      isExpandAll: false,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        menuName: undefined,\r\n        visible: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        menuName: [\r\n          { required: true, message: \"菜单名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        orderNum: [\r\n          { required: true, message: \"菜单顺序不能为空\", trigger: \"blur\" }\r\n        ],\r\n        path: [\r\n          { required: true, message: \"路由地址不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 选择图标\r\n    selected(name) {\r\n      this.form.icon = name;\r\n    },\r\n    /** 查询菜单列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listMenu(this.queryParams).then(response => {\r\n        this.menuList = this.handleTree(response.data, \"menuId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.menuName,\r\n        children: node.children\r\n      };\r\n    },\r\n    /** 查询菜单下拉树结构 */\r\n    getTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = [];\r\n        const menu = { menuId: 0, menuName: '主类目', children: [] };\r\n        menu.children = this.handleTree(response.data, \"menuId\");\r\n        this.menuOptions.push(menu);\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        menuId: undefined,\r\n        parentId: 0,\r\n        menuName: undefined,\r\n        icon: undefined,\r\n        menuType: \"M\",\r\n        orderNum: undefined,\r\n        isFrame: \"1\",\r\n        isCache: \"0\",\r\n        visible: \"0\",\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.getTreeselect();\r\n      if (row != null && row.menuId) {\r\n        this.form.parentId = row.menuId;\r\n      } else {\r\n        this.form.parentId = 0;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加菜单\";\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.getTreeselect();\r\n      getMenu(row.menuId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改菜单\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.menuId != undefined) {\r\n            updateMenu(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addMenu(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项？').then(function() {\r\n        return delMenu(row.menuId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;AAqRA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAG,WAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAJ,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,IAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA5B,IAAA;MACA,KAAAkB,IAAA,CAAAW,IAAA,GAAA7B,IAAA;IACA;IACA,aACA0B,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAAxB,OAAA;MACA,IAAAyB,cAAA,OAAAjB,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAtB,QAAA,GAAAsB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA5B,IAAA;QACAyB,KAAA,CAAAxB,OAAA;MACA;IACA;IACA,eACA6B,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAArB,QAAA;QACAsB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,gBACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAZ,cAAA,IAAAC,IAAA,WAAAC,QAAA;QACAU,MAAA,CAAAlC,WAAA;QACA,IAAAmC,IAAA;UAAAJ,MAAA;UAAAzB,QAAA;UAAAsB,QAAA;QAAA;QACAO,IAAA,CAAAP,QAAA,GAAAM,MAAA,CAAAT,UAAA,CAAAD,QAAA,CAAA5B,IAAA;QACAsC,MAAA,CAAAlC,WAAA,CAAAoC,IAAA,CAAAD,IAAA;MACA;IACA;IACA;IACAE,MAAA,WAAAA,OAAA;MACA,KAAAnC,IAAA;MACA,KAAAoC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA7B,IAAA;QACAsB,MAAA,EAAAxB,SAAA;QACAgC,QAAA;QACAjC,QAAA,EAAAC,SAAA;QACAa,IAAA,EAAAb,SAAA;QACAiC,QAAA;QACA1B,QAAA,EAAAP,SAAA;QACAkC,OAAA;QACAC,OAAA;QACAlC,OAAA;QACAmC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA5B,OAAA;IACA;IACA,aACA6B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAV,KAAA;MACA,KAAAL,aAAA;MACA,IAAAe,GAAA,YAAAA,GAAA,CAAAjB,MAAA;QACA,KAAAtB,IAAA,CAAA8B,QAAA,GAAAS,GAAA,CAAAjB,MAAA;MACA;QACA,KAAAtB,IAAA,CAAA8B,QAAA;MACA;MACA,KAAArC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,cACAgD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA9C,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAAgD,SAAA;QACAD,MAAA,CAAA9C,YAAA;MACA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAAf,KAAA;MACA,KAAAL,aAAA;MACA,IAAAqB,aAAA,EAAAN,GAAA,CAAAjB,MAAA,EAAAR,IAAA,WAAAC,QAAA;QACA6B,MAAA,CAAA5C,IAAA,GAAAe,QAAA,CAAA5B,IAAA;QACAyD,MAAA,CAAAnD,IAAA;QACAmD,MAAA,CAAApD,KAAA;MACA;IACA;IACA;IACAsD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA/C,IAAA,CAAAsB,MAAA,IAAAxB,SAAA;YACA,IAAAqD,gBAAA,EAAAJ,MAAA,CAAA/C,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAgC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAtD,IAAA;cACAsD,MAAA,CAAAvC,OAAA;YACA;UACA;YACA,IAAA8C,aAAA,EAAAP,MAAA,CAAA/C,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAgC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAtD,IAAA;cACAsD,MAAA,CAAAvC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+C,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAAJ,MAAA,CAAAK,OAAA,gBAAAlB,GAAA,CAAA1C,QAAA,aAAAiB,IAAA;QACA,WAAA4C,aAAA,EAAAnB,GAAA,CAAAjB,MAAA;MACA,GAAAR,IAAA;QACA0C,MAAA,CAAAhD,OAAA;QACAgD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;EACA;AACA", "ignoreList": []}]}