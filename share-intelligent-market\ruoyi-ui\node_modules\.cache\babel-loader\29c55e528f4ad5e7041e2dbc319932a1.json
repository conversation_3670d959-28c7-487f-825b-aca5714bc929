{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\inquiry.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\inquiry\\inquiry.vue", "mtime": 1750151094270}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyIpOwp2YXIgX2FwcGx5ID0gcmVxdWlyZSgiQC9hcGkvZW50ZXJwcmlzZS9hcHBseSIpOwp2YXIgX2lucXVpcnkgPSByZXF1aXJlKCJAL2FwaS9wcm9qZWN0L2lucXVpcnkiKTsKdmFyIF91dGlsID0gcmVxdWlyZSgiQC9hcGkvdG9vbC91dGlsIik7CnZhciBfbGVzc0RldGFpbHMgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vY29tcG9uZW50cy9sZXNzRGV0YWlscy52dWUiKSk7CnZhciBfbW9yZURldGFpbHMgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vY29tcG9uZW50cy9tb3JlRGV0YWlscy52dWUiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBjb21wb25lbnRzOiB7CiAgICBsZXNzRGV0YWlsczogX2xlc3NEZXRhaWxzLmRlZmF1bHQsCiAgICBtb3JlRGV0YWlsczogX21vcmVEZXRhaWxzLmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDkvpvlupTllYbliJfooagKICAgICAgZW50ZXJwcmlzZU9wdGlvbnM6IFtdLAogICAgICAvLyDpgInkuK3nmoTkvpvlupTllYYKICAgICAgZW50ZXJwcmlzZToge30sCiAgICAgIGNsYXNzaWZ5T3B0aW9uczogW10sCiAgICAgIHR5cGVPcHRpb25zOiBbXSwKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIGNsYXNzaWZ5OiB7fSwKICAgICAgLy8g6K+m5oOFCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDljLnphY3mj5DkvpvllYYKICAgICAgcHJvdmlkZXJPcGVuOiBmYWxzZSwKICAgICAgaW5wdXQ6ICIiLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgaW5xdWlyeV9ubzogdW5kZWZpbmVkLAogICAgICAgIHRpdGxlOiB1bmRlZmluZWQsCiAgICAgICAgZW50ZXJwcmlzZV9uYW1lOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsCiAgICAgICAgdHlwZTogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIC8vIOihqOagvOaVsOaNrgogICAgICBsaXN0OiBbXSwKICAgICAgLy8g5Y+W5raI5by556qX5pi+56S6CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0Q2xhc3NpZnkoKTsKICAgIHRoaXMuZ2V0RW51bXMoKTsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyog5p+l6K+i5LyB5Lia5L+h5oGvICovcmVtb3RlRW50ZXJwcmlzZTogZnVuY3Rpb24gcmVtb3RlRW50ZXJwcmlzZShlKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICgwLCBfYXBwbHkuc2VhcmNoRGF0YSkoZSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIF90aGlzLmVudGVycHJpc2VPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8qIOWIh+aNouS8geS4muS/oeaBryAqL2NoYW5nZUVudGVycHJpc2U6IGZ1bmN0aW9uIGNoYW5nZUVudGVycHJpc2UoZSkgewogICAgICB0aGlzLmZvcm0uZW50ZXJwcmlzZV9pZCA9IHRoaXMuZW50ZXJwcmlzZS5pZDsKICAgICAgdGhpcy5mb3JtLmVudGVycHJpc2VfbmFtZSA9IHRoaXMuZW50ZXJwcmlzZS5uYW1lOwogICAgfSwKICAgIGdldEVudW1zOiBmdW5jdGlvbiBnZXRFbnVtcygpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgICgwLCBfdXRpbC5saXN0RW51bSkoKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczIudHlwZU9wdGlvbnMgPSByZXMuZGF0YS5pbnF1aXJ5VHlwZTsKICAgICAgICBfdGhpczIuc3RhdHVzT3B0aW9ucyA9IHJlcy5kYXRhLmlucXVpcnlTdGF0dXM7CiAgICAgIH0pOwogICAgfSwKICAgIGdldENsYXNzaWZ5OiBmdW5jdGlvbiBnZXRDbGFzc2lmeSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgICgwLCBfdXRpbC5saXN0Q2xhc3NpZnkpKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMzLmNsYXNzaWZ5T3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLyoqIOafpeivouS/oeaBr+WIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBpZiAodGhpcy5jbGFzc2lmeSkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2xhc3NpZnlfaWQgPSB0aGlzLmNsYXNzaWZ5WzBdOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2xhc3NpZnkyX2lkID0gdGhpcy5jbGFzc2lmeS5sZW5ndGggPiAxID8gdGhpcy5jbGFzc2lmeVsxXSA6IC0xOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2xhc3NpZnkzX2lkID0gdGhpcy5jbGFzc2lmeS5sZW5ndGggPiAyID8gdGhpcy5jbGFzc2lmeVsyXSA6IC0xOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2xhc3NpZnlfaWQgPSAtMTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNsYXNzaWZ5Ml9pZCA9IC0xOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2xhc3NpZnkzX2lkID0gLTE7CiAgICAgIH0KICAgICAgKDAsIF9pbnF1aXJ5Lmxpc3REYXRhKSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzNC5saXN0ID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICBfdGhpczQudG90YWwgPSByZXNwb25zZS5jb3VudDsKICAgICAgICBfdGhpczQubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmn6XnnIvor6bmg4UKICAgIGhhbmRsZUNoZWNrOiBmdW5jdGlvbiBoYW5kbGVDaGVjayhyb3cpIHt9LAogICAgaGFuZGxlRGlzcGF0Y2g6IGZ1bmN0aW9uIGhhbmRsZURpc3BhdGNoKHJvdykgewogICAgICB0aGlzLmVudGVycHJpc2UgPSAnJzsKICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuZm9ybS5pbnF1aXJ5X2lkID0gcm93LmlkOwogICAgfSwKICAgIGhhbmRsZURldGFpbDogZnVuY3Rpb24gaGFuZGxlRGV0YWlsKHJvdykgewogICAgICBpZiAocm93LnR5cGUgPT0gJ05PUk1BTCcpIHsKICAgICAgICB0aGlzLiRyZWZzLmxlc3NEZXRhaWxzLm9wZW4ocm93LmlkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRyZWZzLm1vcmVEZXRhaWxzLm9wZW4ocm93LmlkKTsKICAgICAgfQogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIG9wRGlzcGF0Y2g6IGZ1bmN0aW9uIG9wRGlzcGF0Y2goc3RhdHVzKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBpZiAoIXRoaXMuZm9ybS5lbnRlcnByaXNlX2lkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeS+m+W6lOWVhicKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgKDAsIF9pbnF1aXJ5LmRpc3BhdGNoRGF0YSkodGhpcy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczUuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnycKICAgICAgICB9KTsKICAgICAgICBfdGhpczUuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_apply", "require", "_inquiry", "_util", "_lessDetails", "_interopRequireDefault", "_moreDetails", "components", "lessDetails", "moreDetails", "data", "enterpriseOptions", "enterprise", "classifyOptions", "typeOptions", "statusOptions", "classify", "open", "providerOpen", "input", "loading", "ids", "single", "multiple", "showSearch", "total", "form", "queryParams", "pageNum", "pageSize", "inquiry_no", "undefined", "title", "enterprise_name", "status", "type", "list", "dialogVisible", "created", "getClassify", "getEnums", "getList", "methods", "remoteEnterprise", "e", "_this", "searchData", "then", "res", "changeEnterprise", "enterprise_id", "id", "name", "_this2", "listEnum", "inquiryType", "inquiryStatus", "_this3", "listClassify", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "_this4", "classify_id", "classify2_id", "length", "classify3_id", "listData", "response", "count", "handleCheck", "row", "handleDispatch", "inquiry_id", "handleDetail", "$refs", "reset", "opDispatch", "_this5", "$message", "message", "dispatchData"], "sources": ["src/views/project/inquiry/inquiry.vue"], "sourcesContent": ["<template>\r\n  <!-- 公开招募寻源 -->\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" size='small' v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\">\r\n            <el-cascader filterable clearable placeholder=\"选择产品分类\" v-model=\"classify\" :options=\"classifyOptions\"\r\n              :props='{label: \"name\", value: \"id\",checkStrictly: true}'></el-cascader>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"type\">\r\n            <el-select clearable v-model=\"queryParams.type\" placeholder=\"询价类型\">\r\n              <el-option v-for=\"item in typeOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select clearable v-model=\"queryParams.status\" placeholder=\"询价状态\">\r\n              <el-option v-for=\"item in statusOptions\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"inquiry_no\">\r\n            <el-input clearable v-model=\"queryParams.inquiry_no\" placeholder=\"输入询价单号\" style=\"width: 200px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"title\">\r\n            <el-input clearable v-model=\"queryParams.title\" placeholder=\"输入询价标题\" :maxlength='100'\r\n              style=\"width: 240px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"enterprise_name\">\r\n            <el-input clearable v-model=\"queryParams.enterprise_name\" placeholder=\"输入询价公司\" :maxlength='50'\r\n              style=\"width: 300px\">\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"50\" />\r\n          <el-table-column label=\"询价日期\" width=\"120\" align=\"center\" prop=\"inquiry_date\" />\r\n          <el-table-column label=\"询价单编号\" width=\"140\" align=\"center\" prop=\"inquiry_no\" />\r\n          <el-table-column label=\"询价类型\" width=\"120\" align=\"center\" prop=\"typeStr\" />\r\n          <el-table-column label=\"询价标题\" width=\"280\" align=\"center\" prop=\"title\" />\r\n          <el-table-column label=\"询价公司\" align=\"center\" width=\"280\" prop=\"enterprise_name\" />\r\n          <el-table-column label=\"截止时间\" width=\"120\" align=\"center\" prop=\"deadline\" />\r\n          <el-table-column label=\"操作员\" width=\"120\" align=\"center\" prop=\"operator\" />\r\n          <el-table-column label=\"询价状态\" width=\"120\" align=\"center\" prop=\"status\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"报价公司数量\" width=\"120\" align=\"center\" prop=\"offers\" />\r\n          <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-view\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n              <el-button v-if=\"scope.row.status == 'GOING'\" icon=\"el-icon-phone-outline\" type=\"text\" size=\"mini\" @click=\"handleDispatch(scope.row)\">匹配供应商</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog title=\"匹配供应商\" :visible.sync=\"dialogVisible\" append-to-body center>\r\n      <el-form ref=\"form1\" :label-position=\"'left'\" :model=\"form\" label-width=\"100px\">\r\n        <el-select clearable style=\"width: 90%;\" v-model=\"enterprise\" filterable remote reserve-keyword placeholder=\"请输入企业名称\"\r\n          :remote-method=\"remoteEnterprise\" @change='changeEnterprise' value-key='id' :loading=\"loading\">\r\n          <el-option v-for=\"item in enterpriseOptions\" :key=\"item.id\" :label=\"item.name\" :value=\"item\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"opDispatch\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 急速报价详情 -->\r\n    <lessDetails ref=\"lessDetails\"></lessDetails>\r\n    <!-- 精准报价详情 -->\r\n    <moreDetails ref=\"moreDetails\"></moreDetails>\r\n\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    searchData\r\n  } from '@/api/enterprise/apply';\r\n  import {\r\n    listData,\r\n    dispatchData\r\n  } from '@/api/project/inquiry';\r\n  import {\r\n    listEnum,\r\n    listClassify\r\n  } from '@/api/tool/util';\r\n  import lessDetails from \"./components/lessDetails.vue\";\r\n  import moreDetails from \"./components/moreDetails.vue\";\r\n  export default {\r\n    components: {\r\n      lessDetails,\r\n      moreDetails\r\n    },\r\n    data() {\r\n      return {\r\n        // 供应商列表\r\n        enterpriseOptions: [],\r\n        // 选中的供应商\r\n        enterprise: {},\r\n        classifyOptions: [],\r\n        typeOptions: [],\r\n        statusOptions: [],\r\n        classify: {},\r\n        // 详情\r\n        open: false,\r\n        // 匹配提供商\r\n        providerOpen: false,\r\n        input: \"\",\r\n        // 遮罩层\r\n        loading: false,\r\n        // 选中数组\r\n        ids: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          inquiry_no: undefined,\r\n          title: undefined,\r\n          enterprise_name: undefined,\r\n          status: undefined,\r\n          type: undefined,\r\n        },\r\n        // 表格数据\r\n        list: [],\r\n        // 取消弹窗显示\r\n        dialogVisible: false,\r\n      };\r\n    },\r\n    created() {\r\n      this.getClassify()\r\n      this.getEnums()\r\n      this.getList()\r\n    },\r\n    methods: {\r\n      /* 查询企业信息 */\r\n      remoteEnterprise(e) {\r\n        this.loading = true;\r\n        searchData(e).then(res => {\r\n          this.loading = false;\r\n          this.enterpriseOptions = res.data;\r\n        })\r\n      },\r\n      /* 切换企业信息 */\r\n      changeEnterprise(e) {\r\n        this.form.enterprise_id = this.enterprise.id;\r\n        this.form.enterprise_name = this.enterprise.name;\r\n      },\r\n      getEnums() {\r\n        listEnum().then(res => {\r\n          this.typeOptions = res.data.inquiryType;\r\n          this.statusOptions = res.data.inquiryStatus;\r\n        })\r\n      },\r\n      getClassify() {\r\n        listClassify().then(res => {\r\n          this.classifyOptions = res.data;\r\n        })\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n      /** 查询信息列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        if (this.classify) {\r\n          this.queryParams.classify_id = this.classify[0]\r\n          this.queryParams.classify2_id = this.classify.length > 1 ? this.classify[1] : -1\r\n          this.queryParams.classify3_id = this.classify.length > 2 ? this.classify[2] : -1\r\n        } else {\r\n          this.queryParams.classify_id = -1\r\n          this.queryParams.classify2_id = -1\r\n          this.queryParams.classify3_id = -1\r\n        }\r\n        listData(this.queryParams).then((response) => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      // 查看详情\r\n      handleCheck(row) {\r\n      },\r\n      handleDispatch(row){\r\n        this.enterprise = ''\r\n        this.form = {};\r\n        this.dialogVisible = true\r\n        this.form.inquiry_id = row.id\r\n      },\r\n      handleDetail(row) {\r\n        if(row.type=='NORMAL'){\r\n          this.$refs.lessDetails.open(row.id)\r\n        }\r\n        else{\r\n          this.$refs.moreDetails.open(row.id)\r\n        }\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.resetForm(\"form\");\r\n      },\r\n      opDispatch(status) {\r\n        if(!this.form.enterprise_id){\r\n          this.$message({\r\n            type: 'error',\r\n            message: '请选择供应商'\r\n          })\r\n          return\r\n        }\r\n        dispatchData(this.form).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '操作成功'\r\n          })\r\n          this.dialogVisible = false\r\n        })\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n  .item-form {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .wire {\r\n    background: rgb(219, 219, 219);\r\n    height: 1px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .form1-box {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .form1-search {\r\n    padding: 10px 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-evenly;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;AA8FA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,QAAA,GAAAD,OAAA;AAIA,IAAAE,KAAA,GAAAF,OAAA;AAIA,IAAAG,YAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAD,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAM,UAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,iBAAA;MACA;MACAC,UAAA;MACAC,eAAA;MACAC,WAAA;MACAC,aAAA;MACAC,QAAA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;MACAC,KAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD,SAAA;QACAE,eAAA,EAAAF,SAAA;QACAG,MAAA,EAAAH,SAAA;QACAI,IAAA,EAAAJ;MACA;MACA;MACAK,IAAA;MACA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,YACAC,gBAAA,WAAAA,iBAAAC,CAAA;MAAA,IAAAC,KAAA;MACA,KAAAzB,OAAA;MACA,IAAA0B,iBAAA,EAAAF,CAAA,EAAAG,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAzB,OAAA;QACAyB,KAAA,CAAAlC,iBAAA,GAAAqC,GAAA,CAAAtC,IAAA;MACA;IACA;IACA,YACAuC,gBAAA,WAAAA,iBAAAL,CAAA;MACA,KAAAlB,IAAA,CAAAwB,aAAA,QAAAtC,UAAA,CAAAuC,EAAA;MACA,KAAAzB,IAAA,CAAAO,eAAA,QAAArB,UAAA,CAAAwC,IAAA;IACA;IACAZ,QAAA,WAAAA,SAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,cAAA,IAAAP,IAAA,WAAAC,GAAA;QACAK,MAAA,CAAAvC,WAAA,GAAAkC,GAAA,CAAAtC,IAAA,CAAA6C,WAAA;QACAF,MAAA,CAAAtC,aAAA,GAAAiC,GAAA,CAAAtC,IAAA,CAAA8C,aAAA;MACA;IACA;IACAjB,WAAA,WAAAA,YAAA;MAAA,IAAAkB,MAAA;MACA,IAAAC,kBAAA,IAAAX,IAAA,WAAAC,GAAA;QACAS,MAAA,CAAA5C,eAAA,GAAAmC,GAAA,CAAAtC,IAAA;MACA;IACA;IACA,aACAiD,WAAA,WAAAA,YAAA;MACA,KAAAhC,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAmB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,aACAlB,OAAA,WAAAA,QAAA;MAAA,IAAAqB,MAAA;MACA,KAAA1C,OAAA;MACA,SAAAJ,QAAA;QACA,KAAAW,WAAA,CAAAoC,WAAA,QAAA/C,QAAA;QACA,KAAAW,WAAA,CAAAqC,YAAA,QAAAhD,QAAA,CAAAiD,MAAA,YAAAjD,QAAA;QACA,KAAAW,WAAA,CAAAuC,YAAA,QAAAlD,QAAA,CAAAiD,MAAA,YAAAjD,QAAA;MACA;QACA,KAAAW,WAAA,CAAAoC,WAAA;QACA,KAAApC,WAAA,CAAAqC,YAAA;QACA,KAAArC,WAAA,CAAAuC,YAAA;MACA;MACA,IAAAC,iBAAA,OAAAxC,WAAA,EAAAoB,IAAA,WAAAqB,QAAA;QACAN,MAAA,CAAA1B,IAAA,GAAAgC,QAAA,CAAA1D,IAAA;QACAoD,MAAA,CAAArC,KAAA,GAAA2C,QAAA,CAAAC,KAAA;QACAP,MAAA,CAAA1C,OAAA;MACA;IACA;IACA;IACAkD,WAAA,WAAAA,YAAAC,GAAA,GACA;IACAC,cAAA,WAAAA,eAAAD,GAAA;MACA,KAAA3D,UAAA;MACA,KAAAc,IAAA;MACA,KAAAW,aAAA;MACA,KAAAX,IAAA,CAAA+C,UAAA,GAAAF,GAAA,CAAApB,EAAA;IACA;IACAuB,YAAA,WAAAA,aAAAH,GAAA;MACA,IAAAA,GAAA,CAAApC,IAAA;QACA,KAAAwC,KAAA,CAAAnE,WAAA,CAAAS,IAAA,CAAAsD,GAAA,CAAApB,EAAA;MACA,OACA;QACA,KAAAwB,KAAA,CAAAlE,WAAA,CAAAQ,IAAA,CAAAsD,GAAA,CAAApB,EAAA;MACA;IACA;IACA;IACAyB,KAAA,WAAAA,MAAA;MACA,KAAAf,SAAA;IACA;IACAgB,UAAA,WAAAA,WAAA3C,MAAA;MAAA,IAAA4C,MAAA;MACA,UAAApD,IAAA,CAAAwB,aAAA;QACA,KAAA6B,QAAA;UACA5C,IAAA;UACA6C,OAAA;QACA;QACA;MACA;MACA,IAAAC,qBAAA,OAAAvD,IAAA,EAAAqB,IAAA,WAAAC,GAAA;QACA8B,MAAA,CAAAC,QAAA;UACA5C,IAAA;UACA6C,OAAA;QACA;QACAF,MAAA,CAAAzC,aAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}