{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\components\\enterprise-user.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\supply\\components\\enterprise-user.vue", "mtime": 1750151094287}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_util", "require", "_normal", "props", "enterpriseId", "Number", "data", "show", "loading", "showSearch", "total", "userList", "queryParams", "undefined", "pageNum", "pageSize", "name", "telphone", "methods", "open", "getList", "_this", "listUser", "then", "response", "count", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleOp", "row", "_this2", "adminData", "id", "adminId", "$message", "type", "message"], "sources": ["src/views/supply/components/enterprise-user.vue"], "sourcesContent": ["<!-- 添加文章 -->\r\n<template>\r\n  <el-dialog title=\"公司用户\" :visible.sync=\"show\" width=\"80%\" :before-close=\"() => show = false\">\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n        <el-form-item label=\"\" prop=\"name\">\r\n          <el-input clearable v-model=\"queryParams.name\" style=\"width: 240px;\" placeholder=\"请输入姓名\"\r\n            @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"telphone\">\r\n          <el-input clearable v-model=\"queryParams.telphone\" style=\"width: 240px;\" placeholder=\"请输入手机号\"\r\n            @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n\r\n      <el-table v-loading=\"loading\" :data=\"userList\">\r\n        <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\" />\r\n        <el-table-column label=\"手机号\" align=\"center\" width=\"200\" prop=\"telphone\" />\r\n        <el-table-column label=\"真实姓名\" align=\"center\" prop=\"realname\" width=\"200\" />\r\n        <el-table-column label=\"昵称\" align=\"center\" prop=\"nickname\" width=\"200\" />\r\n        <el-table-column label=\"邮箱\" align=\"center\" prop=\"email\" width=\"200\" />\r\n        <el-table-column prop=\"status\" label=\"类型\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag size=\"mini\" v-if=\"scope.row.type=='STAFF'\">员工</el-tag>\r\n            <el-tag size=\"mini\" type='success' v-else>管理员</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag size=\"mini\" type='danger' v-if='scope.row.status == 0'>下线</el-tag>\r\n            <el-tag size=\"mini\" type='success' v-else>上线</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"100\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleOp(scope.row)\">设为管理员</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\" />\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    listUser\r\n  } from '@/api/tool/util';\r\n  import {\r\n    adminData\r\n  } from \"@/api/enterprise/normal\";\r\n  export default {\r\n    props: {\r\n      enterpriseId: Number\r\n    },\r\n    data() {\r\n      return {\r\n        show: false,\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 公告表格数据\r\n        userList: [],\r\n        // 查询参数\r\n        queryParams: {\r\n          enterpriseId: undefined,\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          telphone: undefined,\r\n        },\r\n      };\r\n    },\r\n    methods: {\r\n      open(enterpriseId) {\r\n        this.queryParams.enterpriseId = enterpriseId\r\n        this.show = true\r\n        this.getList();\r\n      },\r\n      /** 查询公告列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listUser(this.queryParams).then(response => {\r\n          this.userList = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n      handleOp(row) {\r\n        adminData({\r\n          id: this.queryParams.enterpriseId,\r\n          adminId: row.id\r\n        }).then(response => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '操作成功!'\r\n          });\r\n          this.getList()\r\n          this.show = false;\r\n        });\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n</style>\r\n"], "mappings": ";;;;;;;AAsDA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,OAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAE,KAAA;IACAC,YAAA,EAAAC;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAR,YAAA,EAAAS,SAAA;QACAC,OAAA;QACAC,QAAA;QACAC,IAAA,EAAAH,SAAA;QACAI,QAAA,EAAAJ;MACA;IACA;EACA;EACAK,OAAA;IACAC,IAAA,WAAAA,KAAAf,YAAA;MACA,KAAAQ,WAAA,CAAAR,YAAA,GAAAA,YAAA;MACA,KAAAG,IAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAA,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA,KAAAb,OAAA;MACA,IAAAc,cAAA,OAAAV,WAAA,EAAAW,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAV,QAAA,GAAAa,QAAA,CAAAlB,IAAA;QACAe,KAAA,CAAAX,KAAA,GAAAc,QAAA,CAAAC,KAAA;QACAJ,KAAA,CAAAb,OAAA;MACA;IACA;IACA,aACAkB,WAAA,WAAAA,YAAA;MACA,KAAAd,WAAA,CAAAE,OAAA;MACA,KAAAM,OAAA;IACA;IACA,aACAO,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACAG,QAAA,WAAAA,SAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,iBAAA;QACAC,EAAA,OAAArB,WAAA,CAAAR,YAAA;QACA8B,OAAA,EAAAJ,GAAA,CAAAG;MACA,GAAAV,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAAI,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACAN,MAAA,CAAAX,OAAA;QACAW,MAAA,CAAAxB,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}