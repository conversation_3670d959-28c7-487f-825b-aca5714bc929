{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\central\\list.vue", "mtime": 1750151094225}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBwcm9kdWN0ZGV0YWlsIGZyb20gIi4uL3N0b3JlL2NvbXBvbmVudHMvcHJvZHVjdGRldGFpbC52dWUiOwppbXBvcnQgY29sbGVjdGlvbkFkZCBmcm9tICIuL2NvbXBvbmVudHMvYWRkLnZ1ZSI7CmltcG9ydCBjb2xsZWN0aW9uTGlzdCBmcm9tICIuL2NvbXBvbmVudHMvb3JkZXJzLnZ1ZSI7CmltcG9ydCBlUHJvZ3Jlc3MgZnJvbSAiLi9jb21wb25lbnRzL2UtcHJvZ3Jlc3MudnVlIjsKaW1wb3J0IGVTb3J0IGZyb20gIi4vY29tcG9uZW50cy9lLXNvcnQudnVlIjsKaW1wb3J0IHsgbGlzdERhdGEsIG9wQ2VudHJhbCB9IGZyb20gJ0AvYXBpL2NlbnRyYWwvbGlzdCc7CmltcG9ydCB7IGxpc3RFbnVtIH0gZnJvbSAnQC9hcGkvdG9vbC91dGlsJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIGNvbGxlY3Rpb25BZGQsCiAgICBwcm9kdWN0ZGV0YWlsLAogICAgY29sbGVjdGlvbkxpc3QsCiAgICBlUHJvZ3Jlc3MsCiAgICBlU29ydAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g54q25oCBCiAgICAgIHN0YXR1czogW10sCiAgICAgIC8vIOWVhuWTgeeKtuaAgQogICAgICBwcm9kdWN0U3RhdHVzOiBbXSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBuYW1lOiB1bmRlZmluZWQsCiAgICAgICAgcHJvZHVjdF9ubzp1bmRlZmluZWQsCiAgICAgICAgdHlwZTogJ0NFTlRSQUwnLAogICAgICAgIGNlbnRyYWxfc3RhdHVzOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlj5bmtojlvLnnqpfmmL7npLoKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOWPkei1t+mHh+mbhuW8ueeqlwogICAgICBjb2xsZWN0aW9uRGlhbG9nVGl0bGU6ICcnLAogICAgICAvLyDmnKzpobXpnaLlvLnnqpfmoIfpopgKICAgICAgdGl0bGU6ICcnLAogICAgICBvcEZvcm06IHsKICAgICAgICBvcGlkOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsCiAgICAgICAgbm90ZTogdW5kZWZpbmVkCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCkKICAgIHRoaXMuZ2V0RW51bSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDor6bmg4UKICAgIGhhbmRsZURlYXRpbHMocm93KSB7CiAgICAgIHRoaXMuJHJlZnMucHJvZHVjdGRldGFpbC5vcGVuKHJvdy5pZCkKICAgIH0sCiAgICAvKiDojrflj5bmnprkuL4gKi8KICAgIGdldEVudW0oKSB7CiAgICAgIGxpc3RFbnVtKCkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuc3RhdHVzID0gcmVzLmRhdGEuY2VudHJhbFN0YXR1czsKICAgICAgICB0aGlzLnByb2R1Y3RTdGF0dXMgPSByZXMuZGF0YS5wcm9kdWN0U3RhdHVzOwogICAgICB9KQogICAgfSwKICAgIC8qKiDmn6Xor6LnlKjmiLfliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIGxpc3REYXRhKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmxpc3QgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzLmNvdW50OwogICAgICB9KQogICAgfSwKICAgIC8qKiDooajljZXmkJzntKIgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLmZvcm0ucGFnZSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOmHjee9rgogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oJ3F1ZXJ5Rm9ybScpCiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKCiAgICAvKiDnvJbovpHov5vluqYgKi8KICAgIGhhbmRsZVByb2dyZXNzKHJvdykgewogICAgICB0aGlzLiRyZWZzLnByb2dyZXNzLm9wZW4ocm93KTsKICAgIH0sCiAgICAvKiDnvJbovpHmjpLluo8gKi8KICAgIGhhbmRsZVNvcnQocm93KSB7CiAgICAgIHRoaXMuJHJlZnMuc29ydC5vcGVuKHJvdyk7CiAgICB9LAogICAgLyoqIOWPkei1t+mHh+mbhiAqLwogICAgaGFuZGxlQWRkKHR5cGUsIHJvdykgewogICAgICBpZiAodHlwZSA9PSAxKSB7CiAgICAgICAgdGhpcy4kcmVmcy5jb2xsZWN0aW9uQWRkLmFkZCgpOwogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gMikgewogICAgICAgIHRoaXMuJHJlZnMuY29sbGVjdGlvbkFkZC5lZGl0KHJvdy5pZCk7CiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAzKSB7CiAgICAgICAgdGhpcy5jb2xsZWN0aW9uRGlhbG9nVGl0bGUgPSAn6aKE57qm6ZuG6YeH5YiX6KGoJzsKICAgICAgICB0aGlzLiRyZWZzLmNvbGxlY3Rpb25MaXN0LmdldExpc3Qocm93LmlkKQogICAgICB9CiAgICB9LAogICAgLyoqIOWPlua2iOmbhumHhyAqLwogICAgaGFuZGxlRGVsZXRlKHJvdywgdmFsKSB7CiAgICAgIC8vIHZhbCAx5piv5Y+W5raI5Y6f5ZugIDLmmK/lj5bmtojph4fpm4YKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9IHZhbDsKICAgICAgdGhpcy5vcEZvcm0gPSB7CiAgICAgICAgb3BpZDogcm93LmlkLAogICAgICAgIHN0YXR1czogJ0NBTkNFTCcsCiAgICAgICAgbm90ZTogdW5kZWZpbmVkCiAgICAgIH0KICAgICAgaWYodmFsID09IDEpIHsKICAgICAgICB0aGlzLm9wRm9ybS5ub3RlID0gcm93LmNlbnRyYWxfbm90ZTsKICAgICAgfQogICAgfSwKICAgIC8qIOS/ruaUueeKtuaAgSAqLwogICAgaGFuZGxlT3AoKSB7CiAgICAgIG9wQ2VudHJhbCh0aGlzLm9wRm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uoe3R5cGU6ICdzdWNjZXNzJywgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnyEnfSk7CiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSkKICAgIH0sCiAgfSwKfTsK"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/central", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\" prop=\"central_status\">\r\n            <el-select clearable v-model=\"queryParams.central_status\" placeholder=\"集采状态\" size='small'>\r\n              <el-option v-for=\"item in status\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"status\">\r\n            <el-select clearable v-model=\"queryParams.status\" placeholder=\"审核状态\" size='small'>\r\n              <el-option v-for=\"item in productStatus\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='product_no'>\r\n            <el-input clearable v-model=\"queryParams.product_no\" placeholder=\"集采编号\"  size='small'\r\n              style=\"width: 200px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop='name'>\r\n            <el-input clearable v-model=\"queryParams.name\" placeholder=\"产品名称\" :maxlength='50' size='small'\r\n              style=\"width: 300px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd(1)\">发起集采</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"发起时间\" align=\"center\" prop=\"create_time\" width=\"160\" />\r\n          <el-table-column label=\"集采编号\" align=\"center\" prop=\"system_no\" width=\"140\" />\r\n          <el-table-column label=\"产品编码\" align=\"center\" prop=\"product_no\" width=\"140\" />\r\n          <el-table-column label=\"产品名称\" align=\"center\" prop=\"name\" width=\"180\"  :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"集采价格\" align=\"center\" prop=\"tax_price\" width=\"100\" />\r\n          <el-table-column label=\"单位\" align=\"center\" prop=\"unit\" width=\"100\" />\r\n          <el-table-column label=\"产品型号\" align=\"center\" prop=\"model\" width=\"100\" />\r\n          <el-table-column label=\"供应商\" align=\"center\" prop=\"enterprise_name\" width=\"240\"  :show-overflow-tooltip=\"true\"/>\r\n          <el-table-column label=\"目标数量\" align=\"center\" prop=\"central_goal\" width=\"100\" />\r\n          <el-table-column label=\"预约数量\" align=\"center\" prop=\"central_real\" width=\"100\" />\r\n          <el-table-column label=\"预约进度\" align=\"center\" width=\"120\">\r\n            <template slot-scope='scope'>\r\n              <div>\r\n                <span>{{scope.row.central_percent}}</span>\r\n                <el-button type=\"text\" icon=\"el-icon-edit\" size=\"mini\" @click=\"handleProgress(scope.row)\">编辑</el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"预约列表\" align=\"center\" width=\"120px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\" size=\"mini\" @click=\"handleAdd(3,scope.row)\">查看\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"截止时间\" align=\"center\" prop=\"deadline\" width=\"160\" />\r\n          <el-table-column label=\"排序\" align=\"center\" width=\"120\">\r\n            <template slot-scope='scope'>\r\n              <div>\r\n                <span>{{scope.row.sorts}}</span>\r\n                <el-button type=\"text\" icon=\"el-icon-edit\" size=\"mini\" @click=\"handleSort(scope.row)\">编辑</el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"central_status\" width=\"120px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"scope.row.central_status != 'CANCEL'\">\r\n                {{scope.row.central_statusStr}}\r\n              </div>\r\n              <div v-if=\"scope.row.central_status == 'CANCEL'\">\r\n                已取消\r\n                <el-button type=\"text\" @click=\"handleDelete(scope.row,1)\">原因</el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" width=\"180\" fixed='right'>\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\" size=\"mini\" @click=\"handleDeatils(scope.row)\">详情\r\n              </el-button>\r\n              <el-button type=\"text\" icon=\"el-icon-edit\" size=\"mini\" @click=\"handleAdd(2,scope.row)\">编辑\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.central_status == 'GOING'\" type=\"text\" icon=\"el-icon-warning-outline\" size=\"mini\"\r\n                @click=\"handleDelete(scope.row,2)\">取消</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog :title=\"title==1?'集采停止原因':'确定停止集采？'\" :visible.sync=\"dialogVisible\" width=\"30%\" center>\r\n      <el-input type=\"textarea\" :rows=\"5\" placeholder=\"请输入停止原因\" :disabled=\"title==1?true:false\"\r\n        v-model=\"opForm.note\">\r\n      </el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"title == 2\" type=\"primary\" @click=\"handleOp\">确 定</el-button>\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 集采详情 -->\r\n    <collectionAdd ref=\"collectionAdd\"></collectionAdd>\r\n    <!-- 采集列表 -->\r\n    <collectionList ref=\"collectionList\" :title=\"collectionDialogTitle\"></collectionList>\r\n    <!-- 修改进度弹窗 -->\r\n    <e-progress ref='progress'></e-progress>\r\n    <!-- 修改排序弹窗 -->\r\n    <e-sort ref='sort'></e-sort>\r\n    <productdetail ref='productdetail' @refresh=\"getList\"></productdetail>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import productdetail from \"../store/components/productdetail.vue\";\r\n  import collectionAdd from \"./components/add.vue\";\r\n  import collectionList from \"./components/orders.vue\";\r\n  import eProgress from \"./components/e-progress.vue\";\r\n  import eSort from \"./components/e-sort.vue\";\r\n  import { listData, opCentral } from '@/api/central/list';\r\n  import { listEnum } from '@/api/tool/util';\r\n  export default {\r\n    components: {\r\n      collectionAdd,\r\n      productdetail,\r\n      collectionList,\r\n      eProgress,\r\n      eSort\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 状态\r\n        status: [],\r\n        // 商品状态\r\n        productStatus: [],\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          name: undefined,\r\n          product_no:undefined,\r\n          type: 'CENTRAL',\r\n          central_status: undefined,\r\n          status: undefined\r\n        },\r\n        // 表格数据\r\n        list: [],\r\n        // 取消弹窗显示\r\n        dialogVisible: false,\r\n        // 发起采集弹窗\r\n        collectionDialogTitle: '',\r\n        // 本页面弹窗标题\r\n        title: '',\r\n        opForm: {\r\n          opid: undefined,\r\n          status: undefined,\r\n          note: undefined\r\n        }\r\n      };\r\n    },\r\n    created() {\r\n      this.getList()\r\n      this.getEnum()\r\n    },\r\n    methods: {\r\n      // 详情\r\n      handleDeatils(row) {\r\n        this.$refs.productdetail.open(row.id)\r\n      },\r\n      /* 获取枚举 */\r\n      getEnum() {\r\n        listEnum().then(res => {\r\n          this.status = res.data.centralStatus;\r\n          this.productStatus = res.data.productStatus;\r\n        })\r\n      },\r\n      /** 查询用户列表 */\r\n      getList() {\r\n        listData(this.queryParams).then(res => {\r\n          this.list = res.data;\r\n          this.total = res.count;\r\n        })\r\n      },\r\n      /** 表单搜索 */\r\n      handleQuery() {\r\n        this.form.page = 1;\r\n        this.getList();\r\n      },\r\n      // 重置\r\n      resetQuery() {\r\n        this.resetForm('queryForm')\r\n        this.getList();\r\n      },\r\n\r\n      /* 编辑进度 */\r\n      handleProgress(row) {\r\n        this.$refs.progress.open(row);\r\n      },\r\n      /* 编辑排序 */\r\n      handleSort(row) {\r\n        this.$refs.sort.open(row);\r\n      },\r\n      /** 发起采集 */\r\n      handleAdd(type, row) {\r\n        if (type == 1) {\r\n          this.$refs.collectionAdd.add();\r\n        } else if (type == 2) {\r\n          this.$refs.collectionAdd.edit(row.id);\r\n        } else if (type == 3) {\r\n          this.collectionDialogTitle = '预约集采列表';\r\n          this.$refs.collectionList.getList(row.id)\r\n        }\r\n      },\r\n      /** 取消集采 */\r\n      handleDelete(row, val) {\r\n        // val 1是取消原因 2是取消采集\r\n        this.dialogVisible = true;\r\n        this.title = val;\r\n        this.opForm = {\r\n          opid: row.id,\r\n          status: 'CANCEL',\r\n          note: undefined\r\n        }\r\n        if(val == 1) {\r\n          this.opForm.note = row.central_note;\r\n        }\r\n      },\r\n      /* 修改状态 */\r\n      handleOp() {\r\n        opCentral(this.opForm).then(res => {\r\n          this.dialogVisible = false;\r\n          this.$message({type: 'success', message: '操作成功!'});\r\n          this.getList()\r\n        })\r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}