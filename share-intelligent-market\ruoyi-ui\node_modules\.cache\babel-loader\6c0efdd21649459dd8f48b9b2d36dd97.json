{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\plugins\\cache.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\plugins\\cache.js", "mtime": 1750151094188}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["sessionCache", "set", "key", "value", "sessionStorage", "setItem", "get", "getItem", "setJSON", "jsonValue", "JSON", "stringify", "getJSON", "parse", "remove", "removeItem", "localCache", "localStorage", "_default", "exports", "default", "session", "local"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/plugins/cache.js"], "sourcesContent": ["const sessionCache = {\r\n  set (key, value) {\r\n    if (!sessionStorage) {\r\n      return\r\n    }\r\n    if (key != null && value != null) {\r\n      sessionStorage.setItem(key, value)\r\n    }\r\n  },\r\n  get (key) {\r\n    if (!sessionStorage) {\r\n      return null\r\n    }\r\n    if (key == null) {\r\n      return null\r\n    }\r\n    return sessionStorage.getItem(key)\r\n  },\r\n  setJSON (key, jsonValue) {\r\n    if (jsonValue != null) {\r\n      this.set(key, JSON.stringify(jsonValue))\r\n    }\r\n  },\r\n  getJSON (key) {\r\n    const value = this.get(key)\r\n    if (value != null) {\r\n      return JSON.parse(value)\r\n    }\r\n  },\r\n  remove (key) {\r\n    sessionStorage.removeItem(key);\r\n  }\r\n}\r\nconst localCache = {\r\n  set (key, value) {\r\n    if (!localStorage) {\r\n      return\r\n    }\r\n    if (key != null && value != null) {\r\n      localStorage.setItem(key, value)\r\n    }\r\n  },\r\n  get (key) {\r\n    if (!localStorage) {\r\n      return null\r\n    }\r\n    if (key == null) {\r\n      return null\r\n    }\r\n    return localStorage.getItem(key)\r\n  },\r\n  setJSO<PERSON> (key, jsonValue) {\r\n    if (jsonValue != null) {\r\n      this.set(key, JSON.stringify(jsonValue))\r\n    }\r\n  },\r\n  getJ<PERSON><PERSON> (key) {\r\n    const value = this.get(key)\r\n    if (value != null) {\r\n      return JSON.parse(value)\r\n    }\r\n  },\r\n  remove (key) {\r\n    localStorage.removeItem(key);\r\n  }\r\n}\r\n\r\nexport default {\r\n  /**\r\n   * 会话级缓存\r\n   */\r\n  session: sessionCache,\r\n  /**\r\n   * 本地缓存\r\n   */\r\n  local: localCache\r\n}\r\n"], "mappings": ";;;;;;;;AAAA,IAAMA,YAAY,GAAG;EACnBC,GAAG,WAAHA,GAAGA,CAAEC,GAAG,EAAEC,KAAK,EAAE;IACf,IAAI,CAACC,cAAc,EAAE;MACnB;IACF;IACA,IAAIF,GAAG,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;MAChCC,cAAc,CAACC,OAAO,CAACH,GAAG,EAAEC,KAAK,CAAC;IACpC;EACF,CAAC;EACDG,GAAG,WAAHA,GAAGA,CAAEJ,GAAG,EAAE;IACR,IAAI,CAACE,cAAc,EAAE;MACnB,OAAO,IAAI;IACb;IACA,IAAIF,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,IAAI;IACb;IACA,OAAOE,cAAc,CAACG,OAAO,CAACL,GAAG,CAAC;EACpC,CAAC;EACDM,OAAO,WAAPA,OAAOA,CAAEN,GAAG,EAAEO,SAAS,EAAE;IACvB,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrB,IAAI,CAACR,GAAG,CAACC,GAAG,EAAEQ,IAAI,CAACC,SAAS,CAACF,SAAS,CAAC,CAAC;IAC1C;EACF,CAAC;EACDG,OAAO,WAAPA,OAAOA,CAAEV,GAAG,EAAE;IACZ,IAAMC,KAAK,GAAG,IAAI,CAACG,GAAG,CAACJ,GAAG,CAAC;IAC3B,IAAIC,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOO,IAAI,CAACG,KAAK,CAACV,KAAK,CAAC;IAC1B;EACF,CAAC;EACDW,MAAM,WAANA,MAAMA,CAAEZ,GAAG,EAAE;IACXE,cAAc,CAACW,UAAU,CAACb,GAAG,CAAC;EAChC;AACF,CAAC;AACD,IAAMc,UAAU,GAAG;EACjBf,GAAG,WAAHA,GAAGA,CAAEC,GAAG,EAAEC,KAAK,EAAE;IACf,IAAI,CAACc,YAAY,EAAE;MACjB;IACF;IACA,IAAIf,GAAG,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;MAChCc,YAAY,CAACZ,OAAO,CAACH,GAAG,EAAEC,KAAK,CAAC;IAClC;EACF,CAAC;EACDG,GAAG,WAAHA,GAAGA,CAAEJ,GAAG,EAAE;IACR,IAAI,CAACe,YAAY,EAAE;MACjB,OAAO,IAAI;IACb;IACA,IAAIf,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,IAAI;IACb;IACA,OAAOe,YAAY,CAACV,OAAO,CAACL,GAAG,CAAC;EAClC,CAAC;EACDM,OAAO,WAAPA,OAAOA,CAAEN,GAAG,EAAEO,SAAS,EAAE;IACvB,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrB,IAAI,CAACR,GAAG,CAACC,GAAG,EAAEQ,IAAI,CAACC,SAAS,CAACF,SAAS,CAAC,CAAC;IAC1C;EACF,CAAC;EACDG,OAAO,WAAPA,OAAOA,CAAEV,GAAG,EAAE;IACZ,IAAMC,KAAK,GAAG,IAAI,CAACG,GAAG,CAACJ,GAAG,CAAC;IAC3B,IAAIC,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOO,IAAI,CAACG,KAAK,CAACV,KAAK,CAAC;IAC1B;EACF,CAAC;EACDW,MAAM,WAANA,MAAMA,CAAEZ,GAAG,EAAE;IACXe,YAAY,CAACF,UAAU,CAACb,GAAG,CAAC;EAC9B;AACF,CAAC;AAAA,IAAAgB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACb;AACF;AACA;EACEC,OAAO,EAAErB,YAAY;EACrB;AACF;AACA;EACEsB,KAAK,EAAEN;AACT,CAAC", "ignoreList": []}]}