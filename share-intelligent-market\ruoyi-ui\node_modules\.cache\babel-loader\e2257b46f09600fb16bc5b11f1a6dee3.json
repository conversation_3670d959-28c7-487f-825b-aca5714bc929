{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\TagsView\\ScrollPane.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\TagsView\\ScrollPane.vue", "mtime": 1750151094180}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["tagAndTagSpacing", "_default", "exports", "default", "name", "data", "left", "computed", "scrollWrapper", "$refs", "scrollContainer", "wrap", "mounted", "addEventListener", "emitScroll", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "handleScroll", "e", "event<PERSON>el<PERSON>", "wheelDelta", "deltaY", "$scrollWrapper", "scrollLeft", "$emit", "move<PERSON><PERSON><PERSON>arget", "currentTag", "$container", "$el", "$containerWidth", "offsetWidth", "tagList", "$parent", "tag", "firstTag", "lastTag", "length", "scrollWidth", "currentIndex", "findIndex", "item", "prevTag", "nextTag", "afterNextTagOffsetLeft", "offsetLeft", "beforePrevTagOffsetLeft"], "sources": ["src/layout/components/TagsView/ScrollPane.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar ref=\"scrollContainer\" :vertical=\"false\" class=\"scroll-container\" @wheel.native.prevent=\"handleScroll\">\r\n    <slot />\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script>\r\nconst tagAndTagSpacing = 4 // tagAndTagSpacing\r\n\r\nexport default {\r\n  name: 'ScrollPane',\r\n  data() {\r\n    return {\r\n      left: 0\r\n    }\r\n  },\r\n  computed: {\r\n    scrollWrapper() {\r\n      return this.$refs.scrollContainer.$refs.wrap\r\n    }\r\n  },\r\n  mounted() {\r\n    this.scrollWrapper.addEventListener('scroll', this.emitScroll, true)\r\n  },\r\n  beforeDestroy() {\r\n    this.scrollWrapper.removeEventListener('scroll', this.emitScroll)\r\n  },\r\n  methods: {\r\n    handleScroll(e) {\r\n      const eventDelta = e.wheelDelta || -e.deltaY * 40\r\n      const $scrollWrapper = this.scrollWrapper\r\n      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4\r\n    },\r\n    emitScroll() {\r\n      this.$emit('scroll')\r\n    },\r\n    moveToTarget(currentTag) {\r\n      const $container = this.$refs.scrollContainer.$el\r\n      const $containerWidth = $container.offsetWidth\r\n      const $scrollWrapper = this.scrollWrapper\r\n      const tagList = this.$parent.$refs.tag\r\n\r\n      let firstTag = null\r\n      let lastTag = null\r\n\r\n      // find first tag and last tag\r\n      if (tagList.length > 0) {\r\n        firstTag = tagList[0]\r\n        lastTag = tagList[tagList.length - 1]\r\n      }\r\n\r\n      if (firstTag === currentTag) {\r\n        $scrollWrapper.scrollLeft = 0\r\n      } else if (lastTag === currentTag) {\r\n        $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth\r\n      } else {\r\n        // find preTag and nextTag\r\n        const currentIndex = tagList.findIndex(item => item === currentTag)\r\n        const prevTag = tagList[currentIndex - 1]\r\n        const nextTag = tagList[currentIndex + 1]\r\n\r\n        // the tag's offsetLeft after of nextTag\r\n        const afterNextTagOffsetLeft = nextTag.$el.offsetLeft + nextTag.$el.offsetWidth + tagAndTagSpacing\r\n\r\n        // the tag's offsetLeft before of prevTag\r\n        const beforePrevTagOffsetLeft = prevTag.$el.offsetLeft - tagAndTagSpacing\r\n\r\n        if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {\r\n          $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth\r\n        } else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {\r\n          $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scroll-container {\r\n  white-space: nowrap;\r\n  position: relative;\r\n  overflow: hidden;\r\n  width: 100%;\r\n  ::v-deep {\r\n    .el-scrollbar__bar {\r\n      bottom: 0px;\r\n    }\r\n    .el-scrollbar__wrap {\r\n      height: 49px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAOA,IAAAA,gBAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;IACA;EACA;EACAC,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAC,KAAA,CAAAC,eAAA,CAAAD,KAAA,CAAAE,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAJ,aAAA,CAAAK,gBAAA,gBAAAC,UAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAP,aAAA,CAAAQ,mBAAA,gBAAAF,UAAA;EACA;EACAG,OAAA;IACAC,YAAA,WAAAA,aAAAC,CAAA;MACA,IAAAC,UAAA,GAAAD,CAAA,CAAAE,UAAA,KAAAF,CAAA,CAAAG,MAAA;MACA,IAAAC,cAAA,QAAAf,aAAA;MACAe,cAAA,CAAAC,UAAA,GAAAD,cAAA,CAAAC,UAAA,GAAAJ,UAAA;IACA;IACAN,UAAA,WAAAA,WAAA;MACA,KAAAW,KAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,UAAA;MACA,IAAAC,UAAA,QAAAnB,KAAA,CAAAC,eAAA,CAAAmB,GAAA;MACA,IAAAC,eAAA,GAAAF,UAAA,CAAAG,WAAA;MACA,IAAAR,cAAA,QAAAf,aAAA;MACA,IAAAwB,OAAA,QAAAC,OAAA,CAAAxB,KAAA,CAAAyB,GAAA;MAEA,IAAAC,QAAA;MACA,IAAAC,OAAA;;MAEA;MACA,IAAAJ,OAAA,CAAAK,MAAA;QACAF,QAAA,GAAAH,OAAA;QACAI,OAAA,GAAAJ,OAAA,CAAAA,OAAA,CAAAK,MAAA;MACA;MAEA,IAAAF,QAAA,KAAAR,UAAA;QACAJ,cAAA,CAAAC,UAAA;MACA,WAAAY,OAAA,KAAAT,UAAA;QACAJ,cAAA,CAAAC,UAAA,GAAAD,cAAA,CAAAe,WAAA,GAAAR,eAAA;MACA;QACA;QACA,IAAAS,YAAA,GAAAP,OAAA,CAAAQ,SAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,KAAAd,UAAA;QAAA;QACA,IAAAe,OAAA,GAAAV,OAAA,CAAAO,YAAA;QACA,IAAAI,OAAA,GAAAX,OAAA,CAAAO,YAAA;;QAEA;QACA,IAAAK,sBAAA,GAAAD,OAAA,CAAAd,GAAA,CAAAgB,UAAA,GAAAF,OAAA,CAAAd,GAAA,CAAAE,WAAA,GAAA/B,gBAAA;;QAEA;QACA,IAAA8C,uBAAA,GAAAJ,OAAA,CAAAb,GAAA,CAAAgB,UAAA,GAAA7C,gBAAA;QAEA,IAAA4C,sBAAA,GAAArB,cAAA,CAAAC,UAAA,GAAAM,eAAA;UACAP,cAAA,CAAAC,UAAA,GAAAoB,sBAAA,GAAAd,eAAA;QACA,WAAAgB,uBAAA,GAAAvB,cAAA,CAAAC,UAAA;UACAD,cAAA,CAAAC,UAAA,GAAAsB,uBAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}