{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\Crontab\\result.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\components\\Crontab\\result.vue", "mtime": 1750151094126}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["result.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAa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file": "result.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n\t<div class=\"popup-result\">\r\n\t\t<p class=\"title\">最近5次运行时间</p>\r\n\t\t<ul class=\"popup-result-scroll\">\r\n\t\t\t<template v-if='isShow'>\r\n\t\t\t\t<li v-for='item in resultList' :key=\"item\">{{item}}</li>\r\n\t\t\t</template>\r\n\t\t\t<li v-else>计算结果中...</li>\r\n\t\t</ul>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tdayRule: '',\r\n\t\t\tdayRuleSup: '',\r\n\t\t\tdateArr: [],\r\n\t\t\tresultList: [],\r\n\t\t\tisShow: false\r\n\t\t}\r\n\t},\r\n\tname: 'crontab-result',\r\n\tmethods: {\r\n\t\t// 表达式值变化时，开始去计算结果\r\n\t\texpressionChange() {\r\n\r\n\t\t\t// 计算开始-隐藏结果\r\n\t\t\tthis.isShow = false;\r\n\t\t\t// 获取规则数组[0秒、1分、2时、3日、4月、5星期、6年]\r\n\t\t\tlet ruleArr = this.$options.propsData.ex.split(' ');\r\n\t\t\t// 用于记录进入循环的次数\r\n\t\t\tlet nums = 0;\r\n\t\t\t// 用于暂时存符号时间规则结果的数组\r\n\t\t\tlet resultArr = [];\r\n\t\t\t// 获取当前时间精确至[年、月、日、时、分、秒]\r\n\t\t\tlet nTime = new Date();\r\n\t\t\tlet nYear = nTime.getFullYear();\r\n\t\t\tlet nMonth = nTime.getMonth() + 1;\r\n\t\t\tlet nDay = nTime.getDate();\r\n\t\t\tlet nHour = nTime.getHours();\r\n\t\t\tlet nMin = nTime.getMinutes();\r\n\t\t\tlet nSecond = nTime.getSeconds();\r\n\t\t\t// 根据规则获取到近100年可能年数组、月数组等等\r\n\t\t\tthis.getSecondArr(ruleArr[0]);\r\n\t\t\tthis.getMinArr(ruleArr[1]);\r\n\t\t\tthis.getHourArr(ruleArr[2]);\r\n\t\t\tthis.getDayArr(ruleArr[3]);\r\n\t\t\tthis.getMonthArr(ruleArr[4]);\r\n\t\t\tthis.getWeekArr(ruleArr[5]);\r\n\t\t\tthis.getYearArr(ruleArr[6], nYear);\r\n\t\t\t// 将获取到的数组赋值-方便使用\r\n\t\t\tlet sDate = this.dateArr[0];\r\n\t\t\tlet mDate = this.dateArr[1];\r\n\t\t\tlet hDate = this.dateArr[2];\r\n\t\t\tlet DDate = this.dateArr[3];\r\n\t\t\tlet MDate = this.dateArr[4];\r\n\t\t\tlet YDate = this.dateArr[5];\r\n\t\t\t// 获取当前时间在数组中的索引\r\n\t\t\tlet sIdx = this.getIndex(sDate, nSecond);\r\n\t\t\tlet mIdx = this.getIndex(mDate, nMin);\r\n\t\t\tlet hIdx = this.getIndex(hDate, nHour);\r\n\t\t\tlet DIdx = this.getIndex(DDate, nDay);\r\n\t\t\tlet MIdx = this.getIndex(MDate, nMonth);\r\n\t\t\tlet YIdx = this.getIndex(YDate, nYear);\r\n\t\t\t// 重置月日时分秒的函数(后面用的比较多)\r\n\t\t\tconst resetSecond = function () {\r\n\t\t\t\tsIdx = 0;\r\n\t\t\t\tnSecond = sDate[sIdx]\r\n\t\t\t}\r\n\t\t\tconst resetMin = function () {\r\n\t\t\t\tmIdx = 0;\r\n\t\t\t\tnMin = mDate[mIdx]\r\n\t\t\t\tresetSecond();\r\n\t\t\t}\r\n\t\t\tconst resetHour = function () {\r\n\t\t\t\thIdx = 0;\r\n\t\t\t\tnHour = hDate[hIdx]\r\n\t\t\t\tresetMin();\r\n\t\t\t}\r\n\t\t\tconst resetDay = function () {\r\n\t\t\t\tDIdx = 0;\r\n\t\t\t\tnDay = DDate[DIdx]\r\n\t\t\t\tresetHour();\r\n\t\t\t}\r\n\t\t\tconst resetMonth = function () {\r\n\t\t\t\tMIdx = 0;\r\n\t\t\t\tnMonth = MDate[MIdx]\r\n\t\t\t\tresetDay();\r\n\t\t\t}\r\n\t\t\t// 如果当前年份不为数组中当前值\r\n\t\t\tif (nYear !== YDate[YIdx]) {\r\n\t\t\t\tresetMonth();\r\n\t\t\t}\r\n\t\t\t// 如果当前月份不为数组中当前值\r\n\t\t\tif (nMonth !== MDate[MIdx]) {\r\n\t\t\t\tresetDay();\r\n\t\t\t}\r\n\t\t\t// 如果当前“日”不为数组中当前值\r\n\t\t\tif (nDay !== DDate[DIdx]) {\r\n\t\t\t\tresetHour();\r\n\t\t\t}\r\n\t\t\t// 如果当前“时”不为数组中当前值\r\n\t\t\tif (nHour !== hDate[hIdx]) {\r\n\t\t\t\tresetMin();\r\n\t\t\t}\r\n\t\t\t// 如果当前“分”不为数组中当前值\r\n\t\t\tif (nMin !== mDate[mIdx]) {\r\n\t\t\t\tresetSecond();\r\n\t\t\t}\r\n\r\n\t\t\t// 循环年份数组\r\n\t\t\tgoYear: for (let Yi = YIdx; Yi < YDate.length; Yi++) {\r\n\t\t\t\tlet YY = YDate[Yi];\r\n\t\t\t\t// 如果到达最大值时\r\n\t\t\t\tif (nMonth > MDate[MDate.length - 1]) {\r\n\t\t\t\t\tresetMonth();\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t}\r\n\t\t\t\t// 循环月份数组\r\n\t\t\t\tgoMonth: for (let Mi = MIdx; Mi < MDate.length; Mi++) {\r\n\t\t\t\t\t// 赋值、方便后面运算\r\n\t\t\t\t\tlet MM = MDate[Mi];\r\n\t\t\t\t\tMM = MM < 10 ? '0' + MM : MM;\r\n\t\t\t\t\t// 如果到达最大值时\r\n\t\t\t\t\tif (nDay > DDate[DDate.length - 1]) {\r\n\t\t\t\t\t\tresetDay();\r\n\t\t\t\t\t\tif (Mi == MDate.length - 1) {\r\n\t\t\t\t\t\t\tresetMonth();\r\n\t\t\t\t\t\t\tcontinue goYear;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 循环日期数组\r\n\t\t\t\t\tgoDay: for (let Di = DIdx; Di < DDate.length; Di++) {\r\n\t\t\t\t\t\t// 赋值、方便后面运算\r\n\t\t\t\t\t\tlet DD = DDate[Di];\r\n\t\t\t\t\t\tlet thisDD = DD < 10 ? '0' + DD : DD;\r\n\r\n\t\t\t\t\t\t// 如果到达最大值时\r\n\t\t\t\t\t\tif (nHour > hDate[hDate.length - 1]) {\r\n\t\t\t\t\t\t\tresetHour();\r\n\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\r\n\t\t\t\t\t\t\t\tresetDay();\r\n\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\tresetMonth();\r\n\t\t\t\t\t\t\t\t\tcontinue goYear;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tcontinue goMonth;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 判断日期的合法性，不合法的话也是跳出当前循环\r\n\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true && this.dayRule !== 'workDay' && this.dayRule !== 'lastWeek' && this.dayRule !== 'lastDay') {\r\n\t\t\t\t\t\t\tresetDay();\r\n\t\t\t\t\t\t\tcontinue goMonth;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 如果日期规则中有值时\r\n\t\t\t\t\t\tif (this.dayRule == 'lastDay') {\r\n\t\t\t\t\t\t\t// 如果不是合法日期则需要将前将日期调到合法日期即月末最后一天\r\n\r\n\t\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\r\n\t\t\t\t\t\t\t\twhile (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\r\n\t\t\t\t\t\t\t\t\tDD--;\r\n\r\n\t\t\t\t\t\t\t\t\tthisDD = DD < 10 ? '0' + DD : DD;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (this.dayRule == 'workDay') {\r\n\t\t\t\t\t\t\t// 校验并调整如果是2月30号这种日期传进来时需调整至正常月底\r\n\t\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\r\n\t\t\t\t\t\t\t\twhile (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\r\n\t\t\t\t\t\t\t\t\tDD--;\r\n\t\t\t\t\t\t\t\t\tthisDD = DD < 10 ? '0' + DD : DD;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 获取达到条件的日期是星期X\r\n\t\t\t\t\t\t\tlet thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');\r\n\t\t\t\t\t\t\t// 当星期日时\r\n\t\t\t\t\t\t\tif (thisWeek == 1) {\r\n\t\t\t\t\t\t\t\t// 先找下一个日，并判断是否为月底\r\n\t\t\t\t\t\t\t\tDD++;\r\n\t\t\t\t\t\t\t\tthisDD = DD < 10 ? '0' + DD : DD;\r\n\t\t\t\t\t\t\t\t// 判断下一日已经不是合法日期\r\n\t\t\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\r\n\t\t\t\t\t\t\t\t\tDD -= 3;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else if (thisWeek == 7) {\r\n\t\t\t\t\t\t\t\t// 当星期6时只需判断不是1号就可进行操作\r\n\t\t\t\t\t\t\t\tif (this.dayRuleSup !== 1) {\r\n\t\t\t\t\t\t\t\t\tDD--;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tDD += 2;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (this.dayRule == 'weekDay') {\r\n\t\t\t\t\t\t\t// 如果指定了是星期几\r\n\t\t\t\t\t\t\t// 获取当前日期是属于星期几\r\n\t\t\t\t\t\t\tlet thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');\r\n\t\t\t\t\t\t\t// 校验当前星期是否在星期池（dayRuleSup）中\r\n\t\t\t\t\t\t\tif (this.dayRuleSup.indexOf(thisWeek) < 0) {\r\n\t\t\t\t\t\t\t\t// 如果到达最大值时\r\n\t\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\tresetDay();\r\n\t\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\tresetMonth();\r\n\t\t\t\t\t\t\t\t\t\tcontinue goYear;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tcontinue goMonth;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (this.dayRule == 'assWeek') {\r\n\t\t\t\t\t\t\t// 如果指定了是第几周的星期几\r\n\t\t\t\t\t\t\t// 获取每月1号是属于星期几\r\n\t\t\t\t\t\t\tlet thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');\r\n\t\t\t\t\t\t\tif (this.dayRuleSup[1] >= thisWeek) {\r\n\t\t\t\t\t\t\t\tDD = (this.dayRuleSup[0] - 1) * 7 + this.dayRuleSup[1] - thisWeek + 1;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tDD = this.dayRuleSup[0] * 7 + this.dayRuleSup[1] - thisWeek + 1;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (this.dayRule == 'lastWeek') {\r\n\t\t\t\t\t\t\t// 如果指定了每月最后一个星期几\r\n\t\t\t\t\t\t\t// 校验并调整如果是2月30号这种日期传进来时需调整至正常月底\r\n\t\t\t\t\t\t\tif (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\r\n\t\t\t\t\t\t\t\twhile (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {\r\n\t\t\t\t\t\t\t\t\tDD--;\r\n\t\t\t\t\t\t\t\t\tthisDD = DD < 10 ? '0' + DD : DD;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 获取月末最后一天是星期几\r\n\t\t\t\t\t\t\tlet thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');\r\n\t\t\t\t\t\t\t// 找到要求中最近的那个星期几\r\n\t\t\t\t\t\t\tif (this.dayRuleSup < thisWeek) {\r\n\t\t\t\t\t\t\t\tDD -= thisWeek - this.dayRuleSup;\r\n\t\t\t\t\t\t\t} else if (this.dayRuleSup > thisWeek) {\r\n\t\t\t\t\t\t\t\tDD -= 7 - (this.dayRuleSup - thisWeek)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 判断时间值是否小于10置换成“05”这种格式\r\n\t\t\t\t\t\tDD = DD < 10 ? '0' + DD : DD;\r\n\r\n\t\t\t\t\t\t// 循环“时”数组\r\n\t\t\t\t\t\tgoHour: for (let hi = hIdx; hi < hDate.length; hi++) {\r\n\t\t\t\t\t\t\tlet hh = hDate[hi] < 10 ? '0' + hDate[hi] : hDate[hi]\r\n\r\n\t\t\t\t\t\t\t// 如果到达最大值时\r\n\t\t\t\t\t\t\tif (nMin > mDate[mDate.length - 1]) {\r\n\t\t\t\t\t\t\t\tresetMin();\r\n\t\t\t\t\t\t\t\tif (hi == hDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\tresetHour();\r\n\t\t\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\tresetDay();\r\n\t\t\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\t\tresetMonth();\r\n\t\t\t\t\t\t\t\t\t\t\tcontinue goYear;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tcontinue goMonth;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tcontinue goDay;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 循环\"分\"数组\r\n\t\t\t\t\t\t\tgoMin: for (let mi = mIdx; mi < mDate.length; mi++) {\r\n\t\t\t\t\t\t\t\tlet mm = mDate[mi] < 10 ? '0' + mDate[mi] : mDate[mi];\r\n\r\n\t\t\t\t\t\t\t\t// 如果到达最大值时\r\n\t\t\t\t\t\t\t\tif (nSecond > sDate[sDate.length - 1]) {\r\n\t\t\t\t\t\t\t\t\tresetSecond();\r\n\t\t\t\t\t\t\t\t\tif (mi == mDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\tresetMin();\r\n\t\t\t\t\t\t\t\t\t\tif (hi == hDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\t\tresetHour();\r\n\t\t\t\t\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tresetDay();\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tresetMonth();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontinue goYear;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontinue goMonth;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tcontinue goDay;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tcontinue goHour;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t// 循环\"秒\"数组\r\n\t\t\t\t\t\t\t\tgoSecond: for (let si = sIdx; si <= sDate.length - 1; si++) {\r\n\t\t\t\t\t\t\t\t\tlet ss = sDate[si] < 10 ? '0' + sDate[si] : sDate[si];\r\n\t\t\t\t\t\t\t\t\t// 添加当前时间（时间合法性在日期循环时已经判断）\r\n\t\t\t\t\t\t\t\t\tif (MM !== '00' && DD !== '00') {\r\n\t\t\t\t\t\t\t\t\t\tresultArr.push(YY + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss)\r\n\t\t\t\t\t\t\t\t\t\tnums++;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t// 如果条数满了就退出循环\r\n\t\t\t\t\t\t\t\t\tif (nums == 5) break goYear;\r\n\t\t\t\t\t\t\t\t\t// 如果到达最大值时\r\n\t\t\t\t\t\t\t\t\tif (si == sDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\tresetSecond();\r\n\t\t\t\t\t\t\t\t\t\tif (mi == mDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\t\tresetMin();\r\n\t\t\t\t\t\t\t\t\t\t\tif (hi == hDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tresetHour();\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (Di == DDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tresetDay();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (Mi == MDate.length - 1) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tresetMonth();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcontinue goYear;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontinue goMonth;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontinue goDay;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tcontinue goHour;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tcontinue goMin;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} //goSecond\r\n\t\t\t\t\t\t\t} //goMin\r\n\t\t\t\t\t\t}//goHour\r\n\t\t\t\t\t}//goDay\r\n\t\t\t\t}//goMonth\r\n\t\t\t}\r\n\t\t\t// 判断100年内的结果条数\r\n\t\t\tif (resultArr.length == 0) {\r\n\t\t\t\tthis.resultList = ['没有达到条件的结果！'];\r\n\t\t\t} else {\r\n\t\t\t\tthis.resultList = resultArr;\r\n\t\t\t\tif (resultArr.length !== 5) {\r\n\t\t\t\t\tthis.resultList.push('最近100年内只有上面' + resultArr.length + '条结果！')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 计算完成-显示结果\r\n\t\t\tthis.isShow = true;\r\n\r\n\r\n\t\t},\r\n\t\t// 用于计算某位数字在数组中的索引\r\n\t\tgetIndex(arr, value) {\r\n\t\t\tif (value <= arr[0] || value > arr[arr.length - 1]) {\r\n\t\t\t\treturn 0;\r\n\t\t\t} else {\r\n\t\t\t\tfor (let i = 0; i < arr.length - 1; i++) {\r\n\t\t\t\t\tif (value > arr[i] && value <= arr[i + 1]) {\r\n\t\t\t\t\t\treturn i + 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取\"年\"数组\r\n\t\tgetYearArr(rule, year) {\r\n\t\t\tthis.dateArr[5] = this.getOrderArr(year, year + 100);\r\n\t\t\tif (rule !== undefined) {\r\n\t\t\t\tif (rule.indexOf('-') >= 0) {\r\n\t\t\t\t\tthis.dateArr[5] = this.getCycleArr(rule, year + 100, false)\r\n\t\t\t\t} else if (rule.indexOf('/') >= 0) {\r\n\t\t\t\t\tthis.dateArr[5] = this.getAverageArr(rule, year + 100)\r\n\t\t\t\t} else if (rule !== '*') {\r\n\t\t\t\t\tthis.dateArr[5] = this.getAssignArr(rule)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取\"月\"数组\r\n\t\tgetMonthArr(rule) {\r\n\t\t\tthis.dateArr[4] = this.getOrderArr(1, 12);\r\n\t\t\tif (rule.indexOf('-') >= 0) {\r\n\t\t\t\tthis.dateArr[4] = this.getCycleArr(rule, 12, false)\r\n\t\t\t} else if (rule.indexOf('/') >= 0) {\r\n\t\t\t\tthis.dateArr[4] = this.getAverageArr(rule, 12)\r\n\t\t\t} else if (rule !== '*') {\r\n\t\t\t\tthis.dateArr[4] = this.getAssignArr(rule)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取\"日\"数组-主要为日期规则\r\n\t\tgetWeekArr(rule) {\r\n\t\t\t// 只有当日期规则的两个值均为“”时则表达日期是有选项的\r\n\t\t\tif (this.dayRule == '' && this.dayRuleSup == '') {\r\n\t\t\t\tif (rule.indexOf('-') >= 0) {\r\n\t\t\t\t\tthis.dayRule = 'weekDay';\r\n\t\t\t\t\tthis.dayRuleSup = this.getCycleArr(rule, 7, false)\r\n\t\t\t\t} else if (rule.indexOf('#') >= 0) {\r\n\t\t\t\t\tthis.dayRule = 'assWeek';\r\n\t\t\t\t\tlet matchRule = rule.match(/[0-9]{1}/g);\r\n\t\t\t\t\tthis.dayRuleSup = [Number(matchRule[1]), Number(matchRule[0])];\r\n\t\t\t\t\tthis.dateArr[3] = [1];\r\n\t\t\t\t\tif (this.dayRuleSup[1] == 7) {\r\n\t\t\t\t\t\tthis.dayRuleSup[1] = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (rule.indexOf('L') >= 0) {\r\n\t\t\t\t\tthis.dayRule = 'lastWeek';\r\n\t\t\t\t\tthis.dayRuleSup = Number(rule.match(/[0-9]{1,2}/g)[0]);\r\n\t\t\t\t\tthis.dateArr[3] = [31];\r\n\t\t\t\t\tif (this.dayRuleSup == 7) {\r\n\t\t\t\t\t\tthis.dayRuleSup = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (rule !== '*' && rule !== '?') {\r\n\t\t\t\t\tthis.dayRule = 'weekDay';\r\n\t\t\t\t\tthis.dayRuleSup = this.getAssignArr(rule)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取\"日\"数组-少量为日期规则\r\n\t\tgetDayArr(rule) {\r\n\t\t\tthis.dateArr[3] = this.getOrderArr(1, 31);\r\n\t\t\tthis.dayRule = '';\r\n\t\t\tthis.dayRuleSup = '';\r\n\t\t\tif (rule.indexOf('-') >= 0) {\r\n\t\t\t\tthis.dateArr[3] = this.getCycleArr(rule, 31, false)\r\n\t\t\t\tthis.dayRuleSup = 'null';\r\n\t\t\t} else if (rule.indexOf('/') >= 0) {\r\n\t\t\t\tthis.dateArr[3] = this.getAverageArr(rule, 31)\r\n\t\t\t\tthis.dayRuleSup = 'null';\r\n\t\t\t} else if (rule.indexOf('W') >= 0) {\r\n\t\t\t\tthis.dayRule = 'workDay';\r\n\t\t\t\tthis.dayRuleSup = Number(rule.match(/[0-9]{1,2}/g)[0]);\r\n\t\t\t\tthis.dateArr[3] = [this.dayRuleSup];\r\n\t\t\t} else if (rule.indexOf('L') >= 0) {\r\n\t\t\t\tthis.dayRule = 'lastDay';\r\n\t\t\t\tthis.dayRuleSup = 'null';\r\n\t\t\t\tthis.dateArr[3] = [31];\r\n\t\t\t} else if (rule !== '*' && rule !== '?') {\r\n\t\t\t\tthis.dateArr[3] = this.getAssignArr(rule)\r\n\t\t\t\tthis.dayRuleSup = 'null';\r\n\t\t\t} else if (rule == '*') {\r\n\t\t\t\tthis.dayRuleSup = 'null';\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取\"时\"数组\r\n\t\tgetHourArr(rule) {\r\n\t\t\tthis.dateArr[2] = this.getOrderArr(0, 23);\r\n\t\t\tif (rule.indexOf('-') >= 0) {\r\n\t\t\t\tthis.dateArr[2] = this.getCycleArr(rule, 24, true)\r\n\t\t\t} else if (rule.indexOf('/') >= 0) {\r\n\t\t\t\tthis.dateArr[2] = this.getAverageArr(rule, 23)\r\n\t\t\t} else if (rule !== '*') {\r\n\t\t\t\tthis.dateArr[2] = this.getAssignArr(rule)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取\"分\"数组\r\n\t\tgetMinArr(rule) {\r\n\t\t\tthis.dateArr[1] = this.getOrderArr(0, 59);\r\n\t\t\tif (rule.indexOf('-') >= 0) {\r\n\t\t\t\tthis.dateArr[1] = this.getCycleArr(rule, 60, true)\r\n\t\t\t} else if (rule.indexOf('/') >= 0) {\r\n\t\t\t\tthis.dateArr[1] = this.getAverageArr(rule, 59)\r\n\t\t\t} else if (rule !== '*') {\r\n\t\t\t\tthis.dateArr[1] = this.getAssignArr(rule)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取\"秒\"数组\r\n\t\tgetSecondArr(rule) {\r\n\t\t\tthis.dateArr[0] = this.getOrderArr(0, 59);\r\n\t\t\tif (rule.indexOf('-') >= 0) {\r\n\t\t\t\tthis.dateArr[0] = this.getCycleArr(rule, 60, true)\r\n\t\t\t} else if (rule.indexOf('/') >= 0) {\r\n\t\t\t\tthis.dateArr[0] = this.getAverageArr(rule, 59)\r\n\t\t\t} else if (rule !== '*') {\r\n\t\t\t\tthis.dateArr[0] = this.getAssignArr(rule)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 根据传进来的min-max返回一个顺序的数组\r\n\t\tgetOrderArr(min, max) {\r\n\t\t\tlet arr = [];\r\n\t\t\tfor (let i = min; i <= max; i++) {\r\n\t\t\t\tarr.push(i);\r\n\t\t\t}\r\n\t\t\treturn arr;\r\n\t\t},\r\n\t\t// 根据规则中指定的零散值返回一个数组\r\n\t\tgetAssignArr(rule) {\r\n\t\t\tlet arr = [];\r\n\t\t\tlet assiginArr = rule.split(',');\r\n\t\t\tfor (let i = 0; i < assiginArr.length; i++) {\r\n\t\t\t\tarr[i] = Number(assiginArr[i])\r\n\t\t\t}\r\n\t\t\tarr.sort(this.compare)\r\n\t\t\treturn arr;\r\n\t\t},\r\n\t\t// 根据一定算术规则计算返回一个数组\r\n\t\tgetAverageArr(rule, limit) {\r\n\t\t\tlet arr = [];\r\n\t\t\tlet agArr = rule.split('/');\r\n\t\t\tlet min = Number(agArr[0]);\r\n\t\t\tlet step = Number(agArr[1]);\r\n\t\t\twhile (min <= limit) {\r\n\t\t\t\tarr.push(min);\r\n\t\t\t\tmin += step;\r\n\t\t\t}\r\n\t\t\treturn arr;\r\n\t\t},\r\n\t\t// 根据规则返回一个具有周期性的数组\r\n\t\tgetCycleArr(rule, limit, status) {\r\n\t\t\t// status--表示是否从0开始（则从1开始）\r\n\t\t\tlet arr = [];\r\n\t\t\tlet cycleArr = rule.split('-');\r\n\t\t\tlet min = Number(cycleArr[0]);\r\n\t\t\tlet max = Number(cycleArr[1]);\r\n\t\t\tif (min > max) {\r\n\t\t\t\tmax += limit;\r\n\t\t\t}\r\n\t\t\tfor (let i = min; i <= max; i++) {\r\n\t\t\t\tlet add = 0;\r\n\t\t\t\tif (status == false && i % limit == 0) {\r\n\t\t\t\t\tadd = limit;\r\n\t\t\t\t}\r\n\t\t\t\tarr.push(Math.round(i % limit + add))\r\n\t\t\t}\r\n\t\t\tarr.sort(this.compare)\r\n\t\t\treturn arr;\r\n\t\t},\r\n\t\t// 比较数字大小（用于Array.sort）\r\n\t\tcompare(value1, value2) {\r\n\t\t\tif (value2 - value1 > 0) {\r\n\t\t\t\treturn -1;\r\n\t\t\t} else {\r\n\t\t\t\treturn 1;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 格式化日期格式如：2017-9-19 18:04:33\r\n\t\tformatDate(value, type) {\r\n\t\t\t// 计算日期相关值\r\n\t\t\tlet time = typeof value == 'number' ? new Date(value) : value;\r\n\t\t\tlet Y = time.getFullYear();\r\n\t\t\tlet M = time.getMonth() + 1;\r\n\t\t\tlet D = time.getDate();\r\n\t\t\tlet h = time.getHours();\r\n\t\t\tlet m = time.getMinutes();\r\n\t\t\tlet s = time.getSeconds();\r\n\t\t\tlet week = time.getDay();\r\n\t\t\t// 如果传递了type的话\r\n\t\t\tif (type == undefined) {\r\n\t\t\t\treturn Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D) + ' ' + (h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m) + ':' + (s < 10 ? '0' + s : s);\r\n\t\t\t} else if (type == 'week') {\r\n\t\t\t\t// 在quartz中 1为星期日\r\n\t\t\t\treturn week + 1;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 检查日期是否存在\r\n\t\tcheckDate(value) {\r\n\t\t\tlet time = new Date(value);\r\n\t\t\tlet format = this.formatDate(time)\r\n\t\t\treturn value === format;\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t'ex': 'expressionChange'\r\n\t},\r\n\tprops: ['ex'],\r\n\tmounted: function () {\r\n\t\t// 初始化 获取一次结果\r\n\t\tthis.expressionChange();\r\n\t}\r\n}\r\n\r\n</script>\r\n"]}]}