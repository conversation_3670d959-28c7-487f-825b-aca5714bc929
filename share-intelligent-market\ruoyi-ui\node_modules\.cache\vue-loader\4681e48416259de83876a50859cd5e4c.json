{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1750151094180}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/TagsView", "sourcesContent": ["<template>\r\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\r\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\r\n      <router-link\r\n        v-for=\"tag in visitedViews\"\r\n        ref=\"tag\"\r\n        :key=\"tag.path\"\r\n        :class=\"isActive(tag)?'active':''\"\r\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\r\n        tag=\"span\"\r\n        class=\"tags-view-item\"\r\n        :style=\"activeStyle(tag)\"\r\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\r\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\r\n      >\r\n        {{ tag.title }}\r\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\r\n      </router-link>\r\n    </scroll-pane>\r\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n      <li @click=\"refreshSelectedTag(selectedTag)\"><i class=\"el-icon-refresh-right\"></i> 刷新页面</li>\r\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\"><i class=\"el-icon-close\"></i> 关闭当前</li>\r\n      <li @click=\"closeOthersTags\"><i class=\"el-icon-circle-close\"></i> 关闭其他</li>\r\n      <li v-if=\"!isFirstView()\" @click=\"closeLeftTags\"><i class=\"el-icon-back\"></i> 关闭左侧</li>\r\n      <li v-if=\"!isLastView()\" @click=\"closeRightTags\"><i class=\"el-icon-right\"></i> 关闭右侧</li>\r\n      <li @click=\"closeAllTags(selectedTag)\"><i class=\"el-icon-circle-close\"></i> 全部关闭</li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ScrollPane from './ScrollPane'\r\nimport path from 'path'\r\n\r\nexport default {\r\n  components: { ScrollPane },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      top: 0,\r\n      left: 0,\r\n      selectedTag: {},\r\n      affixTags: []\r\n    }\r\n  },\r\n  computed: {\r\n    visitedViews() {\r\n      return this.$store.state.tagsView.visitedViews\r\n    },\r\n    routes() {\r\n      return this.$store.state.permission.routes\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.addTags()\r\n      this.moveToCurrentTag()\r\n    },\r\n    visible(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.closeMenu)\r\n      } else {\r\n        document.body.removeEventListener('click', this.closeMenu)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTags()\r\n    this.addTags()\r\n  },\r\n  methods: {\r\n    isActive(route) {\r\n      return route.path === this.$route.path\r\n    },\r\n    activeStyle(tag) {\r\n      if (!this.isActive(tag)) return {};\r\n      return {\r\n        \"background-color\": this.theme,\r\n        \"border-color\": this.theme\r\n      };\r\n    },\r\n    isAffix(tag) {\r\n      return tag.meta && tag.meta.affix\r\n    },\r\n    isFirstView() {\r\n      try {\r\n        return this.selectedTag.fullPath === this.visitedViews[1].fullPath || this.selectedTag.fullPath === '/index'\r\n      } catch (err) {\r\n        return false\r\n      }\r\n    },\r\n    isLastView() {\r\n      try {\r\n        return this.selectedTag.fullPath === this.visitedViews[this.visitedViews.length - 1].fullPath\r\n      } catch (err) {\r\n        return false\r\n      }\r\n    },\r\n    filterAffixTags(routes, basePath = '/') {\r\n      let tags = []\r\n      routes.forEach(route => {\r\n        if (route.meta && route.meta.affix) {\r\n          const tagPath = path.resolve(basePath, route.path)\r\n          tags.push({\r\n            fullPath: tagPath,\r\n            path: tagPath,\r\n            name: route.name,\r\n            meta: { ...route.meta }\r\n          })\r\n        }\r\n        if (route.children) {\r\n          const tempTags = this.filterAffixTags(route.children, route.path)\r\n          if (tempTags.length >= 1) {\r\n            tags = [...tags, ...tempTags]\r\n          }\r\n        }\r\n      })\r\n      return tags\r\n    },\r\n    initTags() {\r\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n      for (const tag of affixTags) {\r\n        // Must have tag name\r\n        if (tag.name) {\r\n          this.$store.dispatch('tagsView/addVisitedView', tag)\r\n        }\r\n      }\r\n    },\r\n    addTags() {\r\n      const { name } = this.$route\r\n      if (name) {\r\n        this.$store.dispatch('tagsView/addView', this.$route)\r\n      }\r\n      return false\r\n    },\r\n    moveToCurrentTag() {\r\n      const tags = this.$refs.tag\r\n      this.$nextTick(() => {\r\n        for (const tag of tags) {\r\n          if (tag.to.path === this.$route.path) {\r\n            this.$refs.scrollPane.moveToTarget(tag)\r\n            // when query is different then update\r\n            if (tag.to.fullPath !== this.$route.fullPath) {\r\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n            }\r\n            break\r\n          }\r\n        }\r\n      })\r\n    },\r\n    refreshSelectedTag(view) {\r\n      this.$tab.refreshPage(view);\r\n    },\r\n    closeSelectedTag(view) {\r\n      this.$tab.closePage(view).then(({ visitedViews }) => {\r\n        if (this.isActive(view)) {\r\n          this.toLastView(visitedViews, view)\r\n        }\r\n      })\r\n    },\r\n    closeRightTags() {\r\n      this.$tab.closeRightPage(this.selectedTag).then(visitedViews => {\r\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\r\n          this.toLastView(visitedViews)\r\n        }\r\n      })\r\n    },\r\n    closeLeftTags() {\r\n      this.$tab.closeLeftPage(this.selectedTag).then(visitedViews => {\r\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\r\n          this.toLastView(visitedViews)\r\n        }\r\n      })\r\n    },\r\n    closeOthersTags() {\r\n      this.$router.push(this.selectedTag).catch(()=>{});\r\n      this.$tab.closeOtherPage(this.selectedTag).then(() => {\r\n        this.moveToCurrentTag()\r\n      })\r\n    },\r\n    closeAllTags(view) {\r\n      this.$tab.closeAllPage().then(({ visitedViews }) => {\r\n        if (this.affixTags.some(tag => tag.path === this.$route.path)) {\r\n          return\r\n        }\r\n        this.toLastView(visitedViews, view)\r\n      })\r\n    },\r\n    toLastView(visitedViews, view) {\r\n      const latestView = visitedViews.slice(-1)[0]\r\n      if (latestView) {\r\n        this.$router.push(latestView.fullPath)\r\n      } else {\r\n        // now the default is to redirect to the home page if there is no tags-view,\r\n        // you can adjust it according to your needs.\r\n        if (view.name === 'Dashboard') {\r\n          // to reload home page\r\n          this.$router.replace({ path: '/redirect' + view.fullPath })\r\n        } else {\r\n          this.$router.push('/')\r\n        }\r\n      }\r\n    },\r\n    openMenu(tag, e) {\r\n      const menuMinWidth = 105\r\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n      const offsetWidth = this.$el.offsetWidth // container width\r\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\r\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n      if (left > maxLeft) {\r\n        this.left = maxLeft\r\n      } else {\r\n        this.left = left\r\n      }\r\n\r\n      this.top = e.clientY\r\n      this.visible = true\r\n      this.selectedTag = tag\r\n    },\r\n    closeMenu() {\r\n      this.visible = false\r\n    },\r\n    handleScroll() {\r\n      this.closeMenu()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tags-view-container {\r\n  height: 34px;\r\n  width: 100%;\r\n  background: #fff;\r\n  border-bottom: 1px solid #d8dce5;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n  .tags-view-wrapper {\r\n    .tags-view-item {\r\n      display: inline-block;\r\n      position: relative;\r\n      cursor: pointer;\r\n      height: 26px;\r\n      line-height: 26px;\r\n      border: 1px solid #d8dce5;\r\n      color: #495060;\r\n      background: #fff;\r\n      padding: 0 8px;\r\n      font-size: 12px;\r\n      margin-left: 5px;\r\n      margin-top: 4px;\r\n      &:first-of-type {\r\n        margin-left: 15px;\r\n      }\r\n      &:last-of-type {\r\n        margin-right: 15px;\r\n      }\r\n      &.active {\r\n        background-color: #42b983;\r\n        color: #fff;\r\n        border-color: #42b983;\r\n        &::before {\r\n          content: '';\r\n          background: #fff;\r\n          display: inline-block;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 50%;\r\n          position: relative;\r\n          margin-right: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .contextmenu {\r\n    margin: 0;\r\n    background: #fff;\r\n    z-index: 3000;\r\n    position: absolute;\r\n    list-style-type: none;\r\n    padding: 5px 0;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n    font-weight: 400;\r\n    color: #333;\r\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\r\n    li {\r\n      margin: 0;\r\n      padding: 7px 16px;\r\n      cursor: pointer;\r\n      &:hover {\r\n        background: #eee;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n//reset element css of el-icon-close\r\n.tags-view-wrapper {\r\n  .tags-view-item {\r\n    .el-icon-close {\r\n      width: 16px;\r\n      height: 16px;\r\n      vertical-align: 2px;\r\n      border-radius: 50%;\r\n      text-align: center;\r\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\r\n      transform-origin: 100% 50%;\r\n      &:before {\r\n        transform: scale(.6);\r\n        display: inline-block;\r\n        vertical-align: -3px;\r\n      }\r\n      &:hover {\r\n        background-color: #b4bccc;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}