
8bb1d33f230940f0a62ec17716369d4f566f2713	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fui\\u002Ftinymce-5-dark\\u002Fcontent.inline.css\",\"contentHash\":\"1793432c4744952fc56ec49978e324e4\"}","integrity":"sha512-es5aYvROR5wOL68RZxGEQzxY6+mZ1+h0s3pZorbw1n2/rb6NJBPd1HWppaapjsdn0Ks6GWXbAvL0fzbVatEtLg==","time":1750496064279,"size":21614}