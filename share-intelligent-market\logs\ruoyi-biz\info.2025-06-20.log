09:11:01.889 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:11:04.189 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of add2d054-c558-40dd-98ce-d6eddfd28f63_config-0
09:11:04.294 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 56 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:04.353 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:04.372 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:04.624 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 247 ms to scan 248 urls, producing 0 keys and 0 values 
09:11:04.634 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:04.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:04.670 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:04.897 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 224 ms to scan 248 urls, producing 0 keys and 0 values 
09:11:04.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:04.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/364389956
09:11:04.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/542598487
09:11:04.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:04.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:04.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:07.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381867404_127.0.0.1_49476
09:11:07.975 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Notify connected event to listeners.
09:11:07.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:07.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [add2d054-c558-40dd-98ce-d6eddfd28f63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/1788036341
09:11:08.253 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:11:15.904 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9704"]
09:11:15.906 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:11:15.907 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
09:11:16.756 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:11:30.517 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:11:32.783 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bb51adcd-5a02-4bab-b474-9656844ab5ec
09:11:32.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] RpcClient init label, labels = {module=naming, source=sdk}
09:11:32.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:11:32.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:11:32.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:11:32.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:11:32.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750381892828_127.0.0.1_49978
09:11:32.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:32.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Notify connected event to listeners.
09:11:32.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$342/1788036341
09:11:43.365 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9704"]
09:11:43.515 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-biz-shop 192.168.0.68:9704 register finished
09:11:43.871 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:11:43.885 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:11:45.753 [main] INFO  c.r.b.s.RuoyiBizShopApplication - [logStarted,61] - Started RuoyiBizShopApplication in 45.11 seconds (JVM running for 46.732)
09:11:45.798 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop-dev.yaml, group=DEFAULT_GROUP
09:11:45.798 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop.yaml, group=DEFAULT_GROUP
09:11:45.799 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-biz-shop, group=DEFAULT_GROUP
09:11:46.570 [RMI TCP Connection(29)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:31:42.778 [nacos-grpc-client-executor-266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:31:42.779 [nacos-grpc-client-executor-266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb51adcd-5a02-4bab-b474-9656844ab5ec] Ack server push request, request = NotifySubscriberRequest, requestId = 18
09:31:51.512 [http-nio-9704-exec-4] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_c9becf9ebb7e326e43d6b118d826a735=1750313378,1750383095;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
12:39:46.426 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:39:46.428 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:39:46.766 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:39:46.767 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@a72a4a7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:39:46.767 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750381892828_127.0.0.1_49978
12:39:46.770 [nacos-grpc-client-executor-2680] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750381892828_127.0.0.1_49978]Ignore complete event,isRunning:false,isAbandon=false
12:39:46.827 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@485c305e[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 2681]
