
231f5e29d6ddfc65d93a40d952c4585d7b246481	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Fwriter\\u002Fcontent.min.css\",\"contentHash\":\"2051de66c0446b4ca1853ea0638600e9\"}","integrity":"sha512-YiiGfm/vUW6JqUeRF7qiudh7+hr/t2kGUzYAPhXTqsI6ByKa5Wtx26IA9sIaZK1uV6UpQsBAXgHv/Xnh5CQVIw==","time":1750496064273,"size":1731}