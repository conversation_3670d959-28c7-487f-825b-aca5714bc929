
39e73f944ae7d313f3829e807929215bc3e67b4c	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fchunk-vendors.js\",\"contentHash\":\"1ad232954fd7862974f0f9a3ca5a5dc1\"}","integrity":"sha512-pn9mRdJHGOOLNamvi197gFzn6iWpFdIbnKQk+PoEjaTNaoo2H4Y1nijH4kjdap0sl9p18d+O6pdpEFlhQLLp2Q==","time":1750496065817,"size":13607752}