
bca0dc753e4872bf930d4068b8f72befb9fa196c	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Ftinymce-5\\u002Fcontent.css\",\"contentHash\":\"b881b696695275870c212332356b1476\"}","integrity":"sha512-FZ5gxrqXsGzvHgmH3paGvooTOrUgQWDD8YOBDhbvrA64fyhwFuGYVifMEHkfx7BTJll3Isisng/NRTBjftSC+Q==","time":1750496064277,"size":2130}