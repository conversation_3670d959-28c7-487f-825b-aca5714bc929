package com.ruoyi.shop.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.shop.api.domain.MarketUser;
import com.ruoyi.shop.api.factory.RemoteMarketUserFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 市场系统用户远程服务接口
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteMarketUserService", value = ServiceNameConstants.SHOP_SERVICE, fallbackFactory = RemoteMarketUserFallbackFactory.class)
public interface RemoteMarketUserService {

    /**
     * 通过手机号查询市场系统用户信息
     *
     * @param telphone 手机号
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/telphone/{telphone}")
    public R<MarketUser> getUserInfoByTelphone(@PathVariable("telphone") String telphone, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册市场系统用户信息
     *
     * @param marketUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody MarketUser marketUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
