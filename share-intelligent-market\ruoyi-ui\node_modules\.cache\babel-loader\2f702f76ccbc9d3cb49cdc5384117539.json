{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\directive\\permission\\hasRole.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\directive\\permission\\hasRole.js", "mtime": 1750151094163}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtbWFya2V0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3Iuc29tZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnZhciBfc3RvcmUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvc3RvcmUiKSk7Ci8qKg0KKiB2LWhhc1JvbGUg6KeS6Imy5p2D6ZmQ5aSE55CGDQoqIENvcHlyaWdodCAoYykgMjAxOSBydW95aQ0KKi8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGluc2VydGVkOiBmdW5jdGlvbiBpbnNlcnRlZChlbCwgYmluZGluZywgdm5vZGUpIHsKICAgIHZhciB2YWx1ZSA9IGJpbmRpbmcudmFsdWU7CiAgICB2YXIgc3VwZXJfYWRtaW4gPSAiYWRtaW4iOwogICAgdmFyIHJvbGVzID0gX3N0b3JlLmRlZmF1bHQuZ2V0dGVycyAmJiBfc3RvcmUuZGVmYXVsdC5nZXR0ZXJzLnJvbGVzOwogICAgaWYgKHZhbHVlICYmIHZhbHVlIGluc3RhbmNlb2YgQXJyYXkgJiYgdmFsdWUubGVuZ3RoID4gMCkgewogICAgICB2YXIgcm9sZUZsYWcgPSB2YWx1ZTsKICAgICAgdmFyIGhhc1JvbGUgPSByb2xlcy5zb21lKGZ1bmN0aW9uIChyb2xlKSB7CiAgICAgICAgcmV0dXJuIHN1cGVyX2FkbWluID09PSByb2xlIHx8IHJvbGVGbGFnLmluY2x1ZGVzKHJvbGUpOwogICAgICB9KTsKICAgICAgaWYgKCFoYXNSb2xlKSB7CiAgICAgICAgZWwucGFyZW50Tm9kZSAmJiBlbC5wYXJlbnROb2RlLnJlbW92ZUNoaWxkKGVsKTsKICAgICAgfQogICAgfSBlbHNlIHsKICAgICAgdGhyb3cgbmV3IEVycm9yKCJcdThCRjdcdThCQkVcdTdGNkVcdTg5RDJcdTgyNzJcdTY3NDNcdTk2NTBcdTY4MDdcdTdCN0VcdTUwM0NcIiIpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_default", "exports", "default", "inserted", "el", "binding", "vnode", "value", "super_admin", "roles", "store", "getters", "Array", "length", "roleFlag", "hasRole", "some", "role", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/directive/permission/hasRole.js"], "sourcesContent": [" /**\r\n * v-hasRole 角色权限处理\r\n * Copyright (c) 2019 ruoyi\r\n */\r\n \r\nimport store from '@/store'\r\n\r\nexport default {\r\n  inserted(el, binding, vnode) {\r\n    const { value } = binding\r\n    const super_admin = \"admin\";\r\n    const roles = store.getters && store.getters.roles\r\n\r\n    if (value && value instanceof Array && value.length > 0) {\r\n      const roleFlag = value\r\n\r\n      const hasRole = roles.some(role => {\r\n        return super_admin === role || roleFlag.includes(role)\r\n      })\r\n\r\n      if (!hasRole) {\r\n        el.parentNode && el.parentNode.removeChild(el)\r\n      }\r\n    } else {\r\n      throw new Error(`请设置角色权限标签值\"`)\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAKA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AALC;AACD;AACA;AACA;AAHC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOc;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,WAAW,GAAG,OAAO;IAC3B,IAAMC,KAAK,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,KAAK;IAElD,IAAIF,KAAK,IAAIA,KAAK,YAAYK,KAAK,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,QAAQ,GAAGP,KAAK;MAEtB,IAAMQ,OAAO,GAAGN,KAAK,CAACO,IAAI,CAAC,UAAAC,IAAI,EAAI;QACjC,OAAOT,WAAW,KAAKS,IAAI,IAAIH,QAAQ,CAACI,QAAQ,CAACD,IAAI,CAAC;MACxD,CAAC,CAAC;MAEF,IAAI,CAACF,OAAO,EAAE;QACZX,EAAE,CAACe,UAAU,IAAIf,EAAE,CAACe,UAAU,CAACC,WAAW,CAAChB,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIiB,KAAK,iEAAc,CAAC;IAChC;EACF;AACF,CAAC", "ignoreList": []}]}