{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\dept\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\system\\dept\\index.vue", "mtime": 1750151094292}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_dept", "require", "_vueTreeselect", "_interopRequireDefault", "name", "dicts", "components", "Treeselect", "data", "loading", "showSearch", "deptList", "deptOptions", "title", "open", "isExpandAll", "refreshTable", "queryParams", "deptName", "undefined", "status", "form", "rules", "parentId", "required", "message", "trigger", "orderNum", "email", "type", "phone", "pattern", "created", "getList", "methods", "_this", "listDept", "then", "response", "handleTree", "normalizer", "node", "children", "length", "id", "deptId", "label", "cancel", "reset", "leader", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "_this2", "toggleExpandAll", "_this3", "$nextTick", "handleUpdate", "_this4", "getDept", "listDept<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitForm", "_this5", "$refs", "validate", "valid", "updateDept", "$modal", "msgSuccess", "addDept", "handleDelete", "_this6", "confirm", "delDept", "catch"], "sources": ["src/views/system/dept/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"部门名称\" prop=\"deptName\">\r\n        <el-input\r\n          v-model=\"queryParams.deptName\"\r\n          placeholder=\"请输入部门名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"部门状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:dept:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-sort\"\r\n          size=\"mini\"\r\n          @click=\"toggleExpandAll\"\r\n        >展开/折叠</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-if=\"refreshTable\"\r\n      v-loading=\"loading\"\r\n      :data=\"deptList\"\r\n      row-key=\"deptId\"\r\n      :default-expand-all=\"isExpandAll\"\r\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n    >\r\n      <el-table-column prop=\"deptName\" label=\"部门名称\" width=\"260\"></el-table-column>\r\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"200\"></el-table-column>\r\n      <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:dept:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd(scope.row)\"\r\n            v-hasPermi=\"['system:dept:add']\"\r\n          >新增</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.parentId != 0\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:dept:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 添加或修改部门对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"24\" v-if=\"form.parentId !== 0\">\r\n            <el-form-item label=\"上级部门\" prop=\"parentId\">\r\n              <treeselect v-model=\"form.parentId\" :options=\"deptOptions\" :normalizer=\"normalizer\" placeholder=\"选择上级部门\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门名称\" prop=\"deptName\">\r\n              <el-input v-model=\"form.deptName\" placeholder=\"请输入部门名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人\" prop=\"leader\">\r\n              <el-input v-model=\"form.leader\" placeholder=\"请输入负责人\" maxlength=\"20\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话\" prop=\"phone\">\r\n              <el-input v-model=\"form.phone\" placeholder=\"请输入联系电话\" maxlength=\"11\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮箱\" prop=\"email\">\r\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from \"@/api/system/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Dept\",\r\n  dicts: ['sys_normal_disable'],\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 表格树数据\r\n      deptList: [],\r\n      // 部门树选项\r\n      deptOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部展开\r\n      isExpandAll: true,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        deptName: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parentId: [\r\n          { required: true, message: \"上级部门不能为空\", trigger: \"blur\" }\r\n        ],\r\n        deptName: [\r\n          { required: true, message: \"部门名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        orderNum: [\r\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          {\r\n            type: \"email\",\r\n            message: \"请输入正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phone: [\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询部门列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDept(this.queryParams).then(response => {\r\n        this.deptList = this.handleTree(response.data, \"deptId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 转换部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.deptId,\r\n        label: node.deptName,\r\n        children: node.children\r\n      };\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        deptId: undefined,\r\n        parentId: undefined,\r\n        deptName: undefined,\r\n        orderNum: undefined,\r\n        leader: undefined,\r\n        phone: undefined,\r\n        email: undefined,\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      if (row != undefined) {\r\n        this.form.parentId = row.deptId;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加部门\";\r\n\r\n      // const depts = { deptId: 0, deptName: '主类目', children: [] };\r\n      \r\n      // listDept().then(response => {\r\n      //   // this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n      //   depts.children = this.handleTree(response.data, \"menuId\");\r\n      //   this.deptOptions.push(depts);\r\n      // });\r\n\r\n      listDept().then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n      });\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      getDept(row.deptId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改部门\";\r\n      });\r\n      listDeptExcludeChild(row.deptId).then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.deptId != undefined) {\r\n            updateDept(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDept(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除名称为\"' + row.deptName + '\"的数据项？').then(function() {\r\n        return delDept(row.deptId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;AAgKA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,KAAA,GACA;UACAC,IAAA;UACAJ,OAAA;UACAC,OAAA;QACA,EACA;QACAI,KAAA,GACA;UACAC,OAAA;UACAN,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,cAAA,OAAAnB,WAAA,EAAAoB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,QAAA,GAAAwB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA9B,IAAA;QACA2B,KAAA,CAAA1B,OAAA;MACA;IACA;IACA,eACA+B,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAAvB,QAAA;QACAwB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA;IACAK,MAAA,WAAAA,OAAA;MACA,KAAAjC,IAAA;MACA,KAAAkC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA3B,IAAA;QACAwB,MAAA,EAAA1B,SAAA;QACAI,QAAA,EAAAJ,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAQ,QAAA,EAAAR,SAAA;QACA8B,MAAA,EAAA9B,SAAA;QACAW,KAAA,EAAAX,SAAA;QACAS,KAAA,EAAAT,SAAA;QACAC,MAAA;MACA;MACA,KAAA8B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlB,OAAA;IACA;IACA,aACAmB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAP,KAAA;MACA,IAAAM,GAAA,IAAAnC,SAAA;QACA,KAAAE,IAAA,CAAAE,QAAA,GAAA+B,GAAA,CAAAT,MAAA;MACA;MACA,KAAA/B,IAAA;MACA,KAAAD,KAAA;;MAEA;;MAEA;MACA;MACA;MACA;MACA;;MAEA,IAAAuB,cAAA,IAAAC,IAAA,WAAAC,QAAA;QACAiB,MAAA,CAAA3C,WAAA,GAAA2C,MAAA,CAAAhB,UAAA,CAAAD,QAAA,CAAA9B,IAAA;MACA;IACA;IACA,cACAgD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAzC,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAA2C,SAAA;QACAD,MAAA,CAAAzC,YAAA;MACA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,KAAAZ,KAAA;MACA,IAAAa,aAAA,EAAAP,GAAA,CAAAT,MAAA,EAAAR,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAAvC,IAAA,GAAAiB,QAAA,CAAA9B,IAAA;QACAoD,MAAA,CAAA9C,IAAA;QACA8C,MAAA,CAAA/C,KAAA;MACA;MACA,IAAAiD,0BAAA,EAAAR,GAAA,CAAAT,MAAA,EAAAR,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAAhD,WAAA,GAAAgD,MAAA,CAAArB,UAAA,CAAAD,QAAA,CAAA9B,IAAA;MACA;IACA;IACA;IACAuD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA3C,IAAA,CAAAwB,MAAA,IAAA1B,SAAA;YACA,IAAAiD,gBAAA,EAAAJ,MAAA,CAAA3C,IAAA,EAAAgB,IAAA,WAAAC,QAAA;cACA0B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAlD,IAAA;cACAkD,MAAA,CAAA/B,OAAA;YACA;UACA;YACA,IAAAsC,aAAA,EAAAP,MAAA,CAAA3C,IAAA,EAAAgB,IAAA,WAAAC,QAAA;cACA0B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAlD,IAAA;cACAkD,MAAA,CAAA/B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAuC,YAAA,WAAAA,aAAAlB,GAAA;MAAA,IAAAmB,MAAA;MACA,KAAAJ,MAAA,CAAAK,OAAA,gBAAApB,GAAA,CAAApC,QAAA,aAAAmB,IAAA;QACA,WAAAsC,aAAA,EAAArB,GAAA,CAAAT,MAAA;MACA,GAAAR,IAAA;QACAoC,MAAA,CAAAxC,OAAA;QACAwC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;EACA;AACA", "ignoreList": []}]}