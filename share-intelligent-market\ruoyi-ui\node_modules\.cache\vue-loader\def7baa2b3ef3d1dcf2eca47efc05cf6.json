{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\components\\orderDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\order\\components\\orderDetails.vue", "mtime": 1750151094267}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBnZXREYXRhLA0KICBwYXlEYXRhLA0KICBkZWxpdmVyRGF0YSwNCiAgY29uZmlybVBheSwNCiAgY29uZmlybURlbGl2ZXIsDQp9IGZyb20gIkAvYXBpL29yZGVyL2xpc3QiOw0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBidG5sb2FkOiBmYWxzZSwNCiAgICAgIGZvcm06IHt9LA0KICAgICAgZGZvcm06IHsNCiAgICAgICAgbG9naXN0aWNzX25vOiAiIiwNCiAgICAgICAgbGlua2VyOiAiIiwNCiAgICAgICAgcHJvb2Y6ICIiLA0KICAgICAgICBsaW5rcGhvbmU6ICIiLA0KICAgICAgfSwNCiAgICAgIHBheXM6IFtdLA0KICAgICAgYWN0aXZldGFiOiAiaXRlbXMiLA0KICAgICAgZGVsaXZlcnM6IFtdLA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkZWxpdmVyT3BlbjogZmFsc2UsDQogICAgICBydWxlczogew0KICAgICAgICBzaGlwcGVkX3F0eTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuWPkei0p+aVsOmHj+S4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbG9naXN0aWNzX25vOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi54mp5paZ5Y+35LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkge30sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRMb2dvKGUpIHsNCiAgICAgIHRoaXMuZGZvcm0ucHJvb2YgPSBlOw0KICAgICAgaWYgKHRoaXMuZGZvcm0ucHJvb2YpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5kZm9ybS5jbGVhclZhbGlkYXRlKCJwcm9vZiIpOw0KICAgICAgfQ0KICAgIH0sDQogICAgb3BlbihvcmRlcklkKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgZ2V0RGF0YShvcmRlcklkKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzLmRhdGE7DQogICAgICB9KTsNCiAgICAgIHBheURhdGEoew0KICAgICAgICBvcmRlcl9pZDogb3JkZXJJZCwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnBheXMgPSByZXMuZGF0YTsNCiAgICAgIH0pOw0KICAgICAgZGVsaXZlckRhdGEoew0KICAgICAgICBvcmRlcl9pZDogb3JkZXJJZCwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmRlbGl2ZXJzID0gcmVzLmRhdGE7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVBheShyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOivpeS7mOasvueUs+ivt+WNlT8iLCAi5o+Q56S6Iiwgew0KICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgbGV0IGRhdGEgPSB7DQogICAgICAgICAgaWQ6IHJvdy5pZCwNCiAgICAgICAgfTsNCiAgICAgICAgY29uZmlybVBheShkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8hIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICByb3cucGF5X3N0YXR1cyA9ICJDT05GSVJNIjsNCiAgICAgICAgICByb3cucGF5X3N0YXR1c1N0ciA9ICLlt7Lnoa7orqQiOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlRGVsaXZlcihyb3cpIHsNCiAgICAgIHRoaXMuZGZvcm0ub3JkZXJfaXRlbV9pZCA9IHJvdy5pZDsNCiAgICAgIHRoaXMuZGZvcm0ub3JkZXJfaWQgPSByb3cub3JkZXJfaWQ7DQogICAgICB0aGlzLmRmb3JtLmxlZnRfcXR5ID0gcm93LnF1YW50aXR5IC0gcm93LnNoaXBlZF9xdWFudGl0eTsNCiAgICAgIHRoaXMuZGZvcm0uc2hpcHBlZF9xdHkgPSByb3cucXVhbnRpdHkgLSByb3cuc2hpcGVkX3F1YW50aXR5Ow0KICAgICAgdGhpcy5kZm9ybS5sb2dpc3RpY3Nfbm8gPSAiIjsNCiAgICAgIHRoaXMuZGZvcm0ubGlua2VyID0gIiI7DQogICAgICB0aGlzLmRmb3JtLmxpbmtwaG9uZSA9ICIiOw0KICAgICAgdGhpcy5kZWxpdmVyT3BlbiA9IHRydWU7DQogICAgfSwNCiAgICBvcERlbGl2ZXIoKSB7DQogICAgICB0aGlzLiRyZWZzLmRmb3JtLnZhbGlkYXRlKCh2YWxpZGF0ZSkgPT4gew0KICAgICAgICBpZiAodmFsaWRhdGUpIHsNCiAgICAgICAgICBpZiAodGhpcy5kZm9ybS5zaGlwcGVkX3F0eSA+IHRoaXMuZGZvcm0ubGVmdF9xdHkpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICAgICAgICBtZXNzYWdlOiAi5Y+R6LSn5pWw6YeP5LiN5b6X5aSa5L2Z5oC75pWw6YePISIsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5idG5sb2FkID0gdHJ1ZTsNCiAgICAgICAgICBjb25maXJtRGVsaXZlcih0aGlzLmRmb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfIiwNCiAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB0aGlzLmJ0bmxvYWQgPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuZGVsaXZlck9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMub3Blbih0aGlzLmRmb3JtLm9yZGVyX2lkKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+35a6M5ZaE5L+h5oGv5YaN5o+Q5LqkISIpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["orderDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4jBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "orderDetails.vue", "sourceRoot": "src/views/order/components", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-dialog\r\n      width=\"80%\"\r\n      title=\"订单详情\"\r\n      :visible.sync=\"dialogVisible\"\r\n      append-to-body\r\n      center\r\n    >\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        title=\"基本信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"订单类型\">{{\r\n          form.order_type_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单状态\">\r\n          <el-tag size=\"mini\" type=\"primary\">{{ form.status_str }}</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"订单号\">{{\r\n          form.order_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"下单时间\">{{\r\n          form.create_time\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"付款方式\">{{\r\n          form.payment_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采状态\">{{\r\n          form.central_status_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采付款状态\">{{\r\n          form.central_pay_status_str\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"操作员\">{{\r\n          form.operator\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单总价\">{{\r\n          form.total_price\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"集采定金\">{{\r\n          form.deposit\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"物流单号\">{{\r\n          form.logistics_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"合同编号\">{{\r\n          form.contract_no\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"地址\">{{\r\n          form.address\r\n        }}</el-descriptions-item>\r\n        <!-- <el-descriptions-item label=\"订单条款\">{{\r\n                    form.terms\r\n                }}</el-descriptions-item> -->\r\n        <el-descriptions-item label=\"订单备注\">{{\r\n          form.remark\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        style=\"margin-top: 20px\"\r\n        title=\"需方信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"需方名称\">{{\r\n          form.demand_name\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方代理\">{{\r\n          form.demand_proxy\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方电话\">{{\r\n          form.demand_phone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"需方地址\">{{\r\n          form.demand_location\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-descriptions\r\n        class=\"margin-top\"\r\n        style=\"margin-top: 20px\"\r\n        title=\"供方信息\"\r\n        :column=\"3\"\r\n        direction=\"horizontal\"\r\n        border\r\n      >\r\n        <el-descriptions-item label=\"供方名称\">{{\r\n          form.supply_name\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方代理\">{{\r\n          form.supply_proxy\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方电话\">{{\r\n          form.supply_phone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供方地址\">{{\r\n          form.supply_location\r\n        }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-tabs v-model=\"activetab\" type=\"card\" style=\"margin-top: 20px\">\r\n        <el-tab-pane label=\"订单明细\" name=\"items\">\r\n          <el-table :data=\"form.items\">\r\n            <el-table-column label=\"物料分类\" align=\"center\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.classify_name }}-{{ scope.row.classify2_name }}-{{\r\n                  scope.row.classify3_name\r\n                }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料编号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_name\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"规格\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"specs\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"单位\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"unit\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物品数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"已发数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"shiped_quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"品牌\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"brand\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"包装\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pack\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"税率\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"tax_rate\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"商品总价\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"total_price\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"运费\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"freight\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"明细总价\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"total_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"期望交货日期\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"delivery_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"交货日期\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"delivered_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              fixed=\"right\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.shiped_quantity < scope.row.quantity\"\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleDeliver(scope.row)\"\r\n                  >确认发货</el-button\r\n                >\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"付款申请单\" name=\"pay\">\r\n          <el-table :data=\"pays\">\r\n            <el-table-column\r\n              label=\"申请单号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"request_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"申请时间\"\r\n              align=\"center\"\r\n              width=\"160\"\r\n              prop=\"create_time\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"申请金额\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"total_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付方式\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"paymentStr\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"币种\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"currency\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付比例\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_ratio\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付金额\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_amount\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付状态\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pay_statusStr\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付时间\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"pay_time\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"支付凭证\"\r\n              align=\"center\"\r\n              width=\"120\"\r\n              prop=\"pay_proof\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-image\r\n                  v-if=\"scope.row.pay_proof\"\r\n                  style=\"width: 100px; height: 100px\"\r\n                  :src=\"scope.row.pay_proof\"\r\n                  :preview-src-list=\"[scope.row.pay_proof]\"\r\n                >\r\n                </el-image>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              align=\"center\"\r\n              width=\"80\"\r\n              fixed=\"right\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"\r\n                    scope.row.pay_status == 'WAIT' &&\r\n                    (scope.row.payment == 'OFFLINE' ||\r\n                      scope.row.payment == 'PERIOD')\r\n                  \"\r\n                  type=\"text\"\r\n                  size=\"mini\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handlePay(scope.row)\"\r\n                  >确认</el-button\r\n                >\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"发货记录\" name=\"deliver\">\r\n          <el-table :data=\"delivers\">\r\n            <el-table-column\r\n              label=\"物流单号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"logistics_no\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"车牌号\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"carnno\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"司机名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"linker\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"司机电话\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"linkphone\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              align=\"center\"\r\n              property=\"proof\"\r\n              label=\"发货凭证\"\r\n              width=\"100px\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"scope.row.proof\">\r\n                  <el-image\r\n                    style=\"width: 60px; height: 60px\"\r\n                    :src=\"scope.row.proof\"\r\n                    :preview-src-list=\"[scope.row.proof]\"\r\n                  >\r\n                  </el-image>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              align=\"center\"\r\n              property=\"proof\"\r\n              label=\"签收凭证\"\r\n              width=\"100px\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"scope.row.sproof\">\r\n                  <el-image\r\n                    style=\"width: 60px; height: 60px\"\r\n                    :src=\"scope.row.sproof\"\r\n                    :preview-src-list=\"[scope.row.sproof]\"\r\n                  >\r\n                  </el-image>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"物料名称\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"product_name\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"单位\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"unit\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"规格\"\r\n              align=\"center\"\r\n              width=\"150\"\r\n              prop=\"specs\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"quantity\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"品牌\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"brand\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"包装\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"pack\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"期望交货日期\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"delivery_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"交货日期\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"delivered_date\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"发货数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"shipped_qty\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收数量\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_qty\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收人\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_by\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"签收时间\"\r\n              align=\"center\"\r\n              width=\"100\"\r\n              prop=\"signed_time\"\r\n            >\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n    <el-dialog\r\n      width=\"40%\"\r\n      title=\"发货\"\r\n      :visible.sync=\"deliverOpen\"\r\n      append-to-body\r\n      center\r\n    >\r\n      <el-form\r\n        ref=\"dform\"\r\n        :rules=\"rules\"\r\n        :model=\"dform\"\r\n        label-width=\"110px\"\r\n        label-position=\"left\"\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"发货数量\" prop=\"shipped_qty\">\r\n              <el-input\r\n                type=\"number\"\r\n                v-model=\"dform.shipped_qty\"\r\n                placeholder=\"请输入发货数量\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物流号\" prop=\"logistics_no\">\r\n              <el-input\r\n                clearable\r\n                v-model=\"dform.logistics_no\"\r\n                placeholder=\"请输入物流号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"司机名称\" prop=\"linker\">\r\n              <el-input\r\n                v-model=\"dform.linker\"\r\n                placeholder=\"请输入司机名称\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"司机电话\" prop=\"linkphone\">\r\n              <el-input\r\n                clearable\r\n                v-model=\"dform.linkphone\"\r\n                placeholder=\"请输入物流号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"车牌号\" prop=\"carno\">\r\n              <el-input\r\n                v-model=\"dform.carno\"\r\n                placeholder=\"请输入车牌号\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"发货凭证\" prop=\"proof\">\r\n              <ImageUpload\r\n                v-model=\"dform.proof\"\r\n                @input=\"getLogo\"\r\n                :limit=\"1\"\r\n                :isShowTip=\"false\"\r\n              ></ImageUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"deliverOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"opDeliver\" :loading=\"btnload\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getData,\r\n  payData,\r\n  deliverData,\r\n  confirmPay,\r\n  confirmDeliver,\r\n} from \"@/api/order/list\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      btnload: false,\r\n      form: {},\r\n      dform: {\r\n        logistics_no: \"\",\r\n        linker: \"\",\r\n        proof: \"\",\r\n        linkphone: \"\",\r\n      },\r\n      pays: [],\r\n      activetab: \"items\",\r\n      delivers: [],\r\n      dialogVisible: false,\r\n      deliverOpen: false,\r\n      rules: {\r\n        shipped_qty: [\r\n          {\r\n            required: true,\r\n            message: \"发货数量不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        logistics_no: [\r\n          {\r\n            required: true,\r\n            message: \"物料号不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {},\r\n  methods: {\r\n    getLogo(e) {\r\n      this.dform.proof = e;\r\n      if (this.dform.proof) {\r\n        this.$refs.dform.clearValidate(\"proof\");\r\n      }\r\n    },\r\n    open(orderId) {\r\n      this.dialogVisible = true;\r\n      getData(orderId).then((res) => {\r\n        this.form = res.data;\r\n      });\r\n      payData({\r\n        order_id: orderId,\r\n      }).then((res) => {\r\n        this.pays = res.data;\r\n      });\r\n      deliverData({\r\n        order_id: orderId,\r\n      }).then((res) => {\r\n        this.delivers = res.data;\r\n      });\r\n    },\r\n    handlePay(row) {\r\n      this.$confirm(\"是否确认该付款申请单?\", \"提示\", {\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        let data = {\r\n          id: row.id,\r\n        };\r\n        confirmPay(data).then((res) => {\r\n          this.$message({\r\n            type: \"success\",\r\n            message: \"操作成功!\",\r\n          });\r\n          row.pay_status = \"CONFIRM\";\r\n          row.pay_statusStr = \"已确认\";\r\n        });\r\n      });\r\n    },\r\n    handleDeliver(row) {\r\n      this.dform.order_item_id = row.id;\r\n      this.dform.order_id = row.order_id;\r\n      this.dform.left_qty = row.quantity - row.shiped_quantity;\r\n      this.dform.shipped_qty = row.quantity - row.shiped_quantity;\r\n      this.dform.logistics_no = \"\";\r\n      this.dform.linker = \"\";\r\n      this.dform.linkphone = \"\";\r\n      this.deliverOpen = true;\r\n    },\r\n    opDeliver() {\r\n      this.$refs.dform.validate((validate) => {\r\n        if (validate) {\r\n          if (this.dform.shipped_qty > this.dform.left_qty) {\r\n            this.$message({\r\n              type: \"error\",\r\n              message: \"发货数量不得多余总数量!\",\r\n            });\r\n            return;\r\n          }\r\n          this.btnload = true;\r\n          confirmDeliver(this.dform).then(() => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n            this.btnload = false;\r\n            this.deliverOpen = false;\r\n            this.open(this.dform.order_id);\r\n          });\r\n        } else {\r\n          this.$modal.msgError(\"请完善信息再提交!\");\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.item-form {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.wire {\r\n  background: #ccc;\r\n  height: 1px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.red {\r\n  color: red;\r\n}\r\n</style>\r\n"]}]}