{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\offer.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\project\\offer\\offer.vue", "mtime": 1750151094273}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["offer.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "offer.vue", "sourceRoot": "src/views/project/offer", "sourcesContent": ["<template>\r\n  <!-- 报价单管理 -->\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" size='small' v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"\">\r\n            <el-cascader \r\n              filterable \r\n              clearable \r\n              placeholder=\"选择产品分类\" \r\n              v-model=\"classify\" \r\n              :options=\"classifyOptions\"\r\n              :props='{label: \"name\", value: \"id\",checkStrictly: true}'></el-cascader>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"inquiry_no\">\r\n            <el-input clearable v-model=\"queryParams.inquiry_no\" placeholder=\"输入询价单号\" style=\"width: 200px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"offer_no\">\r\n            <el-input clearable v-model=\"queryParams.offer_no\" placeholder=\"输入报价单号\" style=\"width: 200px\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"enterprise_name\">\r\n            <el-input \r\n              clearable \r\n              v-model=\"queryParams.enterprise_name\" \r\n              placeholder=\"输入报价公司\" :maxlength='50'\r\n              style=\"width: 300px\">\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" height=\"500\" :data=\"list\">\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"50\" />\r\n          <el-table-column label=\"报价日期\" align=\"center\" width=\"120\" prop=\"offer_date\" />\r\n          <el-table-column label=\"报价单编号\" align=\"center\" width=\"140\" prop=\"offer_no\" />\r\n          <el-table-column label=\"报价公司\" align=\"center\" width=\"280\" prop=\"enterprise_name\" />\r\n          <el-table-column label=\"联系人\" align=\"center\" width=\"120\" prop=\"linker\" />\r\n          <el-table-column label=\"联系电话\" align=\"center\" width=\"120\" prop=\"linkphone\" />\r\n          <el-table-column label=\"操作者\" align=\"center\" width=\"120\" prop=\"operator\" />\r\n          <el-table-column label=\"报价信息\" align=\"center\" prop=\"offer_version\">\r\n            <template slot-scope=\"scope\">\r\n              {{scope.row.offer_version}}次报价\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"询价截止日期\" width=\"120\" align=\"center\" prop=\"deadline\" />\r\n          <el-table-column label=\"询价标题\" width=\"280\" align=\"center\" prop=\"inquiry_title\" />\r\n          <el-table-column label=\"询价公司\" width=\"280\" align=\"center\" prop=\"inquiry_enterprise_name\" />\r\n          <el-table-column label=\"询价类型\" width=\"120\" align=\"center\" prop=\"typeStr\" />\r\n          <el-table-column label=\"询价状态\" width=\"120\" align=\"center\" prop=\"status\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag size=\"mini\" type='warning'>{{scope.row.statusStr}}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\" size=\"mini\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 急速报价详情 -->\r\n    <lessDetails ref=\"lessDetails\"></lessDetails>\r\n    <!-- 精准报价详情 -->\r\n    <moreDetails ref=\"moreDetails\"></moreDetails>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    listClassify\r\n  } from '@/api/tool/util';\r\n  import {\r\n    listData\r\n  } from '@/api/project/offer';\r\n  import lessDetails from \"./components/lessDetails.vue\";\r\n  import moreDetails from \"./components/moreDetails.vue\";\r\n  export default {\r\n    components: {\r\n      lessDetails,\r\n      moreDetails,\r\n    },\r\n    data() {\r\n      return {\r\n        classifyOptions: [],\r\n        classify:{},\r\n        // 遮罩层\r\n        loading: false,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表单参数\r\n        form: {},\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          inquiry_no: undefined,\r\n          offer_no: undefined,\r\n          enterprise_name: undefined\r\n        },\r\n        // 表格数据\r\n        list: [],\r\n      };\r\n    },\r\n    created() {\r\n      this.getClassify()\r\n      this.getList()\r\n    },\r\n    methods: {\r\n      handleDetail(row) {\r\n        if(row.type=='NORMAL'){\r\n          this.$refs.lessDetails.open(row.id)\r\n        }\r\n        else{\r\n          this.$refs.moreDetails.open(row.id)\r\n        }\r\n      },\r\n      getClassify() {\r\n        listClassify().then(res => {\r\n          this.classifyOptions = res.data;\r\n        })\r\n      },\r\n      /** 查询企业信息列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        if (this.classify) {\r\n          this.queryParams.classify_id = this.classify[0]\r\n          this.queryParams.classify2_id = this.classify.length > 1 ? this.classify[1] : -1\r\n          this.queryParams.classify3_id = this.classify.length > 2 ? this.classify[2] : -1\r\n        } else {\r\n          this.queryParams.classify_id = -1\r\n          this.queryParams.classify2_id = -1\r\n          this.queryParams.classify3_id = -1\r\n        }\r\n        listData(this.queryParams).then((response) => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n    },\r\n  };\r\n</script>\r\n\r\n<style scoped>\r\n</style>\r\n"]}]}