{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\tool\\build\\index.vue", "mtime": 1750151094310}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgZHJhZ2dhYmxlIGZyb20gJ3Z1ZWRyYWdnYWJsZScNCmltcG9ydCBiZWF1dGlmaWVyIGZyb20gJ2pzLWJlYXV0aWZ5Jw0KaW1wb3J0IENsaXBib2FyZEpTIGZyb20gJ2NsaXBib2FyZCcNCmltcG9ydCByZW5kZXIgZnJvbSAnQC91dGlscy9nZW5lcmF0b3IvcmVuZGVyJw0KaW1wb3J0IFJpZ2h0UGFuZWwgZnJvbSAnLi9SaWdodFBhbmVsJw0KaW1wb3J0IHsgaW5wdXRDb21wb25lbnRzLCBzZWxlY3RDb21wb25lbnRzLCBsYXlvdXRDb21wb25lbnRzLCBmb3JtQ29uZiB9IGZyb20gJ0AvdXRpbHMvZ2VuZXJhdG9yL2NvbmZpZycNCmltcG9ydCB7IGJlYXV0aWZpZXJDb25mLCB0aXRsZUNhc2UgfSBmcm9tICdAL3V0aWxzL2luZGV4Jw0KaW1wb3J0IHsgbWFrZVVwSHRtbCwgdnVlVGVtcGxhdGUsIHZ1ZVNjcmlwdCwgY3NzU3R5bGUgfSBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9odG1sJw0KaW1wb3J0IHsgbWFrZVVwSnMgfSBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9qcycNCmltcG9ydCB7IG1ha2VVcENzcyB9IGZyb20gJ0AvdXRpbHMvZ2VuZXJhdG9yL2NzcycNCmltcG9ydCBkcmF3aW5nRGVmYXVsdCBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9kcmF3aW5nRGVmYXVsdCcNCmltcG9ydCBsb2dvIGZyb20gJ0AvYXNzZXRzL2xvZ28vbG9nby5wbmcnDQppbXBvcnQgQ29kZVR5cGVEaWFsb2cgZnJvbSAnLi9Db2RlVHlwZURpYWxvZycNCmltcG9ydCBEcmFnZ2FibGVJdGVtIGZyb20gJy4vRHJhZ2dhYmxlSXRlbScNCg0KbGV0IG9sZEFjdGl2ZUlkDQpsZXQgdGVtcEFjdGl2ZURhdGENCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7DQogICAgZHJhZ2dhYmxlLA0KICAgIHJlbmRlciwNCiAgICBSaWdodFBhbmVsLA0KICAgIENvZGVUeXBlRGlhbG9nLA0KICAgIERyYWdnYWJsZUl0ZW0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9nbywNCiAgICAgIGlkR2xvYmFsOiAxMDAsDQogICAgICBmb3JtQ29uZiwNCiAgICAgIGlucHV0Q29tcG9uZW50cywNCiAgICAgIHNlbGVjdENvbXBvbmVudHMsDQogICAgICBsYXlvdXRDb21wb25lbnRzLA0KICAgICAgbGFiZWxXaWR0aDogMTAwLA0KICAgICAgZHJhd2luZ0xpc3Q6IGRyYXdpbmdEZWZhdWx0LA0KICAgICAgZHJhd2luZ0RhdGE6IHt9LA0KICAgICAgYWN0aXZlSWQ6IGRyYXdpbmdEZWZhdWx0WzBdLmZvcm1JZCwNCiAgICAgIGRyYXdlclZpc2libGU6IGZhbHNlLA0KICAgICAgZm9ybURhdGE6IHt9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBnZW5lcmF0ZUNvbmY6IG51bGwsDQogICAgICBzaG93RmlsZU5hbWU6IGZhbHNlLA0KICAgICAgYWN0aXZlRGF0YTogZHJhd2luZ0RlZmF1bHRbMF0NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8g6Ziy5q2iIGZpcmVmb3gg5LiLIOaLluaLvSDkvJrmlrDmiZPljaHkuIDkuKrpgInpobnljaENCiAgICBkb2N1bWVudC5ib2R5Lm9uZHJvcCA9IGV2ZW50ID0+IHsNCiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCkNCiAgICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBmdW5jLW5hbWVzDQogICAgJ2FjdGl2ZURhdGEubGFiZWwnOiBmdW5jdGlvbiAodmFsLCBvbGRWYWwpIHsNCiAgICAgIGlmICgNCiAgICAgICAgdGhpcy5hY3RpdmVEYXRhLnBsYWNlaG9sZGVyID09PSB1bmRlZmluZWQNCiAgICAgICAgfHwgIXRoaXMuYWN0aXZlRGF0YS50YWcNCiAgICAgICAgfHwgb2xkQWN0aXZlSWQgIT09IHRoaXMuYWN0aXZlSWQNCiAgICAgICkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuYWN0aXZlRGF0YS5wbGFjZWhvbGRlciA9IHRoaXMuYWN0aXZlRGF0YS5wbGFjZWhvbGRlci5yZXBsYWNlKG9sZFZhbCwgJycpICsgdmFsDQogICAgfSwNCiAgICBhY3RpdmVJZDogew0KICAgICAgaGFuZGxlcih2YWwpIHsNCiAgICAgICAgb2xkQWN0aXZlSWQgPSB2YWwNCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgY29uc3QgY2xpcGJvYXJkID0gbmV3IENsaXBib2FyZEpTKCcjY29weU5vZGUnLCB7DQogICAgICB0ZXh0OiB0cmlnZ2VyID0+IHsNCiAgICAgICAgY29uc3QgY29kZVN0ciA9IHRoaXMuZ2VuZXJhdGVDb2RlKCkNCiAgICAgICAgdGhpcy4kbm90aWZ5KHsNCiAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsDQogICAgICAgICAgbWVzc2FnZTogJ+S7o+eggeW3suWkjeWItuWIsOWJquWIh+adv++8jOWPr+eymOi0tOOAgicsDQogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybiBjb2RlU3RyDQogICAgICB9DQogICAgfSkNCiAgICBjbGlwYm9hcmQub24oJ2Vycm9yJywgZSA9PiB7DQogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfku6PnoIHlpI3liLblpLHotKUnKQ0KICAgIH0pDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhY3RpdmVGb3JtSXRlbShlbGVtZW50KSB7DQogICAgICB0aGlzLmFjdGl2ZURhdGEgPSBlbGVtZW50DQogICAgICB0aGlzLmFjdGl2ZUlkID0gZWxlbWVudC5mb3JtSWQNCiAgICB9LA0KICAgIG9uRW5kKG9iaiwgYSkgew0KICAgICAgaWYgKG9iai5mcm9tICE9PSBvYmoudG8pIHsNCiAgICAgICAgdGhpcy5hY3RpdmVEYXRhID0gdGVtcEFjdGl2ZURhdGENCiAgICAgICAgdGhpcy5hY3RpdmVJZCA9IHRoaXMuaWRHbG9iYWwNCiAgICAgIH0NCiAgICB9LA0KICAgIGFkZENvbXBvbmVudChpdGVtKSB7DQogICAgICBjb25zdCBjbG9uZSA9IHRoaXMuY2xvbmVDb21wb25lbnQoaXRlbSkNCiAgICAgIHRoaXMuZHJhd2luZ0xpc3QucHVzaChjbG9uZSkNCiAgICAgIHRoaXMuYWN0aXZlRm9ybUl0ZW0oY2xvbmUpDQogICAgfSwNCiAgICBjbG9uZUNvbXBvbmVudChvcmlnaW4pIHsNCiAgICAgIGNvbnN0IGNsb25lID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShvcmlnaW4pKQ0KICAgICAgY2xvbmUuZm9ybUlkID0gKyt0aGlzLmlkR2xvYmFsDQogICAgICBjbG9uZS5zcGFuID0gZm9ybUNvbmYuc3Bhbg0KICAgICAgY2xvbmUucmVuZGVyS2V5ID0gK25ldyBEYXRlKCkgLy8g5pS55Y+YcmVuZGVyS2V55ZCO5Y+v5Lul5a6e546w5by65Yi25pu05paw57uE5Lu2DQogICAgICBpZiAoIWNsb25lLmxheW91dCkgY2xvbmUubGF5b3V0ID0gJ2NvbEZvcm1JdGVtJw0KICAgICAgaWYgKGNsb25lLmxheW91dCA9PT0gJ2NvbEZvcm1JdGVtJykgew0KICAgICAgICBjbG9uZS52TW9kZWwgPSBgZmllbGQke3RoaXMuaWRHbG9iYWx9YA0KICAgICAgICBjbG9uZS5wbGFjZWhvbGRlciAhPT0gdW5kZWZpbmVkICYmIChjbG9uZS5wbGFjZWhvbGRlciArPSBjbG9uZS5sYWJlbCkNCiAgICAgICAgdGVtcEFjdGl2ZURhdGEgPSBjbG9uZQ0KICAgICAgfSBlbHNlIGlmIChjbG9uZS5sYXlvdXQgPT09ICdyb3dGb3JtSXRlbScpIHsNCiAgICAgICAgZGVsZXRlIGNsb25lLmxhYmVsDQogICAgICAgIGNsb25lLmNvbXBvbmVudE5hbWUgPSBgcm93JHt0aGlzLmlkR2xvYmFsfWANCiAgICAgICAgY2xvbmUuZ3V0dGVyID0gdGhpcy5mb3JtQ29uZi5ndXR0ZXINCiAgICAgICAgdGVtcEFjdGl2ZURhdGEgPSBjbG9uZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRlbXBBY3RpdmVEYXRhDQogICAgfSwNCiAgICBBc3NlbWJsZUZvcm1EYXRhKCkgew0KICAgICAgdGhpcy5mb3JtRGF0YSA9IHsNCiAgICAgICAgZmllbGRzOiBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuZHJhd2luZ0xpc3QpKSwNCiAgICAgICAgLi4udGhpcy5mb3JtQ29uZg0KICAgICAgfQ0KICAgIH0sDQogICAgZ2VuZXJhdGUoZGF0YSkgew0KICAgICAgY29uc3QgZnVuYyA9IHRoaXNbYGV4ZWMke3RpdGxlQ2FzZSh0aGlzLm9wZXJhdGlvblR5cGUpfWBdDQogICAgICB0aGlzLmdlbmVyYXRlQ29uZiA9IGRhdGENCiAgICAgIGZ1bmMgJiYgZnVuYyhkYXRhKQ0KICAgIH0sDQogICAgZXhlY1J1bihkYXRhKSB7DQogICAgICB0aGlzLkFzc2VtYmxlRm9ybURhdGEoKQ0KICAgICAgdGhpcy5kcmF3ZXJWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgZXhlY0Rvd25sb2FkKGRhdGEpIHsNCiAgICAgIGNvbnN0IGNvZGVTdHIgPSB0aGlzLmdlbmVyYXRlQ29kZSgpDQogICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NvZGVTdHJdLCB7IHR5cGU6ICd0ZXh0L3BsYWluO2NoYXJzZXQ9dXRmLTgnIH0pDQogICAgICB0aGlzLiRkb3dubG9hZC5zYXZlQXMoYmxvYiwgZGF0YS5maWxlTmFtZSkNCiAgICB9LA0KICAgIGV4ZWNDb3B5KGRhdGEpIHsNCiAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjb3B5Tm9kZScpLmNsaWNrKCkNCiAgICB9LA0KICAgIGVtcHR5KCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5riF56m65omA5pyJ57uE5Lu25ZCX77yfJywgJ+aPkOekuicsIHsgdHlwZTogJ3dhcm5pbmcnIH0pLnRoZW4oDQogICAgICAgICgpID0+IHsNCiAgICAgICAgICB0aGlzLmRyYXdpbmdMaXN0ID0gW10NCiAgICAgICAgfQ0KICAgICAgKQ0KICAgIH0sDQogICAgZHJhd2luZ0l0ZW1Db3B5KGl0ZW0sIHBhcmVudCkgew0KICAgICAgbGV0IGNsb25lID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShpdGVtKSkNCiAgICAgIGNsb25lID0gdGhpcy5jcmVhdGVJZEFuZEtleShjbG9uZSkNCiAgICAgIHBhcmVudC5wdXNoKGNsb25lKQ0KICAgICAgdGhpcy5hY3RpdmVGb3JtSXRlbShjbG9uZSkNCiAgICB9LA0KICAgIGNyZWF0ZUlkQW5kS2V5KGl0ZW0pIHsNCiAgICAgIGl0ZW0uZm9ybUlkID0gKyt0aGlzLmlkR2xvYmFsDQogICAgICBpdGVtLnJlbmRlcktleSA9ICtuZXcgRGF0ZSgpDQogICAgICBpZiAoaXRlbS5sYXlvdXQgPT09ICdjb2xGb3JtSXRlbScpIHsNCiAgICAgICAgaXRlbS52TW9kZWwgPSBgZmllbGQke3RoaXMuaWRHbG9iYWx9YA0KICAgICAgfSBlbHNlIGlmIChpdGVtLmxheW91dCA9PT0gJ3Jvd0Zvcm1JdGVtJykgew0KICAgICAgICBpdGVtLmNvbXBvbmVudE5hbWUgPSBgcm93JHt0aGlzLmlkR2xvYmFsfWANCiAgICAgIH0NCiAgICAgIGlmIChBcnJheS5pc0FycmF5KGl0ZW0uY2hpbGRyZW4pKSB7DQogICAgICAgIGl0ZW0uY2hpbGRyZW4gPSBpdGVtLmNoaWxkcmVuLm1hcChjaGlsZEl0ZW0gPT4gdGhpcy5jcmVhdGVJZEFuZEtleShjaGlsZEl0ZW0pKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGl0ZW0NCiAgICB9LA0KICAgIGRyYXdpbmdJdGVtRGVsZXRlKGluZGV4LCBwYXJlbnQpIHsNCiAgICAgIHBhcmVudC5zcGxpY2UoaW5kZXgsIDEpDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IGxlbiA9IHRoaXMuZHJhd2luZ0xpc3QubGVuZ3RoDQogICAgICAgIGlmIChsZW4pIHsNCiAgICAgICAgICB0aGlzLmFjdGl2ZUZvcm1JdGVtKHRoaXMuZHJhd2luZ0xpc3RbbGVuIC0gMV0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBnZW5lcmF0ZUNvZGUoKSB7DQogICAgICBjb25zdCB7IHR5cGUgfSA9IHRoaXMuZ2VuZXJhdGVDb25mDQogICAgICB0aGlzLkFzc2VtYmxlRm9ybURhdGEoKQ0KICAgICAgY29uc3Qgc2NyaXB0ID0gdnVlU2NyaXB0KG1ha2VVcEpzKHRoaXMuZm9ybURhdGEsIHR5cGUpKQ0KICAgICAgY29uc3QgaHRtbCA9IHZ1ZVRlbXBsYXRlKG1ha2VVcEh0bWwodGhpcy5mb3JtRGF0YSwgdHlwZSkpDQogICAgICBjb25zdCBjc3MgPSBjc3NTdHlsZShtYWtlVXBDc3ModGhpcy5mb3JtRGF0YSkpDQogICAgICByZXR1cm4gYmVhdXRpZmllci5odG1sKGh0bWwgKyBzY3JpcHQgKyBjc3MsIGJlYXV0aWZpZXJDb25mLmh0bWwpDQogICAgfSwNCiAgICBkb3dubG9hZCgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuc2hvd0ZpbGVOYW1lID0gdHJ1ZQ0KICAgICAgdGhpcy5vcGVyYXRpb25UeXBlID0gJ2Rvd25sb2FkJw0KICAgIH0sDQogICAgcnVuKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy5zaG93RmlsZU5hbWUgPSBmYWxzZQ0KICAgICAgdGhpcy5vcGVyYXRpb25UeXBlID0gJ3J1bicNCiAgICB9LA0KICAgIGNvcHkoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLnNob3dGaWxlTmFtZSA9IGZhbHNlDQogICAgICB0aGlzLm9wZXJhdGlvblR5cGUgPSAnY29weScNCiAgICB9LA0KICAgIHRhZ0NoYW5nZShuZXdUYWcpIHsNCiAgICAgIG5ld1RhZyA9IHRoaXMuY2xvbmVDb21wb25lbnQobmV3VGFnKQ0KICAgICAgbmV3VGFnLnZNb2RlbCA9IHRoaXMuYWN0aXZlRGF0YS52TW9kZWwNCiAgICAgIG5ld1RhZy5mb3JtSWQgPSB0aGlzLmFjdGl2ZUlkDQogICAgICBuZXdUYWcuc3BhbiA9IHRoaXMuYWN0aXZlRGF0YS5zcGFuDQogICAgICBkZWxldGUgdGhpcy5hY3RpdmVEYXRhLnRhZw0KICAgICAgZGVsZXRlIHRoaXMuYWN0aXZlRGF0YS50YWdJY29uDQogICAgICBkZWxldGUgdGhpcy5hY3RpdmVEYXRhLmRvY3VtZW50DQogICAgICBPYmplY3Qua2V5cyhuZXdUYWcpLmZvckVhY2goa2V5ID0+IHsNCiAgICAgICAgaWYgKHRoaXMuYWN0aXZlRGF0YVtrZXldICE9PSB1bmRlZmluZWQNCiAgICAgICAgICAmJiB0eXBlb2YgdGhpcy5hY3RpdmVEYXRhW2tleV0gPT09IHR5cGVvZiBuZXdUYWdba2V5XSkgew0KICAgICAgICAgIG5ld1RhZ1trZXldID0gdGhpcy5hY3RpdmVEYXRhW2tleV0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMuYWN0aXZlRGF0YSA9IG5ld1RhZw0KICAgICAgdGhpcy51cGRhdGVEcmF3aW5nTGlzdChuZXdUYWcsIHRoaXMuZHJhd2luZ0xpc3QpDQogICAgfSwNCiAgICB1cGRhdGVEcmF3aW5nTGlzdChuZXdUYWcsIGxpc3QpIHsNCiAgICAgIGNvbnN0IGluZGV4ID0gbGlzdC5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLmZvcm1JZCA9PT0gdGhpcy5hY3RpdmVJZCkNCiAgICAgIGlmIChpbmRleCA+IC0xKSB7DQogICAgICAgIGxpc3Quc3BsaWNlKGluZGV4LCAxLCBuZXdUYWcpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBsaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoaXRlbS5jaGlsZHJlbikpIHRoaXMudXBkYXRlRHJhd2luZ0xpc3QobmV3VGFnLCBpdGVtLmNoaWxkcmVuKQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <div class=\"left-board\">\r\n      <div class=\"logo-wrapper\">\r\n        <div class=\"logo\">\r\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\r\n        </div>\r\n      </div>\r\n      <el-scrollbar class=\"left-scrollbar\">\r\n        <div class=\"components-list\">\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" />输入型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\"\r\n            :list=\"inputComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :clone=\"cloneComponent\"\r\n            draggable=\".components-item\"\r\n            :sort=\"false\"\r\n            @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in inputComponents\" :key=\"index\" class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" />选择型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\"\r\n            :list=\"selectComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :clone=\"cloneComponent\"\r\n            draggable=\".components-item\"\r\n            :sort=\"false\"\r\n            @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in selectComponents\"\r\n              :key=\"index\"\r\n              class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" /> 布局型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\" :list=\"layoutComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\" :clone=\"cloneComponent\"\r\n            draggable=\".components-item\" :sort=\"false\" @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in layoutComponents\" :key=\"index\" class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <div class=\"center-board\">\r\n      <div class=\"action-bar\">\r\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\r\n          导出vue文件\r\n        </el-button>\r\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\r\n          复制代码\r\n        </el-button>\r\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\r\n          清空\r\n        </el-button>\r\n      </div>\r\n      <el-scrollbar class=\"center-scrollbar\">\r\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\r\n          <el-form\r\n            :size=\"formConf.size\"\r\n            :label-position=\"formConf.labelPosition\"\r\n            :disabled=\"formConf.disabled\"\r\n            :label-width=\"formConf.labelWidth + 'px'\"\r\n          >\r\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\r\n              <draggable-item\r\n                v-for=\"(element, index) in drawingList\"\r\n                :key=\"element.renderKey\"\r\n                :drawing-list=\"drawingList\"\r\n                :element=\"element\"\r\n                :index=\"index\"\r\n                :active-id=\"activeId\"\r\n                :form-conf=\"formConf\"\r\n                @activeItem=\"activeFormItem\"\r\n                @copyItem=\"drawingItemCopy\"\r\n                @deleteItem=\"drawingItemDelete\"\r\n              />\r\n            </draggable>\r\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\r\n              从左侧拖入或点选组件进行表单设计\r\n            </div>\r\n          </el-form>\r\n        </el-row>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <right-panel\r\n      :active-data=\"activeData\"\r\n      :form-conf=\"formConf\"\r\n      :show-field=\"!!drawingList.length\"\r\n      @tag-change=\"tagChange\"\r\n    />\r\n\r\n    <code-type-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"选择生成类型\"\r\n      :show-file-name=\"showFileName\"\r\n      @confirm=\"generate\"\r\n    />\r\n    <input id=\"copyNode\" type=\"hidden\">\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport draggable from 'vuedraggable'\r\nimport beautifier from 'js-beautify'\r\nimport ClipboardJS from 'clipboard'\r\nimport render from '@/utils/generator/render'\r\nimport RightPanel from './RightPanel'\r\nimport { inputComponents, selectComponents, layoutComponents, formConf } from '@/utils/generator/config'\r\nimport { beautifierConf, titleCase } from '@/utils/index'\r\nimport { makeUpHtml, vueTemplate, vueScript, cssStyle } from '@/utils/generator/html'\r\nimport { makeUpJs } from '@/utils/generator/js'\r\nimport { makeUpCss } from '@/utils/generator/css'\r\nimport drawingDefault from '@/utils/generator/drawingDefault'\r\nimport logo from '@/assets/logo/logo.png'\r\nimport CodeTypeDialog from './CodeTypeDialog'\r\nimport DraggableItem from './DraggableItem'\r\n\r\nlet oldActiveId\r\nlet tempActiveData\r\n\r\nexport default {\r\n  components: {\r\n    draggable,\r\n    render,\r\n    RightPanel,\r\n    CodeTypeDialog,\r\n    DraggableItem\r\n  },\r\n  data() {\r\n    return {\r\n      logo,\r\n      idGlobal: 100,\r\n      formConf,\r\n      inputComponents,\r\n      selectComponents,\r\n      layoutComponents,\r\n      labelWidth: 100,\r\n      drawingList: drawingDefault,\r\n      drawingData: {},\r\n      activeId: drawingDefault[0].formId,\r\n      drawerVisible: false,\r\n      formData: {},\r\n      dialogVisible: false,\r\n      generateConf: null,\r\n      showFileName: false,\r\n      activeData: drawingDefault[0]\r\n    }\r\n  },\r\n  created() {\r\n    // 防止 firefox 下 拖拽 会新打卡一个选项卡\r\n    document.body.ondrop = event => {\r\n      event.preventDefault()\r\n      event.stopPropagation()\r\n    }\r\n  },\r\n  watch: {\r\n    // eslint-disable-next-line func-names\r\n    'activeData.label': function (val, oldVal) {\r\n      if (\r\n        this.activeData.placeholder === undefined\r\n        || !this.activeData.tag\r\n        || oldActiveId !== this.activeId\r\n      ) {\r\n        return\r\n      }\r\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\r\n    },\r\n    activeId: {\r\n      handler(val) {\r\n        oldActiveId = val\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    const clipboard = new ClipboardJS('#copyNode', {\r\n      text: trigger => {\r\n        const codeStr = this.generateCode()\r\n        this.$notify({\r\n          title: '成功',\r\n          message: '代码已复制到剪切板，可粘贴。',\r\n          type: 'success'\r\n        })\r\n        return codeStr\r\n      }\r\n    })\r\n    clipboard.on('error', e => {\r\n      this.$message.error('代码复制失败')\r\n    })\r\n  },\r\n  methods: {\r\n    activeFormItem(element) {\r\n      this.activeData = element\r\n      this.activeId = element.formId\r\n    },\r\n    onEnd(obj, a) {\r\n      if (obj.from !== obj.to) {\r\n        this.activeData = tempActiveData\r\n        this.activeId = this.idGlobal\r\n      }\r\n    },\r\n    addComponent(item) {\r\n      const clone = this.cloneComponent(item)\r\n      this.drawingList.push(clone)\r\n      this.activeFormItem(clone)\r\n    },\r\n    cloneComponent(origin) {\r\n      const clone = JSON.parse(JSON.stringify(origin))\r\n      clone.formId = ++this.idGlobal\r\n      clone.span = formConf.span\r\n      clone.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件\r\n      if (!clone.layout) clone.layout = 'colFormItem'\r\n      if (clone.layout === 'colFormItem') {\r\n        clone.vModel = `field${this.idGlobal}`\r\n        clone.placeholder !== undefined && (clone.placeholder += clone.label)\r\n        tempActiveData = clone\r\n      } else if (clone.layout === 'rowFormItem') {\r\n        delete clone.label\r\n        clone.componentName = `row${this.idGlobal}`\r\n        clone.gutter = this.formConf.gutter\r\n        tempActiveData = clone\r\n      }\r\n      return tempActiveData\r\n    },\r\n    AssembleFormData() {\r\n      this.formData = {\r\n        fields: JSON.parse(JSON.stringify(this.drawingList)),\r\n        ...this.formConf\r\n      }\r\n    },\r\n    generate(data) {\r\n      const func = this[`exec${titleCase(this.operationType)}`]\r\n      this.generateConf = data\r\n      func && func(data)\r\n    },\r\n    execRun(data) {\r\n      this.AssembleFormData()\r\n      this.drawerVisible = true\r\n    },\r\n    execDownload(data) {\r\n      const codeStr = this.generateCode()\r\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\r\n      this.$download.saveAs(blob, data.fileName)\r\n    },\r\n    execCopy(data) {\r\n      document.getElementById('copyNode').click()\r\n    },\r\n    empty() {\r\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\r\n        () => {\r\n          this.drawingList = []\r\n        }\r\n      )\r\n    },\r\n    drawingItemCopy(item, parent) {\r\n      let clone = JSON.parse(JSON.stringify(item))\r\n      clone = this.createIdAndKey(clone)\r\n      parent.push(clone)\r\n      this.activeFormItem(clone)\r\n    },\r\n    createIdAndKey(item) {\r\n      item.formId = ++this.idGlobal\r\n      item.renderKey = +new Date()\r\n      if (item.layout === 'colFormItem') {\r\n        item.vModel = `field${this.idGlobal}`\r\n      } else if (item.layout === 'rowFormItem') {\r\n        item.componentName = `row${this.idGlobal}`\r\n      }\r\n      if (Array.isArray(item.children)) {\r\n        item.children = item.children.map(childItem => this.createIdAndKey(childItem))\r\n      }\r\n      return item\r\n    },\r\n    drawingItemDelete(index, parent) {\r\n      parent.splice(index, 1)\r\n      this.$nextTick(() => {\r\n        const len = this.drawingList.length\r\n        if (len) {\r\n          this.activeFormItem(this.drawingList[len - 1])\r\n        }\r\n      })\r\n    },\r\n    generateCode() {\r\n      const { type } = this.generateConf\r\n      this.AssembleFormData()\r\n      const script = vueScript(makeUpJs(this.formData, type))\r\n      const html = vueTemplate(makeUpHtml(this.formData, type))\r\n      const css = cssStyle(makeUpCss(this.formData))\r\n      return beautifier.html(html + script + css, beautifierConf.html)\r\n    },\r\n    download() {\r\n      this.dialogVisible = true\r\n      this.showFileName = true\r\n      this.operationType = 'download'\r\n    },\r\n    run() {\r\n      this.dialogVisible = true\r\n      this.showFileName = false\r\n      this.operationType = 'run'\r\n    },\r\n    copy() {\r\n      this.dialogVisible = true\r\n      this.showFileName = false\r\n      this.operationType = 'copy'\r\n    },\r\n    tagChange(newTag) {\r\n      newTag = this.cloneComponent(newTag)\r\n      newTag.vModel = this.activeData.vModel\r\n      newTag.formId = this.activeId\r\n      newTag.span = this.activeData.span\r\n      delete this.activeData.tag\r\n      delete this.activeData.tagIcon\r\n      delete this.activeData.document\r\n      Object.keys(newTag).forEach(key => {\r\n        if (this.activeData[key] !== undefined\r\n          && typeof this.activeData[key] === typeof newTag[key]) {\r\n          newTag[key] = this.activeData[key]\r\n        }\r\n      })\r\n      this.activeData = newTag\r\n      this.updateDrawingList(newTag, this.drawingList)\r\n    },\r\n    updateDrawingList(newTag, list) {\r\n      const index = list.findIndex(item => item.formId === this.activeId)\r\n      if (index > -1) {\r\n        list.splice(index, 1, newTag)\r\n      } else {\r\n        list.forEach(item => {\r\n          if (Array.isArray(item.children)) this.updateDrawingList(newTag, item.children)\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang='scss'>\r\nbody, html{\r\n  margin: 0;\r\n  padding: 0;\r\n  background: #fff;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  -webkit-font-smoothing: antialiased;\r\n  text-rendering: optimizeLegibility;\r\n  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;\r\n}\r\n\r\ninput, textarea{\r\n  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;\r\n}\r\n\r\n.editor-tabs{\r\n  background: #121315;\r\n  .el-tabs__header{\r\n    margin: 0;\r\n    border-bottom-color: #121315;\r\n    .el-tabs__nav{\r\n      border-color: #121315;\r\n    }\r\n  }\r\n  .el-tabs__item{\r\n    height: 32px;\r\n    line-height: 32px;\r\n    color: #888a8e;\r\n    border-left: 1px solid #121315 !important;\r\n    background: #363636;\r\n    margin-right: 5px;\r\n    user-select: none;\r\n  }\r\n  .el-tabs__item.is-active{\r\n    background: #1e1e1e;\r\n    border-bottom-color: #1e1e1e!important;\r\n    color: #fff;\r\n  }\r\n  .el-icon-edit{\r\n    color: #f1fa8c;\r\n  }\r\n  .el-icon-document{\r\n    color: #a95812;\r\n  }\r\n}\r\n\r\n// home\r\n.right-scrollbar {\r\n  .el-scrollbar__view {\r\n    padding: 12px 18px 15px 15px;\r\n  }\r\n}\r\n.left-scrollbar .el-scrollbar__wrap {\r\n  box-sizing: border-box;\r\n  overflow-x: hidden !important;\r\n  margin-bottom: 0 !important;\r\n}\r\n.center-tabs{\r\n  .el-tabs__header{\r\n    margin-bottom: 0!important;\r\n  }\r\n  .el-tabs__item{\r\n    width: 50%;\r\n    text-align: center;\r\n  }\r\n  .el-tabs__nav{\r\n    width: 100%;\r\n  }\r\n}\r\n.reg-item{\r\n  padding: 12px 6px;\r\n  background: #f8f8f8;\r\n  position: relative;\r\n  border-radius: 4px;\r\n  .close-btn{\r\n    position: absolute;\r\n    right: -6px;\r\n    top: -6px;\r\n    display: block;\r\n    width: 16px;\r\n    height: 16px;\r\n    line-height: 16px;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    border-radius: 50%;\r\n    color: #fff;\r\n    text-align: center;\r\n    z-index: 1;\r\n    cursor: pointer;\r\n    font-size: 12px;\r\n    &:hover{\r\n      background: rgba(210, 23, 23, 0.5)\r\n    }\r\n  }\r\n  & + .reg-item{\r\n    margin-top: 18px;\r\n  }\r\n}\r\n.action-bar{\r\n  & .el-button+.el-button {\r\n    margin-left: 15px;\r\n  }\r\n  & i {\r\n    font-size: 20px;\r\n    vertical-align: middle;\r\n    position: relative;\r\n    top: -1px;\r\n  }\r\n}\r\n\r\n.custom-tree-node{\r\n  width: 100%;\r\n  font-size: 14px;\r\n  .node-operation{\r\n    float: right;\r\n  }\r\n  i[class*=\"el-icon\"] + i[class*=\"el-icon\"]{\r\n    margin-left: 6px;\r\n  }\r\n  .el-icon-plus{\r\n    color: #409EFF;\r\n  }\r\n  .el-icon-delete{\r\n    color: #157a0c;\r\n  }\r\n}\r\n\r\n.left-scrollbar .el-scrollbar__view{\r\n  overflow-x: hidden;\r\n}\r\n\r\n.el-rate{\r\n  display: inline-block;\r\n  vertical-align: text-top;\r\n}\r\n.el-upload__tip{\r\n  line-height: 1.2;\r\n}\r\n\r\n$selectedColor: #f6f7ff;\r\n$lighterBlue: #409EFF;\r\n\r\n.container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.components-list {\r\n  padding: 8px;\r\n  box-sizing: border-box;\r\n  height: 100%;\r\n  .components-item {\r\n    display: inline-block;\r\n    width: 48%;\r\n    margin: 1%;\r\n    transition: transform 0ms !important;\r\n  }\r\n}\r\n.components-draggable{\r\n  padding-bottom: 20px;\r\n}\r\n.components-title{\r\n  font-size: 14px;\r\n  color: #222;\r\n  margin: 6px 2px;\r\n  .svg-icon{\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n.components-body {\r\n  padding: 8px 10px;\r\n  background: $selectedColor;\r\n  font-size: 12px;\r\n  cursor: move;\r\n  border: 1px dashed $selectedColor;\r\n  border-radius: 3px;\r\n  .svg-icon{\r\n    color: #777;\r\n    font-size: 15px;\r\n  }\r\n  &:hover {\r\n    border: 1px dashed #787be8;\r\n    color: #787be8;\r\n    .svg-icon {\r\n      color: #787be8;\r\n    }\r\n  }\r\n}\r\n\r\n.left-board {\r\n  width: 260px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  height: 100vh;\r\n}\r\n.left-scrollbar{\r\n  height: calc(100vh - 42px);\r\n  overflow: hidden;\r\n}\r\n.center-scrollbar {\r\n  height: calc(100vh - 42px);\r\n  overflow: hidden;\r\n  border-left: 1px solid #f1e8e8;\r\n  border-right: 1px solid #f1e8e8;\r\n  box-sizing: border-box;\r\n}\r\n.center-board {\r\n  height: 100vh;\r\n  width: auto;\r\n  margin: 0 350px 0 260px;\r\n  box-sizing: border-box;\r\n}\r\n.empty-info{\r\n  position: absolute;\r\n  top: 46%;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  color: #ccb1ea;\r\n  letter-spacing: 4px;\r\n}\r\n.action-bar{\r\n  position: relative;\r\n  height: 42px;\r\n  text-align: right;\r\n  padding: 0 15px;\r\n  box-sizing: border-box;;\r\n  border: 1px solid #f1e8e8;\r\n  border-top: none;\r\n  border-left: none;\r\n  .delete-btn{\r\n    color: #F56C6C;\r\n  }\r\n}\r\n.logo-wrapper{\r\n  position: relative;\r\n  height: 42px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #f1e8e8;\r\n  box-sizing: border-box;\r\n}\r\n.logo{\r\n  position: absolute;\r\n  left: 12px;\r\n  top: 6px;\r\n  line-height: 30px;\r\n  color: #00afff;\r\n  font-weight: 600;\r\n  font-size: 17px;\r\n  white-space: nowrap;\r\n  > img{\r\n    width: 30px;\r\n    height: 30px;\r\n    vertical-align: top;\r\n  }\r\n  .github{\r\n    display: inline-block;\r\n    vertical-align: sub;\r\n    margin-left: 15px;\r\n    > img{\r\n      height: 22px;\r\n    }\r\n  }\r\n}\r\n\r\n.center-board-row {\r\n  padding: 12px 12px 15px 12px;\r\n  box-sizing: border-box;\r\n  & > .el-form {\r\n    // 69 = 12+15+42\r\n    height: calc(100vh - 69px);\r\n  }\r\n}\r\n.drawing-board {\r\n  height: 100%;\r\n  position: relative;\r\n  .components-body {\r\n    padding: 0;\r\n    margin: 0;\r\n    font-size: 0;\r\n  }\r\n  .sortable-ghost {\r\n    position: relative;\r\n    display: block;\r\n    overflow: hidden;\r\n    &::before {\r\n      content: \" \";\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      height: 3px;\r\n      background: rgb(89, 89, 223);\r\n      z-index: 2;\r\n    }\r\n  }\r\n  .components-item.sortable-ghost {\r\n    width: 100%;\r\n    height: 60px;\r\n    background-color: $selectedColor;\r\n  }\r\n  .active-from-item {\r\n    & > .el-form-item{\r\n      background: $selectedColor;\r\n      border-radius: 6px;\r\n    }\r\n    & > .drawing-item-copy, & > .drawing-item-delete{\r\n      display: initial;\r\n    }\r\n    & > .component-name{\r\n      color: $lighterBlue;\r\n    }\r\n  }\r\n  .el-form-item{\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n.drawing-item{\r\n  position: relative;\r\n  cursor: move;\r\n  &.unfocus-bordered:not(.activeFromItem) > div:first-child  {\r\n    border: 1px dashed #ccc;\r\n  }\r\n  .el-form-item{\r\n    padding: 12px 10px;\r\n  }\r\n}\r\n.drawing-row-item{\r\n  position: relative;\r\n  cursor: move;\r\n  box-sizing: border-box;\r\n  border: 1px dashed #ccc;\r\n  border-radius: 3px;\r\n  padding: 0 2px;\r\n  margin-bottom: 15px;\r\n  .drawing-row-item {\r\n    margin-bottom: 2px;\r\n  }\r\n  .el-col{\r\n    margin-top: 22px;\r\n  }\r\n  .el-form-item{\r\n    margin-bottom: 0;\r\n  }\r\n  .drag-wrapper{\r\n    min-height: 80px;\r\n  }\r\n  &.active-from-item{\r\n    border: 1px dashed $lighterBlue;\r\n  }\r\n  .component-name{\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    font-size: 12px;\r\n    color: #bbb;\r\n    display: inline-block;\r\n    padding: 0 6px;\r\n  }\r\n}\r\n.drawing-item, .drawing-row-item{\r\n  &:hover {\r\n    & > .el-form-item{\r\n      background: $selectedColor;\r\n      border-radius: 6px;\r\n    }\r\n    & > .drawing-item-copy, & > .drawing-item-delete{\r\n      display: initial;\r\n    }\r\n  }\r\n  & > .drawing-item-copy, & > .drawing-item-delete{\r\n    display: none;\r\n    position: absolute;\r\n    top: -10px;\r\n    width: 22px;\r\n    height: 22px;\r\n    line-height: 22px;\r\n    text-align: center;\r\n    border-radius: 50%;\r\n    font-size: 12px;\r\n    border: 1px solid;\r\n    cursor: pointer;\r\n    z-index: 1;\r\n  }\r\n  & > .drawing-item-copy{\r\n    right: 56px;\r\n    border-color: $lighterBlue;\r\n    color: $lighterBlue;\r\n    background: #fff;\r\n    &:hover{\r\n      background: $lighterBlue;\r\n      color: #fff;\r\n    }\r\n  }\r\n  & > .drawing-item-delete{\r\n    right: 24px;\r\n    border-color: #F56C6C;\r\n    color: #F56C6C;\r\n    background: #fff;\r\n    &:hover{\r\n      background: #F56C6C;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}