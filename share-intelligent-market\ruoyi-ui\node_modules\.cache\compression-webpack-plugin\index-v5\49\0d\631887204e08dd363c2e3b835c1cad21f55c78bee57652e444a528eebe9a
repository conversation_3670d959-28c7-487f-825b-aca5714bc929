
945c456f015d57b8af5909217b25a86c54011067	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fcontent\\u002Fdark\\u002Fcontent.min.css\",\"contentHash\":\"30200abda77c7ba564975d9b1f82ddfa\"}","integrity":"sha512-6dA+lH+Wqml7JYmGf2hwy5+AYlZ78kYU4FImn5OMBlFg+Uc5wjRheew5duejfhf2yGNneYI2FRo3QzeamMS6qw==","time":1750496064272,"size":1791}