
3419fc4c1849eca3fd5b959ebf9ff48fccd2f6eb	{"key":"{\"nodeVersion\":\"v18.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"tinymce\\u002Fskins\\u002Fui\\u002Foxide\\u002Fskin.min.css\",\"contentHash\":\"9e51f08f4e8f8e9a46b135ae642c0cde\"}","integrity":"sha512-kbhKtSTFf9DxB9S+R2srwepJYekw1y4sYe0Kedy9LOaghna7G/8dhChlwrTTaPUBJlJE2V3mAtbJsG4MoWr/FQ==","time":1750496064276,"size":40552}