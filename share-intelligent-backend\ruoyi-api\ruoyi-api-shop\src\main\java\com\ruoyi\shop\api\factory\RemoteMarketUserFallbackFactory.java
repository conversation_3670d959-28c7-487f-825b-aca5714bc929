package com.ruoyi.shop.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.shop.api.RemoteMarketUserService;
import com.ruoyi.shop.api.domain.MarketUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 市场系统用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteMarketUserFallbackFactory implements FallbackFactory<RemoteMarketUserService> {
    
    private static final Logger log = LoggerFactory.getLogger(RemoteMarketUserFallbackFactory.class);

    @Override
    public RemoteMarketUserService create(Throwable throwable) {
        log.error("市场系统用户服务调用失败:{}", throwable.getMessage());
        return new RemoteMarketUserService() {
            
            @Override
            public R<MarketUser> getUserInfoByTelphone(String telphone, String source) {
                return R.fail("获取市场系统用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> registerUserInfo(MarketUser marketUser, String source) {
                return R.fail("注册市场系统用户失败:" + throwable.getMessage());
            }
        };
    }
}
