{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\login.vue", "mtime": 1750151094240}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "_js<PERSON><PERSON>ie", "_interopRequireDefault", "_jsencrypt", "name", "data", "codeUrl", "loginForm", "username", "password", "rememberMe", "code", "uuid", "loginRules", "required", "trigger", "message", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "redirect", "undefined", "watch", "$route", "handler", "route", "query", "immediate", "created", "getCode", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this", "getCodeImg", "then", "res", "console", "log", "img", "Cookies", "get", "decrypt", "Boolean", "handleLogin", "_this2", "$refs", "validate", "valid", "set", "expires", "encrypt", "remove", "$store", "dispatch", "$router", "push", "path", "catch"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\r\n      <h3 class=\"title\">产销平台</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaOnOff\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width:100%;\"\r\n          @click.native.prevent=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right;\" v-if=\"register\">\r\n          <router-link class=\"link-type\" :to=\"'/register'\">立即注册</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span>Copyright © 2018-2022 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from \"@/api/login\";\r\nimport Cookies from \"js-cookie\";\r\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\r\n\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      codeUrl: \"\",\r\n      loginForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        rememberMe: false,\r\n        code: \"\",\r\n        uuid: \"\"\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\r\n        ],\r\n        // code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\r\n      },\r\n      loading: false,\r\n      // 验证码开关\r\n      captchaOnOff: true,\r\n      // 注册开关\r\n      register: false,\r\n      redirect: undefined\r\n    };\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        this.redirect = route.query && route.query.redirect;\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode();\r\n    this.getCookie();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        console.log(res)\r\n        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;\r\n        if (this.captchaOnOff) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.loginForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    getCookie() {\r\n      const username = Cookies.get(\"username\");\r\n      const password = Cookies.get(\"password\");\r\n      const rememberMe = Cookies.get('rememberMe')\r\n      this.loginForm = {\r\n        username: username === undefined ? this.loginForm.username : username,\r\n        password: password === undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)\r\n      };\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\r\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 });\r\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });\r\n          } else {\r\n            Cookies.remove(\"username\");\r\n            Cookies.remove(\"password\");\r\n            Cookies.remove('rememberMe');\r\n          }\r\n          this.$store.dispatch(\"Login\", this.loginForm).then(() => {\r\n            this.$router.push({ path: \"/\" }).catch(()=>{});\r\n          }).catch(() => {\r\n            this.loading = false;\r\n            if (this.captchaOnOff) {\r\n              this.getCode();\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAgEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,UAAA;QACAL,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEA;MACA;MACAC,OAAA;MACA;MACAC,YAAA;MACA;MACAC,QAAA;MACAC,QAAA,EAAAC;IACA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,KAAA;QACA,KAAAL,QAAA,GAAAK,KAAA,CAAAC,KAAA,IAAAD,KAAA,CAAAC,KAAA,CAAAN,QAAA;MACA;MACAO,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAH,KAAA,CAAAd,YAAA,GAAAiB,GAAA,CAAAjB,YAAA,KAAAG,SAAA,UAAAc,GAAA,CAAAjB,YAAA;QACA,IAAAc,KAAA,CAAAd,YAAA;UACAc,KAAA,CAAA1B,OAAA,8BAAA6B,GAAA,CAAAG,GAAA;UACAN,KAAA,CAAAzB,SAAA,CAAAK,IAAA,GAAAuB,GAAA,CAAAvB,IAAA;QACA;MACA;IACA;IACAkB,SAAA,WAAAA,UAAA;MACA,IAAAtB,QAAA,GAAA+B,iBAAA,CAAAC,GAAA;MACA,IAAA/B,QAAA,GAAA8B,iBAAA,CAAAC,GAAA;MACA,IAAA9B,UAAA,GAAA6B,iBAAA,CAAAC,GAAA;MACA,KAAAjC,SAAA;QACAC,QAAA,EAAAA,QAAA,KAAAa,SAAA,QAAAd,SAAA,CAAAC,QAAA,GAAAA,QAAA;QACAC,QAAA,EAAAA,QAAA,KAAAY,SAAA,QAAAd,SAAA,CAAAE,QAAA,OAAAgC,kBAAA,EAAAhC,QAAA;QACAC,UAAA,EAAAA,UAAA,KAAAW,SAAA,WAAAqB,OAAA,CAAAhC,UAAA;MACA;IACA;IACAiC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAtC,SAAA,CAAAuC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA3B,OAAA;UACA,IAAA2B,MAAA,CAAArC,SAAA,CAAAG,UAAA;YACA6B,iBAAA,CAAAS,GAAA,aAAAJ,MAAA,CAAArC,SAAA,CAAAC,QAAA;cAAAyC,OAAA;YAAA;YACAV,iBAAA,CAAAS,GAAA,iBAAAE,kBAAA,EAAAN,MAAA,CAAArC,SAAA,CAAAE,QAAA;cAAAwC,OAAA;YAAA;YACAV,iBAAA,CAAAS,GAAA,eAAAJ,MAAA,CAAArC,SAAA,CAAAG,UAAA;cAAAuC,OAAA;YAAA;UACA;YACAV,iBAAA,CAAAY,MAAA;YACAZ,iBAAA,CAAAY,MAAA;YACAZ,iBAAA,CAAAY,MAAA;UACA;UACAP,MAAA,CAAAQ,MAAA,CAAAC,QAAA,UAAAT,MAAA,CAAArC,SAAA,EAAA2B,IAAA;YACAU,MAAA,CAAAU,OAAA,CAAAC,IAAA;cAAAC,IAAA;YAAA,GAAAC,KAAA;UACA,GAAAA,KAAA;YACAb,MAAA,CAAA3B,OAAA;YACA,IAAA2B,MAAA,CAAA1B,YAAA;cACA0B,MAAA,CAAAf,OAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}