{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\system\\user.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\api\\system\\user.js", "mtime": 1750151093989}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750495810570}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "_ruoyi", "listUser", "query", "request", "url", "method", "params", "getUser", "userId", "parseStrEmpty", "addUser", "data", "updateUser", "<PERSON><PERSON><PERSON>", "resetUserPwd", "password", "changeUserStatus", "status", "getUserProfile", "updateUserProfile", "updateUserPwd", "oldPassword", "newPassword", "uploadAvatar", "getAuthRole", "updateAuthRole"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-market/ruoyi-ui/src/api/system/user.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport { parseStrEmpty } from \"@/utils/ruoyi\";\r\n\r\n// 查询用户列表\r\nexport function listUser(query) {\r\n  return request({\r\n    url: '/system/user/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询用户详细\r\nexport function getUser(userId) {\r\n  return request({\r\n    url: '/system/user/' + parseStrEmpty(userId),\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增用户\r\nexport function addUser(data) {\r\n  return request({\r\n    url: '/system/user',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改用户\r\nexport function updateUser(data) {\r\n  return request({\r\n    url: '/system/user',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除用户\r\nexport function delUser(userId) {\r\n  return request({\r\n    url: '/system/user/' + userId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function resetUserPwd(userId, password) {\r\n  const data = {\r\n    userId,\r\n    password\r\n  }\r\n  return request({\r\n    url: '/system/user/resetPwd',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户状态修改\r\nexport function changeUserStatus(userId, status) {\r\n  const data = {\r\n    userId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/user/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询用户个人信息\r\nexport function getUserProfile() {\r\n  return request({\r\n    url: '/system/user/profile',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改用户个人信息\r\nexport function updateUserProfile(data) {\r\n  return request({\r\n    url: '/system/user/profile',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function updateUserPwd(oldPassword, newPassword) {\r\n  const data = {\r\n    oldPassword,\r\n    newPassword\r\n  }\r\n  return request({\r\n    url: '/system/user/profile/updatePwd',\r\n    method: 'put',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 用户头像上传\r\nexport function uploadAvatar(data) {\r\n  return request({\r\n    url: '/shop/data/upload/image',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询授权角色\r\nexport function getAuthRole(userId) {\r\n  return request({\r\n    url: '/system/user/authRole/' + userId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 保存授权角色\r\nexport function updateAuthRole(data) {\r\n  return request({\r\n    url: '/system/user/authRole',\r\n    method: 'put',\r\n    params: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA;AACO,SAASE,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAG,IAAAK,oBAAa,EAACD,MAAM,CAAC;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACN,MAAM,EAAEO,QAAQ,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNO,QAAQ,EAARA;EACF,CAAC;EACD,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACR,MAAM,EAAES,MAAM,EAAE;EAC/C,IAAMN,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNS,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,iBAAiBA,CAACR,IAAI,EAAE;EACtC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,aAAaA,CAACC,WAAW,EAAEC,WAAW,EAAE;EACtD,IAAMX,IAAI,GAAG;IACXU,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EACF,CAAC;EACD,OAAO,IAAAnB,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,YAAYA,CAACZ,IAAI,EAAE;EACjC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,WAAWA,CAAChB,MAAM,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,MAAM;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASoB,cAAcA,CAACd,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}