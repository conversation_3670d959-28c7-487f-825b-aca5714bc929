{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\msg\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\src\\views\\msg\\list.vue", "mtime": 1750151094247}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\babel.config.js", "mtime": 1750151093878}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750495815049}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750495809566}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-market\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750495816682}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_list", "require", "name", "data", "_defineProperty2", "default", "optionsStatus", "value", "label", "normsList", "loading", "show", "title", "form", "rules", "content", "required", "message", "trigger", "pageNum", "pageSize", "isread", "created", "getList", "methods", "handleReade", "row", "_this", "setStatus", "opid", "id", "status", "then", "res", "type", "_this2", "response", "code", "$message", "msg", "uploadNorm", "fileList", "undefined", "url", "length", "normfile", "<PERSON><PERSON><PERSON>", "console", "log", "_this3", "listData", "queryParams", "list", "total", "count", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "ids", "map", "item", "target_id", "single", "multiple", "handleAdd", "add", "handleUpdate", "_this4", "inforId", "JSON", "parse", "stringify", "getData", "edit", "handleDelete", "_this5", "inforIds", "join", "$modal", "confirm", "delData", "msgSuccess", "catch", "handleCopy", "clipboardObj", "navigator", "clipboard", "writeText", "reset", "handleSubmit", "_this6", "$refs", "validate", "addData", "$emit", "msgError"], "sources": ["src/views/msg/list.vue"], "sourcesContent": ["// 消息管理\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"是否已读\" prop=\"name\">\r\n          <el-select clearable  v-model=\"queryParams.isread\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"item in optionsStatus\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\">\r\n            </el-option>\r\n          </el-select>\r\n        <!-- <el-input clearable v-model=\"queryParams.name\" style=\"width: 300px\" placeholder=\"请输入名称\" :maxlength=\"60\"\r\n          @keyup.enter.native=\"handleQuery\" /> -->\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col>\r\n\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\">删除\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.$index + 1 }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标题\" align=\"center\" prop=\"title\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"内容\" align=\"center\" prop=\"content\" />\r\n\r\n      <el-table-column label=\"时间\" align=\"center\" prop=\"create_time\" sortable />\r\n      <el-table-column label=\"是否已读\" align=\"center\" prop=\"create_by\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag type=\"success\" v-if=\"scope.row.is_read == 1\">已读</el-tag>\r\n          <el-tag type=\"danger\" v-else>未读</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button v-if=\"scope.row.is_read != 1\" size=\"mini\" type=\"text\" @click=\"handleReade(scope.row)\">已读\r\n          </el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\">查看详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" style=\"color: red;\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n    <!-- 添加弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"70%\" :before-close=\"() => (show = false)\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" :rules=\"rules\">\r\n\r\n        <el-form-item label=\"内容\" prop=\"content\">\r\n          <el-input :disabled=\"form.id?true:false\" :autosize=\"{ minRows: 2, maxRows: 5}\" clearable v-model=\"form.content\" placeholder=\"请输入名称\"\r\n            type=\"textarea\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"时间\" prop=\"create_time\" v-if=\"form.id\">\r\n          <el-input :disabled=\"form.id?true:false\" v-model=\"form.create_time\" placeholder=\"请输入名称\"\r\n            type=\"text\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否已读\" prop=\"create_time\" v-if=\"form.id\">\r\n          <el-tag type=\"success\" v-if=\"form.is_read == 1\">已读</el-tag>\r\n          <el-tag type=\"danger\" v-else>未读</el-tag>\r\n        </el-form-item>\r\n\r\n\r\n\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"show = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit\" v-if=\"!form.id\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    listData,\r\n    setStatus,\r\n    delData,\r\n    addData,\r\n    getData\r\n  } from \"@/api/msg/list.js\";\r\n  export default {\r\n    name: \"Infor\",\r\n    data() {\r\n      return {\r\n        optionsStatus: [{\r\n            value: 1,\r\n            label: \"已读\",\r\n          },\r\n          {\r\n            value: 0,\r\n            label: \"未读\",\r\n          },\r\n        ],\r\n        normsList: [],\r\n        loading: false,\r\n        show: false,\r\n        title: \"\",\r\n        form: {},\r\n        rules: {\r\n          content: [{\r\n            required: true,\r\n            message: \"请填写内容\",\r\n            trigger: \"blur\",\r\n          }, ],\r\n\r\n        },\r\n\r\n        // 遮罩层\r\n        loading: true,\r\n        // 选中数组\r\n        ids: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 表格数据\r\n        list: [],\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          isread: \"\"\r\n        },\r\n        form: {\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.getList();\r\n    },\r\n    methods: {\r\n      // 确认已读\r\n      handleReade(row) {\r\n        setStatus({\r\n          opid: row.id,\r\n          status: 1\r\n        }).then(res => {\r\n          this.getList()\r\n        })\r\n      },\r\n      // 修改状态\r\n      setStatus(row, type) {\r\n        setStatus({\r\n          opid: row.id,\r\n          status: type\r\n        }).then((response) => {\r\n          if (response.code == 200) {\r\n            this.$message({\r\n              message: response.msg,\r\n              type: \"success\",\r\n            });\r\n            this.getList();\r\n          }\r\n        });\r\n      },\r\n      uploadNorm(fileList) {\r\n        let name = undefined;\r\n        let url = undefined;\r\n        if (fileList.length) {\r\n          name = fileList[0].name;\r\n          url = fileList[0].url;\r\n        }\r\n        this.form.normfile = name;\r\n        this.form.normurl = url;\r\n        console.log(this.form);\r\n      },\r\n      /** 查询公告列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listData(this.queryParams).then((response) => {\r\n          this.list = response.data;\r\n          this.total = response.count;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          isread: \"\"\r\n        };\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n      // 多选框选中数据\r\n      handleSelectionChange(selection) {\r\n        this.ids = selection.map((item) => item.target_id);\r\n        this.single = selection.length != 1;\r\n        this.multiple = !selection.length;\r\n      },\r\n      /** 新增按钮操作 */\r\n      handleAdd() {\r\n        this.add();\r\n      },\r\n      /** 修改按钮操作 */\r\n      handleUpdate(row) {\r\n        const inforId = row.id || this.ids;\r\n\r\n        this.form = JSON.parse(JSON.stringify(row));\r\n        this.title = \"查看详情\";\r\n        this.show = true;\r\n        getData(inforId).then((response) => {\r\n          this.edit(response.data);\r\n        });\r\n      },\r\n      /** 删除按钮操作 */\r\n      handleDelete(row) {\r\n        const inforIds = row.target_id || this.ids.join(\",\");\r\n        this.$modal\r\n          .confirm('是否确认删除编号为\"' + inforIds + '\"的数据项？')\r\n          .then(function() {\r\n            return delData(inforIds);\r\n          })\r\n          .then(() => {\r\n            this.getList();\r\n            this.$modal.msgSuccess(\"删除成功\");\r\n          })\r\n          .catch(() => {});\r\n      },\r\n      handleCopy(row) {\r\n        const clipboardObj = navigator.clipboard;\r\n        this.$message({\r\n          message: \"链接已复制\",\r\n          type: \"success\",\r\n        });\r\n        clipboardObj.writeText(\r\n          \"https://sc.cnudj.com/infor?id=\" + row.id\r\n        );\r\n      },\r\n      reset() {\r\n        this.form = {\r\n\r\n        };\r\n        this.resetForm(\"form\");\r\n      },\r\n      add() {\r\n        this.reset();\r\n        this.title = \"添加\";\r\n        this.show = true;\r\n      },\r\n      edit(data) {\r\n        this.title = \"编辑\";\r\n        this.show = true;\r\n        this.form = data;\r\n      },\r\n      handleSubmit() {\r\n        this.$refs.form.validate((validate) => {\r\n          if (validate) {\r\n            this.loading = true;\r\n            if (!this.form.id) {\r\n              console.log(this.form);\r\n              addData(this.form).then((response) => {\r\n                this.$message({\r\n                  type: \"success\",\r\n                  message: \"操作成功!\",\r\n                });\r\n                this.loading = false;\r\n                this.show = false;\r\n                this.getList()\r\n                this.$emit(\"refresh\");\r\n              });\r\n            }\r\n          } else {\r\n            this.$modal.msgError(\"请完善信息再提交!\");\r\n          }\r\n        });\r\n      },\r\n    },\r\n  };\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA8FA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAOA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,SAAA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,KAAA;QACAC,OAAA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IAAA,cAGA,cAEA,eAEA,mBAEA,qBAEA,gBAEA,YAEA,oBAEA;MACAC,OAAA;MACAC,QAAA;MACAC,MAAA;IACA,YACA,CACA;EAEA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,eAAA;QACAC,IAAA,EAAAH,GAAA,CAAAI,EAAA;QACAC,MAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACAN,KAAA,CAAAJ,OAAA;MACA;IACA;IACA;IACAK,SAAA,WAAAA,UAAAF,GAAA,EAAAQ,IAAA;MAAA,IAAAC,MAAA;MACA,IAAAP,eAAA;QACAC,IAAA,EAAAH,GAAA,CAAAI,EAAA;QACAC,MAAA,EAAAG;MACA,GAAAF,IAAA,WAAAI,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAF,MAAA,CAAAG,QAAA;YACArB,OAAA,EAAAmB,QAAA,CAAAG,GAAA;YACAL,IAAA;UACA;UACAC,MAAA,CAAAZ,OAAA;QACA;MACA;IACA;IACAiB,UAAA,WAAAA,WAAAC,QAAA;MACA,IAAAvC,IAAA,GAAAwC,SAAA;MACA,IAAAC,GAAA,GAAAD,SAAA;MACA,IAAAD,QAAA,CAAAG,MAAA;QACA1C,IAAA,GAAAuC,QAAA,IAAAvC,IAAA;QACAyC,GAAA,GAAAF,QAAA,IAAAE,GAAA;MACA;MACA,KAAA9B,IAAA,CAAAgC,QAAA,GAAA3C,IAAA;MACA,KAAAW,IAAA,CAAAiC,OAAA,GAAAH,GAAA;MACAI,OAAA,CAAAC,GAAA,MAAAnC,IAAA;IACA;IACA,aACAU,OAAA,WAAAA,QAAA;MAAA,IAAA0B,MAAA;MACA,KAAAvC,OAAA;MACA,IAAAwC,cAAA,OAAAC,WAAA,EAAAnB,IAAA,WAAAI,QAAA;QACAa,MAAA,CAAAG,IAAA,GAAAhB,QAAA,CAAAjC,IAAA;QACA8C,MAAA,CAAAI,KAAA,GAAAjB,QAAA,CAAAkB,KAAA;QACAL,MAAA,CAAAvC,OAAA;MACA;IACA;IACA,aACA6C,WAAA,WAAAA,YAAA;MACA,KAAAJ,WAAA,CAAAhC,OAAA;MACA,KAAAI,OAAA;IACA;IACA,aACAiC,UAAA,WAAAA,WAAA;MACA,KAAAL,WAAA;QACAhC,OAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,KAAAoC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAC,GAAA,GAAAD,SAAA,CAAAE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,SAAA;MAAA;MACA,KAAAC,MAAA,GAAAL,SAAA,CAAAf,MAAA;MACA,KAAAqB,QAAA,IAAAN,SAAA,CAAAf,MAAA;IACA;IACA,aACAsB,SAAA,WAAAA,UAAA;MACA,KAAAC,GAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA1C,GAAA;MAAA,IAAA2C,MAAA;MACA,IAAAC,OAAA,GAAA5C,GAAA,CAAAI,EAAA,SAAA8B,GAAA;MAEA,KAAA/C,IAAA,GAAA0D,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA/C,GAAA;MACA,KAAAd,KAAA;MACA,KAAAD,IAAA;MACA,IAAA+D,aAAA,EAAAJ,OAAA,EAAAtC,IAAA,WAAAI,QAAA;QACAiC,MAAA,CAAAM,IAAA,CAAAvC,QAAA,CAAAjC,IAAA;MACA;IACA;IACA,aACAyE,YAAA,WAAAA,aAAAlD,GAAA;MAAA,IAAAmD,MAAA;MACA,IAAAC,QAAA,GAAApD,GAAA,CAAAqC,SAAA,SAAAH,GAAA,CAAAmB,IAAA;MACA,KAAAC,MAAA,CACAC,OAAA,gBAAAH,QAAA,aACA9C,IAAA;QACA,WAAAkD,aAAA,EAAAJ,QAAA;MACA,GACA9C,IAAA;QACA6C,MAAA,CAAAtD,OAAA;QACAsD,MAAA,CAAAG,MAAA,CAAAG,UAAA;MACA,GACAC,KAAA;IACA;IACAC,UAAA,WAAAA,WAAA3D,GAAA;MACA,IAAA4D,YAAA,GAAAC,SAAA,CAAAC,SAAA;MACA,KAAAlD,QAAA;QACArB,OAAA;QACAiB,IAAA;MACA;MACAoD,YAAA,CAAAG,SAAA,CACA,mCAAA/D,GAAA,CAAAI,EACA;IACA;IACA4D,KAAA,WAAAA,MAAA;MACA,KAAA7E,IAAA,IAEA;MACA,KAAA4C,SAAA;IACA;IACAU,GAAA,WAAAA,IAAA;MACA,KAAAuB,KAAA;MACA,KAAA9E,KAAA;MACA,KAAAD,IAAA;IACA;IACAgE,IAAA,WAAAA,KAAAxE,IAAA;MACA,KAAAS,KAAA;MACA,KAAAD,IAAA;MACA,KAAAE,IAAA,GAAAV,IAAA;IACA;IACAwF,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAhF,IAAA,CAAAiF,QAAA,WAAAA,QAAA;QACA,IAAAA,QAAA;UACAF,MAAA,CAAAlF,OAAA;UACA,KAAAkF,MAAA,CAAA/E,IAAA,CAAAiB,EAAA;YACAiB,OAAA,CAAAC,GAAA,CAAA4C,MAAA,CAAA/E,IAAA;YACA,IAAAkF,aAAA,EAAAH,MAAA,CAAA/E,IAAA,EAAAmB,IAAA,WAAAI,QAAA;cACAwD,MAAA,CAAAtD,QAAA;gBACAJ,IAAA;gBACAjB,OAAA;cACA;cACA2E,MAAA,CAAAlF,OAAA;cACAkF,MAAA,CAAAjF,IAAA;cACAiF,MAAA,CAAArE,OAAA;cACAqE,MAAA,CAAAI,KAAA;YACA;UACA;QACA;UACAJ,MAAA,CAAAZ,MAAA,CAAAiB,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}